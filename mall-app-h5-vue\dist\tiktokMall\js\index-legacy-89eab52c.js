System.register(["./index-legacy-46a00900.js"],(function(e,t){"use strict";var a,n,s,r,c,i,d,l=document.createElement("style");return l.textContent=".iframe-content[data-v-fdb38d29]{width:100vw;height:100vh}.iframe-content>iframe[data-v-fdb38d29]{width:100%;height:100%}\n",document.head.appendChild(l),{setters:[e=>{a=e._,n=e.k,s=e.m,r=e.c,c=e.a,i=e.b,d=e.o}],execute:function(){const t={class:"iframe-content"},l=["src"];e("default",a({__name:"index",setup(e){const a=n(),u=s((()=>a.customer_service_url||""));return(e,a)=>(d(),r("div",t,[c("iframe",{src:i(u),class:"flex-1"},null,8,l)]))}},[["__scopeId","data-v-fdb38d29"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/customerServiceOther/index.vue"]]))}}}));
