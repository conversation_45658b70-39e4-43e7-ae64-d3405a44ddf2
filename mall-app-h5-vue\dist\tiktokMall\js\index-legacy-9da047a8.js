System.register(["./index-legacy-46a00900.js","./config-legacy-cd858c6f.js"],(function(e,t){"use strict";var o,a,n,l,s,c,r,i,d,u,f,m,p,v,h,g,k,w,x,y,S,b=document.createElement("style");return b.textContent=".iframe-content[data-v-2edf1d0e]{width:100%;min-height:calc(100vh - 46px);background-color:#fff;overflow-y:scroll}\n",document.head.appendChild(b),{setters:[e=>{o=e._,a=e.d,n=e.Y,l=e.l,s=e.j,c=e.u,r=e.m,i=e.r,d=e.q,u=e.av,f=e.c,m=e.e,p=e.w,v=e.a,h=e.o,g=e.f,k=e.t,w=e.b,x=e.D,y=e.E},e=>{S=e.l}],execute:function(){const t=(e=>(x("data-v-2edf1d0e"),e=e(),y(),e))((()=>v("div",{style:{height:"46px"}},null,-1))),b=["src"],_=a({name:"ShopContractSign"});e("default",o(Object.assign(_,{setup(e){const o=n(),a=l(),x=s(),{t:y,locale:_}=c(),I=S.find((e=>"tiktokMall"===e.key)),$=I?I.name:"Argos",j=r((()=>x.userInfo?.token||"")),C=i(""),{hostname:q,origin:D}=window.location,E="localhost"===q?"https://tkittkit.com/promote/#/pact/":`${D}/promote/#/pact/`;return sessionStorage.removeItem("SellToken"),sessionStorage.setItem("SellToken",j.value),C.value=`${E}?token=${j.value}&lang=${_.value}&name=${$}`,d((()=>{const{back:e}=o.query,t=!!e&&Boolean(Number(e));window.closePopup=async()=>{await x.getUserInfo(!0),t?a.back():a.push("/shop")}})),(e,o)=>{const a=u("fx-header");return h(),f("div",null,[m(a,{fixed:!0},{title:p((()=>[g(k(w(y)("签订电子合同")),1)])),_:1}),t,v("iframe",{src:C.value,class:"iframe-content"},null,8,b)])}}}),[["__scopeId","data-v-2edf1d0e"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/shop/contractSign/index.vue"]]))}}}));
