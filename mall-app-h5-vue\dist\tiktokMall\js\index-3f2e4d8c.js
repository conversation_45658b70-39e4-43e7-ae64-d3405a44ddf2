import{_ as a,r as e,av as l,c as s,e as t,w as o,a as d,b as i,aV as r,L as u,o as n,f as p,D as m,E as c}from"./index-3d21abf8.js";import{B as f}from"./index-2406f514.js";import"./use-route-cd41a893.js";const v={class:"h-full w-full bg-white"},x={class:"main"},_=(a=>(m("data-v-6dc2c842"),a=a(),c(),a))((()=>d("div",null,"Email",-1))),y=a({__name:"index",setup(a){let m=e("");const c=()=>{};return(a,e)=>{const y=l("fx-header"),h=l("ExInput"),b=f;return n(),s("div",v,[t(y,null,{title:o((()=>[p(" Email ")])),_:1}),d("div",x,[_,d("div",null,[t(h,{style:{"padding-bottom":"0!important"},placeholderText:"Please enter your email",modelValue:i(m),"onUpdate:modelValue":e[0]||(e[0]=a=>r(m)?m.value=a:m=a)},null,8,["modelValue"])]),t(b,{class:"w-full",type:i(m)?"primary ":"",style:u({marginTop:"10px",backgroundColor:i(m)?"#1552F0":"#F6F6F6",color:i(m)?"#fff":"#999"}),onClick:c},{default:o((()=>[p("Save")])),_:1},8,["type","style"])])])}}},[["__scopeId","data-v-6dc2c842"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/email/index.vue"]]);export{y as default};
