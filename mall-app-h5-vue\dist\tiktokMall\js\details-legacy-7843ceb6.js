System.register(["./index-legacy-46a00900.js"],(function(t,e){"use strict";var i,a,s,d,l,c,r,o,p,n,v,f,u,m,x,g,b,h,w,y,k=document.createElement("style");return k.textContent='.details[data-v-42a057c9]{padding-top:50px;padding-bottom:50px}.details .search-wrap[data-v-42a057c9]{margin:0 15px;border-radius:25px;height:45px;text-align:center}.details .search-wrap .search-icon[data-v-42a057c9]{height:24px}.details .product-header[data-v-42a057c9]{background:#FFFFFF;border-radius:4px;padding:20px 0;margin-top:20px}.details .product-header .moeny[data-v-42a057c9]{font-weight:600;font-size:20px}.details .product-header .title[data-v-42a057c9]{margin-top:10px;color:#999}.details .product-header .after[data-v-42a057c9]{position:relative}.details .product-header .after[data-v-42a057c9]:after{position:absolute;height:100%;width:1px;background:#DDDDDD;content:"";right:0;top:0}.details .list[data-v-42a057c9]{position:relative;border-bottom:1px solid #EFF2F6}.details .list .item[data-v-42a057c9]{background:#FFFFFF;border-radius:4px}.details .list .item .more-icon[data-v-42a057c9]{width:20px}.details .list .item .product-img[data-v-42a057c9]{width:100px;height:auto}.details .list .item .number[data-v-42a057c9]{position:absolute;bottom:12px;right:12px;font-weight:700;color:var(--site-main-color)}.details .list .item .left[data-v-42a057c9]{-webkit-box-align:center;-webkit-align-items:center;align-items:center;display:-webkit-box;display:-webkit-flex;display:flex}.details .list .item .left .product-info[data-v-42a057c9]{padding-left:10px;word-break:break-all;-webkit-box-flex:1;-webkit-flex:1 1;flex:1 1}.details .list .item .left .product-info.is-ar[data-v-42a057c9]{padding-right:10px;padding-left:0}.details .list .item .left .product-info .name[data-v-42a057c9]{font-size:14px;color:#333;font-weight:700;overflow:hidden;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;-ms-text-overflow:ellipsis;text-overflow:ellipsis}.details .list .item .left .product-info .Specification[data-v-42a057c9]{font-size:12px;color:#999}.details .list .item .left .product-info .Specification span[data-v-42a057c9]{margin-right:10px}.details .list .item .left .product-info .money[data-v-42a057c9]{color:var(--site-main-color);font-weight:700}.details .list .product-img-wrap[data-v-42a057c9]{position:relative;overflow:hidden}.details .list .delete-wrap[data-v-42a057c9]{padding:0 15px;background:rgba(0,0,0,.6);position:absolute;left:0;top:0;font-size:12px;color:#fff}.details-item[data-v-42a057c9]{-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between;padding-top:8px;padding-bottom:8px;background:#FFFFFF}.details-item .title[data-v-42a057c9]{color:#999}.details-item .text[data-v-42a057c9]{color:#333}[data-v-42a057c9] .van-search__content{background:#fff}[data-v-42a057c9] .van-field__control{text-align:center}\n',document.head.appendChild(k),{setters:[t=>{i=t._,a=t.i,s=t.u,d=t.Y,l=t.r,c=t.q,r=t.av,o=t.c,p=t.e,n=t.w,v=t.a,f=t.b,u=t.n,m=t.t,x=t.x,g=t.o,b=t.f,h=t.aF,w=t.D,y=t.E}],execute:function(){const e={class:"details page-main-content has-fixed-header"},k=(t=>(w("data-v-42a057c9"),t=t(),y(),t))((()=>v("div",{class:"fixed-header-spacer"},null,-1))),F={class:"list ml-4 mr-4 mt-4"},P={class:"item pl-3 pr-3 pb-3 pt-3 flex"},_={class:"flex-1 flex left"},D={class:"product-img-wrap w-20 h-20"},S=["src"],N={class:"name"},$={class:"Specification"},j={key:0},z={key:0,class:"money"},C={key:0,class:"pl-4 pr-4 font-14"},E={class:"flex details-item pl-3 pr-3"},q={class:"title"},U={class:"text"},I={class:"flex details-item pl-3 pr-3"},J={class:"title"},O={class:"text"},T={class:"flex details-item pl-3 pr-3"},Y={class:"title"},A={class:"text"},B={class:"flex details-item pl-3 pr-3"},G={class:"title"},H={class:"text"},K={class:"flex details-item pl-3 pr-3"},L={class:"title"},M={class:"text"},Q={class:"flex details-item pl-3 pr-3"},R={class:"title"},V={class:"text"};t("default",i({__name:"details",setup(t){const i=a(),{t:w}=s(),y=d();let W=l({});return c((()=>{W.value=JSON.parse(y.query.item)})),(t,a)=>{const s=r("fx-header");return g(),o("div",e,[p(s,{fixed:""},{title:n((()=>[b(m(f(w)("product.11")),1)])),_:1}),k,v("div",F,[v("div",P,[v("div",_,[v("div",D,[v("img",{class:"product-img",src:f(W).imgUrl1},null,8,S)]),v("div",{class:u(["product-info",{"is-ar":f(i)}])},[v("div",N,m(f(W).name),1),v("div",$,[x(" <span>{{t('product.4')}}: {{ info.unit }}</span> "),f(W).categoryName?(g(),o("span",j,m(f(W).categoryName),1)):x("v-if",!0),v("span",null,m(f(w)("product.9"))+": "+m(f(W).soldNum),1)]),f(W).systemPrice||0===f(W).systemPrice?(g(),o("div",z,"$"+m(f(W).discountPrice?f(h)(f(W).discountPrice):f(h)(f(W).sellingPrice)),1)):x("v-if",!0)],2)]),x(' <div class="number">\r\n              x1\r\n            </div> ')])]),f(W).systemPrice||0===f(W).systemPrice?(g(),o("div",C,[v("div",E,[v("div",q,m(f(w)("product.9")),1),v("div",U,m(f(W).soldNum),1)]),v("div",I,[v("div",J,m(f(w)("product.12")),1),v("div",O,"$"+m(f(W).discountPrice?f(h)(f(W).discountPrice):f(h)(f(W).sellingPrice)),1)]),x(' <div class="flex details-item pl-3 pr-3">\r\n        <div class="title">Discount Price</div>\r\n        <div class="text">$18.90</div>\r\n      </div> '),v("div",T,[v("div",Y,m(f(w)("product.13")),1),v("div",A,"$"+m(f(h)(f(W).systemPrice)),1)]),v("div",B,[v("div",G,m(f(w)("product.14")),1),v("div",H,"$"+m(f(W).discountPrice?f(h)(f(W).discountPrice-f(W).systemPrice):f(h)(f(W).sellingPrice-f(W).systemPrice)),1)]),v("div",K,[v("div",L,m(f(w)("product.15")),1),v("div",M,m(f(W).isShelf/1==1?f(w)("product.17"):f(w)("product.18")),1)]),v("div",Q,[v("div",R,m(f(w)("product.16")),1),v("div",V,m(f(W).recTime/1==1?f(w)("product.17"):f(w)("product.18")),1)])])):x("v-if",!0)])}}},[["__scopeId","data-v-42a057c9"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/product/details.vue"]]))}}}));
