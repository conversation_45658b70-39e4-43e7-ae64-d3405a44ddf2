import{_ as e,u as a,i as l,r as s,av as t,c as i,e as o,w as r,a as n,b as d,o as c,f as u,t as v,F as p,y as g,n as h,K as m,b4 as f,aF as x,x as w,A as _,T as k,ak as b,D as y,E as j}from"./index-3d21abf8.js";import{P as C}from"./index-fc51b7d2.js";import{L as T}from"./index-40e83579.js";import{E as $}from"./index-5d897066.js";import{f as U}from"./exchange.api-23bc91cd.js";import{u as D}from"./index-54dce367.js";import"./use-id-a0619e01.js";const O=e=>(y("data-v-7309278a"),e=e(),j(),e),S={class:"record"},V={class:"list-wrap"},E={key:0},L=["onClick"],R={class:"item"},A={class:"label"},B={class:"value"},F=["onClick"],H=[O((()=>n("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"},null,-1))),O((()=>n("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"},null,-1)))],q={class:"item"},z={class:"label"},I={class:"value"},K={class:"item"},M={class:"label"},N={class:"value"},P={class:"item"},G={class:"label"},J={key:0,class:"item"},Q={class:"label"},W=e({__name:"record",setup(e){const{t:y}=a(),j=new URL("/www/png/name-20d65991.png",self.location),O=l(),W=s([]),X=s(!1),Y=s(!1),Z=s(!1),{toClipboard:ee}=D(),ae=s({page_no:1}),le=()=>{const e={...ae.value};U(e).then((e=>{const a=e.elements||[];W.value=1===ae.value.page_no?a:[...W.value,...a],ae.value.page_no++,Y.value=!1,X.value=!1,Z.value=a.length<e.pageSize})).catch((()=>{Z.value=!0,Y.value=!1,X.value=!1}))},se=()=>{Z.value=!1,Y.value=!0,ae.value.page_no=1,le()};return(e,a)=>{const l=t("fx-header"),s=$,U=T,D=C;return c(),i("div",S,[o(l,{fixed:""},{title:r((()=>[u(v(d(y)("withdrawalRecord")),1)])),_:1}),n("div",V,[o(D,{modelValue:X.value,"onUpdate:modelValue":a[1]||(a[1]=e=>X.value=e),"pulling-text":d(y)("pullingText"),"loosing-text":d(y)("loosingText"),"loading-text":d(y)("loading"),onRefresh:se},{default:r((()=>[o(U,{loading:Y.value,"onUpdate:loading":a[0]||(a[0]=e=>Y.value=e),finished:Z.value,"loading-text":d(y)("loading"),"finished-text":W.value.length?d(y)("product.3"):"",onLoad:le},{default:r((()=>[W.value.length?(c(),i("ul",E,[(c(!0),i(p,null,g(W.value,((a,l)=>{return c(),i("li",{class:h(["px-2 mt-4 item-list",{"is-ar":d(O)}]),key:l,onClick:e=>(e=>{const a={order_no:e.order_no};2===e.state&&(a.r=e.failure_msg||"--"),b({path:"/withdraw/record-details",query:a})})(a)},[n("div",R,[n("div",A,v(e.$t("rechargeOrderNumber")),1),n("div",B,[n("span",null,v(a.order_no),1),(c(),i("svg",{onClick:m((e=>(async e=>{try{await ee(e),k(y("copySuccess"))}catch(a){}})(a.order_no)),["stop"]),xmlns:"http://www.w3.org/2000/svg",width:"18",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"feather feather-copy"},H,8,F))])]),n("div",q,[n("div",z,v(e.$t("rechargeOrderTime")),1),n("div",I,v(d(f)(a.createTime)),1)]),n("div",K,[n("div",M,v(e.$t("withdrawOrderAmount")),1),n("div",N,v(d(x)(a.volume,["BTC","ETH"].includes(a.coin)?6:2))+" "+v("bank"===a.coin_blockchain?"USD":a.coin),1)]),n("div",P,[n("div",G,v(e.$t("rechargeOrderStatus")),1),n("div",{class:h(["value",`color-${a.state}`])},v((s=a.state,null!=(t={0:y("processing"),1:y("successful"),2:y("failure")}[s])?t:"")),3)]),2===a.state?(c(),i("div",J,[n("div",Q,v(e.$t("失败原因")),1),n("div",{class:h(["value",`color-${a.state}`])},v(a.failure_msg||"--"),3)])):w("v-if",!0)],10,L);var s,t})),128))])):w("v-if",!0),W.value.length||Y.value?w("v-if",!0):(c(),_(s,{key:1,image:d(j).href,description:d(y)("noData")},null,8,["image","description"]))])),_:1},8,["loading","finished","loading-text","finished-text"])])),_:1},8,["modelValue","pulling-text","loosing-text","loading-text"])])])}}},[["__scopeId","data-v-7309278a"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/withdraw/record.vue"]]);export{W as default};
