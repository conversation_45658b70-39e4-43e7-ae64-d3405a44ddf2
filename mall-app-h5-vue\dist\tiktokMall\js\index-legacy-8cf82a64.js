System.register(["./index-legacy-46a00900.js","./use-id-legacy-df76950f.js"],(function(t,i){"use strict";var e,n,l,r,a,d,o,c=document.createElement("style");return c.textContent=":root{--van-empty-padding: var(--van-padding-xl) 0;--van-empty-image-size: 160px;--van-empty-description-margin-top: var(--van-padding-md);--van-empty-description-padding: 0 60px;--van-empty-description-color: var(--van-text-color-2);--van-empty-description-font-size: var(--van-font-size-md);--van-empty-description-line-height: var(--van-line-height-md);--van-empty-bottom-margin-top: 24px }.van-empty{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;box-sizing:border-box;padding:var(--van-empty-padding)}.van-empty__image{width:var(--van-empty-image-size);height:var(--van-empty-image-size)}.van-empty__image img{width:100%;height:100%}.van-empty__description{margin-top:var(--van-empty-description-margin-top);padding:var(--van-empty-description-padding);color:var(--van-empty-description-color);font-size:var(--van-empty-description-font-size);line-height:var(--van-empty-description-line-height)}.van-empty__bottom{margin-top:var(--van-empty-bottom-margin-top)}\n",document.head.appendChild(c),{setters:[t=>{e=t.P,n=t.R,l=t.d,r=t.e,a=t.aE,d=t.X},t=>{o=t.u}],execute:function(){const[i,c]=e("empty"),s={image:n("default"),imageSize:[Number,String,Array],description:String};var p=l({name:i,props:s,setup(t,{slots:i}){const e=()=>{const e=i.description?i.description():t.description;if(e)return r("p",{class:c("description")},[e])},n=()=>{if(i.default)return r("div",{class:c("bottom")},[i.default()])},l=o(),d=t=>`${l}-${t}`,s=t=>`url(#${d(t)})`,p=(t,i,e)=>r("stop",{"stop-color":t,offset:`${i}%`,"stop-opacity":e},null),h=(t,i)=>[p(t,0),p(i,100)],x=t=>[r("defs",null,[r("radialGradient",{id:d(t),cx:"50%",cy:"54%",fx:"50%",fy:"54%",r:"297%",gradientTransform:"matrix(-.16 0 0 -.33 .58 .72)"},[p("#EBEDF0",0),p("#F2F3F5",100,.3)])]),r("ellipse",{fill:s(t),opacity:".8",cx:"80",cy:"140",rx:"46",ry:"8"},null)],y=()=>[r("defs",null,[r("linearGradient",{id:d("a"),x1:"64%",y1:"100%",x2:"64%"},[p("#FFF",0,.5),p("#F2F3F5",100)])]),r("g",{opacity:".8"},[r("path",{d:"M36 131V53H16v20H2v58h34z",fill:s("a")},null),r("path",{d:"M123 15h22v14h9v77h-31V15z",fill:s("a")},null)])],m=()=>[r("defs",null,[r("linearGradient",{id:d("b"),x1:"64%",y1:"97%",x2:"64%",y2:"0%"},[p("#F2F3F5",0,.3),p("#F2F3F5",100)])]),r("g",{opacity:".8"},[r("path",{d:"M87 6c3 0 7 3 8 6a8 8 0 1 1-1 16H80a7 7 0 0 1-8-6c0-4 3-7 6-7 0-5 4-9 9-9Z",fill:s("b")},null),r("path",{d:"M19 23c2 0 3 1 4 3 2 0 4 2 4 4a4 4 0 0 1-4 3v1h-7v-1l-1 1c-2 0-3-2-3-4 0-1 1-3 3-3 0-2 2-4 4-4Z",fill:s("b")},null)])],g=()=>r("svg",{viewBox:"0 0 160 160"},[r("defs",null,[r("linearGradient",{id:d(1),x1:"64%",y1:"100%",x2:"64%"},[p("#FFF",0,.5),p("#F2F3F5",100)]),r("linearGradient",{id:d(2),x1:"50%",x2:"50%",y2:"84%"},[p("#EBEDF0",0),p("#DCDEE0",100,0)]),r("linearGradient",{id:d(3),x1:"100%",x2:"100%",y2:"100%"},[h("#EAEDF0","#DCDEE0")]),r("radialGradient",{id:d(4),cx:"50%",cy:"0%",fx:"50%",fy:"0%",r:"100%",gradientTransform:"matrix(0 1 -.54 0 .5 -.5)"},[p("#EBEDF0",0),p("#FFF",100,0)])]),r("g",{fill:"none"},[y(),r("path",{fill:s(4),d:"M0 139h160v21H0z"},null),r("path",{d:"M80 54a7 7 0 0 1 3 13v27l-2 2h-2a2 2 0 0 1-2-2V67a7 7 0 0 1 3-13z",fill:s(2)},null),r("g",{opacity:".6","stroke-linecap":"round","stroke-width":"7"},[r("path",{d:"M64 47a19 19 0 0 0-5 13c0 5 2 10 5 13",stroke:s(3)},null),r("path",{d:"M53 36a34 34 0 0 0 0 48",stroke:s(3)},null),r("path",{d:"M95 73a19 19 0 0 0 6-13c0-5-2-9-6-13",stroke:s(3)},null),r("path",{d:"M106 84a34 34 0 0 0 0-48",stroke:s(3)},null)]),r("g",{transform:"translate(31 105)"},[r("rect",{fill:"#EBEDF0",width:"98",height:"34",rx:"2"},null),r("rect",{fill:"#FFF",x:"9",y:"8",width:"80",height:"18",rx:"1.1"},null),r("rect",{fill:"#EBEDF0",x:"15",y:"12",width:"18",height:"6",rx:"1.1"},null)])])]),v=()=>r("svg",{viewBox:"0 0 160 160"},[r("defs",null,[r("linearGradient",{x1:"50%",x2:"50%",y2:"100%",id:d(5)},[h("#F2F3F5","#DCDEE0")]),r("linearGradient",{x1:"95%",y1:"48%",x2:"5.5%",y2:"51%",id:d(6)},[h("#EAEDF1","#DCDEE0")]),r("linearGradient",{y1:"45%",x2:"100%",y2:"54%",id:d(7)},[h("#EAEDF1","#DCDEE0")])]),y(),m(),r("g",{transform:"translate(36 50)",fill:"none"},[r("g",{transform:"translate(8)"},[r("rect",{fill:"#EBEDF0",opacity:".6",x:"38",y:"13",width:"36",height:"53",rx:"2"},null),r("rect",{fill:s(5),width:"64",height:"66",rx:"2"},null),r("rect",{fill:"#FFF",x:"6",y:"6",width:"52",height:"55",rx:"1"},null),r("g",{transform:"translate(15 17)",fill:s(6)},[r("rect",{width:"34",height:"6",rx:"1"},null),r("path",{d:"M0 14h34v6H0z"},null),r("rect",{y:"28",width:"34",height:"6",rx:"1"},null)])]),r("rect",{fill:s(7),y:"61",width:"88",height:"28",rx:"1"},null),r("rect",{fill:"#F7F8FA",x:"29",y:"72",width:"30",height:"6",rx:"1"},null)])]),f=()=>r("svg",{viewBox:"0 0 160 160"},[r("defs",null,[r("linearGradient",{x1:"50%",x2:"50%",y2:"100%",id:d(8)},[h("#EAEDF1","#DCDEE0")])]),y(),m(),x("c"),r("path",{d:"m59 60 21 21 21-21h3l9 9v3L92 93l21 21v3l-9 9h-3l-21-21-21 21h-3l-9-9v-3l21-21-21-21v-3l9-9h3Z",fill:s(8)},null)]),u=()=>r("svg",{viewBox:"0 0 160 160"},[r("defs",null,[r("linearGradient",{x1:"50%",y1:"100%",x2:"50%",id:d(9)},[h("#EEE","#D8D8D8")]),r("linearGradient",{x1:"100%",y1:"50%",y2:"50%",id:d(10)},[h("#F2F3F5","#DCDEE0")]),r("linearGradient",{x1:"50%",x2:"50%",y2:"100%",id:d(11)},[h("#F2F3F5","#DCDEE0")]),r("linearGradient",{x1:"50%",x2:"50%",y2:"100%",id:d(12)},[h("#FFF","#F7F8FA")])]),y(),m(),x("d"),r("g",{transform:"rotate(-45 113 -4)",fill:"none"},[r("rect",{fill:s(9),x:"24",y:"52.8",width:"5.8",height:"19",rx:"1"},null),r("rect",{fill:s(10),x:"22.1",y:"67.3",width:"9.9",height:"28",rx:"1"},null),r("circle",{stroke:s(11),"stroke-width":"8",cx:"27",cy:"27",r:"27"},null),r("circle",{fill:s(12),cx:"27",cy:"27",r:"16"},null),r("path",{d:"M37 7c-8 0-15 5-16 12",stroke:s(11),"stroke-width":"3",opacity:".5","stroke-linecap":"round",transform:"rotate(45 29 13)"},null)])]),F=()=>{var e;if(i.image)return i.image();const n={error:f,search:u,network:g,default:v};return(null==(e=n[t.image])?void 0:e.call(n))||r("img",{src:t.image},null)};return()=>r("div",{class:c()},[r("div",{class:c("image"),style:a(t.imageSize)},[F()]),e(),n()])}});t("E",d(p))}}}));
