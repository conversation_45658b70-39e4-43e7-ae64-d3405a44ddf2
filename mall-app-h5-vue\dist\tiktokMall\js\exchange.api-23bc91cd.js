import{N as a,O as t}from"./index-3d21abf8.js";const i=()=>a({url:"/wap/api/channelBlockchain!list.action",method:t.POST,loadingPass:!0}),r=i=>a({url:"/wap/api/withdraw!fee.action",method:t.POST,loadingPass:!0,data:i}),e=i=>a({url:"/wap/api/withdraw!withdraw_open.action",method:t.POST,data:i,loadingPass:!0}),d=i=>a({url:"/wap/api/withdraw!apply.action",method:t.POST,data:i}),o=i=>a({url:"/wap/api/rechargeBlockchain!recharge_open.action",method:t.POST,data:i,loadingPass:!0}),h=i=>a({url:"/wap/api/rechargeBlockchain!recharge.action",method:t.POST,data:i}),c=i=>a({url:"/wap/api/rechargeBlockchain!list.action",method:t.POST,data:i}),p=i=>a({url:"/wap/api/rechargeBlockchain!get.action",method:t.POST,data:i}),s=i=>a({url:"/wap/api/withdraw!list.action",method:t.POST,data:i}),n=i=>a({url:"/wap/api/withdraw!get.action",method:t.POST,data:i}),l=i=>a({url:"/wap/api/user!bindWithdrawAddress.action",method:t.POST,data:i}),w=i=>a({url:"/wap/api/thirdPartyRecharge!getCoinList.action",method:t.POST,data:i}),P=i=>a({url:"/wap/api/thirdPartyRecharge!recharge.action",method:t.POST,data:i}),g=(i,r="PHP_recharge")=>a({url:`/wap/api/thirdPartyRecharge!${r}.action`,method:t.POST,data:i});export{i as a,l as b,e as c,d,r as e,s as f,h as g,o as h,g as i,P as j,c as k,p as r,w as t,n as w};
