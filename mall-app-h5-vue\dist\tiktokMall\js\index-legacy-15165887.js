System.register(["./index-legacy-46a00900.js"],(function(e,t){"use strict";var a,i,l,o,n,u,v,r,s,c,h,p,f,d,g,w,m,y,b,C,x,M,S,$,z,T,I,O,P;return{setters:[e=>{a=e.P,i=e.ai,l=e.S,o=e.a4,n=e.d,u=e.r,v=e.p,r=e.aC,s=e.aH,c=e.m,h=e.a9,p=e.g,f=e.aI,d=e.aJ,g=e.aK,w=e.q,m=e.at,y=e.aL,b=e.aM,C=e.aN,x=e.ac,M=e.e,S=e.aO,$=e.s,z=e.aP,T=e.V,I=e.aQ,O=e.X,P=e.a6}],execute:function(){const[t,X]=a("swipe"),Y={loop:i,width:l,height:l,vertical:Boolean,autoplay:o(0),duration:o(500),touchable:i,lazyRender:Boolean,initialSwipe:o(0),indicatorColor:String,showIndicators:i,stopPropagation:i},k=Symbol(t);var D=n({name:t,props:Y,emits:["change"],setup(e,{emit:t,slots:a}){const i=u(),l=u(),o=v({rect:null,width:0,height:0,offset:0,active:0,swiping:!1}),n=r(),{children:O,linkChildren:P}=s(k),Y=c((()=>O.length)),D=c((()=>o[e.vertical?"height":"width"])),B=c((()=>e.vertical?n.deltaY.value:n.deltaX.value)),H=c((()=>o.rect?(e.vertical?o.rect.height:o.rect.width)-D.value*Y.value:0)),R=c((()=>Math.ceil(Math.abs(H.value)/D.value))),j=c((()=>Y.value*D.value)),q=c((()=>(o.active+Y.value)%Y.value)),A=c((()=>{const t=e.vertical?"vertical":"horizontal";return n.direction.value===t})),E=c((()=>{const t={transitionDuration:`${o.swiping?0:e.duration}ms`,transform:`translate${e.vertical?"Y":"X"}(${o.offset}px)`};if(D.value){const a=e.vertical?"height":"width",i=e.vertical?"width":"height";t[a]=`${j.value}px`,t[i]=e[i]?`${e[i]}px`:""}return t})),J=(t,a=0)=>{let i=t*D.value;e.loop||(i=Math.min(i,-H.value));let l=a-i;return e.loop||(l=I(l,H.value,0)),l},K=({pace:a=0,offset:i=0,emitChange:l})=>{if(Y.value<=1)return;const{active:n}=o,u=(t=>{const{active:a}=o;return t?e.loop?I(a+t,-1,Y.value):I(a+t,0,R.value):a})(a),v=J(u,i);if(e.loop){if(O[0]&&v!==H.value){const e=v<H.value;O[0].setOffset(e?j.value:0)}if(O[Y.value-1]&&0!==v){const e=v>0;O[Y.value-1].setOffset(e?-j.value:0)}}o.active=u,o.offset=v,l&&u!==n&&t("change",q.value)},L=()=>{o.swiping=!0,o.active<=-1?K({pace:Y.value}):o.active>=Y.value&&K({pace:-Y.value})},N=()=>{L(),n.reset(),z((()=>{o.swiping=!1,K({pace:1,emitChange:!0})}))};let Q;const V=()=>clearTimeout(Q),W=()=>{V(),e.autoplay>0&&Y.value>1&&(Q=setTimeout((()=>{N(),W()}),+e.autoplay))},F=(t=+e.initialSwipe)=>{if(!i.value)return;const a=()=>{var a,l;if(!S(i)){const t={width:i.value.offsetWidth,height:i.value.offsetHeight};o.rect=t,o.width=+(null!=(a=e.width)?a:t.width),o.height=+(null!=(l=e.height)?l:t.height)}Y.value&&(t=Math.min(Y.value-1,t)),o.active=t,o.swiping=!0,o.offset=J(t),O.forEach((e=>{e.setOffset(0)})),W()};S(i)?$().then(a):a()},G=()=>F(o.active);let U;const Z=t=>{e.touchable&&(n.start(t),U=Date.now(),V(),L())},_=()=>{if(!e.touchable||!o.swiping)return;const t=Date.now()-U,a=B.value/t;if((Math.abs(a)>.25||Math.abs(B.value)>D.value/2)&&A.value){const t=e.vertical?n.offsetY.value:n.offsetX.value;let a=0;a=e.loop?t>0?B.value>0?-1:1:0:-Math[B.value>0?"ceil":"floor"](B.value/D.value),K({pace:a,emitChange:!0})}else B.value&&K({pace:0});o.swiping=!1,W()},ee=(t,a)=>{const i=a===q.value,l=i?{backgroundColor:e.indicatorColor}:void 0;return M("i",{style:l,class:X("indicator",{active:i})},null)};return h({prev:()=>{L(),n.reset(),z((()=>{o.swiping=!1,K({pace:-1,emitChange:!0})}))},next:N,state:o,resize:G,swipeTo:(t,a={})=>{L(),n.reset(),z((()=>{let i;i=e.loop&&t===Y.value?0===o.active?0:t:t%Y.value,a.immediate?z((()=>{o.swiping=!1})):o.swiping=!1,K({pace:i-o.active,emitChange:!0})}))}}),P({size:D,props:e,count:Y,activeIndicator:q}),p((()=>e.initialSwipe),(e=>F(+e))),p(Y,(()=>F(o.active))),p((()=>e.autoplay),W),p([f,d],G),p(g(),(e=>{"visible"===e?W():V()})),w(F),m((()=>F(o.active))),y((()=>F(o.active))),b(V),C(V),x("touchmove",(t=>{e.touchable&&o.swiping&&(n.move(t),A.value)&&(!e.loop&&(0===o.active&&B.value>0||o.active===Y.value-1&&B.value<0)||(T(t,e.stopPropagation),K({offset:B.value})))}),{target:l}),()=>{var t;return M("div",{ref:i,class:X()},[M("div",{ref:l,style:E.value,class:X("track",{vertical:e.vertical}),onTouchstartPassive:Z,onTouchend:_,onTouchcancel:_},[null==(t=a.default)?void 0:t.call(a)]),a.indicator?a.indicator({active:q.value,total:Y.value}):e.showIndicators&&Y.value>1?M("div",{class:X("indicators",{vertical:e.vertical})},[Array(Y.value).fill("").map(ee)]):void 0])}}});e("a",O(D));const[B,H]=a("swipe-item");var R=n({name:B,setup(e,{slots:t}){let a;const i=v({offset:0,inited:!1,mounted:!1}),{parent:l,index:o}=P(k);if(!l)return;const n=c((()=>{const e={},{vertical:t}=l.props;return l.size.value&&(e[t?"height":"width"]=`${l.size.value}px`),i.offset&&(e.transform=`translate${t?"Y":"X"}(${i.offset}px)`),e})),u=c((()=>{const{loop:e,lazyRender:t}=l.props;if(!t||a)return!0;if(!i.mounted)return!1;const n=l.activeIndicator.value,u=l.count.value-1,v=0===n&&e?u:n-1,r=n===u&&e?0:n+1;return a=o.value===n||o.value===v||o.value===r,a}));return w((()=>{$((()=>{i.mounted=!0}))})),h({setOffset:e=>{i.offset=e}}),()=>{var e;return M("div",{class:H(),style:n.value},[u.value?null==(e=t.default)?void 0:e.call(t):null])}}});e("S",O(R))}}}));
