System.register(["./index-legacy-46a00900.js","./index-legacy-8ad4c0d7.js","./use-placeholder-legacy-f22ccc27.js"],(function(e,t){"use strict";var s,l,c,r,n,a,u,i;return{setters:[e=>{s=e._,l=e.u,c=e.l,r=e.c,n=e.e,a=e.a,u=e.o},e=>{i=e.N},()=>{}],execute:function(){const t={class:"flex flex-col w-screen h-screen"},o=["src"];e("default",s({__name:"WebView",props:["query"],setup(e){const s=e;l();const f=c(),p=()=>{f.go(-1)};return(e,l)=>{const c=i;return u(),r("div",t,[n(c,{ref:"navEl",title:e.$t(s.query.title),"left-arrow":"",onClickLeft:p,fixed:""},null,8,["title"]),a("iframe",{src:s.query.src,class:"flex-1 mt-12"},null,8,o)])}}},[["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/components/webView/WebView.vue"]]))}}}));
