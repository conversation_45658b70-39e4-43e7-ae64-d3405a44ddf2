import{P as e,a4 as a,d as o,r as s,p as t,aL as l,aR as r,ac as n,a9 as i,g as c,h as d,aS as f,e as u,I as p,aT as b,aP as m,aU as g,ad as w,X as h}from"./index-3d21abf8.js";const[v,y]=e("notice-bar");const x=h(o({name:v,props:{text:String,mode:String,color:String,delay:a(1),speed:a(60),leftIcon:String,wrapable:Boolean,background:String,scrollable:{type:Boolean,default:null}},emits:["close","replay"],setup(e,{emit:a,slots:o}){let h,v=0,x=0;const S=s(),k=s(),I=t({show:!0,offset:0,duration:0}),T=o=>{"closeable"===e.mode&&(I.show=!1,a("close",o))},B=()=>{if(o["right-icon"])return o["right-icon"]();const a="closeable"===e.mode?"cross":"link"===e.mode?"arrow":void 0;return a?u(p,{name:a,class:y("right-icon"),onClick:T},null):void 0},P=()=>{I.offset=v,I.duration=0,g((()=>{m((()=>{I.offset=-x,I.duration=(x+v)/+e.speed,a("replay")}))}))},X=()=>{const a=!1===e.scrollable&&!e.wrapable,s={transform:I.offset?`translateX(${I.offset}px)`:"",transitionDuration:`${I.duration}s`};return u("div",{ref:S,role:"marquee",class:y("wrap")},[u("div",{ref:k,style:s,class:[y("content"),{"van-ellipsis":a}],onTransitionend:P},[o.default?o.default():e.text])])},$=()=>{const{delay:a,speed:o,scrollable:s}=e,t=w(a)?1e3*+a:0;v=0,x=0,I.offset=0,I.duration=0,clearTimeout(h),h=setTimeout((()=>{if(!S.value||!k.value||!1===s)return;const e=b(S).width,a=b(k).width;(s||a>e)&&m((()=>{v=e,x=a,I.offset=-x,I.duration=x/+o}))}),t)};return l($),r($),n("pageshow",$),i({reset:$}),c((()=>[e.text,e.scrollable]),$),()=>{const{color:a,wrapable:s,background:t}=e;return d(u("div",{role:"alert",class:y({wrapable:s}),style:{color:a,background:t}},[o["left-icon"]?o["left-icon"]():e.leftIcon?u(p,{class:y("left-icon"),name:e.leftIcon},null):void 0,X(),B()]),[[f,I.show]])}}}));export{x as N};
