System.register(["./index-legacy-46a00900.js","./index-legacy-72e00c5f.js","./use-placeholder-legacy-f22ccc27.js","./use-route-legacy-be86ac1c.js"],(function(t,e){"use strict";var o,n,l,a,s,c,i,r,u,d,f,m,g,B,b,p,v,C,h,w,y,x,k,S,O,D,T,P,H,A,j,z,N,E;return{setters:[t=>{o=t.P,n=t.d,l=t.r,a=t.aH,s=t.ai,c=t.e,i=t.X,r=t.Q,u=t.a6,d=t.m,f=t.a9,m=t.bk,g=t.S,B=t.a5,b=t.R,p=t.bm,v=t.p,C=t.ag,h=t.aY,w=t.a8,y=t.bn,x=t.bs,k=t.bt,S=t.a3,O=t.br,D=t.bu,T=t.bf,P=t.b9,H=t.bv,A=t.bw},t=>{j=t.B},t=>{z=t.u},t=>{N=t.r,E=t.u}],execute:function(){t("D",_);const[e,F]=o("action-bar"),I=Symbol(e),R={placeholder:Boolean,safeAreaInsetBottom:s};var U=n({name:e,props:R,setup(t,{slots:e}){const o=l(),n=z(o,F),{linkChildren:s}=a(I);s();const i=()=>{var n;return c("div",{ref:o,class:[F(),{"van-safe-area-bottom":t.safeAreaInsetBottom}]},[null==(n=e.default)?void 0:n.call(e)])};return()=>t.placeholder?n(i):i()}});const K=i(U),[L,M]=o("action-bar-button"),Q=r({},N,{type:String,text:String,icon:String,color:String,loading:Boolean,disabled:Boolean});var X=n({name:L,props:Q,setup(t,{slots:e}){const o=E(),{parent:n,index:l}=u(I),a=d((()=>{if(n){const t=n.children[l.value-1];return!(t&&"isButton"in t)}})),s=d((()=>{if(n){const t=n.children[l.value+1];return!(t&&"isButton"in t)}}));return f({isButton:!0}),()=>{const{type:n,icon:l,text:i,color:r,loading:u,disabled:d}=t;return c(j,{class:M([n,{last:s.value,first:a.value}]),size:"large",type:n,icon:l,color:r,loading:u,disabled:d,onClick:o},{default:()=>[e.default?e.default():i]})}}});const Y=i(X),[$,q,G]=o("dialog"),J=r({},m,{title:String,theme:String,width:g,message:[String,Function],callback:Function,allowHtml:Boolean,className:B,transition:b("van-dialog-bounce"),messageAlign:String,closeOnPopstate:s,showCancelButton:Boolean,cancelButtonText:String,cancelButtonColor:String,cancelButtonDisabled:Boolean,confirmButtonText:String,confirmButtonColor:String,confirmButtonDisabled:Boolean,showConfirmButton:s,closeOnClickOverlay:Boolean}),V=[...p,"transition","closeOnPopstate"];var W=n({name:$,props:J,emits:["confirm","cancel","keydown","update:show"],setup(t,{emit:e,slots:o}){const n=l(),a=v({confirm:!1,cancel:!1}),s=t=>e("update:show",t),i=e=>{var o;s(!1),null==(o=t.callback)||o.call(t,e)},r=o=>()=>{t.show&&(e(o),t.beforeClose?(a[o]=!0,T(t.beforeClose,{args:[o],done(){i(o),a[o]=!1},canceled(){a[o]=!1}})):i(o))},u=r("cancel"),d=r("confirm"),f=x((o=>{var l,a;o.target===(null==(a=null==(l=n.value)?void 0:l.popupRef)?void 0:a.value)&&({Enter:t.showConfirmButton?d:k,Escape:t.showCancelButton?u:k}[o.key](),e("keydown",o))}),["enter","esc"]),m=()=>{const e=o.title?o.title():t.title;if(e)return c("div",{class:q("header",{isolated:!t.message&&!o.default})},[e])},g=e=>{const{message:o,allowHtml:n,messageAlign:l}=t,a=q("message",{"has-title":e,[l]:l}),s=S(o)?o():o;return n&&"string"==typeof s?c("div",{class:a,innerHTML:s},null):c("div",{class:a},[s])},B=()=>{if(o.default)return c("div",{class:q("content")},[o.default()]);const{title:e,message:n,allowHtml:l}=t;if(n){const t=!(!e&&!o.title);return c("div",{key:l?1:0,class:q("content",{isolated:!t})},[g(t)])}},b=()=>o.footer?o.footer():"round-button"===t.theme?c(K,{class:q("footer")},{default:()=>[t.showCancelButton&&c(Y,{type:"warning",text:t.cancelButtonText||G("cancel"),class:q("cancel"),color:t.cancelButtonColor,loading:a.cancel,disabled:t.cancelButtonDisabled,onClick:u},null),t.showConfirmButton&&c(Y,{type:"danger",text:t.confirmButtonText||G("confirm"),class:q("confirm"),color:t.confirmButtonColor,loading:a.confirm,disabled:t.confirmButtonDisabled,onClick:d},null)]}):c("div",{class:[D,q("footer")]},[t.showCancelButton&&c(j,{size:"large",text:t.cancelButtonText||G("cancel"),class:q("cancel"),style:{color:t.cancelButtonColor},loading:a.cancel,disabled:t.cancelButtonDisabled,onClick:u},null),t.showConfirmButton&&c(j,{size:"large",text:t.confirmButtonText||G("confirm"),class:[q("confirm"),{[O]:t.showCancelButton}],style:{color:t.confirmButtonColor},loading:a.confirm,disabled:t.confirmButtonDisabled,onClick:d},null)]);return()=>{const{width:e,title:o,theme:l,message:a,className:i}=t;return c(y,C({ref:n,role:"dialog",class:[q([l]),i],style:{width:w(e)},tabindex:0,"aria-labelledby":o||a,onKeydown:f,"onUpdate:show":s},h(t,V)),{default:()=>[m(),B(),b()]})}}});let Z;function _(t){return P?new Promise(((e,o)=>{Z||function(){const t={setup(){const{state:t,toggle:e}=A();return()=>c(W,C(t,{"onUpdate:show":e}),null)}};({instance:Z}=H(t))}(),Z.open(r({},_.currentOptions,t,{callback:t=>{("confirm"===t?e:o)(t)}}))})):Promise.resolve()}_.defaultOptions={title:"",width:"",theme:null,message:"",overlay:!0,callback:null,teleport:"body",className:"",allowHtml:!1,lockScroll:!0,transition:void 0,beforeClose:null,overlayClass:"",overlayStyle:void 0,messageAlign:"",cancelButtonText:"",cancelButtonColor:null,cancelButtonDisabled:!1,confirmButtonText:"",confirmButtonColor:null,confirmButtonDisabled:!1,showConfirmButton:!0,showCancelButton:!1,closeOnPopstate:!0,closeOnClickOverlay:!1},_.currentOptions=r({},_.defaultOptions),_.alert=_,_.confirm=t=>_(r({showCancelButton:!0},t)),_.close=()=>{Z&&Z.toggle(!1)},_.setDefaultOptions=t=>{r(_.currentOptions,t)},_.resetDefaultOptions=()=>{_.currentOptions=r({},_.defaultOptions)},_.Component=i(W),_.install=t=>{t.use(_.Component),t.config.globalProperties.$dialog=_}}}}));
