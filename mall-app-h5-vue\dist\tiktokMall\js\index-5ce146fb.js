import{_ as A,d as e,u as a,r as t,m as l,s as n,b4 as i,aF as o,av as s,c,e as g,w as d,cF as r,o as u,f as m,t as p,a as w,F as h,y as C,L as v,A as U,x as I,T as f,az as S,D as E,E as B}from"./index-3d21abf8.js";import{P as D}from"./index-fc51b7d2.js";import{L as R}from"./index-40e83579.js";import{E as Q}from"./index-5d897066.js";import{A as b}from"./index-3d6106f5.js";import{C as x,r as N,a as J}from"./config-9feb1ce7.js";import"./use-id-a0619e01.js";const O=e({name:"FinacialStatements",components:{CountTo:x},setup(){const{t:A}=a(),e=new URL("data:image/png;base64,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",self.location),s=t([...N]),c=localStorage.getItem("sellerId")||"",g=t(!1),d=l((()=>J.map((e=>({name:A(e.name),value:e.value}))))),u=new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAKtSURBVHgB7Zi7ctpAGIV/QeiVEmj0CNwKuogunfETRH4CJ1UmlUWZyk6VMnaZyqRMZdExwzVPEKUBSgJNAgPk7MyKrIVAWgn5MqPTaFd7+7Tas/q1RImiSTlUqOu6OpvN3iCpUhyDK4qdyWRa7Xbb3ltnX0G5XL7C5ZweQABtdLtd06ss7XXzIeG49Hw+r45Go+/ugp0ZrFar2nK5/CncsjabjUUxCDOnMzgnv16vtcFg8Eus88LdaLFYnKChk232er1Tik8NvK07BxLj1nH5JFZIeTTaGgIzN6SY5Xo7O2ZM0ROXF+BUSNcpZuG1GkLadpfvAGKh3hCHRINCpVK5pJjEdwvNya9Wq5a7zs42M5lM/mSz2b+Ae81vVbEFKNgCLDqi8OAmLu+dPNaiCQd/Iz9ApvF43AakCsgqv6Xncjkb93/QEYSZY0vnswB31e/3P3jVVXw62m4B0DSVStU6nU4kZ/N9dkDcsWynAFxxX/2DLsaaOEUHNs+qWJ+3bACKBncnwNno8+A+exBwOBxO0UGN/jtbw0Z+WygUpIMHFnhwOE2Aq2EMm8ICckgba3H7lMzZ6XRa2tnz+fyeYwFo+MExpSmA4GAbpvktOLsg42zu2HMB7i0c+zVI20CATGGdXSqVDFwuBTgTpvhIASX1qUPH73BpioP5tcEDXThprLkm+miQhKS/xXD2mTC4FqDJtg4e6IwkJQ3InE0hFabts4xmnpQSwKhKAKMqAYyqyIBhQi8ZhQW0nARCry9Rglg/hQLEN/VGyNbZUQl+D2IBDQWIiOTaI5Ix4gBVKIKKxeIr/EiZJBwACWJhGQsODJ6f4pznJUkqEqAjBspOCMRTArcw49eYeelw6yiAjuBoDTN64QFqsT/EMOHWUQEdcdATJFV2eoX/jxYleiT9A42FLjnzkPSzAAAAAElFTkSuQmCC",self.location),m={calendar:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAF+SURBVHgB7Zj/UcMgFMe/9fq/2aCs4ARJJ9ANjBPYDawT1A3UEZzAbqAbFDeoE+jjCheSEMqPC2l7fO6+d03hwQsPCDzgAilIDP4wJGJH+iOtPGzW0kbYFhgRJjtSnbnypdktPOxwhTQEj9rM0qAIYYn+3NGfOdxgFhvx/Eb6IO3h2JiaZym1g+NCmsI5pc+uM90QV51KaxyGX8FIW/mby/oubNGMToV2mJekDZp5ukTTR48nNG/zaihnCFvFelQWhvKNVv6oF9hWMTf8t0czkTnc0ev+DrSraK34OfwQDYkQlKR3D7sH0j0OoXNaqQpfBwXfUj5w0jMCSLVRB3PyDtpCLL4kNdIw+CmcHzEa9eThwlmH+EUqBSsMnC9tDor96gdpGNwb8zYTS3YwluxgLNnBWLKDsWQHYzlrByc/rJqoEZAriYShfbG/1Qu7qY8C/SQjx7joVwtOusGRu3ON6ZJHd3CkQjsrmiKrVZocmcGOGPprjIvI1XilQ06Kf/JOva1PtAe6AAAAAElFTkSuQmCC",self.location),credit:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAFGSURBVHgB7ZiBbcIwEEV/UQZoJ2iYoN2gdJNuUDYAJgAmgA0yArABGxAmgA3gTiRwCTkIinwOwk/6kv1lKZ/YXGwDgYBf3hS/R/oivcOOFWl5bxAHSkgHT5rhzkuZeAyXaywDySmOSRvRn5NS2BCT/kT/g7QvD+IB+a9IYM9CPP8/NztiwKdor2HPUrTP67CDlhMCNiVS/D6K/yoLKutfdGOw5VdE5WmneJLJkn6mAlpAruJb2LKvMkOZaUoI2JSXD8jFfoPiPvOWf4XrgEOcNqN1/StcBuSiyxtPrm+/NfxKXAWMSYOsPcTl6KD5Kq4C8vad1xl/Lqc1fBWXU5ySRg/4lURwQ/dBX+Wp6qDcTfRgz7dop3lDHtx58e5Efw5/B/eu9my+dmjV1UcZfoszj+ESlM5C2vXbD2zXIa9/vs1YIRBoGUc107GxkH5nqgAAAABJRU5ErkJggg==",self.location),cancel:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAF4SURBVHgB7ZiNTcMwEEa/IgaADcoGHaEbwAZkg44QZwLYgHQCYAI6AhtQJoANWp/qqhfHtuKfuFHkJ53SSx3lNfbVsYHCzFk4vruT8ayOY/Iv41PG3ueiVxmHzPGCgVxDziqpd/FSxg/LdyrGZCXjieX3OHW7kQ0uv+Yd+fhi993wL260hrwgvpGPHcwOPcHJUQRjmbVghdNMk6qdkVuEUcl4U58fZAhLOzpfs3wLT1J0cQ2zoEBXLohQwRZdKV1SoCtHuffTI0K7mGjYzaEJ6XINAokRBNyS5/PBckSKMdjAPgaj5IhU/4OLgee8SSEoYK5WW3V7ESso0C8IwfJoyZgiEXBXq1DHWsu9CH2CFdxyeuFQ26DpLtUYNFWrrbq9CO3iVh3pFd01Q5Dk74B2VmLGYJu4nZHywhpLEYxFF+Qr+jXysYLZoTeh06L5j+UfGH8Bv0b3YdASYu+6gDZwJrN5NCVJo5zrnW0p4xF5NjC3cOxoFWbNERnpt8/Se1PmAAAAAElFTkSuQmCC",self.location),refund:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAFDSURBVHgB7ZiBbQIhFIb/XBygI7wNdIO+DdoN2hU6QesEdoO2G7QTtG7QDXQD3UAhoDlPuAOOPDHyJX9yIhe+8BIOACqV8iCVhcpGZScUPdaHHbuXmbCYS3TWJ7i6oNwhK5/cY6fTPeToVo4PfzStTtPW87fKEnL8q3y2fh8np/G8sIU8zjEbFE7xghNPO0MedjX2CTIK4GpL/GcjCcNRtT7BOeThbkNdZsZyM4Kk8guzyWBkJIcgwcixfX5CRsYKEowctdp+kJExgoRzuWeYrVo2UgUJbrkvZGaCeAjncnpRXyLg0GPZInDPmSLYldO82sTwovI+1Cm2xITwWRpiGtIpdgbXKm8wB6w7pLNG4Lc+pcRzCG4kfCUeMzupDI7JiDjhZ4Zxemnw4OtY9M2Cpvi7GQ3B3DRJ324tkG8Jq1Ruhz39veqNh2RQngAAAABJRU5ErkJggg==",self.location)},p=t(0),w=new URL("/www/png/name-20d65991.png",self.location),h=t([]),C=t(!1),v=t(!0),U=t(!1),I=t({pageNum:1,pageSize:10}),E=()=>{f.loading({duration:0,message:A("loading"),forbidClick:!0}),S({sellerId:c,content_type:p.value}).then((A=>{f.clear();const e=A.head;s.value.forEach((A=>{A.number=e[A.key]}))})).catch((()=>{f.clear()}))},B=()=>{const A={...I.value,content_type:p.value,sellerId:c};r(A).then((A=>{const e=A.pageList||[];h.value=1===I.value.pageNum?e:[...h.value,...e],I.value.pageNum++,v.value=!1,C.value=!1,e.length<I.value.pageSize&&(U.value=!0)})).catch((()=>{U.value=!0,v.value=!1,C.value=!1}))},D=()=>{U.value=!1,v.value=!0,I.value.pageNum=1,B()};return n((()=>{E()})),{t:A,blockBg:e,reportDataRef:s,listData:h,refreshing:C,loading:v,finished:U,empytImg:w,filterIcon:u,imgObj:m,actionShow:g,reportFilterAction:d,formatZoneDate:i,numberStrFormat:o,getListData:B,onRefresh:D,selectHandle:A=>{p.value=A.value,h.value=[],E(),D()}}}}),G={class:"page-main-content financial-statements-page"},Z=["src"],j=(A=>(E("data-v-1a4ab426"),A=A(),B(),A))((()=>w("div",{style:{height:"46px"}},null,-1))),z={class:"report-content"},X=["src"],Y={key:1,class:"number-count"},k={key:0,class:"list-content"},F={class:"info-content"},T={class:"item"},y=["src"],M={class:"item"},V=["src"],W={class:"item"},L=["src"],K={class:"red"},P={class:"item"},H=["src"],q={class:"amout"},_={class:"profit"};const $=A(O,[["render",function(A,e,a,t,l,n){const i=s("fx-header"),o=b,r=s("count-to"),f=Q,S=R,E=D;return u(),c("div",G,[g(i,{fixed:!0},{title:d((()=>[m(p(A.$t("financialStatement")),1)])),right:d((()=>[w("div",{class:"filter-icon",onClick:e[0]||(e[0]=e=>A.actionShow=!0)},[w("img",{src:A.filterIcon,alt:""},null,8,Z)])])),_:1}),j,g(o,{show:A.actionShow,"onUpdate:show":e[1]||(e[1]=e=>A.actionShow=e),actions:A.reportFilterAction,"cancel-text":A.t("取消"),"close-on-click-action":"",onSelect:A.selectHandle},null,8,["show","actions","cancel-text","onSelect"]),g(E,{modelValue:A.refreshing,"onUpdate:modelValue":e[3]||(e[3]=e=>A.refreshing=e),"pulling-text":A.t("pullingText"),"loosing-text":A.t("loosingText"),"loading-text":A.t("loading"),onRefresh:A.onRefresh},{default:d((()=>[w("div",z,[(u(!0),c(h,null,C(A.reportDataRef,(e=>(u(),c("div",{key:e.key,class:"item",style:v({"background-color":e.color})},[w("img",{src:A.blockBg.href,alt:""},null,8,X),w("p",null,p(A.t(e.title)),1),Number(e.number)>0?(u(),U(r,{key:0,prefix:e.prefix,decimals:e.decimals,startVal:0,endVal:e.number,duration:1500,class:"number-count"},null,8,["prefix","decimals","endVal"])):(u(),c("h3",Y,"0"))],4)))),128))]),g(S,{loading:A.loading,"onUpdate:loading":e[2]||(e[2]=e=>A.loading=e),finished:A.finished,"loading-text":A.t("loading"),"finished-text":A.listData.length?A.t("product.3"):"",onLoad:A.getListData},{default:d((()=>[A.listData.length?(u(),c("div",k,[(u(!0),c(h,null,C(A.listData,((e,a)=>(u(),c("div",{key:a,class:"item"},[w("div",F,[w("div",T,[w("img",{src:A.imgObj.calendar,alt:""},null,8,y),w("p",null,p(A.t("日期"))+":",1),w("p",null,p(A.formatZoneDate(e.dayString,"YYYY-MM-DD")),1)]),w("div",M,[w("img",{src:A.imgObj.credit,alt:""},null,8,V),w("p",null,p(A.t("总订单"))+":",1),w("p",null,p(e.orderNum),1)]),w("div",W,[w("img",{src:A.imgObj.cancel,alt:""},null,8,L),w("p",null,p(A.t("取消订单"))+":",1),w("p",K,p(e.orderCancel),1)]),w("div",P,[w("img",{src:A.imgObj.refund,alt:""},null,8,H),w("p",null,p(A.t("退款订单"))+":",1),w("p",null,p(e.orderReturns),1)])]),w("div",q,[I(' <div v-if="item.totalSales">${{ item.totalSales }}</div> '),I(" <p>({{ t('利润') }} {{ item.totalProfit }})</p> "),w("div",_,p(A.t("利润"))+" "+p(A.numberStrFormat(e.totalProfit)),1)])])))),128))])):I("v-if",!0),A.listData.length||A.loading?I("v-if",!0):(u(),U(f,{key:1,image:A.empytImg.href,description:A.t("noData")},null,8,["image","description"]))])),_:1},8,["loading","finished","loading-text","finished-text","onLoad"])])),_:1},8,["modelValue","pulling-text","loosing-text","loading-text","onRefresh"])])}],["__scopeId","data-v-1a4ab426"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/FinancialStatements/index.vue"]]);export{$ as default};
