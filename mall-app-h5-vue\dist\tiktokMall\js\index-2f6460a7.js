import{P as A,ai as e,S as i,a4 as n,d as t,aH as s,e as a,a8 as d,bu as o,X as r,Q as c,a6 as g,m as l,ct as u,ag as B,bb as v,I as p,_ as y,l as R,u as U,r as k,q as X,cu as C,av as x,c as f,a as F,t as Q,w as J,F as I,y as S,o as w,A as O,D as P,E as h}from"./index-3d21abf8.js";import{r as V,u as m}from"./use-route-cd41a893.js";const[E,H]=A("grid"),j={square:Boolean,center:e,border:e,gutter:i,reverse:Boolean,iconSize:i,direction:String,clickable:Boolean,columnNum:n(4)},M=Symbol(E);const K=r(t({name:E,props:j,setup(A,{slots:e}){const{linkChildren:i}=s(M);return i({props:A}),()=>{var i;return a("div",{style:{paddingLeft:d(A.gutter)},class:[H(),{[o]:A.border&&!A.gutter}]},[null==(i=e.default)?void 0:i.call(e)])}}})),[G,T]=A("grid-item");const b=r(t({name:G,props:c({},V,{dot:Boolean,text:String,icon:String,badge:i,iconColor:String,iconPrefix:String,badgeProps:Object}),setup(A,{slots:e}){const{parent:i,index:n}=g(M),t=m();if(!i)return;const s=l((()=>{const{square:A,gutter:e,columnNum:t}=i.props,s=100/+t+"%",a={flexBasis:s};if(A)a.paddingTop=s;else if(e){const A=d(e);a.paddingRight=A,n.value>=t&&(a.marginTop=A)}return a})),o=l((()=>{const{square:A,gutter:e}=i.props;if(A&&e){const A=d(e);return{right:A,bottom:A,height:"auto"}}}));return()=>{const{center:n,border:d,square:r,gutter:c,reverse:g,direction:l,clickable:y}=i.props,R=[T("content",[l,{center:n,square:r,reverse:g,clickable:y,surround:d&&c}]),{[u]:d}];return a("div",{class:[T({square:r})],style:s.value},[a("div",{role:y?"button":void 0,class:R,style:o.value,tabindex:y?0:void 0,onClick:t},[e.default?e.default():[e.icon?a(v,B({dot:A.dot,content:A.badge},A.badgeProps),{default:e.icon}):A.icon?a(p,{dot:A.dot,name:A.icon,size:i.props.iconSize,badge:A.badge,class:T("icon"),color:A.iconColor,badgeProps:A.badgeProps,classPrefix:A.iconPrefix},null):void 0,e.text?e.text():A.text?a("span",{class:T("text")},[A.text]):void 0]])])}}})),q="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAbCAYAAABvCO8sAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAD+SURBVHgB7dUxDoIwFAbgUiLqxhG4gayOHdn0BMaT6Q1wkkhM6qKuHoEjuBkYeBaMCxJ5fX2b/gkJKU2+vw0FIf5hjoedmGU68sbjDZTlOklUIYhBgS0WBNrcRuYqoKoUFR0EO9g7ZFQOTfD9IDStws5wW6IpIyyD2tI8P8fgCw2fsPVK0S8NF4oGuVArkAO1Bl1REuiCkkEqOngOvwVGctaDvTKZ9A6TV3jQ1xXU9bbnUQFSqkTNC8EFUjES6IJZg66YFciBoUEuDAXuj5eF9CDlwJoMnsPSL0+m1o0Da4La0lTrcArmrw8idsGaoL40S6XuD69Spt3OBfuNPAEoBciwl2zMXQAAAABJRU5ErkJggg==",D=A=>(P("data-v-6c288631"),A=A(),h(),A),Z={class:"safety"},L={class:"content"},z={class:"title textColor"},Y={class:"tit1 textColor"},N={class:"tit2"},W={class:"verifyBox"},_={class:"left"},$={class:"imgBox"},AA=["src"],eA=["src"],iA=D((()=>F("div",{class:"right icon"},[F("img",{src:q,alt:""})],-1))),nA={class:"name textColor"},tA={class:"content"},sA=["onClick"],aA={class:"textColor"},dA=D((()=>F("div",{class:"icon"},[F("img",{src:q,alt:""})],-1))),oA=y({__name:"index",setup(A){const e=R(),{t:i}=U(),n=k([{title:i("googleAuthenticator"),name:"google",icon:{verifyno:new URL("data:image/png;base64,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",self.location),verify:new URL("data:image/png;base64,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",self.location)},isVerify:!1,url:"/bindVerify?type=3",type:3},{title:i("phoneVerify"),name:"phone",icon:{verifyno:new URL("data:image/png;base64,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",self.location),verify:new URL("data:image/png;base64,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",self.location)},isVerify:!1,url:"/bindVerify?type=1",type:1},{title:i("emailVerify"),name:"email",icon:{verifyno:new URL("data:image/png;base64,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",self.location),verify:new URL("data:image/png;base64,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",self.location)},isVerify:!1,url:"/bindVerify?type=2",type:2}]),t=k([{name:i("changeLoginPassword"),url:"/changePassword"},{name:i("changeFunsPassword"),url:"/changeFundsPassword"},{name:i("manualReset"),url:"/resetVerify?type=0"}]);X((()=>{s()}));const s=()=>{C({}).then((A=>{d(A)}))},d=A=>{n.value.forEach((e=>{"google"==e.name?e.isVerify=A.google_auth_bind:"phone"==e.name?e.isVerify=A.phone_authority:"email"==e.name&&(e.isVerify=A.email_authority)}))};return(A,i)=>{const s=x("fx-header"),d=b,o=K;return w(),f("div",Z,[a(s,{back:!1,onBack:i[0]||(i[0]=e=>A.$router.push("/my/index"))}),F("div",L,[F("div",z,Q(A.$t("safe")),1),F("div",Y,Q(A.$t("twoFactorAuthentication")),1),F("div",N,Q(A.$t("twoAuthenticationTips")),1)]),a(o,{"column-num":2,gutter:12,class:"verify"},{default:J((()=>[(w(!0),f(I,null,S(n.value,((A,i)=>(w(),O(d,{key:i,onClick:i=>{return n=A.url,t=A.isVerify,s=A.type,void(t?e.push(`/changeVerify?type=${s}`):e.push(n));var n,t,s}},{default:J((()=>[F("div",W,[F("div",_,[F("div",$,[A.isVerify?(w(),f("img",{key:0,src:A.icon.verify,alt:""},null,8,AA)):(w(),f("img",{key:1,src:A.icon.verifyno,alt:""},null,8,eA))])]),iA]),F("div",nA,Q(A.title),1)])),_:2},1032,["onClick"])))),128))])),_:1}),F("div",tA,[(w(!0),f(I,null,S(t.value,((e,i)=>(w(),f("div",{key:i,onClick:i=>A.$router.push(e.url),class:"flex justify-between items-center h-50"},[F("div",aA,Q(e.name),1),dA],8,sA)))),128))])])}}},[["__scopeId","data-v-6c288631"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/safety/index.vue"]]);export{oA as default};
