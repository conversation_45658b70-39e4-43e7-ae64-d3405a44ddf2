import{_ as n,d as e,u as i,m as t,i as r,r as s,T as o,B as u,cc as c,cb as a,av as l,c as A,e as f,w as d,a as h,x as g,L as p,b as w,f as m,t as v,F as N,y as U,o as C,n as B,aF as x,D as Q,E as R}from"./index-3d21abf8.js";import{i as E,l as b}from"./config-be206136.js";import{c as y}from"./index-3ab60a77.js";
/*!
 *  decimal.js v10.4.3
 *  An arbitrary-precision Decimal type for JavaScript.
 *  https://github.com/MikeMcl/decimal.js
 *  Copyright (c) 2022 <PERSON> <<EMAIL>>
 *  MIT Licence
 */var M,S,D=9e15,F=1e9,J="0123456789abcdef",K="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",I="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",Y={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-D,maxE:D,crypto:!1},T=!0,V="[DecimalError] ",H=V+"Invalid argument: ",P=V+"Precision limit exceeded",k=V+"crypto unavailable",O="[object Decimal]",X=Math.floor,Z=Math.pow,L=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,z=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,W=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,G=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,q=1e7,j=7,_=K.length-1,$=I.length-1,nn={toStringTag:O};function en(n){var e,i,t,r=n.length-1,s="",o=n[0];if(r>0){for(s+=o,e=1;e<r;e++)t=n[e]+"",(i=j-t.length)&&(s+=dn(i)),s+=t;o=n[e],(i=j-(t=o+"").length)&&(s+=dn(i))}else if(0===o)return"0";for(;o%10==0;)o/=10;return s+o}function tn(n,e,i){if(n!==~~n||n<e||n>i)throw Error(H+n)}function rn(n,e,i,t){var r,s,o,u;for(s=n[0];s>=10;s/=10)--e;return--e<0?(e+=j,r=0):(r=Math.ceil((e+1)/j),e%=j),s=Z(10,j-e),u=n[r]%s|0,null==t?e<3?(0==e?u=u/100|0:1==e&&(u=u/10|0),o=i<4&&99999==u||i>3&&49999==u||5e4==u||0==u):o=(i<4&&u+1==s||i>3&&u+1==s/2)&&(n[r+1]/s/100|0)==Z(10,e-2)-1||(u==s/2||0==u)&&0==(n[r+1]/s/100|0):e<4?(0==e?u=u/1e3|0:1==e?u=u/100|0:2==e&&(u=u/10|0),o=(t||i<4)&&9999==u||!t&&i>3&&4999==u):o=((t||i<4)&&u+1==s||!t&&i>3&&u+1==s/2)&&(n[r+1]/s/1e3|0)==Z(10,e-3)-1,o}function sn(n,e,i){for(var t,r,s=[0],o=0,u=n.length;o<u;){for(r=s.length;r--;)s[r]*=e;for(s[0]+=J.indexOf(n.charAt(o++)),t=0;t<s.length;t++)s[t]>i-1&&(void 0===s[t+1]&&(s[t+1]=0),s[t+1]+=s[t]/i|0,s[t]%=i)}return s.reverse()}nn.absoluteValue=nn.abs=function(){var n=new this.constructor(this);return n.s<0&&(n.s=1),un(n)},nn.ceil=function(){return un(new this.constructor(this),this.e+1,2)},nn.clampedTo=nn.clamp=function(n,e){var i=this,t=i.constructor;if(n=new t(n),e=new t(e),!n.s||!e.s)return new t(NaN);if(n.gt(e))throw Error(H+e);return i.cmp(n)<0?n:i.cmp(e)>0?e:new t(i)},nn.comparedTo=nn.cmp=function(n){var e,i,t,r,s=this,o=s.d,u=(n=new s.constructor(n)).d,c=s.s,a=n.s;if(!o||!u)return c&&a?c!==a?c:o===u?0:!o^c<0?1:-1:NaN;if(!o[0]||!u[0])return o[0]?c:u[0]?-a:0;if(c!==a)return c;if(s.e!==n.e)return s.e>n.e^c<0?1:-1;for(e=0,i=(t=o.length)<(r=u.length)?t:r;e<i;++e)if(o[e]!==u[e])return o[e]>u[e]^c<0?1:-1;return t===r?0:t>r^c<0?1:-1},nn.cosine=nn.cos=function(){var n,e,i=this,t=i.constructor;return i.d?i.d[0]?(n=t.precision,e=t.rounding,t.precision=n+Math.max(i.e,i.sd())+j,t.rounding=1,i=function(n,e){var i,t,r;if(e.isZero())return e;t=e.d.length,t<32?r=(1/Cn(4,i=Math.ceil(t/3))).toString():(i=16,r="2.3283064365386962890625e-10");n.precision+=i,e=Un(n,1,e.times(r),new n(1));for(var s=i;s--;){var o=e.times(e);e=o.times(o).minus(o).times(8).plus(1)}return n.precision-=i,e}(t,Bn(t,i)),t.precision=n,t.rounding=e,un(2==S||3==S?i.neg():i,n,e,!0)):new t(1):new t(NaN)},nn.cubeRoot=nn.cbrt=function(){var n,e,i,t,r,s,o,u,c,a,l=this,A=l.constructor;if(!l.isFinite()||l.isZero())return new A(l);for(T=!1,(s=l.s*Z(l.s*l,1/3))&&Math.abs(s)!=1/0?t=new A(s.toString()):(i=en(l.d),(s=((n=l.e)-i.length+1)%3)&&(i+=1==s||-2==s?"0":"00"),s=Z(i,1/3),n=X((n+1)/3)-(n%3==(n<0?-1:2)),(t=new A(i=s==1/0?"5e"+n:(i=s.toExponential()).slice(0,i.indexOf("e")+1)+n)).s=l.s),o=(n=A.precision)+3;;)if(a=(c=(u=t).times(u).times(u)).plus(l),t=on(a.plus(l).times(u),a.plus(c),o+2,1),en(u.d).slice(0,o)===(i=en(t.d)).slice(0,o)){if("9999"!=(i=i.slice(o-3,o+1))&&(r||"4999"!=i)){+i&&(+i.slice(1)||"5"!=i.charAt(0))||(un(t,n+1,1),e=!t.times(t).times(t).eq(l));break}if(!r&&(un(u,n+1,0),u.times(u).times(u).eq(l))){t=u;break}o+=4,r=1}return T=!0,un(t,n,A.rounding,e)},nn.decimalPlaces=nn.dp=function(){var n,e=this.d,i=NaN;if(e){if(i=((n=e.length-1)-X(this.e/j))*j,n=e[n])for(;n%10==0;n/=10)i--;i<0&&(i=0)}return i},nn.dividedBy=nn.div=function(n){return on(this,new this.constructor(n))},nn.dividedToIntegerBy=nn.divToInt=function(n){var e=this.constructor;return un(on(this,new e(n),0,1,1),e.precision,e.rounding)},nn.equals=nn.eq=function(n){return 0===this.cmp(n)},nn.floor=function(){return un(new this.constructor(this),this.e+1,3)},nn.greaterThan=nn.gt=function(n){return this.cmp(n)>0},nn.greaterThanOrEqualTo=nn.gte=function(n){var e=this.cmp(n);return 1==e||0===e},nn.hyperbolicCosine=nn.cosh=function(){var n,e,i,t,r,s=this,o=s.constructor,u=new o(1);if(!s.isFinite())return new o(s.s?1/0:NaN);if(s.isZero())return u;i=o.precision,t=o.rounding,o.precision=i+Math.max(s.e,s.sd())+4,o.rounding=1,(r=s.d.length)<32?e=(1/Cn(4,n=Math.ceil(r/3))).toString():(n=16,e="2.3283064365386962890625e-10"),s=Un(o,1,s.times(e),new o(1),!0);for(var c,a=n,l=new o(8);a--;)c=s.times(s),s=u.minus(c.times(l.minus(c.times(l))));return un(s,o.precision=i,o.rounding=t,!0)},nn.hyperbolicSine=nn.sinh=function(){var n,e,i,t,r=this,s=r.constructor;if(!r.isFinite()||r.isZero())return new s(r);if(e=s.precision,i=s.rounding,s.precision=e+Math.max(r.e,r.sd())+4,s.rounding=1,(t=r.d.length)<3)r=Un(s,2,r,r,!0);else{n=(n=1.4*Math.sqrt(t))>16?16:0|n,r=Un(s,2,r=r.times(1/Cn(5,n)),r,!0);for(var o,u=new s(5),c=new s(16),a=new s(20);n--;)o=r.times(r),r=r.times(u.plus(o.times(c.times(o).plus(a))))}return s.precision=e,s.rounding=i,un(r,e,i,!0)},nn.hyperbolicTangent=nn.tanh=function(){var n,e,i=this,t=i.constructor;return i.isFinite()?i.isZero()?new t(i):(n=t.precision,e=t.rounding,t.precision=n+7,t.rounding=1,on(i.sinh(),i.cosh(),t.precision=n,t.rounding=e)):new t(i.s)},nn.inverseCosine=nn.acos=function(){var n,e=this,i=e.constructor,t=e.abs().cmp(1),r=i.precision,s=i.rounding;return-1!==t?0===t?e.isNeg()?An(i,r,s):new i(0):new i(NaN):e.isZero()?An(i,r+4,s).times(.5):(i.precision=r+6,i.rounding=1,e=e.asin(),n=An(i,r+4,s).times(.5),i.precision=r,i.rounding=s,n.minus(e))},nn.inverseHyperbolicCosine=nn.acosh=function(){var n,e,i=this,t=i.constructor;return i.lte(1)?new t(i.eq(1)?0:NaN):i.isFinite()?(n=t.precision,e=t.rounding,t.precision=n+Math.max(Math.abs(i.e),i.sd())+4,t.rounding=1,T=!1,i=i.times(i).minus(1).sqrt().plus(i),T=!0,t.precision=n,t.rounding=e,i.ln()):new t(i)},nn.inverseHyperbolicSine=nn.asinh=function(){var n,e,i=this,t=i.constructor;return!i.isFinite()||i.isZero()?new t(i):(n=t.precision,e=t.rounding,t.precision=n+2*Math.max(Math.abs(i.e),i.sd())+6,t.rounding=1,T=!1,i=i.times(i).plus(1).sqrt().plus(i),T=!0,t.precision=n,t.rounding=e,i.ln())},nn.inverseHyperbolicTangent=nn.atanh=function(){var n,e,i,t,r=this,s=r.constructor;return r.isFinite()?r.e>=0?new s(r.abs().eq(1)?r.s/0:r.isZero()?r:NaN):(n=s.precision,e=s.rounding,t=r.sd(),Math.max(t,n)<2*-r.e-1?un(new s(r),n,e,!0):(s.precision=i=t-r.e,r=on(r.plus(1),new s(1).minus(r),i+n,1),s.precision=n+4,s.rounding=1,r=r.ln(),s.precision=n,s.rounding=e,r.times(.5))):new s(NaN)},nn.inverseSine=nn.asin=function(){var n,e,i,t,r=this,s=r.constructor;return r.isZero()?new s(r):(e=r.abs().cmp(1),i=s.precision,t=s.rounding,-1!==e?0===e?((n=An(s,i+4,t).times(.5)).s=r.s,n):new s(NaN):(s.precision=i+6,s.rounding=1,r=r.div(new s(1).minus(r.times(r)).sqrt().plus(1)).atan(),s.precision=i,s.rounding=t,r.times(2)))},nn.inverseTangent=nn.atan=function(){var n,e,i,t,r,s,o,u,c,a=this,l=a.constructor,A=l.precision,f=l.rounding;if(a.isFinite()){if(a.isZero())return new l(a);if(a.abs().eq(1)&&A+4<=$)return(o=An(l,A+4,f).times(.25)).s=a.s,o}else{if(!a.s)return new l(NaN);if(A+4<=$)return(o=An(l,A+4,f).times(.5)).s=a.s,o}for(l.precision=u=A+10,l.rounding=1,n=i=Math.min(28,u/j+2|0);n;--n)a=a.div(a.times(a).plus(1).sqrt().plus(1));for(T=!1,e=Math.ceil(u/j),t=1,c=a.times(a),o=new l(a),r=a;-1!==n;)if(r=r.times(c),s=o.minus(r.div(t+=2)),r=r.times(c),void 0!==(o=s.plus(r.div(t+=2))).d[e])for(n=e;o.d[n]===s.d[n]&&n--;);return i&&(o=o.times(2<<i-1)),T=!0,un(o,l.precision=A,l.rounding=f,!0)},nn.isFinite=function(){return!!this.d},nn.isInteger=nn.isInt=function(){return!!this.d&&X(this.e/j)>this.d.length-2},nn.isNaN=function(){return!this.s},nn.isNegative=nn.isNeg=function(){return this.s<0},nn.isPositive=nn.isPos=function(){return this.s>0},nn.isZero=function(){return!!this.d&&0===this.d[0]},nn.lessThan=nn.lt=function(n){return this.cmp(n)<0},nn.lessThanOrEqualTo=nn.lte=function(n){return this.cmp(n)<1},nn.logarithm=nn.log=function(n){var e,i,t,r,s,o,u,c,a=this,l=a.constructor,A=l.precision,f=l.rounding;if(null==n)n=new l(10),e=!0;else{if(i=(n=new l(n)).d,n.s<0||!i||!i[0]||n.eq(1))return new l(NaN);e=n.eq(10)}if(i=a.d,a.s<0||!i||!i[0]||a.eq(1))return new l(i&&!i[0]?-1/0:1!=a.s?NaN:i?0:1/0);if(e)if(i.length>1)s=!0;else{for(r=i[0];r%10==0;)r/=10;s=1!==r}if(T=!1,o=mn(a,u=A+5),t=e?ln(l,u+10):mn(n,u),rn((c=on(o,t,u,1)).d,r=A,f))do{if(o=mn(a,u+=10),t=e?ln(l,u+10):mn(n,u),c=on(o,t,u,1),!s){+en(c.d).slice(r+1,r+15)+1==1e14&&(c=un(c,A+1,0));break}}while(rn(c.d,r+=10,f));return T=!0,un(c,A,f)},nn.minus=nn.sub=function(n){var e,i,t,r,s,o,u,c,a,l,A,f,d=this,h=d.constructor;if(n=new h(n),!d.d||!n.d)return d.s&&n.s?d.d?n.s=-n.s:n=new h(n.d||d.s!==n.s?d:NaN):n=new h(NaN),n;if(d.s!=n.s)return n.s=-n.s,d.plus(n);if(a=d.d,f=n.d,u=h.precision,c=h.rounding,!a[0]||!f[0]){if(f[0])n.s=-n.s;else{if(!a[0])return new h(3===c?-0:0);n=new h(d)}return T?un(n,u,c):n}if(i=X(n.e/j),l=X(d.e/j),a=a.slice(),s=l-i){for((A=s<0)?(e=a,s=-s,o=f.length):(e=f,i=l,o=a.length),s>(t=Math.max(Math.ceil(u/j),o)+2)&&(s=t,e.length=1),e.reverse(),t=s;t--;)e.push(0);e.reverse()}else{for((A=(t=a.length)<(o=f.length))&&(o=t),t=0;t<o;t++)if(a[t]!=f[t]){A=a[t]<f[t];break}s=0}for(A&&(e=a,a=f,f=e,n.s=-n.s),o=a.length,t=f.length-o;t>0;--t)a[o++]=0;for(t=f.length;t>s;){if(a[--t]<f[t]){for(r=t;r&&0===a[--r];)a[r]=q-1;--a[r],a[t]+=q}a[t]-=f[t]}for(;0===a[--o];)a.pop();for(;0===a[0];a.shift())--i;return a[0]?(n.d=a,n.e=an(a,i),T?un(n,u,c):n):new h(3===c?-0:0)},nn.modulo=nn.mod=function(n){var e,i=this,t=i.constructor;return n=new t(n),!i.d||!n.s||n.d&&!n.d[0]?new t(NaN):!n.d||i.d&&!i.d[0]?un(new t(i),t.precision,t.rounding):(T=!1,9==t.modulo?(e=on(i,n.abs(),0,3,1)).s*=n.s:e=on(i,n,0,t.modulo,1),e=e.times(n),T=!0,i.minus(e))},nn.naturalExponential=nn.exp=function(){return wn(this)},nn.naturalLogarithm=nn.ln=function(){return mn(this)},nn.negated=nn.neg=function(){var n=new this.constructor(this);return n.s=-n.s,un(n)},nn.plus=nn.add=function(n){var e,i,t,r,s,o,u,c,a,l,A=this,f=A.constructor;if(n=new f(n),!A.d||!n.d)return A.s&&n.s?A.d||(n=new f(n.d||A.s===n.s?A:NaN)):n=new f(NaN),n;if(A.s!=n.s)return n.s=-n.s,A.minus(n);if(a=A.d,l=n.d,u=f.precision,c=f.rounding,!a[0]||!l[0])return l[0]||(n=new f(A)),T?un(n,u,c):n;if(s=X(A.e/j),t=X(n.e/j),a=a.slice(),r=s-t){for(r<0?(i=a,r=-r,o=l.length):(i=l,t=s,o=a.length),r>(o=(s=Math.ceil(u/j))>o?s+1:o+1)&&(r=o,i.length=1),i.reverse();r--;)i.push(0);i.reverse()}for((o=a.length)-(r=l.length)<0&&(r=o,i=l,l=a,a=i),e=0;r;)e=(a[--r]=a[r]+l[r]+e)/q|0,a[r]%=q;for(e&&(a.unshift(e),++t),o=a.length;0==a[--o];)a.pop();return n.d=a,n.e=an(a,t),T?un(n,u,c):n},nn.precision=nn.sd=function(n){var e,i=this;if(void 0!==n&&n!==!!n&&1!==n&&0!==n)throw Error(H+n);return i.d?(e=fn(i.d),n&&i.e+1>e&&(e=i.e+1)):e=NaN,e},nn.round=function(){var n=this,e=n.constructor;return un(new e(n),n.e+1,e.rounding)},nn.sine=nn.sin=function(){var n,e,i=this,t=i.constructor;return i.isFinite()?i.isZero()?new t(i):(n=t.precision,e=t.rounding,t.precision=n+Math.max(i.e,i.sd())+j,t.rounding=1,i=function(n,e){var i,t=e.d.length;if(t<3)return e.isZero()?e:Un(n,2,e,e);i=(i=1.4*Math.sqrt(t))>16?16:0|i,e=e.times(1/Cn(5,i)),e=Un(n,2,e,e);for(var r,s=new n(5),o=new n(16),u=new n(20);i--;)r=e.times(e),e=e.times(s.plus(r.times(o.times(r).minus(u))));return e}(t,Bn(t,i)),t.precision=n,t.rounding=e,un(S>2?i.neg():i,n,e,!0)):new t(NaN)},nn.squareRoot=nn.sqrt=function(){var n,e,i,t,r,s,o=this,u=o.d,c=o.e,a=o.s,l=o.constructor;if(1!==a||!u||!u[0])return new l(!a||a<0&&(!u||u[0])?NaN:u?o:1/0);for(T=!1,0==(a=Math.sqrt(+o))||a==1/0?(((e=en(u)).length+c)%2==0&&(e+="0"),a=Math.sqrt(e),c=X((c+1)/2)-(c<0||c%2),t=new l(e=a==1/0?"5e"+c:(e=a.toExponential()).slice(0,e.indexOf("e")+1)+c)):t=new l(a.toString()),i=(c=l.precision)+3;;)if(t=(s=t).plus(on(o,s,i+2,1)).times(.5),en(s.d).slice(0,i)===(e=en(t.d)).slice(0,i)){if("9999"!=(e=e.slice(i-3,i+1))&&(r||"4999"!=e)){+e&&(+e.slice(1)||"5"!=e.charAt(0))||(un(t,c+1,1),n=!t.times(t).eq(o));break}if(!r&&(un(s,c+1,0),s.times(s).eq(o))){t=s;break}i+=4,r=1}return T=!0,un(t,c,l.rounding,n)},nn.tangent=nn.tan=function(){var n,e,i=this,t=i.constructor;return i.isFinite()?i.isZero()?new t(i):(n=t.precision,e=t.rounding,t.precision=n+10,t.rounding=1,(i=i.sin()).s=1,i=on(i,new t(1).minus(i.times(i)).sqrt(),n+10,0),t.precision=n,t.rounding=e,un(2==S||4==S?i.neg():i,n,e,!0)):new t(NaN)},nn.times=nn.mul=function(n){var e,i,t,r,s,o,u,c,a,l=this,A=l.constructor,f=l.d,d=(n=new A(n)).d;if(n.s*=l.s,!(f&&f[0]&&d&&d[0]))return new A(!n.s||f&&!f[0]&&!d||d&&!d[0]&&!f?NaN:f&&d?0*n.s:n.s/0);for(i=X(l.e/j)+X(n.e/j),(c=f.length)<(a=d.length)&&(s=f,f=d,d=s,o=c,c=a,a=o),s=[],t=o=c+a;t--;)s.push(0);for(t=a;--t>=0;){for(e=0,r=c+t;r>t;)u=s[r]+d[t]*f[r-t-1]+e,s[r--]=u%q|0,e=u/q|0;s[r]=(s[r]+e)%q|0}for(;!s[--o];)s.pop();return e?++i:s.shift(),n.d=s,n.e=an(s,i),T?un(n,A.precision,A.rounding):n},nn.toBinary=function(n,e){return xn(this,2,n,e)},nn.toDecimalPlaces=nn.toDP=function(n,e){var i=this,t=i.constructor;return i=new t(i),void 0===n?i:(tn(n,0,F),void 0===e?e=t.rounding:tn(e,0,8),un(i,n+i.e+1,e))},nn.toExponential=function(n,e){var i,t=this,r=t.constructor;return void 0===n?i=cn(t,!0):(tn(n,0,F),void 0===e?e=r.rounding:tn(e,0,8),i=cn(t=un(new r(t),n+1,e),!0,n+1)),t.isNeg()&&!t.isZero()?"-"+i:i},nn.toFixed=function(n,e){var i,t,r=this,s=r.constructor;return void 0===n?i=cn(r):(tn(n,0,F),void 0===e?e=s.rounding:tn(e,0,8),i=cn(t=un(new s(r),n+r.e+1,e),!1,n+t.e+1)),r.isNeg()&&!r.isZero()?"-"+i:i},nn.toFraction=function(n){var e,i,t,r,s,o,u,c,a,l,A,f,d=this,h=d.d,g=d.constructor;if(!h)return new g(d);if(a=i=new g(1),t=c=new g(0),o=(s=(e=new g(t)).e=fn(h)-d.e-1)%j,e.d[0]=Z(10,o<0?j+o:o),null==n)n=s>0?e:a;else{if(!(u=new g(n)).isInt()||u.lt(a))throw Error(H+u);n=u.gt(e)?s>0?e:a:u}for(T=!1,u=new g(en(h)),l=g.precision,g.precision=s=h.length*j*2;A=on(u,e,0,1,1),1!=(r=i.plus(A.times(t))).cmp(n);)i=t,t=r,r=a,a=c.plus(A.times(r)),c=r,r=e,e=u.minus(A.times(r)),u=r;return r=on(n.minus(i),t,0,1,1),c=c.plus(r.times(a)),i=i.plus(r.times(t)),c.s=a.s=d.s,f=on(a,t,s,1).minus(d).abs().cmp(on(c,i,s,1).minus(d).abs())<1?[a,t]:[c,i],g.precision=l,T=!0,f},nn.toHexadecimal=nn.toHex=function(n,e){return xn(this,16,n,e)},nn.toNearest=function(n,e){var i=this,t=i.constructor;if(i=new t(i),null==n){if(!i.d)return i;n=new t(1),e=t.rounding}else{if(n=new t(n),void 0===e?e=t.rounding:tn(e,0,8),!i.d)return n.s?i:n;if(!n.d)return n.s&&(n.s=i.s),n}return n.d[0]?(T=!1,i=on(i,n,0,e,1).times(n),T=!0,un(i)):(n.s=i.s,i=n),i},nn.toNumber=function(){return+this},nn.toOctal=function(n,e){return xn(this,8,n,e)},nn.toPower=nn.pow=function(n){var e,i,t,r,s,o,u=this,c=u.constructor,a=+(n=new c(n));if(!(u.d&&n.d&&u.d[0]&&n.d[0]))return new c(Z(+u,a));if((u=new c(u)).eq(1))return u;if(t=c.precision,s=c.rounding,n.eq(1))return un(u,t,s);if((e=X(n.e/j))>=n.d.length-1&&(i=a<0?-a:a)<=9007199254740991)return r=hn(c,u,i,t),n.s<0?new c(1).div(r):un(r,t,s);if((o=u.s)<0){if(e<n.d.length-1)return new c(NaN);if(0==(1&n.d[e])&&(o=1),0==u.e&&1==u.d[0]&&1==u.d.length)return u.s=o,u}return(e=0!=(i=Z(+u,a))&&isFinite(i)?new c(i+"").e:X(a*(Math.log("0."+en(u.d))/Math.LN10+u.e+1)))>c.maxE+1||e<c.minE-1?new c(e>0?o/0:0):(T=!1,c.rounding=u.s=1,i=Math.min(12,(e+"").length),(r=wn(n.times(mn(u,t+i)),t)).d&&rn((r=un(r,t+5,1)).d,t,s)&&(e=t+10,+en((r=un(wn(n.times(mn(u,e+i)),e),e+5,1)).d).slice(t+1,t+15)+1==1e14&&(r=un(r,t+1,0))),r.s=o,T=!0,c.rounding=s,un(r,t,s))},nn.toPrecision=function(n,e){var i,t=this,r=t.constructor;return void 0===n?i=cn(t,t.e<=r.toExpNeg||t.e>=r.toExpPos):(tn(n,1,F),void 0===e?e=r.rounding:tn(e,0,8),i=cn(t=un(new r(t),n,e),n<=t.e||t.e<=r.toExpNeg,n)),t.isNeg()&&!t.isZero()?"-"+i:i},nn.toSignificantDigits=nn.toSD=function(n,e){var i=this.constructor;return void 0===n?(n=i.precision,e=i.rounding):(tn(n,1,F),void 0===e?e=i.rounding:tn(e,0,8)),un(new i(this),n,e)},nn.toString=function(){var n=this,e=n.constructor,i=cn(n,n.e<=e.toExpNeg||n.e>=e.toExpPos);return n.isNeg()&&!n.isZero()?"-"+i:i},nn.truncated=nn.trunc=function(){return un(new this.constructor(this),this.e+1,1)},nn.valueOf=nn.toJSON=function(){var n=this,e=n.constructor,i=cn(n,n.e<=e.toExpNeg||n.e>=e.toExpPos);return n.isNeg()?"-"+i:i};var on=function(){function n(n,e,i){var t,r=0,s=n.length;for(n=n.slice();s--;)t=n[s]*e+r,n[s]=t%i|0,r=t/i|0;return r&&n.unshift(r),n}function e(n,e,i,t){var r,s;if(i!=t)s=i>t?1:-1;else for(r=s=0;r<i;r++)if(n[r]!=e[r]){s=n[r]>e[r]?1:-1;break}return s}function i(n,e,i,t){for(var r=0;i--;)n[i]-=r,r=n[i]<e[i]?1:0,n[i]=r*t+n[i]-e[i];for(;!n[0]&&n.length>1;)n.shift()}return function(t,r,s,o,u,c){var a,l,A,f,d,h,g,p,w,m,v,N,U,C,B,x,Q,R,E,b,y=t.constructor,S=t.s==r.s?1:-1,D=t.d,F=r.d;if(!(D&&D[0]&&F&&F[0]))return new y(t.s&&r.s&&(D?!F||D[0]!=F[0]:F)?D&&0==D[0]||!F?0*S:S/0:NaN);for(c?(d=1,l=t.e-r.e):(c=q,d=j,l=X(t.e/d)-X(r.e/d)),E=F.length,Q=D.length,m=(w=new y(S)).d=[],A=0;F[A]==(D[A]||0);A++);if(F[A]>(D[A]||0)&&l--,null==s?(C=s=y.precision,o=y.rounding):C=u?s+(t.e-r.e)+1:s,C<0)m.push(1),h=!0;else{if(C=C/d+2|0,A=0,1==E){for(f=0,F=F[0],C++;(A<Q||f)&&C--;A++)B=f*c+(D[A]||0),m[A]=B/F|0,f=B%F|0;h=f||A<Q}else{for((f=c/(F[0]+1)|0)>1&&(F=n(F,f,c),D=n(D,f,c),E=F.length,Q=D.length),x=E,N=(v=D.slice(0,E)).length;N<E;)v[N++]=0;(b=F.slice()).unshift(0),R=F[0],F[1]>=c/2&&++R;do{f=0,(a=e(F,v,E,N))<0?(U=v[0],E!=N&&(U=U*c+(v[1]||0)),(f=U/R|0)>1?(f>=c&&(f=c-1),1==(a=e(g=n(F,f,c),v,p=g.length,N=v.length))&&(f--,i(g,E<p?b:F,p,c))):(0==f&&(a=f=1),g=F.slice()),(p=g.length)<N&&g.unshift(0),i(v,g,N,c),-1==a&&(a=e(F,v,E,N=v.length))<1&&(f++,i(v,E<N?b:F,N,c)),N=v.length):0===a&&(f++,v=[0]),m[A++]=f,a&&v[0]?v[N++]=D[x]||0:(v=[D[x]],N=1)}while((x++<Q||void 0!==v[0])&&C--);h=void 0!==v[0]}m[0]||m.shift()}if(1==d)w.e=l,M=h;else{for(A=1,f=m[0];f>=10;f/=10)A++;w.e=A+l*d-1,un(w,u?s+w.e+1:s,o,h)}return w}}();function un(n,e,i,t){var r,s,o,u,c,a,l,A,f,d=n.constructor;n:if(null!=e){if(!(A=n.d))return n;for(r=1,u=A[0];u>=10;u/=10)r++;if((s=e-r)<0)s+=j,o=e,c=(l=A[f=0])/Z(10,r-o-1)%10|0;else if((f=Math.ceil((s+1)/j))>=(u=A.length)){if(!t)break n;for(;u++<=f;)A.push(0);l=c=0,r=1,o=(s%=j)-j+1}else{for(l=u=A[f],r=1;u>=10;u/=10)r++;c=(o=(s%=j)-j+r)<0?0:l/Z(10,r-o-1)%10|0}if(t=t||e<0||void 0!==A[f+1]||(o<0?l:l%Z(10,r-o-1)),a=i<4?(c||t)&&(0==i||i==(n.s<0?3:2)):c>5||5==c&&(4==i||t||6==i&&(s>0?o>0?l/Z(10,r-o):0:A[f-1])%10&1||i==(n.s<0?8:7)),e<1||!A[0])return A.length=0,a?(e-=n.e+1,A[0]=Z(10,(j-e%j)%j),n.e=-e||0):A[0]=n.e=0,n;if(0==s?(A.length=f,u=1,f--):(A.length=f+1,u=Z(10,j-s),A[f]=o>0?(l/Z(10,r-o)%Z(10,o)|0)*u:0),a)for(;;){if(0==f){for(s=1,o=A[0];o>=10;o/=10)s++;for(o=A[0]+=u,u=1;o>=10;o/=10)u++;s!=u&&(n.e++,A[0]==q&&(A[0]=1));break}if(A[f]+=u,A[f]!=q)break;A[f--]=0,u=1}for(s=A.length;0===A[--s];)A.pop()}return T&&(n.e>d.maxE?(n.d=null,n.e=NaN):n.e<d.minE&&(n.e=0,n.d=[0])),n}function cn(n,e,i){if(!n.isFinite())return vn(n);var t,r=n.e,s=en(n.d),o=s.length;return e?(i&&(t=i-o)>0?s=s.charAt(0)+"."+s.slice(1)+dn(t):o>1&&(s=s.charAt(0)+"."+s.slice(1)),s=s+(n.e<0?"e":"e+")+n.e):r<0?(s="0."+dn(-r-1)+s,i&&(t=i-o)>0&&(s+=dn(t))):r>=o?(s+=dn(r+1-o),i&&(t=i-r-1)>0&&(s=s+"."+dn(t))):((t=r+1)<o&&(s=s.slice(0,t)+"."+s.slice(t)),i&&(t=i-o)>0&&(r+1===o&&(s+="."),s+=dn(t))),s}function an(n,e){var i=n[0];for(e*=j;i>=10;i/=10)e++;return e}function ln(n,e,i){if(e>_)throw T=!0,i&&(n.precision=i),Error(P);return un(new n(K),e,1,!0)}function An(n,e,i){if(e>$)throw Error(P);return un(new n(I),e,i,!0)}function fn(n){var e=n.length-1,i=e*j+1;if(e=n[e]){for(;e%10==0;e/=10)i--;for(e=n[0];e>=10;e/=10)i++}return i}function dn(n){for(var e="";n--;)e+="0";return e}function hn(n,e,i,t){var r,s=new n(1),o=Math.ceil(t/j+4);for(T=!1;;){if(i%2&&Qn((s=s.times(e)).d,o)&&(r=!0),0===(i=X(i/2))){i=s.d.length-1,r&&0===s.d[i]&&++s.d[i];break}Qn((e=e.times(e)).d,o)}return T=!0,s}function gn(n){return 1&n.d[n.d.length-1]}function pn(n,e,i){for(var t,r=new n(e[0]),s=0;++s<e.length;){if(!(t=new n(e[s])).s){r=t;break}r[i](t)&&(r=t)}return r}function wn(n,e){var i,t,r,s,o,u,c,a=0,l=0,A=0,f=n.constructor,d=f.rounding,h=f.precision;if(!n.d||!n.d[0]||n.e>17)return new f(n.d?n.d[0]?n.s<0?0:1/0:1:n.s?n.s<0?0:n:NaN);for(null==e?(T=!1,c=h):c=e,u=new f(.03125);n.e>-2;)n=n.times(u),A+=5;for(c+=t=Math.log(Z(2,A))/Math.LN10*2+5|0,i=s=o=new f(1),f.precision=c;;){if(s=un(s.times(n),c,1),i=i.times(++l),en((u=o.plus(on(s,i,c,1))).d).slice(0,c)===en(o.d).slice(0,c)){for(r=A;r--;)o=un(o.times(o),c,1);if(null!=e)return f.precision=h,o;if(!(a<3&&rn(o.d,c-t,d,a)))return un(o,f.precision=h,d,T=!0);f.precision=c+=10,i=s=u=new f(1),l=0,a++}o=u}}function mn(n,e){var i,t,r,s,o,u,c,a,l,A,f,d=1,h=n,g=h.d,p=h.constructor,w=p.rounding,m=p.precision;if(h.s<0||!g||!g[0]||!h.e&&1==g[0]&&1==g.length)return new p(g&&!g[0]?-1/0:1!=h.s?NaN:g?0:h);if(null==e?(T=!1,l=m):l=e,p.precision=l+=10,t=(i=en(g)).charAt(0),!(Math.abs(s=h.e)<15e14))return a=ln(p,l+2,m).times(s+""),h=mn(new p(t+"."+i.slice(1)),l-10).plus(a),p.precision=m,null==e?un(h,m,w,T=!0):h;for(;t<7&&1!=t||1==t&&i.charAt(1)>3;)t=(i=en((h=h.times(n)).d)).charAt(0),d++;for(s=h.e,t>1?(h=new p("0."+i),s++):h=new p(t+"."+i.slice(1)),A=h,c=o=h=on(h.minus(1),h.plus(1),l,1),f=un(h.times(h),l,1),r=3;;){if(o=un(o.times(f),l,1),en((a=c.plus(on(o,new p(r),l,1))).d).slice(0,l)===en(c.d).slice(0,l)){if(c=c.times(2),0!==s&&(c=c.plus(ln(p,l+2,m).times(s+""))),c=on(c,new p(d),l,1),null!=e)return p.precision=m,c;if(!rn(c.d,l-10,w,u))return un(c,p.precision=m,w,T=!0);p.precision=l+=10,a=o=h=on(A.minus(1),A.plus(1),l,1),f=un(h.times(h),l,1),r=u=1}c=a,r+=2}}function vn(n){return String(n.s*n.s/0)}function Nn(n,e){var i,t,r;for((i=e.indexOf("."))>-1&&(e=e.replace(".","")),(t=e.search(/e/i))>0?(i<0&&(i=t),i+=+e.slice(t+1),e=e.substring(0,t)):i<0&&(i=e.length),t=0;48===e.charCodeAt(t);t++);for(r=e.length;48===e.charCodeAt(r-1);--r);if(e=e.slice(t,r)){if(r-=t,n.e=i=i-t-1,n.d=[],t=(i+1)%j,i<0&&(t+=j),t<r){for(t&&n.d.push(+e.slice(0,t)),r-=j;t<r;)n.d.push(+e.slice(t,t+=j));e=e.slice(t),t=j-e.length}else t-=r;for(;t--;)e+="0";n.d.push(+e),T&&(n.e>n.constructor.maxE?(n.d=null,n.e=NaN):n.e<n.constructor.minE&&(n.e=0,n.d=[0]))}else n.e=0,n.d=[0];return n}function Un(n,e,i,t,r){var s,o,u,c,a=n.precision,l=Math.ceil(a/j);for(T=!1,c=i.times(i),u=new n(t);;){if(o=on(u.times(c),new n(e++*e++),a,1),u=r?t.plus(o):t.minus(o),t=on(o.times(c),new n(e++*e++),a,1),void 0!==(o=u.plus(t)).d[l]){for(s=l;o.d[s]===u.d[s]&&s--;);if(-1==s)break}s=u,u=t,t=o,o=s}return T=!0,o.d.length=l+1,o}function Cn(n,e){for(var i=n;--e;)i*=n;return i}function Bn(n,e){var i,t=e.s<0,r=An(n,n.precision,1),s=r.times(.5);if((e=e.abs()).lte(s))return S=t?4:1,e;if((i=e.divToInt(r)).isZero())S=t?3:2;else{if((e=e.minus(i.times(r))).lte(s))return S=gn(i)?t?2:3:t?4:1,e;S=gn(i)?t?1:4:t?3:2}return e.minus(r).abs()}function xn(n,e,i,t){var r,s,o,u,c,a,l,A,f,d=n.constructor,h=void 0!==i;if(h?(tn(i,1,F),void 0===t?t=d.rounding:tn(t,0,8)):(i=d.precision,t=d.rounding),n.isFinite()){for(h?(r=2,16==e?i=4*i-3:8==e&&(i=3*i-2)):r=e,(o=(l=cn(n)).indexOf("."))>=0&&(l=l.replace(".",""),(f=new d(1)).e=l.length-o,f.d=sn(cn(f),10,r),f.e=f.d.length),s=c=(A=sn(l,10,r)).length;0==A[--c];)A.pop();if(A[0]){if(o<0?s--:((n=new d(n)).d=A,n.e=s,A=(n=on(n,f,i,t,0,r)).d,s=n.e,a=M),o=A[i],u=r/2,a=a||void 0!==A[i+1],a=t<4?(void 0!==o||a)&&(0===t||t===(n.s<0?3:2)):o>u||o===u&&(4===t||a||6===t&&1&A[i-1]||t===(n.s<0?8:7)),A.length=i,a)for(;++A[--i]>r-1;)A[i]=0,i||(++s,A.unshift(1));for(c=A.length;!A[c-1];--c);for(o=0,l="";o<c;o++)l+=J.charAt(A[o]);if(h){if(c>1)if(16==e||8==e){for(o=16==e?4:3,--c;c%o;c++)l+="0";for(c=(A=sn(l,r,e)).length;!A[c-1];--c);for(o=1,l="1.";o<c;o++)l+=J.charAt(A[o])}else l=l.charAt(0)+"."+l.slice(1);l=l+(s<0?"p":"p+")+s}else if(s<0){for(;++s;)l="0"+l;l="0."+l}else if(++s>c)for(s-=c;s--;)l+="0";else s<c&&(l=l.slice(0,s)+"."+l.slice(s))}else l=h?"0p+0":"0";l=(16==e?"0x":2==e?"0b":8==e?"0o":"")+l}else l=vn(n);return n.s<0?"-"+l:l}function Qn(n,e){if(n.length>e)return n.length=e,!0}function Rn(n){return new this(n).abs()}function En(n){return new this(n).acos()}function bn(n){return new this(n).acosh()}function yn(n,e){return new this(n).plus(e)}function Mn(n){return new this(n).asin()}function Sn(n){return new this(n).asinh()}function Dn(n){return new this(n).atan()}function Fn(n){return new this(n).atanh()}function Jn(n,e){n=new this(n),e=new this(e);var i,t=this.precision,r=this.rounding,s=t+4;return n.s&&e.s?n.d||e.d?!e.d||n.isZero()?(i=e.s<0?An(this,t,r):new this(0)).s=n.s:!n.d||e.isZero()?(i=An(this,s,1).times(.5)).s=n.s:e.s<0?(this.precision=s,this.rounding=1,i=this.atan(on(n,e,s,1)),e=An(this,s,1),this.precision=t,this.rounding=r,i=n.s<0?i.minus(e):i.plus(e)):i=this.atan(on(n,e,s,1)):(i=An(this,s,1).times(e.s>0?.25:.75)).s=n.s:i=new this(NaN),i}function Kn(n){return new this(n).cbrt()}function In(n){return un(n=new this(n),n.e+1,2)}function Yn(n,e,i){return new this(n).clamp(e,i)}function Tn(n){if(!n||"object"!=typeof n)throw Error(V+"Object expected");var e,i,t,r=!0===n.defaults,s=["precision",1,F,"rounding",0,8,"toExpNeg",-D,0,"toExpPos",0,D,"maxE",0,D,"minE",-D,0,"modulo",0,9];for(e=0;e<s.length;e+=3)if(i=s[e],r&&(this[i]=Y[i]),void 0!==(t=n[i])){if(!(X(t)===t&&t>=s[e+1]&&t<=s[e+2]))throw Error(H+i+": "+t);this[i]=t}if(i="crypto",r&&(this[i]=Y[i]),void 0!==(t=n[i])){if(!0!==t&&!1!==t&&0!==t&&1!==t)throw Error(H+i+": "+t);if(t){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw Error(k);this[i]=!0}else this[i]=!1}return this}function Vn(n){return new this(n).cos()}function Hn(n){return new this(n).cosh()}function Pn(n,e){return new this(n).div(e)}function kn(n){return new this(n).exp()}function On(n){return un(n=new this(n),n.e+1,3)}function Xn(){var n,e,i=new this(0);for(T=!1,n=0;n<arguments.length;)if((e=new this(arguments[n++])).d)i.d&&(i=i.plus(e.times(e)));else{if(e.s)return T=!0,new this(1/0);i=e}return T=!0,i.sqrt()}function Zn(n){return n instanceof fe||n&&n.toStringTag===O||!1}function Ln(n){return new this(n).ln()}function zn(n,e){return new this(n).log(e)}function Wn(n){return new this(n).log(2)}function Gn(n){return new this(n).log(10)}function qn(){return pn(this,arguments,"lt")}function jn(){return pn(this,arguments,"gt")}function _n(n,e){return new this(n).mod(e)}function $n(n,e){return new this(n).mul(e)}function ne(n,e){return new this(n).pow(e)}function ee(n){var e,i,t,r,s=0,o=new this(1),u=[];if(void 0===n?n=this.precision:tn(n,1,F),t=Math.ceil(n/j),this.crypto)if(crypto.getRandomValues)for(e=crypto.getRandomValues(new Uint32Array(t));s<t;)(r=e[s])>=429e7?e[s]=crypto.getRandomValues(new Uint32Array(1))[0]:u[s++]=r%1e7;else{if(!crypto.randomBytes)throw Error(k);for(e=crypto.randomBytes(t*=4);s<t;)(r=e[s]+(e[s+1]<<8)+(e[s+2]<<16)+((127&e[s+3])<<24))>=214e7?crypto.randomBytes(4).copy(e,s):(u.push(r%1e7),s+=4);s=t/4}else for(;s<t;)u[s++]=1e7*Math.random()|0;for(t=u[--s],n%=j,t&&n&&(r=Z(10,j-n),u[s]=(t/r|0)*r);0===u[s];s--)u.pop();if(s<0)i=0,u=[0];else{for(i=-1;0===u[0];i-=j)u.shift();for(t=1,r=u[0];r>=10;r/=10)t++;t<j&&(i-=j-t)}return o.e=i,o.d=u,o}function ie(n){return un(n=new this(n),n.e+1,this.rounding)}function te(n){return(n=new this(n)).d?n.d[0]?n.s:0*n.s:n.s||NaN}function re(n){return new this(n).sin()}function se(n){return new this(n).sinh()}function oe(n){return new this(n).sqrt()}function ue(n,e){return new this(n).sub(e)}function ce(){var n=0,e=arguments,i=new this(e[n]);for(T=!1;i.s&&++n<e.length;)i=i.plus(e[n]);return T=!0,un(i,this.precision,this.rounding)}function ae(n){return new this(n).tan()}function le(n){return new this(n).tanh()}function Ae(n){return un(n=new this(n),n.e+1,1)}nn[Symbol.for("nodejs.util.inspect.custom")]=nn.toString,nn[Symbol.toStringTag]="Decimal";var fe=nn.constructor=function n(e){var i,t,r;function s(n){var e,i,t,r=this;if(!(r instanceof s))return new s(n);if(r.constructor=s,Zn(n))return r.s=n.s,void(T?!n.d||n.e>s.maxE?(r.e=NaN,r.d=null):n.e<s.minE?(r.e=0,r.d=[0]):(r.e=n.e,r.d=n.d.slice()):(r.e=n.e,r.d=n.d?n.d.slice():n.d));if("number"===(t=typeof n)){if(0===n)return r.s=1/n<0?-1:1,r.e=0,void(r.d=[0]);if(n<0?(n=-n,r.s=-1):r.s=1,n===~~n&&n<1e7){for(e=0,i=n;i>=10;i/=10)e++;return void(T?e>s.maxE?(r.e=NaN,r.d=null):e<s.minE?(r.e=0,r.d=[0]):(r.e=e,r.d=[n]):(r.e=e,r.d=[n]))}return 0*n!=0?(n||(r.s=NaN),r.e=NaN,void(r.d=null)):Nn(r,n.toString())}if("string"!==t)throw Error(H+n);return 45===(i=n.charCodeAt(0))?(n=n.slice(1),r.s=-1):(43===i&&(n=n.slice(1)),r.s=1),G.test(n)?Nn(r,n):function(n,e){var i,t,r,s,o,u,c,a,l;if(e.indexOf("_")>-1){if(e=e.replace(/(\d)_(?=\d)/g,"$1"),G.test(e))return Nn(n,e)}else if("Infinity"===e||"NaN"===e)return+e||(n.s=NaN),n.e=NaN,n.d=null,n;if(z.test(e))i=16,e=e.toLowerCase();else if(L.test(e))i=2;else{if(!W.test(e))throw Error(H+e);i=8}for((s=e.search(/p/i))>0?(c=+e.slice(s+1),e=e.substring(2,s)):e=e.slice(2),o=(s=e.indexOf("."))>=0,t=n.constructor,o&&(s=(u=(e=e.replace(".","")).length)-s,r=hn(t,new t(i),s,2*s)),s=l=(a=sn(e,i,q)).length-1;0===a[s];--s)a.pop();return s<0?new t(0*n.s):(n.e=an(a,l),n.d=a,T=!1,o&&(n=on(n,r,4*u)),c&&(n=n.times(Math.abs(c)<54?Z(2,c):fe.pow(2,c))),T=!0,n)}(r,n)}if(s.prototype=nn,s.ROUND_UP=0,s.ROUND_DOWN=1,s.ROUND_CEIL=2,s.ROUND_FLOOR=3,s.ROUND_HALF_UP=4,s.ROUND_HALF_DOWN=5,s.ROUND_HALF_EVEN=6,s.ROUND_HALF_CEIL=7,s.ROUND_HALF_FLOOR=8,s.EUCLID=9,s.config=s.set=Tn,s.clone=n,s.isDecimal=Zn,s.abs=Rn,s.acos=En,s.acosh=bn,s.add=yn,s.asin=Mn,s.asinh=Sn,s.atan=Dn,s.atanh=Fn,s.atan2=Jn,s.cbrt=Kn,s.ceil=In,s.clamp=Yn,s.cos=Vn,s.cosh=Hn,s.div=Pn,s.exp=kn,s.floor=On,s.hypot=Xn,s.ln=Ln,s.log=zn,s.log10=Gn,s.log2=Wn,s.max=qn,s.min=jn,s.mod=_n,s.mul=$n,s.pow=ne,s.random=ee,s.round=ie,s.sign=te,s.sin=re,s.sinh=se,s.sqrt=oe,s.sub=ue,s.sum=ce,s.tan=ae,s.tanh=le,s.trunc=Ae,void 0===e&&(e={}),e&&!0!==e.defaults)for(r=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],i=0;i<r.length;)e.hasOwnProperty(t=r[i++])||(e[t]=this[t]);return s.config(e),s}(Y);K=new fe(K),I=new fe(I);const de={class:"seller-level-content"},he=(n=>(Q("data-v-c2b30052"),n=n(),R(),n))((()=>h("div",{class:"fixed-header-spacer"},null,-1))),ge={class:"info-content"},pe={class:"ads-info"},we=["innerHTML"],me=["innerHTML"],ve={class:"current-num-info"},Ne={key:0},Ue={key:1},Ce={key:0},Be=["innerHTML"],xe=["innerHTML"],Qe={key:3},Re={class:"level-content"},Ee={class:"name-content"},be={class:"title"},ye={key:0},Me={key:0},Se={key:1},De=["src"],Fe={class:"item"},Je={class:"title"},Ke=["src"],Ie={class:"item"},Ye={class:"title"},Te=["src"],Ve={class:"item"},He={class:"title"},Pe=["src"],ke={class:"item"},Oe={class:"title"},Xe=["src"],Ze={key:0,class:"item"},Le={class:"title"},ze=["src"],We={key:0},Ge=["src"],qe={class:"item"},je={class:"title"},_e=["src"],$e=["src"],ni={class:"item"},ei={class:"title"},ii=["src"],ti=["src"],ri={key:1,class:"item"},si={class:"title"},oi=["src"],ui=["src"],ci=e({name:"SellerLevel"}),ai=n(Object.assign(ci,{setup(n){const{t:e}=i(),Q="tiktokMall",R=t((()=>["inchoi"].includes(Q))),M=t((()=>["shop2u"].includes(Q))),S=r(),D=s(0),F=s(0),J=s([...y(E)]);if(R.value){const n=["升级礼金"];for(let e=0;e<J.value.length;e++)if(J.value[e].data&&J.value[e].data.length)for(let i=0;i<J.value[e].data.length;i++)n.includes(J.value[e].data[i].title)&&J.value[e].data.splice(i--,1)}if(["shop2u"].includes(Q))for(let i=0;i<J.value.length;i++)if(J.value[i].data&&J.value[i].data.length)for(let n=0;n<J.value[i].data.length;n++)"会员升级"===J.value[i].data[n].title&&(J.value[i].data[n].info="会员升级是通过直属推分店数决会员级别，分店数越高，系统将自动升级。");const K=s(""),I=y(b);if(!["familyShop","sm","shop2u","antMall"].includes(Q)){const n=I.findIndex((n=>"SS"===n.name));I.splice(n,1)}if(!["shop2u","antMall"].includes(Q)){const n=I.findIndex((n=>"SSS"===n.name));I.splice(n,1)}const Y=s([...I]),T=t((()=>["shop2u"].includes(Q))),V=new URL("/www/png/name-89c2b4a1.png",self.location),H={icon_01:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAPJSURBVHgB7VdLUttAEB1/qGJDxVtYKSeIxaeKncUJ4pwA+QZwApsTQE6AOQGwyhJ5xwKwfIIoG9g6sOOb95wee1BmRjJZJcWrkjWWZqaf3nT3TCv1jn8MFfUGRFHUuLu7iyqVSuvl5aWBR4G8GuN/iuejpaWlBBirOTEXofX19QgGu2g2cTVKDOmD3NHFxUWiSqIUoc3NzeDh4eEQzUg/A7ExjKVoZmhnk8kqlUD9VivKE1tYWNg7Pz/P1N8SElWO1UyR5OnpqY/7aZqm1iVpNpuNarXaRrMrJIkM7U6RWl5CGxsb8fPzM5XRinQuLy9P1BxYXV2Nc8RizHGk5iUkypwJmRTEvkCRTL0BUCyAYmeaFO5bLqWshMRnSCagf4BM6FueWq22zzaWcrdgGUmKATGGT4U2n6raBoNM1yCz5TIioG/FvGDsg6sT56DKJMMxEiSqkBCXSgxQ2p5vmZiP8NXb+j99jOoqN6kMt44eLrb8hKDKttwzn/PRMJLjkKQNQl18+XcY6rnGSVAkYqPrJcR1VoY6ygPtY7Z3WJrPvrEgcjQz2Ww4CcE5I92Gg566JkQ6aJpk0LeNK4ShXR2RygO8p0oTX6rX6y3zXV29Zv4JyrCZ+BxZ9q8pFhcXRxIxzNwHqgCce21tjX0jkItwn378K4UkJInMN+Hj4yMnmxLG8g3hN/s+h7YgE5vuJVOyPei9yQV+IfrsmePwf6fIoU0YNgIfodK4uro6YMbNk2fklCVlQ55Qxh9j3/GC6R/EPoJExyRGUvnoycOwMXYS4gYqzUAVgElRt0Gqn48sGPxUMEUgNlPzYT03yQC3HSX5wRdpt7e3Z4gUkuohW49AKDbfw9DINVbUi9jGWDch5JIEuWiSH3BncrNmavjIDgzqiKQ6+S4nvo+Rs9IEiNjBq3fmH5lEM46VG6krErkE3PU9YydbjPTt54lXLZ11OEdYkrZtQu3MTGqYdJoImbHxPPRtyHJgCzSh/PuqzZiSzQ849CW74XA4AKmvavYxqfKAc5nqcHwhIQJfyiPCxJfu7++PC0KY/fq8YOSnqxOjMnfo27P1K3WEBTKc8LbKVA02UBk477EOBC61TR2i5prk+vo6W15e/gGJ6UcNTNJeWVkZ39zcjNQcoB9i7Dc1y20x/Mx5kihVBrHyMMsZXD1UpqeuypTLgzzVxhge9iI+Y9LF1XYpU5oQIVUDS5k49ypR9kIxX9myluuUqVrmKqXDMGyRlIXYH9CVLdTtFanyZkIaEnUtqNaUM5RWg2qxukjQHhRUK+/4P/ALZUFTgsEtJGIAAAAASUVORK5CYII=",self.location),icon_02:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAGySURBVHgB7VXtbcJADHVCBoANsgERH79JJ2g36AjtKIxAN2CE6wAolwmaDZoFgD6ntmQFCjmC+oc86XTOOdjP9rtANGDAoyOiQGRZNo7juIBZHw6HJ+99TT0QUyCSJMmiKEqxMrapJ4IJoOrpOfvfCKDy/Jx9KxIKxPF45ParfTKC+Xz+hS3d7/eTLvq4pQOatGItsCjVt1wu2Zc2geP4tUu8IAKLxSLnHZVXWFu2IcSV+qGJ1BB96RIziIAmQHCPrbRnQmxlXs9sd+5CAJhKIofETsjk6tTxcIewjUej0fO1gCcihIi++cftcxaVSQB9+Qrv1i0h5lgsvLWsDd7ZGH+92+0mFwn8BRGVEijl2HMHuNX8UcJ5c4bufKD6dTuG+OkigTbD2Wz2hiQc7B1rLNXXErAhwEJkLfD15PGIv9Nn/qoGuBre+crJkTduFWJuxuMoAFcJSDVOn5HIGXJOyakWzHjuQ0CCbtWGGEtDrqJf0TUdsOO5KwEdgyTwLTc/N7cGJCoKRKdbcElUKkSxHQUi+L/gDEpLhgLRm4AKkVEUxScFojcBFqJU7mjAgAE34AeOX+UhhwPpxQAAAABJRU5ErkJggg==",self.location),icon_03:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAQDSURBVHgBzVe9UttAED6L0IuhIENj8QTI/My4i9yli9Oli3kCzBPYfgJIlw75CQhlKssdBWBRpoqooEMDFBl+831iz3NWJFlOirAzN7o77+1+t3+3Vuo/U2UWZs/z7Ovr66ZlWavPz882thz5KcY6xP7w+Pg4UDNQKQAbGxseFHSIoQR7jPFtfn6+d3R0FE1jLgRQr9ed+/v7fVMxgMSVSiXENMI8SoRUKo56sYaXEuFPA5ILYHNzs/X09LSLqS1bAdZdKD0LwzDOOeOCZ5TajgCikQfCytqEybsQxJvbvCXm3snJSWM0Gg3zlJPAt80vz2BsiYVoxRHBlQIA5W3x9wuDZbWpWE0hUdCSpX96euoDUIPByYtgPqBL0+cmXCA+pwltYzvg7bOUMitub29dyQi6y+GtoXxF87iua+MSA8QJAUaPj48104oTFoDygRKzY7S1HgpJK6ebbm5ursDHMweiPOatTT4qw95H7Q6A2TZ/HwNYW1trKclroO2C8UzlEM1tukmIQdqAwijNzz3I2xLZbfNCYwD44bMWBJP3oUAHVJgOPDF5QlDqwKwLdBMZVQ5JgeKwTSskACQ4PAGyJ0pcWdvr6+v7GAcYCciHh4dIC5ibm1spygyTIKsn09YEgLu7O0/WMZAeytyXryMHmtwDiCZNKtFNC3xQJQnAeYaFzKnVatUxANCqfAPNjEjuMZcFiK+rnnqJ9jGvRHcpEkslwOEGj983phB9KwOELwD0e8CId2Ruq78g6oA+nncSIGUPGkFEIUy7lvzkq9lIx4udBaAwmIwgsgVIyIxR/0BWFqo8ohWQcitQzEepDRfV1OxkmzqTGGCA4XalAkoKjbaELsdtqRsUTll9gO1mndc6EIThGABIVz1PzUhQPtA1Q4gluYN0ZdfElK5CKZ/koTzJCS9SMtGZPEYsjSgoV4KwUbatYvkGP59tWnEPNSHAzVyW8gx21o4e+c0HK4kByc9ABHVUearKN4DAHcg51PVDHrTYqB+OBmsCNN8C7VePea7KkQ5e13zrWT94Q4wFfhm0+jcCQiAP/wCQyvP9rCc4TTB5X0DYfMqzGg4S3HtoLH3zxZyoA0C2JQIdHNpVU4iuA9gdWea2XgCauFV835sAZy4uLy/j5eXlH5h+wnAxd5eWlr5j/5fKoYuLixB8ZxBehxvf4vseZ/o8wxRdXFz8SnnSWzaowzyf2RWb0a2mdLWa4DJa7acsef6crlTSpuU1K7ltOQORnbH0/CRfCkyQdwbAR1JokrjgHss1W7Is5YUA9K2Q1x0IbRnbEUYoL+e57FUFaNNQTBCsDV+KGpZSf800ECpgh1TEW1bxTABMQifzTszsaDCilH4eFvWFr5J+A8YpsGXJDE8eAAAAAElFTkSuQmCC",self.location),icon_04:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAALZSURBVHgB7VfbcdpAFL3C4ZeQDuQKIh4f/FmuwHQQUgGkAkQFhg5wB3YFJv+8UgHqAGXgCwbIOczuWJFXQiBI/MGZ0exKe3fv2fvalcgVV2SDJSegWq26aB62223dsqwi+kU9ttvtpvg2RfdlNBo9y5FITch13eJyuWxBYTNCwAcBX/Vt9O3QNH73QOxJUuIdoXK53DAKWlYbja1eAyjn7mmJ3xHRz5B10NZDxH3Id0zrjsfjfiKhSqWyk38IWO8vDp9MQnQDmr5cELBiQ94sLomEGBNg3pELAp5wUxM6YfFXNC6yzp1MJj8lA3KSEcw+uHgfvCqYMyETIdQjb7FYzDURtF1Ya4anLicijpBLZZIAKoVl2vpdJQJho/81aS7WbqFxUhHCYt+5OJWhJrUkBhi/C/VbqCe3m83mFq8N9DsJZDzIP+LhPC86bqzUjuPYNzc3E3SLUPJlOp0GURmQbdJF6tWXFBW5VqvZ6/V6xg0jAe6xrh+VMbqMgpi0V5bL5ZomGSz4FHYTnr6Kn28Sg9VqtR/DRjwTmVhCWqGabIuZdEA30exRYnGu1sEPq/+SGMQSyufz+hwqSgIYL5pYSHE7RjxQ43M5lhB2sd8NrxNxMgjQR8YF+4VCoRcaMm5CW1IdG+kJUYnepXZdFIiVLrOLQQoXzVCPZiHFfdMcrNVThFp6IwcJMS2hhBnGehIbfBibh3Zsy5tVBlBsTHvGHeb8oCx0vJoSwHj9AOhrD7HRkwMolUp3yMQ+NwBi9eFw+HJoDizaZqaxH71+GF3GK2gaMoQ6TH32EXdBmjmqcA5MY2c57bHLezkTMp/258aHIxR3hbUZeHJZ2KaPcVdYCnvyH/COEDKlYRJEanv6XGNZQP+ZVRw1J4jIsR7tf4PUT6So092TFDjqz1X9s7XDB27Sj6KqZ11WaNMVJjMhDdyXHmAJXlOd8D1aEfDxDPgjmfXCf8UVp+APG5qQ4n/MB/YAAAAASUVORK5CYII=",self.location),icon_05:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAJBSURBVHgB7ZfbUQIxFIYPsDxLCWsHO8ADj1iBdoBUoFQAViBWIFagHbC+McNt7WDtYH1luPifMXFCICTswvCy/8ySEJLDx8nthyhXrmwqUEo1Gg1/sVjcFgqFAG8r/KAebTabqFwuf45Go5hS6Giger3exJd2UW1auobFYrEzHo8jOhdQrVbro3iQ7wGWcFZQjUWTTxooPn+aTCY9OjUQYAYoWhIERX+9Xr9EUZSo/YIgqCAzd6h2AeOL5sF0Om27fI8TEKbpERDPAiYGyA1A4kNjAOYDbCihMK4zm836lBWIf3GpVJqj6rvCGKCS1Wp1rWdUV9EWVKTf5zoC91xhWNwX4+VU8VS2bGOsQIC45ZKzg3XwRkcKCzpEkYhYga2/FQggvqh+UEohxkCUTVtfKxD9HXqsmNIrdu3o6Q3VavVBgeA0V0SZUHr9yFiI31Vi7yyDLSBxClu3Zgbx9dJTG7ATv7D4/0/zQ1MWiufUCkmJC8Ar9UPPNAipvOEKTugh2e8tZxgZF1M337frXBb1WWRakxcDMikHsikHssm07QNs91dZ5xflTsuinbi6TBni6+JePPLqeGRvRNm0E1fXVoaWy2UEMxbqnZAdPvIDz/P4HurQkYKpC/fFhRLE/lYbnCys8Ml8svqqaUf63xFwK/Vs+uEM2zZnaJKzydftKBs2k+HiCxr++ehMspx3GdtR9tPCbFUOuT9pWc4KJKHwy9ts1gHWM/Xjf6+UUqn/SrNwY98jGy0NJgT0E+XKdSH9Appd+5pWT91vAAAAAElFTkSuQmCC",self.location),icon_06:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHPSURBVHgB5ZbLbcJAEIYXY3F2KohLMC+JG0sFoQQ6IFQQp4IkFUR0QCqIc+PAKxXgdGCJGyDIP6uxZSwS7zrBh+SX7PGud/bz7GPWQvx1VUQBtVotCXNDz7ZtP02n01DX1xgI2O3xeHw46aRS6c1ms0DH3xKGAmyIKzwcDhJ2xHV3uv7GQMhFROFyuXxbLBaPgK1Q5+k6GwHb7bbqmCFKgEcwjm4fRkAM45AhK1FQ2sBOp+PCDGj+5vP5OK5HmaKLdPvRAkopnd1u90rPiM5PfwTKNMzaEdt5DajTzWZDMBfR+FgoSXTb7VYCqJ4bjcbZlVqr1cbpfZrsQzgMYK7TjS3LcgCheodh9+n3zWaTPkSKHNH2oRWdAD3Pc6vV6vqLxiGiGGHeJtl39Xq9K3KEj1Z+8L86BwywEn1uGFAZDXviB8IoPMMM0K+LvfuRnUO1obmh+CW5dCMY2SKZxlReOlFcFMiZyaFUWAqQ5o0sIgxKAQLUZVvOkHIWIuB7KUDBCwaKLg7k3xBK7EG6/mJALBj1z8MJJFF24zt8DAmcDuroicsm2u/3lOj7fJS9fAfsA9SPCzTpKK9FcfnZipPTAoDcZKwjRBbhmsRp8n/pE7014YnlKSRiAAAAAElFTkSuQmCC",self.location),icon_06:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHPSURBVHgB5ZbLbcJAEIYXY3F2KohLMC+JG0sFoQQ6IFQQp4IkFUR0QCqIc+PAKxXgdGCJGyDIP6uxZSwS7zrBh+SX7PGud/bz7GPWQvx1VUQBtVotCXNDz7ZtP02n01DX1xgI2O3xeHw46aRS6c1ms0DH3xKGAmyIKzwcDhJ2xHV3uv7GQMhFROFyuXxbLBaPgK1Q5+k6GwHb7bbqmCFKgEcwjm4fRkAM45AhK1FQ2sBOp+PCDGj+5vP5OK5HmaKLdPvRAkopnd1u90rPiM5PfwTKNMzaEdt5DajTzWZDMBfR+FgoSXTb7VYCqJ4bjcbZlVqr1cbpfZrsQzgMYK7TjS3LcgCheodh9+n3zWaTPkSKHNH2oRWdAD3Pc6vV6vqLxiGiGGHeJtl39Xq9K3KEj1Z+8L86BwywEn1uGFAZDXviB8IoPMMM0K+LvfuRnUO1obmh+CW5dCMY2SKZxlReOlFcFMiZyaFUWAqQ5o0sIgxKAQLUZVvOkHIWIuB7KUDBCwaKLg7k3xBK7EG6/mJALBj1z8MJJFF24zt8DAmcDuroicsm2u/3lOj7fJS9fAfsA9SPCzTpKK9FcfnZipPTAoDcZKwjRBbhmsRp8n/pE7014YnlKSRiAAAAAElFTkSuQmCC",self.location),icon_07:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAG1SURBVHgB3ZZbboJAFIYPBH12Ceyg4y3xrXQF2h3gCmpXUF1B4wrUHXQHtG8mRp0l0B2Y+OiF/oeCoZRSKANp/BMyFw7zzcw5zByiiqUldXY6Het8Pt9STtXr9cVyuXQpD7DVatmaps3ob3JrtdpdGtSIdwDmr8zzPIm6pGyyg9I8HA6OEKIppdxlAkb0sl6vJ5RB7XbbDia5wyRNXdcdNJtJtjopFGBDFAwVmMSsdCACbQvYfdC0EXzjUoHYyjm29QlV339cRxCOygCGAWIFTyN8gRX3o4YGKdDpdOIA+fLfYrUCsFHcVgkQv4CLwo32wX/v2NJvQKU+zKLrAcJ/u0qBq9VKwodDBNRjJUDLshq4PV4RULJ0YK/XM/f7/RYH+bbb7YpSgQzjGwNVE08Dx50ThSoFRmHw35h9GIcqA8Zhm81mwj6kzwOBoTdsp+SkSYIl9C3YtvAKM8IuF3khYF5YYeDxeDSDgedZYKwffYjrZYA0wUx6x8eWYRhTAMM25zUDwJ7TYL5tvINzUnzg0C/iQTGpt7htGiwRyEKaJzBYn1KEMJ9yCbuHy2Ca5iLTW9B/0gfsUwtSRckrKAAAAABJRU5ErkJggg==",self.location),icon_08:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHuSURBVHgB7VbLbcJAEB3M5wZyCU4FMT+JW5YO0kGgAqADqCBKBYEKAhXE3DjwSwVxCY7ghATOm8hWbLM2GNmCA09axp5Zz7zdfbsL0R1XRiboaDQa2m63e6EUUCgURrPZzPT6csFOKN7JZDJdSgHIzWYQSQDFVba2bVt4XlMy0NFUWSAX9gWKjxeLRZsSQLVa/YQRsphCV4ZPhI4APzB6HUvA0z+mBIB8LRgNzcjn822vEH1LwOrn4s5HbHVKFsLZYQMpARcJC9CFVIhSAlwcAmxSgggT4tVFeHMELP6BBjRKHpq3hpRANpudsoUGNKzZ0X3A2xT+73q9rstilUplxTYYg7/lEjgcDhNv7Ogy8ooFM9ErlUpDwzCsWq0m8P7uJDLR+sVi8S/ZZrPh/q9uDANoz+dzQwihbrfbLr7rwK/CDpfLZfsUAS7SohQAAn0Q8F1GSkRnS+I29vv9M49EFsP0CrbBPGgmheDkZVQul5+cRF/r9dolNdF1vYc+j/D/4N30xJqIqd4YtMXL04pFwMVqtZrK/E7Bs2JYVgrDSQJB8C6g/y0VhIlZe6AYuOQg0i6MSRF7BlxgpL4dhJmx6QLc74KoJRDOqShFWCzEr59NwPkzwo8aRYtKxPSfRwCn2ZuiKBalAOQe0R23hl9LadSDrYWNIQAAAABJRU5ErkJggg==",self.location),icon_check:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAJvSURBVHgBrZU9bNNQEMfvvZiWAbVBICFlKNmYgHYoHxKi2RnaOQNqxzJA2OiCY5Z2o7AwFgaQurVI3VNYkBgSlKkSqFaHCCSQ3KBWbRK/652TuP6Kk7j9LX5+9/y/e3fn9wTEkC7radWSBQEwAwKyiJDtmCyaq9D7RsNWm0f3DbOXhughnMWWXEOAHAwAIr5v2mhEOZLBibHvrwoUdXlQcSdKIeZHNFke+6YXQja/uFGkhw5nw6hP68WQA46cNvsazgNbPa/fM1ZdB5xzTgsP4XywGi01xTVxaqDslJ5UPH/1NlRvPXWeHtIXNLnGA9GJfhcSMDGahq0bj2FipB1b/uc6bFk7rl1q6rJUTTkLCVnKzLjie8cWVA9/++yqJZ5JIWAOEsDR56+cpmW5tg17jX3fGgSRk9Tvk5AATk2Xj38r8Onfj9AaQX8+F3no4npTw6xQ9JGItoOh4NQsXrvrvi/XSqHUeOEamL2EuP2WMg998xz9eOqiM+bCrtS+QAyWphBM+tuyQUs3DS8yOerxSaeIjLew+V/rceJUZKxoArBEycp5DRzhg0vXT3dDjt5lZ2HfPnLnuLDVwz+xDgSKDSk1fBM0sNDN6ltY3N100uB1zLRTsw394LtCWlOGRa1ailrArfdo54MTrZeong/Cd4R7FqU0tUAPK2ohCz0xP7u7+frfjOz5ABZfQDwY+rgeT41SCo9j19gIhYM7upN69z+oT79cVYBF6EM/cdboijOhO7mzkyTHt0WR+8QjHTDOEc53BOI8DAD1e6nZwoWoS1/Efeg4ouMcBc4JEHwopjtfmdQlJnefRm3OndhL4wQwpxpFSeTnlQAAAABJRU5ErkJggg==",self.location),icon_close:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAIwSURBVHgBtZa9TsMwEMfPbvgWUmGhTC0DO0xQBujOAG8AzCBRZgbagRl4AvoIMLBHDHxM8AYUFioGiIT4UNXmuDNNcR0njSj8pUpOffnd2efzRUCM7gqFtKzXixJxCQFy8P1jeQLgFnz/pCHE6dTVVTWKIazg+fmcI8QxQQuQRIiVJkDZ5kiaf9zn88WUEDeJ4SpMsc7v3M/NFUNTHfCFhZJA3IMehEKUsxcXpZADjpweDuAPhL6/k72+Pmw74D3nJdIwrRs6k5PQeHyMhUXYeE3EWc6JykFKyj0T3j89DZlKBcZ3dyPhPDdBNn1kayhNAR+rFbSivzOjYrgYHVXPb2dn8Ly/H4KPLC+rsf/6CrW1NWjWah02zYGBMekgrpjuecnv5+ftZwbpK9HhrA+yNeEq0M/PbQekXAWLgogDkA7Ux7bVBaITVRAP+fwLGPuvy4xWVxy8JZXkdJwFAxj0CzgrJ+GfxQ6qcQZRW2QmPkJerAMTztuib1dXJ4i3Dt09Lmc7CVzfc/N02fJB7BPZGBw8Mie40IYXFyPhZuKHyDaVyYQccK+QU67r0djtmKBCe9raAqQKjTotgROu4qfNzXChUY/guyj2suOobBWawKbzsuMBNZiyadUNHmVD13Up6G7tOsheXh4iYgl6FDOoF7TzGurJrcYTur4TyOPIdbjVAUvlhHsE4jokER11avobtqYv4t5TXxd0nVOdrFJjn4GfVVUJynlzfTrmrZNo1ReQziL84EZxNAAAAABJRU5ErkJggg==",self.location)};o.loading({duration:0,message:e("loading"),forbidClick:!0}),u().then((n=>{K.value=n.mallLevel,D.value=n.childNum||0,F.value=n.teamNum||0}));const P=s(100),k=s(100);return c("valid_recharge_amount_for_team_num,valid_recharge_amount_for_seller_upgrade").then((n=>{P.value=n.valid_recharge_amount_for_seller_upgrade,k.value=n.valid_recharge_amount_for_team_num})),a().then((n=>{const e=n.result||[];if(e.length){const n=[100,500,700,1e3,1500,2e3];Y.value.forEach((i=>{e.forEach(((e,t)=>{i.name===e.level&&(i.rechargeAmountCnd=e.rechargeAmountCnd,i.popularizeUserCountCnd=e.popularizeUserCountCnd,i.awardView=e.profitRationMin||e.profitRationMax?`${new fe(e.profitRationMin).mul(100)}%~${new fe(e.profitRationMax).mul(100)}%`:"-",i.promoteViewDaily=e.promoteViewDaily,i.deliveryDays=e.deliveryDays,i.upgradeCash=T.value?n[t]:Number(e.upgradeCash),i.sellerDiscount=e.sellerDiscount?100*Number(e.sellerDiscount)+"%":0,i.hasExclusiveService=Boolean(e.hasExclusiveService),i.recommendAtFirstPage=Boolean(e.recommendAtFirstPage),i.teamNum=e.teamNum||0)}))}))}o.clear()})).catch((()=>{o.clear()})),(n,i)=>{const t=l("fx-header");return C(),A("div",de,[f(t,{fixed:!0},{title:d((()=>[m(v(w(e)("卖家等级")),1)])),_:1}),he,h("div",{style:p({"background-image":"url("+w(V)+")"}),class:"seller-banner"},[g(" <h2 v-html=\"$t('升级销量扶持')\"></h2>\n      <h2 v-html=\"$t('轻松月入过万')\"></h2> ")],4),h("div",ge,[h("div",pe,[h("h2",{innerHTML:n.$t("升级销量扶持")},null,8,we),h("h2",{innerHTML:n.$t("轻松月入过万")},null,8,me)]),h("div",ve,[D.value?(C(),A("p",Ne,[m(v(w(e)("当前分店人数"))+"：",1),h("span",null,v(D.value),1)])):g("v-if",!0),F.value?(C(),A("p",Ue,[m(v(w(e)("当前团队人数"))+"：",1),h("span",null,v(F.value),1)])):g("v-if",!0)]),(C(!0),A(N,null,U(J.value,((i,t)=>(C(),A("div",{key:t,class:"intro-item"},[h("h2",null,v(t+1)+"."+v(n.$t(i.title)),1),(C(!0),A(N,null,U(i.data,(i=>(C(),A("div",{key:i},[i.title?(C(),A("h2",Ce,v(n.$t(i.title)),1)):g("v-if",!0),i.info.indexOf("money")>-1?(C(),A("p",{key:1,innerHTML:w(e)(i.info,{money:P.value})},null,8,Be)):i.info.indexOf("totalMoney")>-1?(C(),A("p",{key:2,innerHTML:w(e)(i.info,{totalMoney:k.value})},null,8,xe)):(C(),A("p",Qe,v(n.$t(i.info)),1))])))),128))])))),128)),h("div",Re,[(C(!0),A(N,null,U(Y.value,(e=>(C(),A("div",{key:e.name,class:B([e.name,"level-item"])},[h("div",{class:B(["top-content",{"is-ar":w(S)}])},[h("div",Ee,[h("div",be,[m(v(e.name)+"-"+v(n.$t("等级卖家"))+" ",1),K.value===e.name?(C(),A("span",ye,v(n.$t("当前的")),1)):g("v-if",!0)]),w(M)?g("v-if",!0):(C(),A("p",Me,v(n.$t("运行资金"))+"："+v(e.rechargeAmountCnd),1)),h("p",null,v(n.$t("分店数"))+"："+v(e.popularizeUserCountCnd),1),e.teamNum?(C(),A("p",Se,v(n.$t("团队人数"))+"："+v(e.teamNum),1)):g("v-if",!0)]),h("img",{src:e.icon,alt:""},null,8,De)],2),h("div",{class:B(["rights-content",{"is-ar":w(S)}])},[h("div",Fe,[h("div",Je,[h("img",{src:H.icon_01,alt:""},null,8,Ke),h("p",null,v(n.$t("销售利润比例"))+"：",1)]),h("p",null,v(e.awardView),1)]),h("div",Ie,[h("div",Ye,[h("img",{src:H.icon_02,alt:""},null,8,Te),h("p",null,v(n.$t("平台流量扶持量（每日）"))+"：",1)]),h("p",null,v(e.promoteViewDaily),1)]),h("div",Ve,[h("div",He,[h("img",{src:H.icon_03,alt:""},null,8,Pe),h("p",null,v(n.$t("全球到货时间"))+"：",1)]),h("p",null,v(e.deliveryDays)+" "+v(n.$t("days")),1)]),h("div",ke,[h("div",Oe,[h("img",{src:H.icon_07,alt:""},null,8,Xe),h("p",null,v(n.$t("采购优惠"))+"：",1)]),h("p",null,v(e.sellerDiscount),1)]),w(R)?g("v-if",!0):(C(),A("div",Ze,[h("div",Le,[h("img",{src:H.icon_04,alt:""},null,8,ze),h("p",null,v(n.$t("升级礼金"))+"：",1)]),e.upgradeCash?(C(),A("p",We," $"+v(w(x)(e.upgradeCash)),1)):(C(),A("img",{key:1,src:H.icon_close,alt:""},null,8,Ge))])),h("div",qe,[h("div",je,[h("img",{src:H.icon_05,alt:""},null,8,_e),h("p",null,v(n.$t("专属客服"))+"：",1)]),h("img",{src:e.hasExclusiveService?H.icon_check:H.icon_close,alt:""},null,8,$e)]),h("div",ni,[h("div",ei,[h("img",{src:H.icon_06,alt:""},null,8,ii),h("p",null,v(n.$t("首页推荐"))+"：",1)]),h("img",{src:e.recommendAtFirstPage?H.icon_check:H.icon_close,alt:""},null,8,ti)]),"SS"===e.name&&["familyShop"].includes(w(Q))?(C(),A("div",ri,[h("div",si,[h("img",{src:H.icon_08,alt:""},null,8,oi),h("p",null,v(n.$t("成为供货商"))+"：",1)]),h("img",{src:H.icon_check,alt:""},null,8,ui)])):g("v-if",!0)],2)],2)))),128))])])])}}}),[["__scopeId","data-v-c2b30052"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/sellerLevel/index.vue"]]);export{ai as default};
