import{_ as e,Y as a,r as t,q as l,J as s,av as u,c as r,e as i,a as v,t as o,x as n,h as p,v as c,f,F as d,w as y,o as h}from"./index-3d21abf8.js";import{B as m}from"./index-2406f514.js";import{a as g}from"./login.api-cb7fcde3.js";import"./use-route-cd41a893.js";const x={class:"verify"},$={class:"content"},k={class:"title textColor"},b={key:0},V={class:"label textColor"},C={key:1},_={key:2},q={class:"iptbox inputBackground"},w=["placeholder"],j=e({__name:"safeVerify",setup(e){const j=a(),I=t(""),T=t(""),B=t(""),L=t(""),P=t(!1),S=t(null),U=t(0);l((()=>{let e=j.query.type;T.value=e,B.value=j.query.account,L.value=j.query.username,3!=T.value&&(F(),E())}));const D=()=>{6==I.value.length?P.value=!0:P.value=!1},E=()=>{if(U.value>0)return!1;g({target:B.value}).then((e=>{U.value=30,S.value=setInterval((()=>{U.value>0?U.value=U.value-1:(U.value=0,F())}),1e3)}))},F=()=>{clearInterval(S.value),S.value=null};return s((()=>{F()})),(e,a)=>{const t=u("fx-header"),l=m;return h(),r("div",x,[i(t),v("div",$,[v("div",k,o(e.$t("safeVertify")),1),3==T.value?(h(),r("p",b,o(e.$t("verifyGoogleTips")),1)):n("v-if",!0),v("span",V,o(2==T.value?e.$t("emailVerify"):1==T.value?e.$t("phoneVerify"):e.$t("googleVerify")),1),2==T.value?(h(),r("p",C,o(e.$t("verifyEmailTips",{account:B.value})),1)):n("v-if",!0),1==T.value?(h(),r("p",_,o(e.$t("verifyPhoneTips",{account:B.value})),1)):n("v-if",!0),v("div",q,[p(v("input",{type:"text",class:"inputBackground textColor",placeholder:e.$t("entryVerifyCode"),"onUpdate:modelValue":a[0]||(a[0]=e=>I.value=e),onInput:D},null,40,w),[[c,I.value]]),3!=T.value?(h(),r("span",{key:0,onClick:E},[f(o(e.$t("reSendVerifyCode")),1),U.value?(h(),r(d,{key:0},[f(" ("+o(U.value)+")s",1)],64)):n("v-if",!0)])):n("v-if",!0)]),i(l,{class:"w-full",disabled:!P.value,style:{"margin-top":"90px"},type:"primary",onClick:a[1]||(a[1]=a=>e.$router.push({name:"resetPassword",query:{type:T.value,account:B.value,verifycode:I.value,username:L.value}}))},{default:y((()=>[f(o(e.$t("nextStep")),1)])),_:1},8,["disabled"]),n(" <button :disabled=\"!hightLight\" class=\"btn\"\r\n                @click=\"$router.push({ name: 'resetPassword', query: { type: currentType, account, verifycode, username } })\"\r\n                :class=\"hightLight ? 'hightLight' : ''\">{{ $t('nextStep')\r\n                }}</button> ")])])}}},[["__scopeId","data-v-100d5362"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/forget/safeVerify.vue"]]);export{j as default};
