import{_ as e,u as a,r as s,q as n,av as l,c as t,e as o,w as r,a as i,o as c,f as m,t as u}from"./index-3d21abf8.js";import{b as v}from"./login.api-cb7fcde3.js";const d={class:"CommonProblem"},f={class:"CommonProblem-padding"},p=["innerHTML"],_=e({__name:"index",setup(e){const{locale:_}=a(),x=s("");n((()=>{b()}));const b=()=>{v({content_code:"020",language:"en-US"===_.value?"en":_.value}).then((e=>{x.value=e?e.content:""}))};return(e,a)=>{const s=l("fx-header");return c(),t("div",d,[o(s,null,{title:r((()=>[m(u(e.$t("termsOfService")),1)])),_:1}),i("div",f,[i("p",{innerHTML:x.value},null,8,p)])])}}},[["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/termsOfService/index.vue"]]);export{_ as default};
