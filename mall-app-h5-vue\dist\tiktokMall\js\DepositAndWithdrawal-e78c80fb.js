import{_ as e,Y as a,l as s,r as t,av as l,c as i,e as d,w as o,a as n,b as u,aV as r,F as c,y as v,o as f,f as p,n as m,t as x,D as h,E as b}from"./index-3d21abf8.js";import{P as g}from"./index-fc51b7d2.js";import{L as j}from"./index-40e83579.js";import{T as _,a as w}from"./index-222e676a.js";/* empty css              */import"./use-id-a0619e01.js";import"./use-route-cd41a893.js";import"./index-0d6a7179.js";import"./use-refs-b86d6bcd.js";const y=e=>(h("data-v-287180ca"),e=e(),b(),e),k={class:"Record pb-12"},D={class:"tab-fixed"},R={class:"list-wrap"},U={class:"tab-wrap flex px-4 mt-5"},V=[y((()=>n("div",{class:"flex justify-between"},[n("div",{class:"type"},"USD"),n("div",{class:"money"},"2000")],-1))),y((()=>n("div",{class:"flex mt-1 justify-between"},[n("div",{class:"time"},"2022-03-11 17:55:12"),n("div",{class:"status flex"},[n("div",{class:"status-icon status-icon-color2 mr-2"}),p(" 成功 ")])],-1)))],C=e({__name:"DepositAndWithdrawal",setup(e){a();const h=s();let b=t(0),y=t(0);const C=t([]),T=t(!1),$=t(!1),A=["充值","提现"],L=["外汇货币","数字货币"],W=()=>{},E=()=>{setTimeout((()=>{for(let e=0;e<10;e++)C.value.push(C.value.length+1);T.value=!1,C.value.length>=40&&($.value=!0)}),1e3)},F=()=>{h.push("RecordDetails")};return(e,a)=>{const s=l("fx-header"),t=_,h=w,I=j,P=g;return f(),i("div",k,[d(s,{fixed:""},{title:o((()=>[p("充提记录")])),_:1}),n("div",D,[d(h,{active:u(b),"onUpdate:active":a[0]||(a[0]=e=>r(b)?b.value=e:b=e),onClickTab:W},{default:o((()=>[(f(),i(c,null,v(A,((e,a)=>d(t,{key:a,title:e},null,8,["title"]))),64))])),_:1},8,["active"])]),n("div",R,[n("div",U,[(f(),i(c,null,v(L,((e,a)=>n("div",{class:m(["tab-item mr-4",[u(y)===a?"active":""]]),key:a},x(e),3))),64))]),d(P,{"loading-text":e.$t("加载中"),"loosing-text":e.$t("释放以刷新"),"pulling-text":e.$t("下拉以刷新"),modelValue:T.value,"onUpdate:modelValue":a[2]||(a[2]=e=>T.value=e),onRefresh:e.onRefresh},{default:o((()=>[d(I,{loading:T.value,"onUpdate:loading":a[1]||(a[1]=e=>T.value=e),finished:$.value,"finished-text":"没有更多了",onLoad:E},{default:o((()=>[n("ul",null,[(f(!0),i(c,null,v(C.value,(a=>(f(),i("li",{class:"px-4 mt-10",key:e.index,onClick:F},V)))),128))])])),_:1},8,["loading","finished"])])),_:1},8,["loading-text","loosing-text","pulling-text","modelValue","onRefresh"])])])}}},[["__scopeId","data-v-287180ca"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/Record/DepositAndWithdrawal.vue"]]);export{C as default};
