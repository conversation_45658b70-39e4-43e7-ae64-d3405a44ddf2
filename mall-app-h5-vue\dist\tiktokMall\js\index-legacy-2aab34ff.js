System.register(["./index-legacy-46a00900.js","./use-id-legacy-df76950f.js","./use-route-legacy-be86ac1c.js","./index-legacy-73ef4548.js","./index-legacy-15165887.js","./use-refs-legacy-0eab8d10.js"],(function(e,t){"use strict";var a,n,o,i,r,l,s,d,c,v,b,u,p,f,h,g,x,w,m,y,k,_,I,z,B,C,S,$,R,T,j,A,L,O,Z,N,W,H,M,F,X,D,E,P,V,q,Q,U,Y,G,J,K,ee,te,ae,ne,oe,ie=document.createElement("style");return ie.textContent=":root{--van-sticky-z-index: 99 }.van-sticky--fixed{position:fixed;z-index:var(--van-sticky-z-index)}:root{--van-tab-text-color: var(--van-gray-7);--van-tab-active-text-color: var(--van-text-color);--van-tab-disabled-text-color: var(--van-text-color-3);--van-tab-font-size: var(--van-font-size-md);--van-tab-line-height: var(--van-line-height-md);--van-tabs-default-color: var(--van-danger-color);--van-tabs-line-height: 44px;--van-tabs-card-height: 30px;--van-tabs-nav-background-color: var(--van-background-color-light);--van-tabs-bottom-bar-width: 40px;--van-tabs-bottom-bar-height: 3px;--van-tabs-bottom-bar-color: var(--van-danger-color) }.van-tab{position:relative;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-flex:1;-webkit-flex:1 1;flex:1 1;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;box-sizing:border-box;padding:0 var(--van-padding-base);color:var(--van-tab-text-color);font-size:var(--van-tab-font-size);line-height:var(--van-tab-line-height);cursor:pointer}.van-tab--active{color:var(--van-tab-active-text-color);font-weight:var(--van-font-weight-bold)}.van-tab--disabled{color:var(--van-tab-disabled-text-color);cursor:not-allowed}.van-tab--grow{-webkit-box-flex:1;-webkit-flex:1 0 auto;flex:1 0 auto;padding:0 var(--van-padding-sm)}.van-tab--shrink{-webkit-box-flex:0;-webkit-flex:none;flex:none;padding:0 var(--van-padding-xs)}.van-tab--card{color:var(--van-tabs-default-color);border-right:var(--van-border-width-base) solid var(--van-tabs-default-color)}.van-tab--card:last-child{border-right:none}.van-tab--card.van-tab--active{color:var(--van-white);background-color:var(--van-tabs-default-color)}.van-tab--card--disabled{color:var(--van-tab-disabled-text-color)}.van-tab__text--ellipsis{display:-webkit-box;overflow:hidden;-webkit-line-clamp:1;-webkit-box-orient:vertical}.van-tabs{position:relative}.van-tabs__wrap{overflow:hidden}.van-tabs__wrap--page-top{position:fixed}.van-tabs__wrap--content-bottom{top:auto;bottom:0}.van-tabs__nav{position:relative;display:-webkit-box;display:-webkit-flex;display:flex;background:var(--van-tabs-nav-background-color);-webkit-user-select:none;-moz-user-select:none;user-select:none}.van-tabs__nav--complete{overflow-x:auto;overflow-y:hidden;-webkit-overflow-scrolling:touch}.van-tabs__nav--complete::-webkit-scrollbar{display:none}.van-tabs__nav--line{box-sizing:content-box;height:100%;padding-bottom:15px}.van-tabs__nav--line.van-tabs__nav--shrink,.van-tabs__nav--line.van-tabs__nav--complete{padding-right:var(--van-padding-xs);padding-left:var(--van-padding-xs)}.van-tabs__nav--card{box-sizing:border-box;height:var(--van-tabs-card-height);margin:0 var(--van-padding-md);border:var(--van-border-width-base) solid var(--van-tabs-default-color);border-radius:var(--van-border-radius-sm)}.van-tabs__nav--card.van-tabs__nav--shrink{display:-webkit-inline-box;display:-webkit-inline-flex;display:inline-flex}.van-tabs__line{position:absolute;bottom:15px;left:0;z-index:1;width:var(--van-tabs-bottom-bar-width);height:var(--van-tabs-bottom-bar-height);background:var(--van-tabs-bottom-bar-color);border-radius:var(--van-tabs-bottom-bar-height)}.van-tabs__track{position:relative;display:-webkit-box;display:-webkit-flex;display:flex;width:100%;height:100%;will-change:left}.van-tabs__content--animated{overflow:hidden}.van-tabs--line .van-tabs__wrap{height:var(--van-tabs-line-height)}.van-tabs--card>.van-tabs__wrap{height:var(--van-tabs-card-height)}.van-tab__panel,.van-tab__panel-wrapper{-webkit-flex-shrink:0;flex-shrink:0;box-sizing:border-box;width:100%}.van-tab__panel-wrapper--inactive{height:0;overflow:visible}\n",document.head.appendChild(ie),{setters:[e=>{a=e.aU,n=e.aD,o=e.b8,i=e.b9,r=e.aM,l=e.aN,s=e.aR,d=e.P,c=e.d,v=e.r,b=e.aB,u=e.p,p=e.m,f=e.ba,h=e.Q,g=e.a$,x=e.g,w=e.ac,m=e.e,y=e.S,k=e.R,_=e.a4,I=e.aO,z=e.aT,B=e.X,C=e.ai,S=e.ad,$=e.bb,R=e.bc,T=e.q,j=e.a9,A=e.Z,L=e.aH,O=e.aI,Z=e.s,N=e.at,W=e.aL,H=e.a8,M=e.bd,F=e.a0,X=e.be,D=e.ag,E=e.aY,P=e.bf,V=e.a5,q=e.a6,Q=e.aP,U=e.aa,Y=e.h,G=e.aS},e=>{J=e.u},e=>{K=e.a,ee=e.r},e=>{te=e.T},e=>{ae=e.a,ne=e.S},e=>{oe=e.u}],execute:function(){const[t,ie]=d("sticky"),re={zIndex:y,position:k("top"),container:Object,offsetTop:_(0),offsetBottom:_(0)};var le=c({name:t,props:re,emits:["scroll","change"],setup(e,{emit:t,slots:a}){const o=v(),d=b(o),c=u({fixed:!1,width:0,height:0,transform:0}),y=p((()=>f("top"===e.position?e.offsetTop:e.offsetBottom))),k=p((()=>{const{fixed:e,height:t,width:a}=c;if(e)return{width:`${a}px`,height:`${t}px`}})),_=p((()=>{if(!c.fixed)return;const t=h(g(e.zIndex),{width:`${c.width}px`,height:`${c.height}px`,[e.position]:`${y.value}px`});return c.transform&&(t.transform=`translate3d(0, ${c.transform}px, 0)`),t})),B=()=>{if(!o.value||I(o))return;const{container:a,position:i}=e,r=z(o),l=n(window);if(c.width=r.width,c.height=r.height,"top"===i)if(a){const e=z(a),t=e.bottom-y.value-c.height;c.fixed=y.value>r.top&&e.bottom>0,c.transform=t<0?t:0}else c.fixed=y.value>r.top;else{const{clientHeight:e}=document.documentElement;if(a){const t=z(a),n=e-t.top-y.value-c.height;c.fixed=e-y.value<r.bottom&&e>t.top,c.transform=n<0?-n:0}else c.fixed=e-y.value<r.bottom}(e=>{t("scroll",{scrollTop:e,isFixed:c.fixed})})(l)};return x((()=>c.fixed),(e=>t("change",e))),w("scroll",B,{target:d,passive:!0}),function(e,t){if(!i||!window.IntersectionObserver)return;const a=new IntersectionObserver((e=>{t(e[0].intersectionRatio>0)}),{root:document.body}),n=()=>{e.value&&a.unobserve(e.value)};r(n),l(n),s((()=>{e.value&&a.observe(e.value)}))}(o,B),()=>{var e;return m("div",{ref:o,style:k.value},[m("div",{class:ie({fixed:c.fixed}),style:_.value},[null==(e=a.default)?void 0:e.call(a)])])}}});const se=B(le),[de,ce]=d("tab");var ve=c({name:de,props:{id:String,dot:Boolean,type:String,color:String,title:String,badge:y,shrink:Boolean,isActive:Boolean,disabled:Boolean,controls:String,scrollable:Boolean,activeColor:String,inactiveColor:String,showZeroBadge:C},setup(e,{slots:t}){const a=p((()=>{const t={},{type:a,color:n,disabled:o,isActive:i,activeColor:r,inactiveColor:l}=e;n&&"card"===a&&(t.borderColor=n,o||(i?t.backgroundColor=n:t.color=n));const s=i?r:l;return s&&(t.color=s),t})),n=()=>{const a=m("span",{class:ce("text",{ellipsis:!e.scrollable})},[t.title?t.title():e.title]);return e.dot||S(e.badge)&&""!==e.badge?m($,{dot:e.dot,content:e.badge,showZero:e.showZeroBadge},{default:()=>[a]}):a};return()=>m("div",{id:e.id,role:"tab",class:[ce([e.type,{grow:e.scrollable&&!e.shrink,shrink:e.shrink,active:e.isActive,disabled:e.disabled}])],style:a.value,tabindex:e.disabled?void 0:e.isActive?0:-1,"aria-selected":e.isActive,"aria-disabled":e.disabled||void 0,"aria-controls":e.controls},[n()])}});const[be,ue]=d("tabs");var pe=c({name:be,props:{count:R(Number),inited:Boolean,animated:Boolean,duration:R(y),swipeable:Boolean,lazyRender:Boolean,currentIndex:R(Number)},emits:["change"],setup(e,{emit:t,slots:a}){const n=v(),o=e=>t("change",e),i=()=>{var t;const i=null==(t=a.default)?void 0:t.call(a);return e.animated||e.swipeable?m(ae,{ref:n,loop:!1,class:ue("track"),duration:1e3*+e.duration,touchable:e.swipeable,lazyRender:e.lazyRender,showIndicators:!1,onChange:o},{default:()=>[i]}):i},r=t=>{const a=n.value;a&&a.state.active!==t&&a.swipeTo(t,{immediate:!e.inited})};return x((()=>e.currentIndex),r),T((()=>{r(e.currentIndex)})),j({swipeRef:n}),()=>m("div",{class:ue("content",{animated:e.animated||e.swipeable})},[i()])}});const[fe,he]=d("tabs"),ge={type:k("line"),color:String,border:Boolean,sticky:Boolean,shrink:Boolean,active:_(0),duration:_(.3),animated:Boolean,ellipsis:C,swipeable:Boolean,scrollspy:Boolean,offsetTop:_(0),background:String,lazyRender:C,lineWidth:y,lineHeight:y,beforeChange:Function,swipeThreshold:_(5),titleActiveColor:String,titleInactiveColor:String},xe=Symbol(fe);var we=c({name:fe,props:ge,emits:["click","change","scroll","disabled","rendered","click-tab","update:active"],setup(e,{emit:t,slots:i}){var r,l;let d,c,h;null==(l=null==(r=A())?void 0:r.vnode)||l.props;const g=v(),y=v(),k=v(),_=v(),B=J(),C=b(g),[$,R]=oe(),{children:T,linkChildren:V}=L(xe),q=u({inited:!1,position:"",lineStyle:{},currentIndex:-1}),Q=p((()=>T.length>e.swipeThreshold||!e.ellipsis||e.shrink)),U=p((()=>({borderColor:e.color,background:e.background}))),Y=(e,t)=>{var a;return null!=(a=e.name)?a:t},G=p((()=>{const e=T[q.currentIndex];if(e)return Y(e,q.currentIndex)})),ee=p((()=>f(e.offsetTop))),te=p((()=>e.sticky?ee.value+d:0)),ae=t=>{const n=y.value,o=$.value;if(!(Q.value&&n&&o&&o[q.currentIndex]))return;const i=o[q.currentIndex].$el;!function(e,t,n){let o=0;const i=e.scrollLeft,r=0===n?1:Math.round(1e3*n/16);!function n(){e.scrollLeft+=(t-i)/r,++o<r&&a(n)}()}(n,i.offsetLeft-(n.offsetWidth-i.offsetWidth)/2,t?0:+e.duration)},ne=()=>{const t=q.inited;Z((()=>{const a=$.value;if(!a||!a[q.currentIndex]||"line"!==e.type||I(g.value))return;const n=a[q.currentIndex].$el,{lineWidth:o,lineHeight:i}=e,r=n.offsetLeft+n.offsetWidth/2,l={width:H(o),backgroundColor:e.color,transform:`translateX(${r}px) translateX(-50%)`};if(t&&(l.transitionDuration=`${e.duration}s`),S(i)){const e=H(i);l.height=e,l.borderRadius=e}q.lineStyle=l}))},ie=(a,n)=>{const o=(e=>{const t=e<q.currentIndex?-1:1;for(;e>=0&&e<T.length;){if(!T[e].disabled)return e;e+=t}})(a);if(!S(o))return;const i=T[o],r=Y(i,o),l=null!==q.currentIndex;q.currentIndex!==o&&(q.currentIndex=o,n||ae(),ne()),r!==e.active&&(t("update:active",r),l&&t("change",r,i.title)),h&&!e.scrollspy&&F(Math.ceil(X(g.value)-ee.value))},re=(e,t)=>{const a=T.find(((t,a)=>Y(t,a)===e)),n=a?T.indexOf(a):0;ie(n,t)},le=(t=!1)=>{if(e.scrollspy){const i=T[q.currentIndex].$el;if(i&&C.value){const r=X(i,C.value)-te.value;c=!0,function(e,t,i,r){let l=n(e);const s=l<t,d=0===i?1:Math.round(1e3*i/16),c=(t-l)/d;!function n(){l+=c,(s&&l>t||!s&&l<t)&&(l=t),o(e,l),s&&l<t||!s&&l>t?a(n):r&&a(r)}()}(C.value,r,t?0:+e.duration,(()=>{c=!1}))}}},de=e=>{h=e.isFixed,t("scroll",e)},ce=()=>T.map(((a,n)=>m(ve,D({key:a.id,id:`${B}-${n}`,ref:R(n),type:e.type,color:e.color,style:a.titleStyle,class:a.titleClass,shrink:e.shrink,isActive:n===q.currentIndex,controls:a.id,scrollable:Q.value,activeColor:e.titleActiveColor,inactiveColor:e.titleInactiveColor,onClick:o=>((a,n,o)=>{const{title:i,disabled:r}=T[n],l=Y(T[n],n);r?t("disabled",l,i):(P(e.beforeChange,{args:[l],done:()=>{ie(n),le()}}),t("click",l,i),K(a)),t("click-tab",{name:l,title:i,event:o,disabled:r})})(a,n,o)},E(a,["dot","badge","title","disabled","showZeroBadge"])),{title:a.$slots.title}))),be=()=>{if("line"===e.type&&T.length)return m("div",{class:he("line"),style:q.lineStyle},null)},ue=()=>{var t,a,n;const{type:o,border:r,sticky:l}=e,s=[m("div",{ref:l?void 0:k,class:[he("wrap"),{[M]:"line"===o&&r}]},[m("div",{ref:y,role:"tablist",class:he("nav",[o,{shrink:e.shrink,complete:Q.value}]),style:U.value,"aria-orientation":"horizontal"},[null==(t=i["nav-left"])?void 0:t.call(i),ce(),be(),null==(a=i["nav-right"])?void 0:a.call(i)])]),null==(n=i["nav-bottom"])?void 0:n.call(i)];return l?m("div",{ref:k},[s]):s};return x([()=>e.color,O],ne),x((()=>e.active),(e=>{e!==G.value&&re(e)})),x((()=>T.length),(()=>{q.inited&&(re(e.active),ne(),Z((()=>{ae(!0)})))})),j({resize:()=>{ne(),Z((()=>{var e,t;return null==(t=null==(e=_.value)?void 0:e.swipeRef.value)?void 0:t.resize()}))},scrollTo:e=>{Z((()=>{re(e),le(!0)}))}}),N(ne),W(ne),s((()=>{re(e.active,!0),Z((()=>{q.inited=!0,k.value&&(d=z(k.value).height),ae(!0)}))})),w("scroll",(()=>{if(e.scrollspy&&!c){const e=(()=>{for(let e=0;e<T.length;e++){const{top:t}=z(T[e].$el);if(t>te.value)return 0===e?0:e-1}return T.length-1})();ie(e)}}),{target:C,passive:!0}),V({id:B,props:e,setLine:ne,onRendered:(e,a)=>t("rendered",e,a),currentName:G,scrollIntoView:ae}),()=>m("div",{ref:g,class:he([e.type])},[e.sticky?m(se,{container:g.value,offsetTop:ee.value,onScroll:de},{default:()=>[ue()]}):ue(),m(pe,{ref:_,count:T.length,inited:q.inited,animated:e.animated,duration:e.duration,swipeable:e.swipeable,lazyRender:e.lazyRender,currentIndex:q.currentIndex,onChange:ie},{default:()=>{var e;return[null==(e=i.default)?void 0:e.call(i)]}})])}});const[me,ye]=d("tab"),ke=h({},ee,{dot:Boolean,name:y,badge:y,title:String,disabled:Boolean,titleClass:V,titleStyle:[String,Object],showZeroBadge:C});var _e=c({name:me,props:ke,setup(e,{slots:t}){const a=J(),n=v(!1),{parent:o,index:i}=q(xe);if(!o)return;const r=()=>{var t;return null!=(t=e.name)?t:i.value},l=p((()=>{const t=r()===o.currentName.value;return t&&!n.value&&(n.value=!0,o.props.lazyRender&&Z((()=>{o.onRendered(r(),e.title)}))),t})),s=v(!l.value);return x(l,(e=>{e?s.value=!1:Q((()=>{s.value=!0}))})),x((()=>e.title),(()=>{o.setLine(),o.scrollIntoView()})),U(te,l),()=>{var e;const r=`${o.id}-${i.value}`,{animated:d,swipeable:c,scrollspy:v,lazyRender:b}=o.props;if(!t.default&&!d)return;const u=v||l.value;if(d||c)return m(ne,{id:a,role:"tabpanel",class:ye("panel-wrapper",{inactive:s.value}),tabindex:l.value?0:-1,"aria-hidden":!l.value,"aria-labelledby":r},{default:()=>{var e;return[m("div",{class:ye("panel")},[null==(e=t.default)?void 0:e.call(t)])]}});const p=n.value||v||!b?null==(e=t.default)?void 0:e.call(t):null;return j({id:a}),Y(m("div",{id:a,role:"tabpanel",class:ye("panel"),tabindex:u?0:-1,"aria-labelledby":r},[p]),[[G,u]])}}});e("T",B(_e)),e("a",B(we))}}}));
