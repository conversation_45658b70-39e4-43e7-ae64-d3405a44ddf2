import{_ as e,Y as a,l,r as s,av as t,I as r,c as d,e as p,w as o,a as n,cE as i,o as c,f as m,b as u,aV as A,D as b,E as h}from"./index-3d21abf8.js";import{B as v}from"./index-2406f514.js";import{C as g}from"./index-6aaac5d3.js";/* empty css              *//* empty css               */import{F as x}from"./index-8c1841f6.js";import{F as V}from"./index-4ac00735.js";import"./use-route-cd41a893.js";import"./index-7dfcb82a.js";import"./use-id-a0619e01.js";const f=e=>(b("data-v-827e863e"),e=e(),h(),e),F={class:"addPay pb-10"},U=f((()=>n("p",{class:"pt-6 pb-2 ash"},"Name",-1))),k=f((()=>n("p",{class:"pt-6 pb-2 ash"},"Phone Number",-1))),B=f((()=>n("p",{class:"pt-6 pb-2 ash"},"银行名称",-1))),E=f((()=>n("p",{class:"pt-6 pb-2 ash"},"Bank Account Number",-1))),C=f((()=>n("p",{class:"pt-6 pb-2 ash"},"开户支行（选填）",-1))),I=f((()=>n("p",{class:"pt-6 pb-2 ash"},"Note（选填）",-1))),N=i('<div class="tips mx-4 mt-8 px-4 pt-4 pb-4" data-v-827e863e><div class="flex tip-title" data-v-827e863e><img class="mr-2" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADYAAAA3CAYAAABHGbl4AAAACXBIWXMAACE4AAAhOAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAPLSURBVHgB3ZrNbtNAEMdnN0nbSJXIhRMHXNEibqRPQPoEBGgrbrQPgNo+QZMnqCIeIOGGSGnDE5BeeyFcKkRbyUj0AgfCKR/+GGbcJMqn4zp2EvsnbT7stZK/ZndndmYF+IySrin8HosZCcRIQtdBBViqqiVRBR8R4CEsIhrFNAl4KiWm6FKi1YaCKCogUEUDPxsmlNVSXAWPmFiYksZENNrcEwJ3+CtMhopo5nRdlCYV6VrYk9c1xdAhA0K8AV8QBU0zs24F3lkYWyi2oB0CmvswFdwJvJOwR68aaZo7ebCZNz6hmgZmr0/iBacPSKcd17abRyTqFKYvilFkRORXNxuHTh8Ya7HbxaFxKgSkYC4QFU1b2BjnLmyFWQuEIclKmIT5QtU03LCbdyOFsaUWFhpfEd0t4Q/uC2r2fW7+cENwh73loqMeI1F5t6KYlykJb7eitn3eFXVqBrgDk7FYg+f8xrC7QxcPnqQkKg3zT2ptu3407MaAsNXNWoqiiAwEBYR9/s/9lweESSnyEDCEEHleE7qv9QhrDUEFggcF3/WeSKgjjJf2QA3BPshqe91W6wizAtpgk+i2WkeYkOIZBJxuq1nC1rZqOwGdW/0kIrJuuSlLGO1kg+CzHEHBsrU/tIRRgPscwkOSh6Mc5twCDi8iSbZYCkIHsjAxb1uSibGyZPR+D0IG7fSTFBriCoSPhEQQDyF8KI6TOUEj1MJ8LQ7MiKoUgP8gfFSlyRWP0IE/oxQnquADJ2cmnF9otn1+uU692WOaokL5Max4XCazuPmNVpsJiBUZiUAZQoaMGBXLVGtb9b8wm2KDH6iXxaWVdqr2PbU98BD/U9yjEGV+tYQhYonzBeAh/qe4R6Hl+NWKPK6O42V6K0PwqVwWly331QmpyGpZCDhU9cy1P3eEhcBqancptycIDrLVyCkfdH/vEcZWo0ikBIFDFK4/Lfb874FtS7O5uOtXmOUTKh+X6L84IIxLn6aJuxAY9BfDatFDHQ0PydXN+gFZ7ghcws73/MIc22cSEM2Dq+PlobsT2+j38XYtQ+lvx2crpgmVvLI/PsYzI+/DGOZR3DhRVh9wAA3L/UmGpYdQGgMPLovjjx453ohxxZN8xZcZlptUXijaIdM4HGepvn+Iq+QK1uljDqZPTtMW152KYlxtnW+PIkGBHve5CopnNEIyrXDvTkyUE7DOhADseH8Y072gNp4kO1oWTAGLdGdFXhS+8b5Q1+MFLw5Ce5/FgVtLco0KUSoRaSpUH0j0/ihWDVOqQpgqH/bS9aWK16e6/wMNqJoi01Wz+gAAAABJRU5ErkJggg==" data-v-827e863e> 特别提醒 </div><div class="pl-4 ash mt-2 font-13" data-v-827e863e>请确保添加您的银行卡号进行即时付款。请勿包含其他银行或 付款方式的详细信息。您必须添加所选银行的付款/收款信息。</div></div><div class="font-13 mt-20 ash px-4" data-v-827e863e>温馨提示：当您出售数字货币时，您选择的收款方式将向买方展示，请 确认信息填写准确无误。</div>',2),Q={class:"px-4 pt-6 mt-3"},P=e({__name:"add",setup(e){a(),l();let i=s("法币");const b=e=>{};return(e,a)=>{const l=t("fx-header"),s=r,h=x,f=g,P=V,R=v;return c(),d("div",F,[p(l,null,{title:o((()=>[m("添加 Al-Rafidain QiServices")])),_:1}),p(P,{onFailed:b},{default:o((()=>[p(f,{inset:""},{default:o((()=>[U,p(h,{class:"select-item",modelValue:u(i),"onUpdate:modelValue":a[0]||(a[0]=e=>A(i)?i.value=e:i=e),disabled:"",name:"pattern",placeholder:"正则校验",rules:[{pattern:e.pattern,message:"请输入正确内容"}]},{extra:o((()=>[p(s,{name:"arrow-down",color:"#878A96",size:"18"})])),_:1},8,["modelValue","rules"])])),_:1}),p(f,{inset:""},{default:o((()=>[k,p(h,{class:"select-item",modelValue:u(i),"onUpdate:modelValue":a[1]||(a[1]=e=>A(i)?i.value=e:i=e),clearable:"",name:"picker",placeholder:"Phone Number",rules:[{pattern:e.pattern,message:"请输入正确内容"}]},null,8,["modelValue","rules"])])),_:1}),p(f,{inset:""},{default:o((()=>[B,p(h,{class:"select-item",modelValue:u(i),"onUpdate:modelValue":a[2]||(a[2]=e=>A(i)?i.value=e:i=e),clearable:"",name:"picker",placeholder:"Phone Number",rules:[{pattern:e.pattern,message:"请输入正确内容"}]},null,8,["modelValue","rules"])])),_:1}),p(f,{inset:""},{default:o((()=>[E,p(h,{class:"select-item",modelValue:u(i),"onUpdate:modelValue":a[3]||(a[3]=e=>A(i)?i.value=e:i=e),name:"picker",clearable:"",placeholder:"Bank Account Number",rules:[{pattern:e.pattern,message:"请输入正确内容"}]},null,8,["modelValue","rules"])])),_:1}),p(f,{inset:""},{default:o((()=>[C,p(h,{class:"select-item",modelValue:u(i),"onUpdate:modelValue":a[4]||(a[4]=e=>A(i)?i.value=e:i=e),name:"picker",clearable:"",placeholder:"开户支行（选填）",rules:[{pattern:e.pattern,message:"请输入正确内容"}]},null,8,["modelValue","rules"])])),_:1}),p(f,{inset:""},{default:o((()=>[I,p(h,{class:"select-item-textarea",modelValue:u(i),"onUpdate:modelValue":a[5]||(a[5]=e=>A(i)?i.value=e:i=e),type:"textarea",name:"picker",clearable:"",placeholder:"Bank Account Number",rules:[{pattern:e.pattern,message:"请输入正确内容"}]},null,8,["modelValue","rules"])])),_:1})])),_:1}),N,n("div",Q,[p(R,{class:"w-full",type:"primary",onClick:e.submit},{default:o((()=>[m("确认")])),_:1},8,["onClick"])])])}}},[["__scopeId","data-v-827e863e"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/payMentMethod/add.vue"]]);export{P as default};
