// vite.config.js
import { defineConfig } from "file:///C:/Users/<USER>/Desktop/%E5%89%8D%E7%AB%AF/mall-app-h5-vue-fix(%E5%8D%96%E5%AE%B6%E7%AB%AF%E5%BC%80%E6%BA%90%E5%8F%AF%E4%BB%A5%E7%BC%96%E8%AF%91%E7%9A%84)/mall-app-h5-vue/node_modules/vite/dist/node/index.js";
import vue from "file:///C:/Users/<USER>/Desktop/%E5%89%8D%E7%AB%AF/mall-app-h5-vue-fix(%E5%8D%96%E5%AE%B6%E7%AB%AF%E5%BC%80%E6%BA%90%E5%8F%AF%E4%BB%A5%E7%BC%96%E8%AF%91%E7%9A%84)/mall-app-h5-vue/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import legacy from "file:///C:/Users/<USER>/Desktop/%E5%89%8D%E7%AB%AF/mall-app-h5-vue-fix(%E5%8D%96%E5%AE%B6%E7%AB%AF%E5%BC%80%E6%BA%90%E5%8F%AF%E4%BB%A5%E7%BC%96%E8%AF%91%E7%9A%84)/mall-app-h5-vue/node_modules/@vitejs/plugin-legacy/dist/index.mjs";
import path from "path";
import Components from "file:///C:/Users/<USER>/Desktop/%E5%89%8D%E7%AB%AF/mall-app-h5-vue-fix(%E5%8D%96%E5%AE%B6%E7%AB%AF%E5%BC%80%E6%BA%90%E5%8F%AF%E4%BB%A5%E7%BC%96%E8%AF%91%E7%9A%84)/mall-app-h5-vue/node_modules/unplugin-vue-components/dist/vite.mjs";
import VueSetupExtend from "file:///C:/Users/<USER>/Desktop/%E5%89%8D%E7%AB%AF/mall-app-h5-vue-fix(%E5%8D%96%E5%AE%B6%E7%AB%AF%E5%BC%80%E6%BA%90%E5%8F%AF%E4%BB%A5%E7%BC%96%E8%AF%91%E7%9A%84)/mall-app-h5-vue/node_modules/vite-plugin-vue-setup-extend/dist/index.mjs";
import { VantResolver } from "file:///C:/Users/<USER>/Desktop/%E5%89%8D%E7%AB%AF/mall-app-h5-vue-fix(%E5%8D%96%E5%AE%B6%E7%AB%AF%E5%BC%80%E6%BA%90%E5%8F%AF%E4%BB%A5%E7%BC%96%E8%AF%91%E7%9A%84)/mall-app-h5-vue/node_modules/unplugin-vue-components/dist/resolvers.mjs";
import viteCompression from "file:///C:/Users/<USER>/Desktop/%E5%89%8D%E7%AB%AF/mall-app-h5-vue-fix(%E5%8D%96%E5%AE%B6%E7%AB%AF%E5%BC%80%E6%BA%90%E5%8F%AF%E4%BB%A5%E7%BC%96%E8%AF%91%E7%9A%84)/mall-app-h5-vue/node_modules/vite-plugin-compression/dist/index.mjs";
var __vite_injected_original_dirname = "C:\\Users\\<USER>\\Desktop\\\u524D\u7AEF\\mall-app-h5-vue-fix(\u5356\u5BB6\u7AEF\u5F00\u6E90\u53EF\u4EE5\u7F16\u8BD1\u7684)\\mall-app-h5-vue";
var scss_path = `@/assets/init.scss`;
var addScssPath = `@/assets/scss/theme.scss`;
var mode = process.env.flag || "test";
console.log("mode", mode);
var publicPath = process.env.NODE_ENV === "development" ? "/" : "./";
var vite_config_default = defineConfig({
  base: "/www/",
  plugins: [
    vue(),
    VueSetupExtend(),
    Components({
      resolvers: [VantResolver()],
      directoryAsNamespace: true
    }),
    legacy({
      targets: ["defaults", "not IE 11"],
      additionalLegacyPolyfills: ["regenerator-runtime/runtime"],
      renderLegacyChunks: true,
      polyfills: [
        "es.symbol",
        "es.array.filter",
        "es.promise",
        "es.promise.finally",
        "es/map",
        "es/set",
        "es.array.for-each",
        "es.object.define-properties",
        "es.object.define-property",
        "es.object.get-own-property-descriptor",
        "es.object.get-own-property-descriptors",
        "es.object.keys",
        "es.object.to-string",
        "web.dom-collections.for-each",
        "esnext.global-this",
        "esnext.string.match-all"
      ]
    }),
    viteCompression({
      threshold: 10240,
      ext: ".gz"
    })
  ],
  css: {
    postcssOptions: {
      config: "./postcss.config.js"
    },
    loaderOptions: {
      scss: {
        additionalData: `@import "${scss_path}";`
      }
    },
    preprocessorOptions: {
      scss: {
        additionalData: `@import "${addScssPath}";`,
        javascriptEnabled: true
      }
    }
  },
  server: {
    open: true,
    port: 8080,
    hmr: true,
    host: "0.0.0.0",
    proxy: {
      "/wap/api": {
        target: "https://tkittkit.com",
        changeOrigin: true,
        secure: false
      },
      "/wap/seller": {
        target: "https://tkittkit.com",
        changeOrigin: true,
        secure: false
      },
      "/wap/public": {
        target: "https://tkittkit.com",
        changeOrigin: true,
        secure: false
      },
      "/api": {
        target: "https://tkittkit.com",
        changeOrigin: true,
        secure: false
      }
    }
  },
  resolve: {
    alias: {
      "vue-i18n": "vue-i18n/dist/vue-i18n.cjs.js",
      "~": path.resolve(__vite_injected_original_dirname, "./"),
      "@": path.resolve(__vite_injected_original_dirname, "src"),
      "theme": path.resolve(__vite_injected_original_dirname, "src/assets/theme")
    }
  },
  build: {
    target: "es2015",
    minify: "terser",
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    chunkSizeWarningLimit: 1e3,
    assetsDir: "static",
    rollupOptions: {
      input: {
        index: path.resolve(__vite_injected_original_dirname, "index.html")
      },
      output: {
        dir: `dist/${mode}`,
        chunkFileNames: "js/[name]-[hash].js",
        entryFileNames: "js/[name]-[hash].js",
        assetFileNames: "[ext]/name-[hash].[ext]"
      }
    }
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcuanMiXSwKICAic291cmNlc0NvbnRlbnQiOiBbImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJDOlxcXFxVc2Vyc1xcXFx0YWZsN1xcXFxEZXNrdG9wXFxcXFx1NTI0RFx1N0FFRlxcXFxtYWxsLWFwcC1oNS12dWUtZml4KFx1NTM1Nlx1NUJCNlx1N0FFRlx1NUYwMFx1NkU5MFx1NTNFRlx1NEVFNVx1N0YxNlx1OEJEMVx1NzY4NClcXFxcbWFsbC1hcHAtaDUtdnVlXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ZpbGVuYW1lID0gXCJDOlxcXFxVc2Vyc1xcXFx0YWZsN1xcXFxEZXNrdG9wXFxcXFx1NTI0RFx1N0FFRlxcXFxtYWxsLWFwcC1oNS12dWUtZml4KFx1NTM1Nlx1NUJCNlx1N0FFRlx1NUYwMFx1NkU5MFx1NTNFRlx1NEVFNVx1N0YxNlx1OEJEMVx1NzY4NClcXFxcbWFsbC1hcHAtaDUtdnVlXFxcXHZpdGUuY29uZmlnLmpzXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ltcG9ydF9tZXRhX3VybCA9IFwiZmlsZTovLy9DOi9Vc2Vycy90YWZsNy9EZXNrdG9wLyVFNSU4OSU4RCVFNyVBQiVBRi9tYWxsLWFwcC1oNS12dWUtZml4KCVFNSU4RCU5NiVFNSVBRSVCNiVFNyVBQiVBRiVFNSVCQyU4MCVFNiVCQSU5MCVFNSU4RiVBRiVFNCVCQiVBNSVFNyVCQyU5NiVFOCVBRiU5MSVFNyU5QSU4NCkvbWFsbC1hcHAtaDUtdnVlL3ZpdGUuY29uZmlnLmpzXCI7aW1wb3J0IHsgZGVmaW5lQ29uZmlnIH0gZnJvbSAndml0ZSdcbmltcG9ydCB2dWUgZnJvbSAnQHZpdGVqcy9wbHVnaW4tdnVlJ1xuaW1wb3J0IGxlZ2FjeSBmcm9tICdAdml0ZWpzL3BsdWdpbi1sZWdhY3knXG5pbXBvcnQgcGF0aCBmcm9tICdwYXRoJ1xuaW1wb3J0IENvbXBvbmVudHMgZnJvbSAndW5wbHVnaW4tdnVlLWNvbXBvbmVudHMvdml0ZSc7XG5pbXBvcnQgVnVlU2V0dXBFeHRlbmQgZnJvbSBcInZpdGUtcGx1Z2luLXZ1ZS1zZXR1cC1leHRlbmRcIlxuaW1wb3J0IHsgVmFudFJlc29sdmVyIH0gZnJvbSAndW5wbHVnaW4tdnVlLWNvbXBvbmVudHMvcmVzb2x2ZXJzJztcbmltcG9ydCB2aXRlQ29tcHJlc3Npb24gZnJvbSAndml0ZS1wbHVnaW4tY29tcHJlc3Npb24nXG5jb25zdCBzY3NzX3BhdGggPSBgQC9hc3NldHMvaW5pdC5zY3NzYFxuXG5jb25zdCBhZGRTY3NzUGF0aCA9IGBAL2Fzc2V0cy9zY3NzL3RoZW1lLnNjc3NgXG5jb25zdCBtb2RlID0gcHJvY2Vzcy5lbnYuZmxhZyB8fCAndGVzdCdcbmNvbnNvbGUubG9nKCdtb2RlJywgbW9kZSlcblxuY29uc3QgcHVibGljUGF0aCA9IChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50JyA/ICcvJzogJy4vJykgLy8gXHU3NTI4XHU0RThFXHU2MjUzXHU1MzA1NStcbmV4cG9ydCBkZWZhdWx0IGRlZmluZUNvbmZpZyh7XG4gIGJhc2U6ICcvd3d3LycsIC8vICcvd3d3LycsIFx1NzUyOFx1NEU4RVx1N0VCRlx1NEUwQVx1NTNEMVx1NUUwM1xuICBwbHVnaW5zOiBbXG4gICAgdnVlKCksXG4gICAgLyoqIHNldHVwIFx1OEJFRFx1NkNENVx1N0NENlx1NUJGQ1x1NTFGQSB2dWUgXHU2NTg3XHU0RUY2bmFtZSBcdTVDNUVcdTYwMjdcdUZGMENcdTc1MjhcdTRFOEVcdTk4NzVcdTk3NjIga2VlcEFsaXZlICovXG4gICAgVnVlU2V0dXBFeHRlbmQoKSxcbiAgICBDb21wb25lbnRzKHtcbiAgICAgIHJlc29sdmVyczogW1ZhbnRSZXNvbHZlcigpXSxcbiAgICAgIGRpcmVjdG9yeUFzTmFtZXNwYWNlOiB0cnVlLFxuICAgIH0pLFxuICAgIGxlZ2FjeSh7XG4gICAgICB0YXJnZXRzOiBbJ2RlZmF1bHRzJywgJ25vdCBJRSAxMSddLFxuICAgICAgYWRkaXRpb25hbExlZ2FjeVBvbHlmaWxsczogWydyZWdlbmVyYXRvci1ydW50aW1lL3J1bnRpbWUnXSxcbiAgICAgIHJlbmRlckxlZ2FjeUNodW5rczogdHJ1ZSxcbiAgICAgIHBvbHlmaWxsczogW1xuICAgICAgICAnZXMuc3ltYm9sJyxcbiAgICAgICAgJ2VzLmFycmF5LmZpbHRlcicsXG4gICAgICAgICdlcy5wcm9taXNlJyxcbiAgICAgICAgJ2VzLnByb21pc2UuZmluYWxseScsXG4gICAgICAgICdlcy9tYXAnLFxuICAgICAgICAnZXMvc2V0JyxcbiAgICAgICAgJ2VzLmFycmF5LmZvci1lYWNoJyxcbiAgICAgICAgJ2VzLm9iamVjdC5kZWZpbmUtcHJvcGVydGllcycsXG4gICAgICAgICdlcy5vYmplY3QuZGVmaW5lLXByb3BlcnR5JyxcbiAgICAgICAgJ2VzLm9iamVjdC5nZXQtb3duLXByb3BlcnR5LWRlc2NyaXB0b3InLFxuICAgICAgICAnZXMub2JqZWN0LmdldC1vd24tcHJvcGVydHktZGVzY3JpcHRvcnMnLFxuICAgICAgICAnZXMub2JqZWN0LmtleXMnLFxuICAgICAgICAnZXMub2JqZWN0LnRvLXN0cmluZycsXG4gICAgICAgICd3ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoJyxcbiAgICAgICAgJ2VzbmV4dC5nbG9iYWwtdGhpcycsXG4gICAgICAgICdlc25leHQuc3RyaW5nLm1hdGNoLWFsbCdcbiAgICAgIF1cbiAgICB9KSxcbiAgICB2aXRlQ29tcHJlc3Npb24oe1xuICAgICAgdGhyZXNob2xkOiAxMDI0MCxcbiAgICAgIGV4dDogJy5neicsXG4gICAgfSksXG4gIF0sXG4gIGNzczoge1xuICAgIHBvc3Rjc3NPcHRpb25zOiB7XG4gICAgICBjb25maWc6ICcuL3Bvc3Rjc3MuY29uZmlnLmpzJyxcbiAgICB9LFxuICAgIGxvYWRlck9wdGlvbnM6IHtcbiAgICAgIHNjc3M6IHtcbiAgICAgICAgYWRkaXRpb25hbERhdGE6IGBAaW1wb3J0IFwiJHtzY3NzX3BhdGh9XCI7YFxuICAgICAgfSxcbiAgICB9LFxuICAgIHByZXByb2Nlc3Nvck9wdGlvbnM6IHtcbiAgICAgIHNjc3M6IHtcbiAgICAgICAgYWRkaXRpb25hbERhdGE6IGBAaW1wb3J0IFwiJHthZGRTY3NzUGF0aH1cIjtgLFxuICAgICAgICBqYXZhc2NyaXB0RW5hYmxlZDogdHJ1ZVxuICAgICAgfVxuICAgIH1cbiAgfSxcbiAgc2VydmVyOiB7XG4gICAgb3BlbjogdHJ1ZSxcbiAgICBwb3J0OiA4MDgwLFxuICAgIGhtcjogdHJ1ZSxcbiAgICBob3N0OiAnMC4wLjAuMCcsXG4gICAgcHJveHk6IHtcbiAgICAgIFwiL3dhcC9hcGlcIjoge1xuICAgICAgICB0YXJnZXQ6IFwiaHR0cHM6Ly90a2l0dGtpdC5jb21cIixcbiAgICAgICAgY2hhbmdlT3JpZ2luOiB0cnVlLFxuICAgICAgICBzZWN1cmU6IGZhbHNlXG4gICAgICB9LFxuICAgICAgXCIvd2FwL3NlbGxlclwiOiB7XG4gICAgICAgIHRhcmdldDogXCJodHRwczovL3RraXR0a2l0LmNvbVwiLFxuICAgICAgICBjaGFuZ2VPcmlnaW46IHRydWUsXG4gICAgICAgIHNlY3VyZTogZmFsc2VcbiAgICAgIH0sXG4gICAgICBcIi93YXAvcHVibGljXCI6IHtcbiAgICAgICAgdGFyZ2V0OiBcImh0dHBzOi8vdGtpdHRraXQuY29tXCIsXG4gICAgICAgIGNoYW5nZU9yaWdpbjogdHJ1ZSxcbiAgICAgICAgc2VjdXJlOiBmYWxzZVxuICAgICAgfSxcbiAgICAgIFwiL2FwaVwiOiB7XG4gICAgICAgIHRhcmdldDogXCJodHRwczovL3RraXR0a2l0LmNvbVwiLFxuICAgICAgICBjaGFuZ2VPcmlnaW46IHRydWUsXG4gICAgICAgIHNlY3VyZTogZmFsc2VcbiAgICAgIH1cbiAgICB9LFxuICB9LFxuICByZXNvbHZlOiB7XG4gICAgYWxpYXM6IHtcbiAgICAgICd2dWUtaTE4bic6ICd2dWUtaTE4bi9kaXN0L3Z1ZS1pMThuLmNqcy5qcycsXG4gICAgICBcIn5cIjogcGF0aC5yZXNvbHZlKF9fZGlybmFtZSwgJy4vJyksXG4gICAgICBcIkBcIjogcGF0aC5yZXNvbHZlKF9fZGlybmFtZSwgJ3NyYycpLFxuICAgICAgJ3RoZW1lJzogcGF0aC5yZXNvbHZlKF9fZGlybmFtZSwgJ3NyYy9hc3NldHMvdGhlbWUnKVxuICAgIH1cbiAgfSxcbiAgYnVpbGQ6IHtcbiAgICB0YXJnZXQ6ICdlczIwMTUnLFxuICAgIG1pbmlmeTogJ3RlcnNlcicsXG4gICAgdGVyc2VyT3B0aW9uczoge1xuICAgICAgY29tcHJlc3M6IHtcbiAgICAgICAgZHJvcF9jb25zb2xlOiB0cnVlLFxuICAgICAgICBkcm9wX2RlYnVnZ2VyOiB0cnVlXG4gICAgICB9XG4gICAgfSxcbiAgICBjaHVua1NpemVXYXJuaW5nTGltaXQ6IDEwMDAsXG4gICAgYXNzZXRzRGlyOiBcInN0YXRpY1wiLFxuICAgIHJvbGx1cE9wdGlvbnM6IHtcbiAgICAgIGlucHV0OiB7XG4gICAgICAgIGluZGV4OiBwYXRoLnJlc29sdmUoX19kaXJuYW1lLCBcImluZGV4Lmh0bWxcIiksXG4gICAgICB9LFxuICAgICAgb3V0cHV0OiB7XG4gICAgICAgIGRpcjogYGRpc3QvJHttb2RlfWAsXG4gICAgICAgIGNodW5rRmlsZU5hbWVzOiAnanMvW25hbWVdLVtoYXNoXS5qcycsXG4gICAgICAgIGVudHJ5RmlsZU5hbWVzOiBcImpzL1tuYW1lXS1baGFzaF0uanNcIixcbiAgICAgICAgYXNzZXRGaWxlTmFtZXM6IFwiW2V4dF0vbmFtZS1baGFzaF0uW2V4dF1cIlxuICAgICAgfVxuICAgIH0sXG4gIH1cbn0pXG5cbmNvbnN0IGdsb2JhbFNhc3MgPSBjb25maWcgPT4ge1xuICBjb25zdCBvbmVPZnNNYXAgPSBjb25maWcubW9kdWxlLnJ1bGUoJ3Njc3MnKS5vbmVPZnMuc3RvcmVcbiAgb25lT2ZzTWFwLmZvckVhY2goaXRlbSA9PiB7XG4gICAgaXRlbVxuICAgICAgLnVzZSgnc2Fzcy1yZXNvdXJjZXMtbG9hZGVyJylcbiAgICAgIC5sb2FkZXIoJ3Nhc3MtcmVzb3VyY2VzLWxvYWRlcicpXG4gICAgICAub3B0aW9ucyh7XG4gICAgICAgIHJlc291cmNlczogJ0AvYXNzZXRzL2luaXQuc2NzcydcbiAgICAgIH0pXG4gICAgICAuZW5kKClcbiAgfSk7XG59XG4iXSwKICAibWFwcGluZ3MiOiAiO0FBQTJmLFNBQVMsb0JBQW9CO0FBQ3hoQixPQUFPLFNBQVM7QUFDaEIsT0FBTyxZQUFZO0FBQ25CLE9BQU8sVUFBVTtBQUNqQixPQUFPLGdCQUFnQjtBQUN2QixPQUFPLG9CQUFvQjtBQUMzQixTQUFTLG9CQUFvQjtBQUM3QixPQUFPLHFCQUFxQjtBQVA1QixJQUFNLG1DQUFtQztBQVF6QyxJQUFNLFlBQVk7QUFFbEIsSUFBTSxjQUFjO0FBQ3BCLElBQU0sT0FBTyxRQUFRLElBQUksUUFBUTtBQUNqQyxRQUFRLElBQUksUUFBUSxJQUFJO0FBRXhCLElBQU0sYUFBYyxRQUFRLElBQUksYUFBYSxnQkFBZ0IsTUFBSztBQUNsRSxJQUFPLHNCQUFRLGFBQWE7QUFBQSxFQUMxQixNQUFNO0FBQUEsRUFDTixTQUFTO0FBQUEsSUFDUCxJQUFJO0FBQUEsSUFFSixlQUFlO0FBQUEsSUFDZixXQUFXO0FBQUEsTUFDVCxXQUFXLENBQUMsYUFBYSxDQUFDO0FBQUEsTUFDMUIsc0JBQXNCO0FBQUEsSUFDeEIsQ0FBQztBQUFBLElBQ0QsT0FBTztBQUFBLE1BQ0wsU0FBUyxDQUFDLFlBQVksV0FBVztBQUFBLE1BQ2pDLDJCQUEyQixDQUFDLDZCQUE2QjtBQUFBLE1BQ3pELG9CQUFvQjtBQUFBLE1BQ3BCLFdBQVc7QUFBQSxRQUNUO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsTUFDRjtBQUFBLElBQ0YsQ0FBQztBQUFBLElBQ0QsZ0JBQWdCO0FBQUEsTUFDZCxXQUFXO0FBQUEsTUFDWCxLQUFLO0FBQUEsSUFDUCxDQUFDO0FBQUEsRUFDSDtBQUFBLEVBQ0EsS0FBSztBQUFBLElBQ0gsZ0JBQWdCO0FBQUEsTUFDZCxRQUFRO0FBQUEsSUFDVjtBQUFBLElBQ0EsZUFBZTtBQUFBLE1BQ2IsTUFBTTtBQUFBLFFBQ0osZ0JBQWdCLFlBQVk7QUFBQSxNQUM5QjtBQUFBLElBQ0Y7QUFBQSxJQUNBLHFCQUFxQjtBQUFBLE1BQ25CLE1BQU07QUFBQSxRQUNKLGdCQUFnQixZQUFZO0FBQUEsUUFDNUIsbUJBQW1CO0FBQUEsTUFDckI7QUFBQSxJQUNGO0FBQUEsRUFDRjtBQUFBLEVBQ0EsUUFBUTtBQUFBLElBQ04sTUFBTTtBQUFBLElBQ04sTUFBTTtBQUFBLElBQ04sS0FBSztBQUFBLElBQ0wsTUFBTTtBQUFBLElBQ04sT0FBTztBQUFBLE1BQ0wsWUFBWTtBQUFBLFFBQ1YsUUFBUTtBQUFBLFFBQ1IsY0FBYztBQUFBLFFBQ2QsUUFBUTtBQUFBLE1BQ1Y7QUFBQSxNQUNBLGVBQWU7QUFBQSxRQUNiLFFBQVE7QUFBQSxRQUNSLGNBQWM7QUFBQSxRQUNkLFFBQVE7QUFBQSxNQUNWO0FBQUEsTUFDQSxlQUFlO0FBQUEsUUFDYixRQUFRO0FBQUEsUUFDUixjQUFjO0FBQUEsUUFDZCxRQUFRO0FBQUEsTUFDVjtBQUFBLE1BQ0EsUUFBUTtBQUFBLFFBQ04sUUFBUTtBQUFBLFFBQ1IsY0FBYztBQUFBLFFBQ2QsUUFBUTtBQUFBLE1BQ1Y7QUFBQSxJQUNGO0FBQUEsRUFDRjtBQUFBLEVBQ0EsU0FBUztBQUFBLElBQ1AsT0FBTztBQUFBLE1BQ0wsWUFBWTtBQUFBLE1BQ1osS0FBSyxLQUFLLFFBQVEsa0NBQVcsSUFBSTtBQUFBLE1BQ2pDLEtBQUssS0FBSyxRQUFRLGtDQUFXLEtBQUs7QUFBQSxNQUNsQyxTQUFTLEtBQUssUUFBUSxrQ0FBVyxrQkFBa0I7QUFBQSxJQUNyRDtBQUFBLEVBQ0Y7QUFBQSxFQUNBLE9BQU87QUFBQSxJQUNMLFFBQVE7QUFBQSxJQUNSLFFBQVE7QUFBQSxJQUNSLGVBQWU7QUFBQSxNQUNiLFVBQVU7QUFBQSxRQUNSLGNBQWM7QUFBQSxRQUNkLGVBQWU7QUFBQSxNQUNqQjtBQUFBLElBQ0Y7QUFBQSxJQUNBLHVCQUF1QjtBQUFBLElBQ3ZCLFdBQVc7QUFBQSxJQUNYLGVBQWU7QUFBQSxNQUNiLE9BQU87QUFBQSxRQUNMLE9BQU8sS0FBSyxRQUFRLGtDQUFXLFlBQVk7QUFBQSxNQUM3QztBQUFBLE1BQ0EsUUFBUTtBQUFBLFFBQ04sS0FBSyxRQUFRO0FBQUEsUUFDYixnQkFBZ0I7QUFBQSxRQUNoQixnQkFBZ0I7QUFBQSxRQUNoQixnQkFBZ0I7QUFBQSxNQUNsQjtBQUFBLElBQ0Y7QUFBQSxFQUNGO0FBQ0YsQ0FBQzsiLAogICJuYW1lcyI6IFtdCn0K
