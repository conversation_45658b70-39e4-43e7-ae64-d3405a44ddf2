System.register(["./index-legacy-46a00900.js","./index-legacy-72e00c5f.js","./index-legacy-f9c0699e.js","./index-legacy-f9c0699e2.js","./login.api-legacy-d31fdc92.js","./index-legacy-0ade4760.js","./use-route-legacy-be86ac1c.js","./index-legacy-df3e01aa.js","./use-id-legacy-df76950f.js"],(function(e,t){"use strict";var n,a,i,o,l,c,d,r,s,f,u,v,p,b,x,g,m,y,h,k,w,j,_,C,I,V,q,z,U,D=document.createElement("style");return D.textContent=".verify-content[data-v-1ca2bd92]{min-height:100vh;background-color:#fff}.info-content[data-v-1ca2bd92]{padding:0 20px;color:#333}.info-content>.info-item[data-v-1ca2bd92]{margin-top:30px}.info-content>.info-item[data-v-1ca2bd92]:last-child{margin-top:20px}.info-content>.info-item>.gap[data-v-1ca2bd92]{margin-bottom:10px}.info-content>.info-item h2[data-v-1ca2bd92]{font-size:20px;font-weight:700}.info-content>.info-item p[data-v-1ca2bd92]{font-size:14px}.info-content .code-content[data-v-1ca2bd92]{width:100%;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between;margin-top:35px}.info-content .code-content.is-ar[data-v-1ca2bd92] .van-field__control{text-align:right}.info-content .code-content .van-cell[data-v-1ca2bd92]{width:185px;height:44px;border:1px solid #ddd;border-radius:4px}.info-content .code-content .van-cell[data-v-1ca2bd92]:after{border:none}.info-content .code-content>.btn[data-v-1ca2bd92]{-webkit-box-flex:1;-webkit-flex:1 1;flex:1 1;padding-left:15px}.info-content .code-content>.btn.is-ar[data-v-1ca2bd92]{padding-right:15px;padding-left:0}.info-content .code-content>.btn .van-button[data-v-1ca2bd92]{width:100%;height:44px;border-radius:4px;background-color:var(--site-main-color);border-color:var(--site-main-color)}.info-content>.change-ver-type[data-v-1ca2bd92]{width:100%;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:end;-webkit-justify-content:flex-end;justify-content:flex-end;color:var(--site-main-color);margin-top:10px}.info-content>.change-ver-type>p[data-v-1ca2bd92]{font-size:14px;padding-left:5px;font-weight:700}.submit-content[data-v-1ca2bd92]{width:100%;position:fixed;left:0;bottom:60px;padding:0 20px}.submit-content .van-button[data-v-1ca2bd92]{width:100%;height:44px;border-radius:4px;background-color:var(--site-main-color);border-color:var(--site-main-color)}\n",document.head.appendChild(D),{setters:[e=>{n=e._,a=e.j,i=e.u,o=e.Y,l=e.l,c=e.r,d=e.q,r=e.i,s=e.m,f=e.av,u=e.c,v=e.e,p=e.w,b=e.a,x=e.t,g=e.b,m=e.x,y=e.f,h=e.n,k=e.T,w=e.ch,j=e.o,_=e.cg,C=e.F,I=e.D,V=e.E},e=>{q=e.B},()=>{},()=>{},e=>{z=e.a},e=>{U=e.F},()=>{},()=>{},()=>{}],execute:function(){const t={class:"verify-content"},D=(e=>(I("data-v-1ca2bd92"),e=e(),V(),e))((()=>b("div",{style:{height:"46px"}},null,-1))),E={class:"info-content"},F={class:"info-item"},S={class:"info-item"},B={class:"gap"},N={key:0},T={class:"submit-content"};e("default",n({__name:"index",setup(e){const n=a(),{t:I}=i(),V=o(),Y=l(),A=c(1),G=c("44"),H=c(""),J=c(0),K=c(null),L=c(!1);A.value=V.query&&V.query.type?Number(V.query.type):1,d((()=>{clearInterval(K.value),K.value=null}));const M=r(),O=s((()=>{if(n.userInfo.token){const{email:e,phone:t}=n.userInfo;let a="";if(1===A.value){if(t){const e=t.split(" ");2===e.length?(G.value=e[0],a=e[1]):a=e[0]}}else a=e||"";return a}Y.push("/login")})),P=()=>{if(J.value>0)return!1;const{email:e,phone:t}=n.userInfo;k.loading({duration:0,forbidClick:!0}),z({target:1===A.value?t:e}).then((()=>{k(I("sendSuccess")),J.value=60,K.value=setInterval((()=>{J.value>0?J.value=J.value-1:(J.value=0,clearInterval(K.value),K.value=null)}),1e3)})).catch((()=>{k.clear()}))},Q=async()=>{if(""===H.value)return void k(I("entryVerifyCode"));L.value=!0;const{email:e,phone:t}=n.userInfo,a={target:1===A.value?t:e,verifcode:H.value};1===A.value?a.phone=t:a.email=e,w(a).then((async e=>{await n.getUserInfo(!0),L.value=!1,Y.back()})).catch((()=>{L.value=!1}))};return(e,n)=>{const a=f("fx-header"),i=U,o=q;return j(),u("div",t,[v(a,{fixed:!0},{title:p((()=>[y(x(1===A.value?g(I)("手机验证"):g(I)("邮箱验证")),1)])),_:1}),D,b("div",E,[b("div",F,[b("p",null,x(g(I)("为了保障您的账号安全，请验证后进行下一步操作")),1)]),b("div",S,[b("p",B,x(1===A.value?g(I)("当前绑定手机号"):g(I)("当前绑定邮箱")),1),b("h2",null,[1===A.value?(j(),u("span",N,"(+"+x(G.value)+")",1)):m("v-if",!0),y(x(g(_)(g(O),!1)),1)])]),b("div",{class:h(["code-content",{"is-ar":g(M)}])},[v(i,{modelValue:H.value,"onUpdate:modelValue":n[0]||(n[0]=e=>H.value=e),type:"tel",label:"",placeholder:g(I)("entryVerifyCode")},null,8,["modelValue","placeholder"]),b("div",{class:h(["btn",{"is-ar":g(M)}])},[v(o,{type:"primary",onClick:P},{default:p((()=>[y(x(g(I)("sendVerifyCode")),1),J.value?(j(),u(C,{key:0},[y("("+x(J.value)+")s",1)],64)):m("v-if",!0)])),_:1})],2)],2)]),b("div",T,[v(o,{type:"primary",loading:L.value,onClick:Q},{default:p((()=>[y(x(g(I)("confirm")),1)])),_:1},8,["loading"])])])}}},[["__scopeId","data-v-1ca2bd92"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/certified/index.vue"]]))}}}));
