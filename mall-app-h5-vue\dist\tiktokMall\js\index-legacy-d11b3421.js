System.register(["./index-legacy-46a00900.js","./config-legacy-9aebe793.js","./index-legacy-b248d96d.js"],(function(e,n){"use strict";var t,i,r,o,s,c,a,l,u,f,d,A,g,h,p,m,v,w,b,x,N,U,C,B,y,Q,R,k,E,S,M=document.createElement("style");return M.textContent=".seller-level-content[data-v-c2b30052]{min-height:100vh;background-color:#fff}.seller-banner[data-v-c2b30052]{width:100%;height:114px;background-size:cover;background-repeat:no-repeat;background-position:center top;padding-left:25px;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center}.seller-banner>h2[data-v-c2b30052]{color:#fff;font-weight:700;font-size:28px;line-height:133.69%}.seller-banner>h2[data-v-c2b30052] span{color:#fdcc2b;text-shadow:1px 1px 0 rgba(0,0,0,.3)}.info-content[data-v-c2b30052]{width:100%;padding:25px 15px;background-color:#fff;border-top-right-radius:20px;border-top-left-radius:20px;position:relative;top:-10px}.info-content>.ads-info[data-v-c2b30052]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.info-content>.ads-info>h2[data-v-c2b30052]{font-weight:700;font-size:18px;color:#333}.info-content>.ads-info>h2[data-v-c2b30052] span{color:#1552f0}.info-content>.ads-info>h2[data-v-c2b30052]:first-child{margin-right:20px}.info-content>.intro-item[data-v-c2b30052]{margin-top:25px}.info-content>.intro-item[data-v-c2b30052]:first-child{margin-top:0}.info-content>.intro-item>h2[data-v-c2b30052]{font-weight:700;font-size:14px;color:var(--site-main-color)}.info-content>.intro-item>div h2[data-v-c2b30052]{font-size:12px;font-weight:700;color:#333;margin-top:15px}.info-content>.intro-item>div p[data-v-c2b30052]{font-size:12px;color:#333;margin-top:5px}.info-content>.intro-item>div[data-v-c2b30052] span{color:#1552f0;padding:0 2px}.level-content[data-v-c2b30052]{margin-top:30px}.level-content .level-item[data-v-c2b30052]{padding:15px 13px;border-radius:10px;margin-top:15px}.level-content .level-item>.top-content[data-v-c2b30052]{border-bottom:1.5px solid #fff;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between;padding-bottom:20px;-webkit-box-align:start;-webkit-align-items:flex-start;align-items:flex-start}.level-content .level-item>.top-content.is-ar>.name-content>div>span[data-v-c2b30052]{margin-left:0;margin-right:10px}.level-content .level-item>.top-content>.name-content>div[data-v-c2b30052]{font-size:15px;font-weight:700;color:#000;margin-bottom:5px;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.level-content .level-item>.top-content>.name-content>div>span[data-v-c2b30052]{display:inline-block;background-color:var(--site-main-color);font-size:10px;line-height:1;font-weight:400;color:#fff;padding:4px 10px;border-radius:20px;margin-left:10px}.level-content .level-item>.top-content>.name-content>p[data-v-c2b30052]{font-size:12px;color:#333;line-height:21px}.level-content .level-item>.top-content>img[data-v-c2b30052]{width:75px;height:auto}.level-content .level-item>.rights-content.is-ar>.item>.title>p[data-v-c2b30052]{padding-left:0;padding-right:5px}.level-content .level-item>.rights-content>.item[data-v-c2b30052]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between;margin-top:10px}.level-content .level-item>.rights-content>.item>.title[data-v-c2b30052]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.level-content .level-item>.rights-content>.item>.title>img[data-v-c2b30052]{width:16px;height:auto}.level-content .level-item>.rights-content>.item>.title>p[data-v-c2b30052]{font-size:12px;color:#333;padding-left:5px}.level-content .level-item>.rights-content>.item>p[data-v-c2b30052]{font-size:12px;color:#333;font-weight:700}.level-content .level-item>.rights-content>.item>img[data-v-c2b30052]{width:13px;height:auto}.level-content .level-item.C[data-v-c2b30052]{background:-webkit-linear-gradient(top,#f2fdff 0%,#e9faff 100%);background:linear-gradient(180deg,#f2fdff 0%,#e9faff 100%)}.level-content .level-item.C>.top-content[data-v-c2b30052]{border-color:#d6f0f5}.level-content .level-item.B[data-v-c2b30052]{background:-webkit-linear-gradient(top,#ffeef2 0%,#ffdbd0 100%);background:linear-gradient(180deg,#ffeef2 0%,#ffdbd0 100%)}.level-content .level-item.B>.top-content[data-v-c2b30052]{border-color:#f9d2cb}.level-content .level-item.A[data-v-c2b30052]{background:-webkit-linear-gradient(top,#f9eeff 0%,#f3dfff 100%);background:linear-gradient(180deg,#f9eeff 0%,#f3dfff 100%)}.level-content .level-item.A>.top-content[data-v-c2b30052]{border-color:#ebd0f9}.level-content .level-item.S[data-v-c2b30052]{background:-webkit-linear-gradient(top,#fff4f0 0%,#ffe5d9 100%);background:linear-gradient(180deg,#fff4f0 0%,#ffe5d9 100%)}.level-content .level-item.S>.top-content[data-v-c2b30052]{border-color:#fcded1}.level-content .level-item.SS[data-v-c2b30052]{background:-webkit-linear-gradient(top,#fff5f1 0%,#ffd6d6 49.48%,#ffc5ab 100%);background:linear-gradient(180deg,#fff5f1 0%,#ffd6d6 49.48%,#ffc5ab 100%)}.level-content .level-item.SS>.top-content[data-v-c2b30052]{border-color:#fcded1}.level-content .level-item.SSS[data-v-c2b30052]{background:-webkit-linear-gradient(top,rgb(250,225,255) 0%,rgb(250,215,255) 49.48%,rgb(245,177,255) 100%);background:linear-gradient(180deg,rgb(250,225,255) 0%,rgb(250,215,255) 49.48%,rgb(245,177,255) 100%)}.level-content .level-item.SSS>.top-content[data-v-c2b30052]{border-color:#f7c2fe}.current-num-info[data-v-c2b30052]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;margin-top:10px;font-size:14px}.current-num-info>p[data-v-c2b30052]{margin-right:20px}.current-num-info>p[data-v-c2b30052]:last-child{margin-right:0}.current-num-info>p>span[data-v-c2b30052]{font-weight:700;color:#1552f0}\n",document.head.appendChild(M),{setters:[e=>{t=e._,i=e.d,r=e.u,o=e.m,s=e.i,c=e.r,a=e.T,l=e.B,u=e.cc,f=e.cb,d=e.av,A=e.c,g=e.e,h=e.w,p=e.a,m=e.x,v=e.L,w=e.b,b=e.f,x=e.t,N=e.F,U=e.y,C=e.o,B=e.n,y=e.aF,Q=e.D,R=e.E},e=>{k=e.i,E=e.l},e=>{S=e.c}],execute:function(){
/*!
       *  decimal.js v10.4.3
       *  An arbitrary-precision Decimal type for JavaScript.
       *  https://github.com/MikeMcl/decimal.js
       *  Copyright (c) 2022 Michael Mclaughlin <<EMAIL>>
       *  MIT Licence
       */
var n,M,D=9e15,F=1e9,J="0123456789abcdef",K="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",I="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",Y={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-D,maxE:D,crypto:!1},T=!0,V="[DecimalError] ",z=V+"Invalid argument: ",H=V+"Precision limit exceeded",P=V+"crypto unavailable",O="[object Decimal]",X=Math.floor,Z=Math.pow,L=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,j=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,W=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,G=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,q=1e7,_=7,$=K.length-1,ee=I.length-1,ne={toStringTag:O};function te(e){var n,t,i,r=e.length-1,o="",s=e[0];if(r>0){for(o+=s,n=1;n<r;n++)i=e[n]+"",(t=_-i.length)&&(o+=Ae(t)),o+=i;s=e[n],(t=_-(i=s+"").length)&&(o+=Ae(t))}else if(0===s)return"0";for(;s%10==0;)s/=10;return o+s}function ie(e,n,t){if(e!==~~e||e<n||e>t)throw Error(z+e)}function re(e,n,t,i){var r,o,s,c;for(o=e[0];o>=10;o/=10)--n;return--n<0?(n+=_,r=0):(r=Math.ceil((n+1)/_),n%=_),o=Z(10,_-n),c=e[r]%o|0,null==i?n<3?(0==n?c=c/100|0:1==n&&(c=c/10|0),s=t<4&&99999==c||t>3&&49999==c||5e4==c||0==c):s=(t<4&&c+1==o||t>3&&c+1==o/2)&&(e[r+1]/o/100|0)==Z(10,n-2)-1||(c==o/2||0==c)&&0==(e[r+1]/o/100|0):n<4?(0==n?c=c/1e3|0:1==n?c=c/100|0:2==n&&(c=c/10|0),s=(i||t<4)&&9999==c||!i&&t>3&&4999==c):s=((i||t<4)&&c+1==o||!i&&t>3&&c+1==o/2)&&(e[r+1]/o/1e3|0)==Z(10,n-3)-1,s}function oe(e,n,t){for(var i,r,o=[0],s=0,c=e.length;s<c;){for(r=o.length;r--;)o[r]*=n;for(o[0]+=J.indexOf(e.charAt(s++)),i=0;i<o.length;i++)o[i]>t-1&&(void 0===o[i+1]&&(o[i+1]=0),o[i+1]+=o[i]/t|0,o[i]%=t)}return o.reverse()}ne.absoluteValue=ne.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),ce(e)},ne.ceil=function(){return ce(new this.constructor(this),this.e+1,2)},ne.clampedTo=ne.clamp=function(e,n){var t=this,i=t.constructor;if(e=new i(e),n=new i(n),!e.s||!n.s)return new i(NaN);if(e.gt(n))throw Error(z+n);return t.cmp(e)<0?e:t.cmp(n)>0?n:new i(t)},ne.comparedTo=ne.cmp=function(e){var n,t,i,r,o=this,s=o.d,c=(e=new o.constructor(e)).d,a=o.s,l=e.s;if(!s||!c)return a&&l?a!==l?a:s===c?0:!s^a<0?1:-1:NaN;if(!s[0]||!c[0])return s[0]?a:c[0]?-l:0;if(a!==l)return a;if(o.e!==e.e)return o.e>e.e^a<0?1:-1;for(n=0,t=(i=s.length)<(r=c.length)?i:r;n<t;++n)if(s[n]!==c[n])return s[n]>c[n]^a<0?1:-1;return i===r?0:i>r^a<0?1:-1},ne.cosine=ne.cos=function(){var e,n,t=this,i=t.constructor;return t.d?t.d[0]?(e=i.precision,n=i.rounding,i.precision=e+Math.max(t.e,t.sd())+_,i.rounding=1,t=function(e,n){var t,i,r;if(n.isZero())return n;(i=n.d.length)<32?r=(1/Ue(4,t=Math.ceil(i/3))).toString():(t=16,r="2.3283064365386962890625e-10"),e.precision+=t,n=Ne(e,1,n.times(r),new e(1));for(var o=t;o--;){var s=n.times(n);n=s.times(s).minus(s).times(8).plus(1)}return e.precision-=t,n}(i,Ce(i,t)),i.precision=e,i.rounding=n,ce(2==M||3==M?t.neg():t,e,n,!0)):new i(1):new i(NaN)},ne.cubeRoot=ne.cbrt=function(){var e,n,t,i,r,o,s,c,a,l,u=this,f=u.constructor;if(!u.isFinite()||u.isZero())return new f(u);for(T=!1,(o=u.s*Z(u.s*u,1/3))&&Math.abs(o)!=1/0?i=new f(o.toString()):(t=te(u.d),(o=((e=u.e)-t.length+1)%3)&&(t+=1==o||-2==o?"0":"00"),o=Z(t,1/3),e=X((e+1)/3)-(e%3==(e<0?-1:2)),(i=new f(t=o==1/0?"5e"+e:(t=o.toExponential()).slice(0,t.indexOf("e")+1)+e)).s=u.s),s=(e=f.precision)+3;;)if(l=(a=(c=i).times(c).times(c)).plus(u),i=se(l.plus(u).times(c),l.plus(a),s+2,1),te(c.d).slice(0,s)===(t=te(i.d)).slice(0,s)){if("9999"!=(t=t.slice(s-3,s+1))&&(r||"4999"!=t)){+t&&(+t.slice(1)||"5"!=t.charAt(0))||(ce(i,e+1,1),n=!i.times(i).times(i).eq(u));break}if(!r&&(ce(c,e+1,0),c.times(c).times(c).eq(u))){i=c;break}s+=4,r=1}return T=!0,ce(i,e,f.rounding,n)},ne.decimalPlaces=ne.dp=function(){var e,n=this.d,t=NaN;if(n){if(t=((e=n.length-1)-X(this.e/_))*_,e=n[e])for(;e%10==0;e/=10)t--;t<0&&(t=0)}return t},ne.dividedBy=ne.div=function(e){return se(this,new this.constructor(e))},ne.dividedToIntegerBy=ne.divToInt=function(e){var n=this.constructor;return ce(se(this,new n(e),0,1,1),n.precision,n.rounding)},ne.equals=ne.eq=function(e){return 0===this.cmp(e)},ne.floor=function(){return ce(new this.constructor(this),this.e+1,3)},ne.greaterThan=ne.gt=function(e){return this.cmp(e)>0},ne.greaterThanOrEqualTo=ne.gte=function(e){var n=this.cmp(e);return 1==n||0===n},ne.hyperbolicCosine=ne.cosh=function(){var e,n,t,i,r,o=this,s=o.constructor,c=new s(1);if(!o.isFinite())return new s(o.s?1/0:NaN);if(o.isZero())return c;t=s.precision,i=s.rounding,s.precision=t+Math.max(o.e,o.sd())+4,s.rounding=1,(r=o.d.length)<32?n=(1/Ue(4,e=Math.ceil(r/3))).toString():(e=16,n="2.3283064365386962890625e-10"),o=Ne(s,1,o.times(n),new s(1),!0);for(var a,l=e,u=new s(8);l--;)a=o.times(o),o=c.minus(a.times(u.minus(a.times(u))));return ce(o,s.precision=t,s.rounding=i,!0)},ne.hyperbolicSine=ne.sinh=function(){var e,n,t,i,r=this,o=r.constructor;if(!r.isFinite()||r.isZero())return new o(r);if(n=o.precision,t=o.rounding,o.precision=n+Math.max(r.e,r.sd())+4,o.rounding=1,(i=r.d.length)<3)r=Ne(o,2,r,r,!0);else{e=(e=1.4*Math.sqrt(i))>16?16:0|e,r=Ne(o,2,r=r.times(1/Ue(5,e)),r,!0);for(var s,c=new o(5),a=new o(16),l=new o(20);e--;)s=r.times(r),r=r.times(c.plus(s.times(a.times(s).plus(l))))}return o.precision=n,o.rounding=t,ce(r,n,t,!0)},ne.hyperbolicTangent=ne.tanh=function(){var e,n,t=this,i=t.constructor;return t.isFinite()?t.isZero()?new i(t):(e=i.precision,n=i.rounding,i.precision=e+7,i.rounding=1,se(t.sinh(),t.cosh(),i.precision=e,i.rounding=n)):new i(t.s)},ne.inverseCosine=ne.acos=function(){var e,n=this,t=n.constructor,i=n.abs().cmp(1),r=t.precision,o=t.rounding;return-1!==i?0===i?n.isNeg()?fe(t,r,o):new t(0):new t(NaN):n.isZero()?fe(t,r+4,o).times(.5):(t.precision=r+6,t.rounding=1,n=n.asin(),e=fe(t,r+4,o).times(.5),t.precision=r,t.rounding=o,e.minus(n))},ne.inverseHyperbolicCosine=ne.acosh=function(){var e,n,t=this,i=t.constructor;return t.lte(1)?new i(t.eq(1)?0:NaN):t.isFinite()?(e=i.precision,n=i.rounding,i.precision=e+Math.max(Math.abs(t.e),t.sd())+4,i.rounding=1,T=!1,t=t.times(t).minus(1).sqrt().plus(t),T=!0,i.precision=e,i.rounding=n,t.ln()):new i(t)},ne.inverseHyperbolicSine=ne.asinh=function(){var e,n,t=this,i=t.constructor;return!t.isFinite()||t.isZero()?new i(t):(e=i.precision,n=i.rounding,i.precision=e+2*Math.max(Math.abs(t.e),t.sd())+6,i.rounding=1,T=!1,t=t.times(t).plus(1).sqrt().plus(t),T=!0,i.precision=e,i.rounding=n,t.ln())},ne.inverseHyperbolicTangent=ne.atanh=function(){var e,n,t,i,r=this,o=r.constructor;return r.isFinite()?r.e>=0?new o(r.abs().eq(1)?r.s/0:r.isZero()?r:NaN):(e=o.precision,n=o.rounding,i=r.sd(),Math.max(i,e)<2*-r.e-1?ce(new o(r),e,n,!0):(o.precision=t=i-r.e,r=se(r.plus(1),new o(1).minus(r),t+e,1),o.precision=e+4,o.rounding=1,r=r.ln(),o.precision=e,o.rounding=n,r.times(.5))):new o(NaN)},ne.inverseSine=ne.asin=function(){var e,n,t,i,r=this,o=r.constructor;return r.isZero()?new o(r):(n=r.abs().cmp(1),t=o.precision,i=o.rounding,-1!==n?0===n?((e=fe(o,t+4,i).times(.5)).s=r.s,e):new o(NaN):(o.precision=t+6,o.rounding=1,r=r.div(new o(1).minus(r.times(r)).sqrt().plus(1)).atan(),o.precision=t,o.rounding=i,r.times(2)))},ne.inverseTangent=ne.atan=function(){var e,n,t,i,r,o,s,c,a,l=this,u=l.constructor,f=u.precision,d=u.rounding;if(l.isFinite()){if(l.isZero())return new u(l);if(l.abs().eq(1)&&f+4<=ee)return(s=fe(u,f+4,d).times(.25)).s=l.s,s}else{if(!l.s)return new u(NaN);if(f+4<=ee)return(s=fe(u,f+4,d).times(.5)).s=l.s,s}for(u.precision=c=f+10,u.rounding=1,e=t=Math.min(28,c/_+2|0);e;--e)l=l.div(l.times(l).plus(1).sqrt().plus(1));for(T=!1,n=Math.ceil(c/_),i=1,a=l.times(l),s=new u(l),r=l;-1!==e;)if(r=r.times(a),o=s.minus(r.div(i+=2)),r=r.times(a),void 0!==(s=o.plus(r.div(i+=2))).d[n])for(e=n;s.d[e]===o.d[e]&&e--;);return t&&(s=s.times(2<<t-1)),T=!0,ce(s,u.precision=f,u.rounding=d,!0)},ne.isFinite=function(){return!!this.d},ne.isInteger=ne.isInt=function(){return!!this.d&&X(this.e/_)>this.d.length-2},ne.isNaN=function(){return!this.s},ne.isNegative=ne.isNeg=function(){return this.s<0},ne.isPositive=ne.isPos=function(){return this.s>0},ne.isZero=function(){return!!this.d&&0===this.d[0]},ne.lessThan=ne.lt=function(e){return this.cmp(e)<0},ne.lessThanOrEqualTo=ne.lte=function(e){return this.cmp(e)<1},ne.logarithm=ne.log=function(e){var n,t,i,r,o,s,c,a,l=this,u=l.constructor,f=u.precision,d=u.rounding;if(null==e)e=new u(10),n=!0;else{if(t=(e=new u(e)).d,e.s<0||!t||!t[0]||e.eq(1))return new u(NaN);n=e.eq(10)}if(t=l.d,l.s<0||!t||!t[0]||l.eq(1))return new u(t&&!t[0]?-1/0:1!=l.s?NaN:t?0:1/0);if(n)if(t.length>1)o=!0;else{for(r=t[0];r%10==0;)r/=10;o=1!==r}if(T=!1,s=ve(l,c=f+5),i=n?ue(u,c+10):ve(e,c),re((a=se(s,i,c,1)).d,r=f,d))do{if(s=ve(l,c+=10),i=n?ue(u,c+10):ve(e,c),a=se(s,i,c,1),!o){+te(a.d).slice(r+1,r+15)+1==1e14&&(a=ce(a,f+1,0));break}}while(re(a.d,r+=10,d));return T=!0,ce(a,f,d)},ne.minus=ne.sub=function(e){var n,t,i,r,o,s,c,a,l,u,f,d,A=this,g=A.constructor;if(e=new g(e),!A.d||!e.d)return A.s&&e.s?A.d?e.s=-e.s:e=new g(e.d||A.s!==e.s?A:NaN):e=new g(NaN),e;if(A.s!=e.s)return e.s=-e.s,A.plus(e);if(l=A.d,d=e.d,c=g.precision,a=g.rounding,!l[0]||!d[0]){if(d[0])e.s=-e.s;else{if(!l[0])return new g(3===a?-0:0);e=new g(A)}return T?ce(e,c,a):e}if(t=X(e.e/_),u=X(A.e/_),l=l.slice(),o=u-t){for((f=o<0)?(n=l,o=-o,s=d.length):(n=d,t=u,s=l.length),o>(i=Math.max(Math.ceil(c/_),s)+2)&&(o=i,n.length=1),n.reverse(),i=o;i--;)n.push(0);n.reverse()}else{for((f=(i=l.length)<(s=d.length))&&(s=i),i=0;i<s;i++)if(l[i]!=d[i]){f=l[i]<d[i];break}o=0}for(f&&(n=l,l=d,d=n,e.s=-e.s),s=l.length,i=d.length-s;i>0;--i)l[s++]=0;for(i=d.length;i>o;){if(l[--i]<d[i]){for(r=i;r&&0===l[--r];)l[r]=q-1;--l[r],l[i]+=q}l[i]-=d[i]}for(;0===l[--s];)l.pop();for(;0===l[0];l.shift())--t;return l[0]?(e.d=l,e.e=le(l,t),T?ce(e,c,a):e):new g(3===a?-0:0)},ne.modulo=ne.mod=function(e){var n,t=this,i=t.constructor;return e=new i(e),!t.d||!e.s||e.d&&!e.d[0]?new i(NaN):!e.d||t.d&&!t.d[0]?ce(new i(t),i.precision,i.rounding):(T=!1,9==i.modulo?(n=se(t,e.abs(),0,3,1)).s*=e.s:n=se(t,e,0,i.modulo,1),n=n.times(e),T=!0,t.minus(n))},ne.naturalExponential=ne.exp=function(){return me(this)},ne.naturalLogarithm=ne.ln=function(){return ve(this)},ne.negated=ne.neg=function(){var e=new this.constructor(this);return e.s=-e.s,ce(e)},ne.plus=ne.add=function(e){var n,t,i,r,o,s,c,a,l,u,f=this,d=f.constructor;if(e=new d(e),!f.d||!e.d)return f.s&&e.s?f.d||(e=new d(e.d||f.s===e.s?f:NaN)):e=new d(NaN),e;if(f.s!=e.s)return e.s=-e.s,f.minus(e);if(l=f.d,u=e.d,c=d.precision,a=d.rounding,!l[0]||!u[0])return u[0]||(e=new d(f)),T?ce(e,c,a):e;if(o=X(f.e/_),i=X(e.e/_),l=l.slice(),r=o-i){for(r<0?(t=l,r=-r,s=u.length):(t=u,i=o,s=l.length),r>(s=(o=Math.ceil(c/_))>s?o+1:s+1)&&(r=s,t.length=1),t.reverse();r--;)t.push(0);t.reverse()}for((s=l.length)-(r=u.length)<0&&(r=s,t=u,u=l,l=t),n=0;r;)n=(l[--r]=l[r]+u[r]+n)/q|0,l[r]%=q;for(n&&(l.unshift(n),++i),s=l.length;0==l[--s];)l.pop();return e.d=l,e.e=le(l,i),T?ce(e,c,a):e},ne.precision=ne.sd=function(e){var n,t=this;if(void 0!==e&&e!==!!e&&1!==e&&0!==e)throw Error(z+e);return t.d?(n=de(t.d),e&&t.e+1>n&&(n=t.e+1)):n=NaN,n},ne.round=function(){var e=this,n=e.constructor;return ce(new n(e),e.e+1,n.rounding)},ne.sine=ne.sin=function(){var e,n,t=this,i=t.constructor;return t.isFinite()?t.isZero()?new i(t):(e=i.precision,n=i.rounding,i.precision=e+Math.max(t.e,t.sd())+_,i.rounding=1,t=function(e,n){var t,i=n.d.length;if(i<3)return n.isZero()?n:Ne(e,2,n,n);t=(t=1.4*Math.sqrt(i))>16?16:0|t,n=n.times(1/Ue(5,t)),n=Ne(e,2,n,n);for(var r,o=new e(5),s=new e(16),c=new e(20);t--;)r=n.times(n),n=n.times(o.plus(r.times(s.times(r).minus(c))));return n}(i,Ce(i,t)),i.precision=e,i.rounding=n,ce(M>2?t.neg():t,e,n,!0)):new i(NaN)},ne.squareRoot=ne.sqrt=function(){var e,n,t,i,r,o,s=this,c=s.d,a=s.e,l=s.s,u=s.constructor;if(1!==l||!c||!c[0])return new u(!l||l<0&&(!c||c[0])?NaN:c?s:1/0);for(T=!1,0==(l=Math.sqrt(+s))||l==1/0?(((n=te(c)).length+a)%2==0&&(n+="0"),l=Math.sqrt(n),a=X((a+1)/2)-(a<0||a%2),i=new u(n=l==1/0?"5e"+a:(n=l.toExponential()).slice(0,n.indexOf("e")+1)+a)):i=new u(l.toString()),t=(a=u.precision)+3;;)if(i=(o=i).plus(se(s,o,t+2,1)).times(.5),te(o.d).slice(0,t)===(n=te(i.d)).slice(0,t)){if("9999"!=(n=n.slice(t-3,t+1))&&(r||"4999"!=n)){+n&&(+n.slice(1)||"5"!=n.charAt(0))||(ce(i,a+1,1),e=!i.times(i).eq(s));break}if(!r&&(ce(o,a+1,0),o.times(o).eq(s))){i=o;break}t+=4,r=1}return T=!0,ce(i,a,u.rounding,e)},ne.tangent=ne.tan=function(){var e,n,t=this,i=t.constructor;return t.isFinite()?t.isZero()?new i(t):(e=i.precision,n=i.rounding,i.precision=e+10,i.rounding=1,(t=t.sin()).s=1,t=se(t,new i(1).minus(t.times(t)).sqrt(),e+10,0),i.precision=e,i.rounding=n,ce(2==M||4==M?t.neg():t,e,n,!0)):new i(NaN)},ne.times=ne.mul=function(e){var n,t,i,r,o,s,c,a,l,u=this,f=u.constructor,d=u.d,A=(e=new f(e)).d;if(e.s*=u.s,!(d&&d[0]&&A&&A[0]))return new f(!e.s||d&&!d[0]&&!A||A&&!A[0]&&!d?NaN:d&&A?0*e.s:e.s/0);for(t=X(u.e/_)+X(e.e/_),(a=d.length)<(l=A.length)&&(o=d,d=A,A=o,s=a,a=l,l=s),o=[],i=s=a+l;i--;)o.push(0);for(i=l;--i>=0;){for(n=0,r=a+i;r>i;)c=o[r]+A[i]*d[r-i-1]+n,o[r--]=c%q|0,n=c/q|0;o[r]=(o[r]+n)%q|0}for(;!o[--s];)o.pop();return n?++t:o.shift(),e.d=o,e.e=le(o,t),T?ce(e,f.precision,f.rounding):e},ne.toBinary=function(e,n){return Be(this,2,e,n)},ne.toDecimalPlaces=ne.toDP=function(e,n){var t=this,i=t.constructor;return t=new i(t),void 0===e?t:(ie(e,0,F),void 0===n?n=i.rounding:ie(n,0,8),ce(t,e+t.e+1,n))},ne.toExponential=function(e,n){var t,i=this,r=i.constructor;return void 0===e?t=ae(i,!0):(ie(e,0,F),void 0===n?n=r.rounding:ie(n,0,8),t=ae(i=ce(new r(i),e+1,n),!0,e+1)),i.isNeg()&&!i.isZero()?"-"+t:t},ne.toFixed=function(e,n){var t,i,r=this,o=r.constructor;return void 0===e?t=ae(r):(ie(e,0,F),void 0===n?n=o.rounding:ie(n,0,8),t=ae(i=ce(new o(r),e+r.e+1,n),!1,e+i.e+1)),r.isNeg()&&!r.isZero()?"-"+t:t},ne.toFraction=function(e){var n,t,i,r,o,s,c,a,l,u,f,d,A=this,g=A.d,h=A.constructor;if(!g)return new h(A);if(l=t=new h(1),i=a=new h(0),s=(o=(n=new h(i)).e=de(g)-A.e-1)%_,n.d[0]=Z(10,s<0?_+s:s),null==e)e=o>0?n:l;else{if(!(c=new h(e)).isInt()||c.lt(l))throw Error(z+c);e=c.gt(n)?o>0?n:l:c}for(T=!1,c=new h(te(g)),u=h.precision,h.precision=o=g.length*_*2;f=se(c,n,0,1,1),1!=(r=t.plus(f.times(i))).cmp(e);)t=i,i=r,r=l,l=a.plus(f.times(r)),a=r,r=n,n=c.minus(f.times(r)),c=r;return r=se(e.minus(t),i,0,1,1),a=a.plus(r.times(l)),t=t.plus(r.times(i)),a.s=l.s=A.s,d=se(l,i,o,1).minus(A).abs().cmp(se(a,t,o,1).minus(A).abs())<1?[l,i]:[a,t],h.precision=u,T=!0,d},ne.toHexadecimal=ne.toHex=function(e,n){return Be(this,16,e,n)},ne.toNearest=function(e,n){var t=this,i=t.constructor;if(t=new i(t),null==e){if(!t.d)return t;e=new i(1),n=i.rounding}else{if(e=new i(e),void 0===n?n=i.rounding:ie(n,0,8),!t.d)return e.s?t:e;if(!e.d)return e.s&&(e.s=t.s),e}return e.d[0]?(T=!1,t=se(t,e,0,n,1).times(e),T=!0,ce(t)):(e.s=t.s,t=e),t},ne.toNumber=function(){return+this},ne.toOctal=function(e,n){return Be(this,8,e,n)},ne.toPower=ne.pow=function(e){var n,t,i,r,o,s,c=this,a=c.constructor,l=+(e=new a(e));if(!(c.d&&e.d&&c.d[0]&&e.d[0]))return new a(Z(+c,l));if((c=new a(c)).eq(1))return c;if(i=a.precision,o=a.rounding,e.eq(1))return ce(c,i,o);if((n=X(e.e/_))>=e.d.length-1&&(t=l<0?-l:l)<=9007199254740991)return r=ge(a,c,t,i),e.s<0?new a(1).div(r):ce(r,i,o);if((s=c.s)<0){if(n<e.d.length-1)return new a(NaN);if(0==(1&e.d[n])&&(s=1),0==c.e&&1==c.d[0]&&1==c.d.length)return c.s=s,c}return(n=0!=(t=Z(+c,l))&&isFinite(t)?new a(t+"").e:X(l*(Math.log("0."+te(c.d))/Math.LN10+c.e+1)))>a.maxE+1||n<a.minE-1?new a(n>0?s/0:0):(T=!1,a.rounding=c.s=1,t=Math.min(12,(n+"").length),(r=me(e.times(ve(c,i+t)),i)).d&&re((r=ce(r,i+5,1)).d,i,o)&&(n=i+10,+te((r=ce(me(e.times(ve(c,n+t)),n),n+5,1)).d).slice(i+1,i+15)+1==1e14&&(r=ce(r,i+1,0))),r.s=s,T=!0,a.rounding=o,ce(r,i,o))},ne.toPrecision=function(e,n){var t,i=this,r=i.constructor;return void 0===e?t=ae(i,i.e<=r.toExpNeg||i.e>=r.toExpPos):(ie(e,1,F),void 0===n?n=r.rounding:ie(n,0,8),t=ae(i=ce(new r(i),e,n),e<=i.e||i.e<=r.toExpNeg,e)),i.isNeg()&&!i.isZero()?"-"+t:t},ne.toSignificantDigits=ne.toSD=function(e,n){var t=this.constructor;return void 0===e?(e=t.precision,n=t.rounding):(ie(e,1,F),void 0===n?n=t.rounding:ie(n,0,8)),ce(new t(this),e,n)},ne.toString=function(){var e=this,n=e.constructor,t=ae(e,e.e<=n.toExpNeg||e.e>=n.toExpPos);return e.isNeg()&&!e.isZero()?"-"+t:t},ne.truncated=ne.trunc=function(){return ce(new this.constructor(this),this.e+1,1)},ne.valueOf=ne.toJSON=function(){var e=this,n=e.constructor,t=ae(e,e.e<=n.toExpNeg||e.e>=n.toExpPos);return e.isNeg()?"-"+t:t};var se=function(){function e(e,n,t){var i,r=0,o=e.length;for(e=e.slice();o--;)i=e[o]*n+r,e[o]=i%t|0,r=i/t|0;return r&&e.unshift(r),e}function t(e,n,t,i){var r,o;if(t!=i)o=t>i?1:-1;else for(r=o=0;r<t;r++)if(e[r]!=n[r]){o=e[r]>n[r]?1:-1;break}return o}function i(e,n,t,i){for(var r=0;t--;)e[t]-=r,r=e[t]<n[t]?1:0,e[t]=r*i+e[t]-n[t];for(;!e[0]&&e.length>1;)e.shift()}return function(r,o,s,c,a,l){var u,f,d,A,g,h,p,m,v,w,b,x,N,U,C,B,y,Q,R,k,E=r.constructor,S=r.s==o.s?1:-1,M=r.d,D=o.d;if(!(M&&M[0]&&D&&D[0]))return new E(r.s&&o.s&&(M?!D||M[0]!=D[0]:D)?M&&0==M[0]||!D?0*S:S/0:NaN);for(l?(g=1,f=r.e-o.e):(l=q,g=_,f=X(r.e/g)-X(o.e/g)),R=D.length,y=M.length,w=(v=new E(S)).d=[],d=0;D[d]==(M[d]||0);d++);if(D[d]>(M[d]||0)&&f--,null==s?(U=s=E.precision,c=E.rounding):U=a?s+(r.e-o.e)+1:s,U<0)w.push(1),h=!0;else{if(U=U/g+2|0,d=0,1==R){for(A=0,D=D[0],U++;(d<y||A)&&U--;d++)C=A*l+(M[d]||0),w[d]=C/D|0,A=C%D|0;h=A||d<y}else{for((A=l/(D[0]+1)|0)>1&&(D=e(D,A,l),M=e(M,A,l),R=D.length,y=M.length),B=R,x=(b=M.slice(0,R)).length;x<R;)b[x++]=0;(k=D.slice()).unshift(0),Q=D[0],D[1]>=l/2&&++Q;do{A=0,(u=t(D,b,R,x))<0?(N=b[0],R!=x&&(N=N*l+(b[1]||0)),(A=N/Q|0)>1?(A>=l&&(A=l-1),1==(u=t(p=e(D,A,l),b,m=p.length,x=b.length))&&(A--,i(p,R<m?k:D,m,l))):(0==A&&(u=A=1),p=D.slice()),(m=p.length)<x&&p.unshift(0),i(b,p,x,l),-1==u&&(u=t(D,b,R,x=b.length))<1&&(A++,i(b,R<x?k:D,x,l)),x=b.length):0===u&&(A++,b=[0]),w[d++]=A,u&&b[0]?b[x++]=M[B]||0:(b=[M[B]],x=1)}while((B++<y||void 0!==b[0])&&U--);h=void 0!==b[0]}w[0]||w.shift()}if(1==g)v.e=f,n=h;else{for(d=1,A=w[0];A>=10;A/=10)d++;v.e=d+f*g-1,ce(v,a?s+v.e+1:s,c,h)}return v}}();function ce(e,n,t,i){var r,o,s,c,a,l,u,f,d,A=e.constructor;e:if(null!=n){if(!(f=e.d))return e;for(r=1,c=f[0];c>=10;c/=10)r++;if((o=n-r)<0)o+=_,s=n,a=(u=f[d=0])/Z(10,r-s-1)%10|0;else if((d=Math.ceil((o+1)/_))>=(c=f.length)){if(!i)break e;for(;c++<=d;)f.push(0);u=a=0,r=1,s=(o%=_)-_+1}else{for(u=c=f[d],r=1;c>=10;c/=10)r++;a=(s=(o%=_)-_+r)<0?0:u/Z(10,r-s-1)%10|0}if(i=i||n<0||void 0!==f[d+1]||(s<0?u:u%Z(10,r-s-1)),l=t<4?(a||i)&&(0==t||t==(e.s<0?3:2)):a>5||5==a&&(4==t||i||6==t&&(o>0?s>0?u/Z(10,r-s):0:f[d-1])%10&1||t==(e.s<0?8:7)),n<1||!f[0])return f.length=0,l?(n-=e.e+1,f[0]=Z(10,(_-n%_)%_),e.e=-n||0):f[0]=e.e=0,e;if(0==o?(f.length=d,c=1,d--):(f.length=d+1,c=Z(10,_-o),f[d]=s>0?(u/Z(10,r-s)%Z(10,s)|0)*c:0),l)for(;;){if(0==d){for(o=1,s=f[0];s>=10;s/=10)o++;for(s=f[0]+=c,c=1;s>=10;s/=10)c++;o!=c&&(e.e++,f[0]==q&&(f[0]=1));break}if(f[d]+=c,f[d]!=q)break;f[d--]=0,c=1}for(o=f.length;0===f[--o];)f.pop()}return T&&(e.e>A.maxE?(e.d=null,e.e=NaN):e.e<A.minE&&(e.e=0,e.d=[0])),e}function ae(e,n,t){if(!e.isFinite())return we(e);var i,r=e.e,o=te(e.d),s=o.length;return n?(t&&(i=t-s)>0?o=o.charAt(0)+"."+o.slice(1)+Ae(i):s>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(e.e<0?"e":"e+")+e.e):r<0?(o="0."+Ae(-r-1)+o,t&&(i=t-s)>0&&(o+=Ae(i))):r>=s?(o+=Ae(r+1-s),t&&(i=t-r-1)>0&&(o=o+"."+Ae(i))):((i=r+1)<s&&(o=o.slice(0,i)+"."+o.slice(i)),t&&(i=t-s)>0&&(r+1===s&&(o+="."),o+=Ae(i))),o}function le(e,n){var t=e[0];for(n*=_;t>=10;t/=10)n++;return n}function ue(e,n,t){if(n>$)throw T=!0,t&&(e.precision=t),Error(H);return ce(new e(K),n,1,!0)}function fe(e,n,t){if(n>ee)throw Error(H);return ce(new e(I),n,t,!0)}function de(e){var n=e.length-1,t=n*_+1;if(n=e[n]){for(;n%10==0;n/=10)t--;for(n=e[0];n>=10;n/=10)t++}return t}function Ae(e){for(var n="";e--;)n+="0";return n}function ge(e,n,t,i){var r,o=new e(1),s=Math.ceil(i/_+4);for(T=!1;;){if(t%2&&ye((o=o.times(n)).d,s)&&(r=!0),0===(t=X(t/2))){t=o.d.length-1,r&&0===o.d[t]&&++o.d[t];break}ye((n=n.times(n)).d,s)}return T=!0,o}function he(e){return 1&e.d[e.d.length-1]}function pe(e,n,t){for(var i,r=new e(n[0]),o=0;++o<n.length;){if(!(i=new e(n[o])).s){r=i;break}r[t](i)&&(r=i)}return r}function me(e,n){var t,i,r,o,s,c,a,l=0,u=0,f=0,d=e.constructor,A=d.rounding,g=d.precision;if(!e.d||!e.d[0]||e.e>17)return new d(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(null==n?(T=!1,a=g):a=n,c=new d(.03125);e.e>-2;)e=e.times(c),f+=5;for(a+=i=Math.log(Z(2,f))/Math.LN10*2+5|0,t=o=s=new d(1),d.precision=a;;){if(o=ce(o.times(e),a,1),t=t.times(++u),te((c=s.plus(se(o,t,a,1))).d).slice(0,a)===te(s.d).slice(0,a)){for(r=f;r--;)s=ce(s.times(s),a,1);if(null!=n)return d.precision=g,s;if(!(l<3&&re(s.d,a-i,A,l)))return ce(s,d.precision=g,A,T=!0);d.precision=a+=10,t=o=c=new d(1),u=0,l++}s=c}}function ve(e,n){var t,i,r,o,s,c,a,l,u,f,d,A=1,g=e,h=g.d,p=g.constructor,m=p.rounding,v=p.precision;if(g.s<0||!h||!h[0]||!g.e&&1==h[0]&&1==h.length)return new p(h&&!h[0]?-1/0:1!=g.s?NaN:h?0:g);if(null==n?(T=!1,u=v):u=n,p.precision=u+=10,i=(t=te(h)).charAt(0),!(Math.abs(o=g.e)<15e14))return l=ue(p,u+2,v).times(o+""),g=ve(new p(i+"."+t.slice(1)),u-10).plus(l),p.precision=v,null==n?ce(g,v,m,T=!0):g;for(;i<7&&1!=i||1==i&&t.charAt(1)>3;)i=(t=te((g=g.times(e)).d)).charAt(0),A++;for(o=g.e,i>1?(g=new p("0."+t),o++):g=new p(i+"."+t.slice(1)),f=g,a=s=g=se(g.minus(1),g.plus(1),u,1),d=ce(g.times(g),u,1),r=3;;){if(s=ce(s.times(d),u,1),te((l=a.plus(se(s,new p(r),u,1))).d).slice(0,u)===te(a.d).slice(0,u)){if(a=a.times(2),0!==o&&(a=a.plus(ue(p,u+2,v).times(o+""))),a=se(a,new p(A),u,1),null!=n)return p.precision=v,a;if(!re(a.d,u-10,m,c))return ce(a,p.precision=v,m,T=!0);p.precision=u+=10,l=s=g=se(f.minus(1),f.plus(1),u,1),d=ce(g.times(g),u,1),r=c=1}a=l,r+=2}}function we(e){return String(e.s*e.s/0)}function be(e,n){var t,i,r;for((t=n.indexOf("."))>-1&&(n=n.replace(".","")),(i=n.search(/e/i))>0?(t<0&&(t=i),t+=+n.slice(i+1),n=n.substring(0,i)):t<0&&(t=n.length),i=0;48===n.charCodeAt(i);i++);for(r=n.length;48===n.charCodeAt(r-1);--r);if(n=n.slice(i,r)){if(r-=i,e.e=t=t-i-1,e.d=[],i=(t+1)%_,t<0&&(i+=_),i<r){for(i&&e.d.push(+n.slice(0,i)),r-=_;i<r;)e.d.push(+n.slice(i,i+=_));n=n.slice(i),i=_-n.length}else i-=r;for(;i--;)n+="0";e.d.push(+n),T&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function xe(e,n){var t,i,r,o,s,c,a,l,u;if(n.indexOf("_")>-1){if(n=n.replace(/(\d)_(?=\d)/g,"$1"),G.test(n))return be(e,n)}else if("Infinity"===n||"NaN"===n)return+n||(e.s=NaN),e.e=NaN,e.d=null,e;if(j.test(n))t=16,n=n.toLowerCase();else if(L.test(n))t=2;else{if(!W.test(n))throw Error(z+n);t=8}for((o=n.search(/p/i))>0?(a=+n.slice(o+1),n=n.substring(2,o)):n=n.slice(2),s=(o=n.indexOf("."))>=0,i=e.constructor,s&&(o=(c=(n=n.replace(".","")).length)-o,r=ge(i,new i(t),o,2*o)),o=u=(l=oe(n,t,q)).length-1;0===l[o];--o)l.pop();return o<0?new i(0*e.s):(e.e=le(l,u),e.d=l,T=!1,s&&(e=se(e,r,4*c)),a&&(e=e.times(Math.abs(a)<54?Z(2,a):gn.pow(2,a))),T=!0,e)}function Ne(e,n,t,i,r){var o,s,c,a,l=e.precision,u=Math.ceil(l/_);for(T=!1,a=t.times(t),c=new e(i);;){if(s=se(c.times(a),new e(n++*n++),l,1),c=r?i.plus(s):i.minus(s),i=se(s.times(a),new e(n++*n++),l,1),void 0!==(s=c.plus(i)).d[u]){for(o=u;s.d[o]===c.d[o]&&o--;);if(-1==o)break}o=c,c=i,i=s,s=o}return T=!0,s.d.length=u+1,s}function Ue(e,n){for(var t=e;--n;)t*=e;return t}function Ce(e,n){var t,i=n.s<0,r=fe(e,e.precision,1),o=r.times(.5);if((n=n.abs()).lte(o))return M=i?4:1,n;if((t=n.divToInt(r)).isZero())M=i?3:2;else{if((n=n.minus(t.times(r))).lte(o))return M=he(t)?i?2:3:i?4:1,n;M=he(t)?i?1:4:i?3:2}return n.minus(r).abs()}function Be(e,t,i,r){var o,s,c,a,l,u,f,d,A,g=e.constructor,h=void 0!==i;if(h?(ie(i,1,F),void 0===r?r=g.rounding:ie(r,0,8)):(i=g.precision,r=g.rounding),e.isFinite()){for(h?(o=2,16==t?i=4*i-3:8==t&&(i=3*i-2)):o=t,(c=(f=ae(e)).indexOf("."))>=0&&(f=f.replace(".",""),(A=new g(1)).e=f.length-c,A.d=oe(ae(A),10,o),A.e=A.d.length),s=l=(d=oe(f,10,o)).length;0==d[--l];)d.pop();if(d[0]){if(c<0?s--:((e=new g(e)).d=d,e.e=s,d=(e=se(e,A,i,r,0,o)).d,s=e.e,u=n),c=d[i],a=o/2,u=u||void 0!==d[i+1],u=r<4?(void 0!==c||u)&&(0===r||r===(e.s<0?3:2)):c>a||c===a&&(4===r||u||6===r&&1&d[i-1]||r===(e.s<0?8:7)),d.length=i,u)for(;++d[--i]>o-1;)d[i]=0,i||(++s,d.unshift(1));for(l=d.length;!d[l-1];--l);for(c=0,f="";c<l;c++)f+=J.charAt(d[c]);if(h){if(l>1)if(16==t||8==t){for(c=16==t?4:3,--l;l%c;l++)f+="0";for(l=(d=oe(f,o,t)).length;!d[l-1];--l);for(c=1,f="1.";c<l;c++)f+=J.charAt(d[c])}else f=f.charAt(0)+"."+f.slice(1);f=f+(s<0?"p":"p+")+s}else if(s<0){for(;++s;)f="0"+f;f="0."+f}else if(++s>l)for(s-=l;s--;)f+="0";else s<l&&(f=f.slice(0,s)+"."+f.slice(s))}else f=h?"0p+0":"0";f=(16==t?"0x":2==t?"0b":8==t?"0o":"")+f}else f=we(e);return e.s<0?"-"+f:f}function ye(e,n){if(e.length>n)return e.length=n,!0}function Qe(e){return new this(e).abs()}function Re(e){return new this(e).acos()}function ke(e){return new this(e).acosh()}function Ee(e,n){return new this(e).plus(n)}function Se(e){return new this(e).asin()}function Me(e){return new this(e).asinh()}function De(e){return new this(e).atan()}function Fe(e){return new this(e).atanh()}function Je(e,n){e=new this(e),n=new this(n);var t,i=this.precision,r=this.rounding,o=i+4;return e.s&&n.s?e.d||n.d?!n.d||e.isZero()?(t=n.s<0?fe(this,i,r):new this(0)).s=e.s:!e.d||n.isZero()?(t=fe(this,o,1).times(.5)).s=e.s:n.s<0?(this.precision=o,this.rounding=1,t=this.atan(se(e,n,o,1)),n=fe(this,o,1),this.precision=i,this.rounding=r,t=e.s<0?t.minus(n):t.plus(n)):t=this.atan(se(e,n,o,1)):(t=fe(this,o,1).times(n.s>0?.25:.75)).s=e.s:t=new this(NaN),t}function Ke(e){return new this(e).cbrt()}function Ie(e){return ce(e=new this(e),e.e+1,2)}function Ye(e,n,t){return new this(e).clamp(n,t)}function Te(e){if(!e||"object"!=typeof e)throw Error(V+"Object expected");var n,t,i,r=!0===e.defaults,o=["precision",1,F,"rounding",0,8,"toExpNeg",-D,0,"toExpPos",0,D,"maxE",0,D,"minE",-D,0,"modulo",0,9];for(n=0;n<o.length;n+=3)if(t=o[n],r&&(this[t]=Y[t]),void 0!==(i=e[t])){if(!(X(i)===i&&i>=o[n+1]&&i<=o[n+2]))throw Error(z+t+": "+i);this[t]=i}if(t="crypto",r&&(this[t]=Y[t]),void 0!==(i=e[t])){if(!0!==i&&!1!==i&&0!==i&&1!==i)throw Error(z+t+": "+i);if(i){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw Error(P);this[t]=!0}else this[t]=!1}return this}function Ve(e){return new this(e).cos()}function ze(e){return new this(e).cosh()}function He(e,n){return new this(e).div(n)}function Pe(e){return new this(e).exp()}function Oe(e){return ce(e=new this(e),e.e+1,3)}function Xe(){var e,n,t=new this(0);for(T=!1,e=0;e<arguments.length;)if((n=new this(arguments[e++])).d)t.d&&(t=t.plus(n.times(n)));else{if(n.s)return T=!0,new this(1/0);t=n}return T=!0,t.sqrt()}function Ze(e){return e instanceof gn||e&&e.toStringTag===O||!1}function Le(e){return new this(e).ln()}function je(e,n){return new this(e).log(n)}function We(e){return new this(e).log(2)}function Ge(e){return new this(e).log(10)}function qe(){return pe(this,arguments,"lt")}function _e(){return pe(this,arguments,"gt")}function $e(e,n){return new this(e).mod(n)}function en(e,n){return new this(e).mul(n)}function nn(e,n){return new this(e).pow(n)}function tn(e){var n,t,i,r,o=0,s=new this(1),c=[];if(void 0===e?e=this.precision:ie(e,1,F),i=Math.ceil(e/_),this.crypto)if(crypto.getRandomValues)for(n=crypto.getRandomValues(new Uint32Array(i));o<i;)(r=n[o])>=429e7?n[o]=crypto.getRandomValues(new Uint32Array(1))[0]:c[o++]=r%1e7;else{if(!crypto.randomBytes)throw Error(P);for(n=crypto.randomBytes(i*=4);o<i;)(r=n[o]+(n[o+1]<<8)+(n[o+2]<<16)+((127&n[o+3])<<24))>=214e7?crypto.randomBytes(4).copy(n,o):(c.push(r%1e7),o+=4);o=i/4}else for(;o<i;)c[o++]=1e7*Math.random()|0;for(i=c[--o],e%=_,i&&e&&(r=Z(10,_-e),c[o]=(i/r|0)*r);0===c[o];o--)c.pop();if(o<0)t=0,c=[0];else{for(t=-1;0===c[0];t-=_)c.shift();for(i=1,r=c[0];r>=10;r/=10)i++;i<_&&(t-=_-i)}return s.e=t,s.d=c,s}function rn(e){return ce(e=new this(e),e.e+1,this.rounding)}function on(e){return(e=new this(e)).d?e.d[0]?e.s:0*e.s:e.s||NaN}function sn(e){return new this(e).sin()}function cn(e){return new this(e).sinh()}function an(e){return new this(e).sqrt()}function ln(e,n){return new this(e).sub(n)}function un(){var e=0,n=arguments,t=new this(n[e]);for(T=!1;t.s&&++e<n.length;)t=t.plus(n[e]);return T=!0,ce(t,this.precision,this.rounding)}function fn(e){return new this(e).tan()}function dn(e){return new this(e).tanh()}function An(e){return ce(e=new this(e),e.e+1,1)}ne[Symbol.for("nodejs.util.inspect.custom")]=ne.toString,ne[Symbol.toStringTag]="Decimal";var gn=ne.constructor=function e(n){var t,i,r;function o(e){var n,t,i,r=this;if(!(r instanceof o))return new o(e);if(r.constructor=o,Ze(e))return r.s=e.s,void(T?!e.d||e.e>o.maxE?(r.e=NaN,r.d=null):e.e<o.minE?(r.e=0,r.d=[0]):(r.e=e.e,r.d=e.d.slice()):(r.e=e.e,r.d=e.d?e.d.slice():e.d));if("number"==(i=typeof e)){if(0===e)return r.s=1/e<0?-1:1,r.e=0,void(r.d=[0]);if(e<0?(e=-e,r.s=-1):r.s=1,e===~~e&&e<1e7){for(n=0,t=e;t>=10;t/=10)n++;return void(T?n>o.maxE?(r.e=NaN,r.d=null):n<o.minE?(r.e=0,r.d=[0]):(r.e=n,r.d=[e]):(r.e=n,r.d=[e]))}return 0*e!=0?(e||(r.s=NaN),r.e=NaN,void(r.d=null)):be(r,e.toString())}if("string"!==i)throw Error(z+e);return 45===(t=e.charCodeAt(0))?(e=e.slice(1),r.s=-1):(43===t&&(e=e.slice(1)),r.s=1),G.test(e)?be(r,e):xe(r,e)}if(o.prototype=ne,o.ROUND_UP=0,o.ROUND_DOWN=1,o.ROUND_CEIL=2,o.ROUND_FLOOR=3,o.ROUND_HALF_UP=4,o.ROUND_HALF_DOWN=5,o.ROUND_HALF_EVEN=6,o.ROUND_HALF_CEIL=7,o.ROUND_HALF_FLOOR=8,o.EUCLID=9,o.config=o.set=Te,o.clone=e,o.isDecimal=Ze,o.abs=Qe,o.acos=Re,o.acosh=ke,o.add=Ee,o.asin=Se,o.asinh=Me,o.atan=De,o.atanh=Fe,o.atan2=Je,o.cbrt=Ke,o.ceil=Ie,o.clamp=Ye,o.cos=Ve,o.cosh=ze,o.div=He,o.exp=Pe,o.floor=Oe,o.hypot=Xe,o.ln=Le,o.log=je,o.log10=Ge,o.log2=We,o.max=qe,o.min=_e,o.mod=$e,o.mul=en,o.pow=nn,o.random=tn,o.round=rn,o.sign=on,o.sin=sn,o.sinh=cn,o.sqrt=an,o.sub=ln,o.sum=un,o.tan=fn,o.tanh=dn,o.trunc=An,void 0===n&&(n={}),n&&!0!==n.defaults)for(r=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],t=0;t<r.length;)n.hasOwnProperty(i=r[t++])||(n[i]=this[i]);return o.config(n),o}(Y);K=new gn(K),I=new gn(I);const hn={class:"seller-level-content"},pn=(e=>(Q("data-v-c2b30052"),e=e(),R(),e))((()=>p("div",{class:"fixed-header-spacer"},null,-1))),mn={class:"info-content"},vn={class:"ads-info"},wn=["innerHTML"],bn=["innerHTML"],xn={class:"current-num-info"},Nn={key:0},Un={key:1},Cn={key:0},Bn=["innerHTML"],yn=["innerHTML"],Qn={key:3},Rn={class:"level-content"},kn={class:"name-content"},En={class:"title"},Sn={key:0},Mn={key:0},Dn={key:1},Fn=["src"],Jn={class:"item"},Kn={class:"title"},In=["src"],Yn={class:"item"},Tn={class:"title"},Vn=["src"],zn={class:"item"},Hn={class:"title"},Pn=["src"],On={class:"item"},Xn={class:"title"},Zn=["src"],Ln={key:0,class:"item"},jn={class:"title"},Wn=["src"],Gn={key:0},qn=["src"],_n={class:"item"},$n={class:"title"},et=["src"],nt=["src"],tt={class:"item"},it={class:"title"},rt=["src"],ot=["src"],st={key:1,class:"item"},ct={class:"title"},at=["src"],lt=["src"],ut=i({name:"SellerLevel"}),ft=Object.assign(ut,{setup(e){const{t:n}=r(),t="tiktokMall",i=o((()=>["inchoi"].includes(t))),Q=o((()=>["shop2u"].includes(t))),R=s(),M=c(0),D=c(0),F=c([...S(k)]);if(i.value){const e=["升级礼金"];for(let n=0;n<F.value.length;n++)if(F.value[n].data&&F.value[n].data.length)for(let t=0;t<F.value[n].data.length;t++)e.includes(F.value[n].data[t].title)&&F.value[n].data.splice(t--,1)}if(["shop2u"].includes(t))for(let r=0;r<F.value.length;r++)if(F.value[r].data&&F.value[r].data.length)for(let e=0;e<F.value[r].data.length;e++)"会员升级"===F.value[r].data[e].title&&(F.value[r].data[e].info="会员升级是通过直属推分店数决会员级别，分店数越高，系统将自动升级。");const J=c(""),K=S(E);if(!["familyShop","sm","shop2u","antMall"].includes(t)){const e=K.findIndex((e=>"SS"===e.name));K.splice(e,1)}if(!["shop2u","antMall"].includes(t)){const e=K.findIndex((e=>"SSS"===e.name));K.splice(e,1)}const I=c([...K]),Y=o((()=>["shop2u"].includes(t))),T=new URL("/www/png/name-89c2b4a1.png",self.location),V={icon_01:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAPJSURBVHgB7VdLUttAEB1/qGJDxVtYKSeIxaeKncUJ4pwA+QZwApsTQE6AOQGwyhJ5xwKwfIIoG9g6sOOb95wee1BmRjJZJcWrkjWWZqaf3nT3TCv1jn8MFfUGRFHUuLu7iyqVSuvl5aWBR4G8GuN/iuejpaWlBBirOTEXofX19QgGu2g2cTVKDOmD3NHFxUWiSqIUoc3NzeDh4eEQzUg/A7ExjKVoZmhnk8kqlUD9VivKE1tYWNg7Pz/P1N8SElWO1UyR5OnpqY/7aZqm1iVpNpuNarXaRrMrJIkM7U6RWl5CGxsb8fPzM5XRinQuLy9P1BxYXV2Nc8RizHGk5iUkypwJmRTEvkCRTL0BUCyAYmeaFO5bLqWshMRnSCagf4BM6FueWq22zzaWcrdgGUmKATGGT4U2n6raBoNM1yCz5TIioG/FvGDsg6sT56DKJMMxEiSqkBCXSgxQ2p5vmZiP8NXb+j99jOoqN6kMt44eLrb8hKDKttwzn/PRMJLjkKQNQl18+XcY6rnGSVAkYqPrJcR1VoY6ygPtY7Z3WJrPvrEgcjQz2Ww4CcE5I92Gg566JkQ6aJpk0LeNK4ShXR2RygO8p0oTX6rX6y3zXV29Zv4JyrCZ+BxZ9q8pFhcXRxIxzNwHqgCce21tjX0jkItwn378K4UkJInMN+Hj4yMnmxLG8g3hN/s+h7YgE5vuJVOyPei9yQV+IfrsmePwf6fIoU0YNgIfodK4uro6YMbNk2fklCVlQ55Qxh9j3/GC6R/EPoJExyRGUvnoycOwMXYS4gYqzUAVgElRt0Gqn48sGPxUMEUgNlPzYT03yQC3HSX5wRdpt7e3Z4gUkuohW49AKDbfw9DINVbUi9jGWDch5JIEuWiSH3BncrNmavjIDgzqiKQ6+S4nvo+Rs9IEiNjBq3fmH5lEM46VG6krErkE3PU9YydbjPTt54lXLZ11OEdYkrZtQu3MTGqYdJoImbHxPPRtyHJgCzSh/PuqzZiSzQ849CW74XA4AKmvavYxqfKAc5nqcHwhIQJfyiPCxJfu7++PC0KY/fq8YOSnqxOjMnfo27P1K3WEBTKc8LbKVA02UBk477EOBC61TR2i5prk+vo6W15e/gGJ6UcNTNJeWVkZ39zcjNQcoB9i7Dc1y20x/Mx5kihVBrHyMMsZXD1UpqeuypTLgzzVxhge9iI+Y9LF1XYpU5oQIVUDS5k49ypR9kIxX9myluuUqVrmKqXDMGyRlIXYH9CVLdTtFanyZkIaEnUtqNaUM5RWg2qxukjQHhRUK+/4P/ALZUFTgsEtJGIAAAAASUVORK5CYII=",self.location),icon_02:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAGySURBVHgB7VXtbcJADHVCBoANsgERH79JJ2g36AjtKIxAN2CE6wAolwmaDZoFgD6ntmQFCjmC+oc86XTOOdjP9rtANGDAoyOiQGRZNo7juIBZHw6HJ+99TT0QUyCSJMmiKEqxMrapJ4IJoOrpOfvfCKDy/Jx9KxIKxPF45ParfTKC+Xz+hS3d7/eTLvq4pQOatGItsCjVt1wu2Zc2geP4tUu8IAKLxSLnHZVXWFu2IcSV+qGJ1BB96RIziIAmQHCPrbRnQmxlXs9sd+5CAJhKIofETsjk6tTxcIewjUej0fO1gCcihIi++cftcxaVSQB9+Qrv1i0h5lgsvLWsDd7ZGH+92+0mFwn8BRGVEijl2HMHuNX8UcJ5c4bufKD6dTuG+OkigTbD2Wz2hiQc7B1rLNXXErAhwEJkLfD15PGIv9Nn/qoGuBre+crJkTduFWJuxuMoAFcJSDVOn5HIGXJOyakWzHjuQ0CCbtWGGEtDrqJf0TUdsOO5KwEdgyTwLTc/N7cGJCoKRKdbcElUKkSxHQUi+L/gDEpLhgLRm4AKkVEUxScFojcBFqJU7mjAgAE34AeOX+UhhwPpxQAAAABJRU5ErkJggg==",self.location),icon_03:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAQDSURBVHgBzVe9UttAED6L0IuhIENj8QTI/My4i9yli9Oli3kCzBPYfgJIlw75CQhlKssdBWBRpoqooEMDFBl+831iz3NWJFlOirAzN7o77+1+t3+3Vuo/U2UWZs/z7Ovr66ZlWavPz882thz5KcY6xP7w+Pg4UDNQKQAbGxseFHSIoQR7jPFtfn6+d3R0FE1jLgRQr9ed+/v7fVMxgMSVSiXENMI8SoRUKo56sYaXEuFPA5ILYHNzs/X09LSLqS1bAdZdKD0LwzDOOeOCZ5TajgCikQfCytqEybsQxJvbvCXm3snJSWM0Gg3zlJPAt80vz2BsiYVoxRHBlQIA5W3x9wuDZbWpWE0hUdCSpX96euoDUIPByYtgPqBL0+cmXCA+pwltYzvg7bOUMitub29dyQi6y+GtoXxF87iua+MSA8QJAUaPj48104oTFoDygRKzY7S1HgpJK6ebbm5ursDHMweiPOatTT4qw95H7Q6A2TZ/HwNYW1trKclroO2C8UzlEM1tukmIQdqAwijNzz3I2xLZbfNCYwD44bMWBJP3oUAHVJgOPDF5QlDqwKwLdBMZVQ5JgeKwTSskACQ4PAGyJ0pcWdvr6+v7GAcYCciHh4dIC5ibm1spygyTIKsn09YEgLu7O0/WMZAeytyXryMHmtwDiCZNKtFNC3xQJQnAeYaFzKnVatUxANCqfAPNjEjuMZcFiK+rnnqJ9jGvRHcpEkslwOEGj983phB9KwOELwD0e8CId2Ruq78g6oA+nncSIGUPGkFEIUy7lvzkq9lIx4udBaAwmIwgsgVIyIxR/0BWFqo8ohWQcitQzEepDRfV1OxkmzqTGGCA4XalAkoKjbaELsdtqRsUTll9gO1mndc6EIThGABIVz1PzUhQPtA1Q4gluYN0ZdfElK5CKZ/koTzJCS9SMtGZPEYsjSgoV4KwUbatYvkGP59tWnEPNSHAzVyW8gx21o4e+c0HK4kByc9ABHVUearKN4DAHcg51PVDHrTYqB+OBmsCNN8C7VePea7KkQ5e13zrWT94Q4wFfhm0+jcCQiAP/wCQyvP9rCc4TTB5X0DYfMqzGg4S3HtoLH3zxZyoA0C2JQIdHNpVU4iuA9gdWea2XgCauFV835sAZy4uLy/j5eXlH5h+wnAxd5eWlr5j/5fKoYuLixB8ZxBehxvf4vseZ/o8wxRdXFz8SnnSWzaowzyf2RWb0a2mdLWa4DJa7acsef6crlTSpuU1K7ltOQORnbH0/CRfCkyQdwbAR1JokrjgHss1W7Is5YUA9K2Q1x0IbRnbEUYoL+e57FUFaNNQTBCsDV+KGpZSf800ECpgh1TEW1bxTABMQifzTszsaDCilH4eFvWFr5J+A8YpsGXJDE8eAAAAAElFTkSuQmCC",self.location),icon_04:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAALZSURBVHgB7VfbcdpAFL3C4ZeQDuQKIh4f/FmuwHQQUgGkAkQFhg5wB3YFJv+8UgHqAGXgCwbIOczuWJFXQiBI/MGZ0exKe3fv2fvalcgVV2SDJSegWq26aB62223dsqwi+kU9ttvtpvg2RfdlNBo9y5FITch13eJyuWxBYTNCwAcBX/Vt9O3QNH73QOxJUuIdoXK53DAKWlYbja1eAyjn7mmJ3xHRz5B10NZDxH3Id0zrjsfjfiKhSqWyk38IWO8vDp9MQnQDmr5cELBiQ94sLomEGBNg3pELAp5wUxM6YfFXNC6yzp1MJj8lA3KSEcw+uHgfvCqYMyETIdQjb7FYzDURtF1Ya4anLicijpBLZZIAKoVl2vpdJQJho/81aS7WbqFxUhHCYt+5OJWhJrUkBhi/C/VbqCe3m83mFq8N9DsJZDzIP+LhPC86bqzUjuPYNzc3E3SLUPJlOp0GURmQbdJF6tWXFBW5VqvZ6/V6xg0jAe6xrh+VMbqMgpi0V5bL5ZomGSz4FHYTnr6Kn28Sg9VqtR/DRjwTmVhCWqGabIuZdEA30exRYnGu1sEPq/+SGMQSyufz+hwqSgIYL5pYSHE7RjxQ43M5lhB2sd8NrxNxMgjQR8YF+4VCoRcaMm5CW1IdG+kJUYnepXZdFIiVLrOLQQoXzVCPZiHFfdMcrNVThFp6IwcJMS2hhBnGehIbfBibh3Zsy5tVBlBsTHvGHeb8oCx0vJoSwHj9AOhrD7HRkwMolUp3yMQ+NwBi9eFw+HJoDizaZqaxH71+GF3GK2gaMoQ6TH32EXdBmjmqcA5MY2c57bHLezkTMp/258aHIxR3hbUZeHJZ2KaPcVdYCnvyH/COEDKlYRJEanv6XGNZQP+ZVRw1J4jIsR7tf4PUT6So092TFDjqz1X9s7XDB27Sj6KqZ11WaNMVJjMhDdyXHmAJXlOd8D1aEfDxDPgjmfXCf8UVp+APG5qQ4n/MB/YAAAAASUVORK5CYII=",self.location),icon_05:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAJBSURBVHgB7ZfbUQIxFIYPsDxLCWsHO8ADj1iBdoBUoFQAViBWIFagHbC+McNt7WDtYH1luPifMXFCICTswvCy/8ySEJLDx8nthyhXrmwqUEo1Gg1/sVjcFgqFAG8r/KAebTabqFwuf45Go5hS6Giger3exJd2UW1auobFYrEzHo8jOhdQrVbro3iQ7wGWcFZQjUWTTxooPn+aTCY9OjUQYAYoWhIERX+9Xr9EUZSo/YIgqCAzd6h2AeOL5sF0Om27fI8TEKbpERDPAiYGyA1A4kNjAOYDbCihMK4zm836lBWIf3GpVJqj6rvCGKCS1Wp1rWdUV9EWVKTf5zoC91xhWNwX4+VU8VS2bGOsQIC45ZKzg3XwRkcKCzpEkYhYga2/FQggvqh+UEohxkCUTVtfKxD9HXqsmNIrdu3o6Q3VavVBgeA0V0SZUHr9yFiI31Vi7yyDLSBxClu3Zgbx9dJTG7ATv7D4/0/zQ1MWiufUCkmJC8Ar9UPPNAipvOEKTugh2e8tZxgZF1M337frXBb1WWRakxcDMikHsikHssm07QNs91dZ5xflTsuinbi6TBni6+JePPLqeGRvRNm0E1fXVoaWy2UEMxbqnZAdPvIDz/P4HurQkYKpC/fFhRLE/lYbnCys8Ml8svqqaUf63xFwK/Vs+uEM2zZnaJKzydftKBs2k+HiCxr++ehMspx3GdtR9tPCbFUOuT9pWc4KJKHwy9ts1gHWM/Xjf6+UUqn/SrNwY98jGy0NJgT0E+XKdSH9Appd+5pWT91vAAAAAElFTkSuQmCC",self.location),icon_06:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHPSURBVHgB5ZbLbcJAEIYXY3F2KohLMC+JG0sFoQQ6IFQQp4IkFUR0QCqIc+PAKxXgdGCJGyDIP6uxZSwS7zrBh+SX7PGud/bz7GPWQvx1VUQBtVotCXNDz7ZtP02n01DX1xgI2O3xeHw46aRS6c1ms0DH3xKGAmyIKzwcDhJ2xHV3uv7GQMhFROFyuXxbLBaPgK1Q5+k6GwHb7bbqmCFKgEcwjm4fRkAM45AhK1FQ2sBOp+PCDGj+5vP5OK5HmaKLdPvRAkopnd1u90rPiM5PfwTKNMzaEdt5DajTzWZDMBfR+FgoSXTb7VYCqJ4bjcbZlVqr1cbpfZrsQzgMYK7TjS3LcgCheodh9+n3zWaTPkSKHNH2oRWdAD3Pc6vV6vqLxiGiGGHeJtl39Xq9K3KEj1Z+8L86BwywEn1uGFAZDXviB8IoPMMM0K+LvfuRnUO1obmh+CW5dCMY2SKZxlReOlFcFMiZyaFUWAqQ5o0sIgxKAQLUZVvOkHIWIuB7KUDBCwaKLg7k3xBK7EG6/mJALBj1z8MJJFF24zt8DAmcDuroicsm2u/3lOj7fJS9fAfsA9SPCzTpKK9FcfnZipPTAoDcZKwjRBbhmsRp8n/pE7014YnlKSRiAAAAAElFTkSuQmCC",self.location),icon_06:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHPSURBVHgB5ZbLbcJAEIYXY3F2KohLMC+JG0sFoQQ6IFQQp4IkFUR0QCqIc+PAKxXgdGCJGyDIP6uxZSwS7zrBh+SX7PGud/bz7GPWQvx1VUQBtVotCXNDz7ZtP02n01DX1xgI2O3xeHw46aRS6c1ms0DH3xKGAmyIKzwcDhJ2xHV3uv7GQMhFROFyuXxbLBaPgK1Q5+k6GwHb7bbqmCFKgEcwjm4fRkAM45AhK1FQ2sBOp+PCDGj+5vP5OK5HmaKLdPvRAkopnd1u90rPiM5PfwTKNMzaEdt5DajTzWZDMBfR+FgoSXTb7VYCqJ4bjcbZlVqr1cbpfZrsQzgMYK7TjS3LcgCheodh9+n3zWaTPkSKHNH2oRWdAD3Pc6vV6vqLxiGiGGHeJtl39Xq9K3KEj1Z+8L86BwywEn1uGFAZDXviB8IoPMMM0K+LvfuRnUO1obmh+CW5dCMY2SKZxlReOlFcFMiZyaFUWAqQ5o0sIgxKAQLUZVvOkHIWIuB7KUDBCwaKLg7k3xBK7EG6/mJALBj1z8MJJFF24zt8DAmcDuroicsm2u/3lOj7fJS9fAfsA9SPCzTpKK9FcfnZipPTAoDcZKwjRBbhmsRp8n/pE7014YnlKSRiAAAAAElFTkSuQmCC",self.location),icon_07:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAG1SURBVHgB3ZZbboJAFIYPBH12Ceyg4y3xrXQF2h3gCmpXUF1B4wrUHXQHtG8mRp0l0B2Y+OiF/oeCoZRSKANp/BMyFw7zzcw5zByiiqUldXY6Het8Pt9STtXr9cVyuXQpD7DVatmaps3ob3JrtdpdGtSIdwDmr8zzPIm6pGyyg9I8HA6OEKIppdxlAkb0sl6vJ5RB7XbbDia5wyRNXdcdNJtJtjopFGBDFAwVmMSsdCACbQvYfdC0EXzjUoHYyjm29QlV339cRxCOygCGAWIFTyN8gRX3o4YGKdDpdOIA+fLfYrUCsFHcVgkQv4CLwo32wX/v2NJvQKU+zKLrAcJ/u0qBq9VKwodDBNRjJUDLshq4PV4RULJ0YK/XM/f7/RYH+bbb7YpSgQzjGwNVE08Dx50ThSoFRmHw35h9GIcqA8Zhm81mwj6kzwOBoTdsp+SkSYIl9C3YtvAKM8IuF3khYF5YYeDxeDSDgedZYKwffYjrZYA0wUx6x8eWYRhTAMM25zUDwJ7TYL5tvINzUnzg0C/iQTGpt7htGiwRyEKaJzBYn1KEMJ9yCbuHy2Ca5iLTW9B/0gfsUwtSRckrKAAAAABJRU5ErkJggg==",self.location),icon_08:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHuSURBVHgB7VbLbcJAEB3M5wZyCU4FMT+JW5YO0kGgAqADqCBKBYEKAhXE3DjwSwVxCY7ghATOm8hWbLM2GNmCA09axp5Zz7zdfbsL0R1XRiboaDQa2m63e6EUUCgURrPZzPT6csFOKN7JZDJdSgHIzWYQSQDFVba2bVt4XlMy0NFUWSAX9gWKjxeLRZsSQLVa/YQRsphCV4ZPhI4APzB6HUvA0z+mBIB8LRgNzcjn822vEH1LwOrn4s5HbHVKFsLZYQMpARcJC9CFVIhSAlwcAmxSgggT4tVFeHMELP6BBjRKHpq3hpRANpudsoUGNKzZ0X3A2xT+73q9rstilUplxTYYg7/lEjgcDhNv7Ogy8ooFM9ErlUpDwzCsWq0m8P7uJDLR+sVi8S/ZZrPh/q9uDANoz+dzQwihbrfbLr7rwK/CDpfLZfsUAS7SohQAAn0Q8F1GSkRnS+I29vv9M49EFsP0CrbBPGgmheDkZVQul5+cRF/r9dolNdF1vYc+j/D/4N30xJqIqd4YtMXL04pFwMVqtZrK/E7Bs2JYVgrDSQJB8C6g/y0VhIlZe6AYuOQg0i6MSRF7BlxgpL4dhJmx6QLc74KoJRDOqShFWCzEr59NwPkzwo8aRYtKxPSfRwCn2ZuiKBalAOQe0R23hl9LadSDrYWNIQAAAABJRU5ErkJggg==",self.location),icon_check:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAJvSURBVHgBrZU9bNNQEMfvvZiWAbVBICFlKNmYgHYoHxKi2RnaOQNqxzJA2OiCY5Z2o7AwFgaQurVI3VNYkBgSlKkSqFaHCCSQ3KBWbRK/652TuP6Kk7j9LX5+9/y/e3fn9wTEkC7radWSBQEwAwKyiJDtmCyaq9D7RsNWm0f3DbOXhughnMWWXEOAHAwAIr5v2mhEOZLBibHvrwoUdXlQcSdKIeZHNFke+6YXQja/uFGkhw5nw6hP68WQA46cNvsazgNbPa/fM1ZdB5xzTgsP4XywGi01xTVxaqDslJ5UPH/1NlRvPXWeHtIXNLnGA9GJfhcSMDGahq0bj2FipB1b/uc6bFk7rl1q6rJUTTkLCVnKzLjie8cWVA9/++yqJZ5JIWAOEsDR56+cpmW5tg17jX3fGgSRk9Tvk5AATk2Xj38r8Onfj9AaQX8+F3no4npTw6xQ9JGItoOh4NQsXrvrvi/XSqHUeOEamL2EuP2WMg998xz9eOqiM+bCrtS+QAyWphBM+tuyQUs3DS8yOerxSaeIjLew+V/rceJUZKxoArBEycp5DRzhg0vXT3dDjt5lZ2HfPnLnuLDVwz+xDgSKDSk1fBM0sNDN6ltY3N100uB1zLRTsw394LtCWlOGRa1ailrArfdo54MTrZeong/Cd4R7FqU0tUAPK2ohCz0xP7u7+frfjOz5ABZfQDwY+rgeT41SCo9j19gIhYM7upN69z+oT79cVYBF6EM/cdboijOhO7mzkyTHt0WR+8QjHTDOEc53BOI8DAD1e6nZwoWoS1/Efeg4ouMcBc4JEHwopjtfmdQlJnefRm3OndhL4wQwpxpFSeTnlQAAAABJRU5ErkJggg==",self.location),icon_close:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAIwSURBVHgBtZa9TsMwEMfPbvgWUmGhTC0DO0xQBujOAG8AzCBRZgbagRl4AvoIMLBHDHxM8AYUFioGiIT4UNXmuDNNcR0njSj8pUpOffnd2efzRUCM7gqFtKzXixJxCQFy8P1jeQLgFnz/pCHE6dTVVTWKIazg+fmcI8QxQQuQRIiVJkDZ5kiaf9zn88WUEDeJ4SpMsc7v3M/NFUNTHfCFhZJA3IMehEKUsxcXpZADjpweDuAPhL6/k72+Pmw74D3nJdIwrRs6k5PQeHyMhUXYeE3EWc6JykFKyj0T3j89DZlKBcZ3dyPhPDdBNn1kayhNAR+rFbSivzOjYrgYHVXPb2dn8Ly/H4KPLC+rsf/6CrW1NWjWah02zYGBMekgrpjuecnv5+ftZwbpK9HhrA+yNeEq0M/PbQekXAWLgogDkA7Ux7bVBaITVRAP+fwLGPuvy4xWVxy8JZXkdJwFAxj0CzgrJ+GfxQ6qcQZRW2QmPkJerAMTztuib1dXJ4i3Dt09Lmc7CVzfc/N02fJB7BPZGBw8Mie40IYXFyPhZuKHyDaVyYQccK+QU67r0djtmKBCe9raAqQKjTotgROu4qfNzXChUY/guyj2suOobBWawKbzsuMBNZiyadUNHmVD13Up6G7tOsheXh4iYgl6FDOoF7TzGurJrcYTur4TyOPIdbjVAUvlhHsE4jokER11avobtqYv4t5TXxd0nVOdrFJjn4GfVVUJynlzfTrmrZNo1ReQziL84EZxNAAAAABJRU5ErkJggg==",self.location)};a.loading({duration:0,message:n("loading"),forbidClick:!0}),l().then((e=>{J.value=e.mallLevel,M.value=e.childNum||0,D.value=e.teamNum||0}));const z=c(100),H=c(100);return u("valid_recharge_amount_for_team_num,valid_recharge_amount_for_seller_upgrade").then((e=>{z.value=e.valid_recharge_amount_for_seller_upgrade,H.value=e.valid_recharge_amount_for_team_num})),f().then((e=>{const n=e.result||[];if(n.length){const e=[100,500,700,1e3,1500,2e3];I.value.forEach((t=>{n.forEach(((n,i)=>{t.name===n.level&&(t.rechargeAmountCnd=n.rechargeAmountCnd,t.popularizeUserCountCnd=n.popularizeUserCountCnd,t.awardView=n.profitRationMin||n.profitRationMax?`${new gn(n.profitRationMin).mul(100)}%~${new gn(n.profitRationMax).mul(100)}%`:"-",t.promoteViewDaily=n.promoteViewDaily,t.deliveryDays=n.deliveryDays,t.upgradeCash=Y.value?e[i]:Number(n.upgradeCash),t.sellerDiscount=n.sellerDiscount?100*Number(n.sellerDiscount)+"%":0,t.hasExclusiveService=Boolean(n.hasExclusiveService),t.recommendAtFirstPage=Boolean(n.recommendAtFirstPage),t.teamNum=n.teamNum||0)}))}))}a.clear()})).catch((()=>{a.clear()})),(e,r)=>{const o=d("fx-header");return C(),A("div",hn,[g(o,{fixed:!0},{title:h((()=>[b(x(w(n)("卖家等级")),1)])),_:1}),pn,p("div",{style:v({"background-image":"url("+w(T)+")"}),class:"seller-banner"},[m(" <h2 v-html=\"$t('升级销量扶持')\"></h2>\n      <h2 v-html=\"$t('轻松月入过万')\"></h2> ")],4),p("div",mn,[p("div",vn,[p("h2",{innerHTML:e.$t("升级销量扶持")},null,8,wn),p("h2",{innerHTML:e.$t("轻松月入过万")},null,8,bn)]),p("div",xn,[M.value?(C(),A("p",Nn,[b(x(w(n)("当前分店人数"))+"：",1),p("span",null,x(M.value),1)])):m("v-if",!0),D.value?(C(),A("p",Un,[b(x(w(n)("当前团队人数"))+"：",1),p("span",null,x(D.value),1)])):m("v-if",!0)]),(C(!0),A(N,null,U(F.value,((t,i)=>(C(),A("div",{key:i,class:"intro-item"},[p("h2",null,x(i+1)+"."+x(e.$t(t.title)),1),(C(!0),A(N,null,U(t.data,(t=>(C(),A("div",{key:t},[t.title?(C(),A("h2",Cn,x(e.$t(t.title)),1)):m("v-if",!0),t.info.indexOf("money")>-1?(C(),A("p",{key:1,innerHTML:w(n)(t.info,{money:z.value})},null,8,Bn)):t.info.indexOf("totalMoney")>-1?(C(),A("p",{key:2,innerHTML:w(n)(t.info,{totalMoney:H.value})},null,8,yn)):(C(),A("p",Qn,x(e.$t(t.info)),1))])))),128))])))),128)),p("div",Rn,[(C(!0),A(N,null,U(I.value,(n=>(C(),A("div",{key:n.name,class:B([n.name,"level-item"])},[p("div",{class:B(["top-content",{"is-ar":w(R)}])},[p("div",kn,[p("div",En,[b(x(n.name)+"-"+x(e.$t("等级卖家"))+" ",1),J.value===n.name?(C(),A("span",Sn,x(e.$t("当前的")),1)):m("v-if",!0)]),w(Q)?m("v-if",!0):(C(),A("p",Mn,x(e.$t("运行资金"))+"："+x(n.rechargeAmountCnd),1)),p("p",null,x(e.$t("分店数"))+"："+x(n.popularizeUserCountCnd),1),n.teamNum?(C(),A("p",Dn,x(e.$t("团队人数"))+"："+x(n.teamNum),1)):m("v-if",!0)]),p("img",{src:n.icon,alt:""},null,8,Fn)],2),p("div",{class:B(["rights-content",{"is-ar":w(R)}])},[p("div",Jn,[p("div",Kn,[p("img",{src:V.icon_01,alt:""},null,8,In),p("p",null,x(e.$t("销售利润比例"))+"：",1)]),p("p",null,x(n.awardView),1)]),p("div",Yn,[p("div",Tn,[p("img",{src:V.icon_02,alt:""},null,8,Vn),p("p",null,x(e.$t("平台流量扶持量（每日）"))+"：",1)]),p("p",null,x(n.promoteViewDaily),1)]),p("div",zn,[p("div",Hn,[p("img",{src:V.icon_03,alt:""},null,8,Pn),p("p",null,x(e.$t("全球到货时间"))+"：",1)]),p("p",null,x(n.deliveryDays)+" "+x(e.$t("days")),1)]),p("div",On,[p("div",Xn,[p("img",{src:V.icon_07,alt:""},null,8,Zn),p("p",null,x(e.$t("采购优惠"))+"：",1)]),p("p",null,x(n.sellerDiscount),1)]),w(i)?m("v-if",!0):(C(),A("div",Ln,[p("div",jn,[p("img",{src:V.icon_04,alt:""},null,8,Wn),p("p",null,x(e.$t("升级礼金"))+"：",1)]),n.upgradeCash?(C(),A("p",Gn," $"+x(w(y)(n.upgradeCash)),1)):(C(),A("img",{key:1,src:V.icon_close,alt:""},null,8,qn))])),p("div",_n,[p("div",$n,[p("img",{src:V.icon_05,alt:""},null,8,et),p("p",null,x(e.$t("专属客服"))+"：",1)]),p("img",{src:n.hasExclusiveService?V.icon_check:V.icon_close,alt:""},null,8,nt)]),p("div",tt,[p("div",it,[p("img",{src:V.icon_06,alt:""},null,8,rt),p("p",null,x(e.$t("首页推荐"))+"：",1)]),p("img",{src:n.recommendAtFirstPage?V.icon_check:V.icon_close,alt:""},null,8,ot)]),"SS"===n.name&&["familyShop"].includes(w(t))?(C(),A("div",st,[p("div",ct,[p("img",{src:V.icon_08,alt:""},null,8,at),p("p",null,x(e.$t("成为供货商"))+"：",1)]),p("img",{src:V.icon_check,alt:""},null,8,lt)])):m("v-if",!0)],2)],2)))),128))])])])}}});e("default",t(ft,[["__scopeId","data-v-c2b30052"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/sellerLevel/index.vue"]]))}}}));
