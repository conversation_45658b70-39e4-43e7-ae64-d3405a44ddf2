import{_ as e,j as t,r as a,u as l,i as o,Y as s,l as n,m as i,p as u,o as r,c as d,e as c,w as p,a as m,t as v,b as f,f as b,x as h,n as x,bA as g,ak as T,T as k,I as _,bn as w,D as y,E as V}from"./index-3d21abf8.js";import{B as C}from"./index-2406f514.js";import"./index-573e22f7.js";import{C as D}from"./index-6aaac5d3.js";/* empty css              *//* empty css               */import{s as $,a as j}from"./product.api-7fdfc848.js";import{D as M}from"./index-d27cfcd1.js";import{F as U}from"./index-4ac00735.js";import{D as Y}from"./function-call-78245787.js";import{F as I}from"./index-8c1841f6.js";const F=e=>(y("data-v-9e40474f"),e=e(),V(),e),N={class:"edit-product-pop"},S={class:"title"},B={class:"tip pt-2 pb-2 pl-4 pr-4"},E=F((()=>m("span",null,"%",-1))),P={class:"tips pt-2 pb-2 pl-4 pr-4"},A={class:"tip pt-2 pb-2 pl-4 pr-4"},H={class:"tip pt-2 pb-2 pl-4 pr-4"},q={class:"tip pt-2 pb-2 pl-4 pr-4"},J=F((()=>m("span",null,"%",-1))),O={style:{margin:"16px"}},z=e({__name:"editProfit",props:{isEdit:Boolean,productArry:Array},emits:["back","update"],setup(e,{emit:y}){const V=e,F=t();a("true");const{t:z}=l(),G=o(),K=a(!1),L=a(!1),Q=a(!1),R=a(!1),W=a(new Date),X=a(""),Z=a(""),ee=a("");s();const te=n(),ae=a({startTime:"",endTime:"",discount:"",percent:""}),le=i((()=>{let e={};return F.userInfo.token?e={...F.userInfo}:te.push("/login"),e})),oe=e=>{switch(e){case 1:L.value=!0;break;case 2:Q.value=!0;break;case 3:R.value=!0}},se=e=>{switch(e){case 1:L.value=!1,ae.value.startTime=g(X.value).format("YYYY-MM-DD");break;case 2:Q.value=!1,ae.value.endTime=g(Z.value).format("YYYY-MM-DD");break;case 3:R.value=!1,ae.value.recTime=ce(ee.value)}},ne=e=>{switch(e){case 1:L.value=!1;break;case 2:Q.value=!1;break;case 3:R.value=!1}},ie=e=>{if([].includes("tiktokMall")&&(!le.value.phoneverif||!le.value.emailverif)){const e=le.value.phoneverif?"bindEmailTips":"请绑定手机号";return void Y.confirm({title:z("dialogTips"),message:z(e),cancelButtonText:z("cancel"),confirmButtonText:z("gotoSet"),confirmButtonColor:"#1552F0",cancelButtonColor:"#999"}).then((()=>{T("/personalInfo")})).catch((()=>{}))}const{startTime:t,endTime:a,discount:l}=ae.value;if(t||a){const e=new Date(t.replace(/-/g,"/")+" 00:00:00").getTime(),o=new Date(a.replace(/-/g,"/")+" 00:00:00").getTime();if(!l)return void k(z("请设置折扣比例"));if(e>o)return void k(z("开始时间应小于结束时间"))}if(Number(l)&&(!t||!a))return void k(z("请正确填写活动开启时间和结束时间"));let o={goodsIds:V.productArry.join(","),...ae.value};if(ae.value.percent>ue.max||ae.value.percent<ue.min)k(z("百分比设置范围为")+`：${ue.min}% ~ ${ue.max}%`);else{const e=/^\+?[1-9][0-9]*$/;e.test(String(Number(ae.value.percent)))?Number(ae.value.discount)&&!e.test(String(Number(ae.value.discount)))?k(z("折扣比例必须为正整数")):(K.value=!0,o.startTime=o.startTime+" 00:00:00",o.endTime=o.endTime+" 00:00:00",o.profit=(ae.value.percent/100).toFixed(2),o.discount=(ae.value.discount/100).toFixed(2),o.percent=(ae.value.percent/100).toFixed(2),k.loading({forbidClick:!0}),j(o).then((()=>{k(z("上架成功")),re()})).catch((e=>{const t="string"==typeof e.data?JSON.parse(e.data):e.data;t&&t._$1?k({message:z(e.msg,{_$1:t._$1}),duration:2e3}):k({message:z(e.msg),duration:2e3}),K.value=!1}))):k(z("百分比必须为正整数"))}},ue=u({min:5,max:20});$().then((e=>{ue.min=Number(e.sysParaMin),ue.max=Number(e.sysParaMax)}));const re=()=>{de(),y("update")},de=()=>{y("close");for(const e in ae.value)ae.value[e]="";setTimeout((()=>{K.value=!1}),500)},ce=e=>e.getFullYear()+"-"+(e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-"+(e.getDate()<10?"0"+e.getDate():e.getDate())+" "+(e.getHours()<10?"0"+e.getHours():e.getHours())+":"+(e.getMinutes()<10?"0"+e.getMinutes():e.getMinutes())+":"+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds());return(e,t)=>{const a=I,l=D,o=_,s=M,n=w,i=C,u=U;return r(),d("div",{class:x(["editProduct",{"is-ar":f(G)}])},[c(n,{show:V.isEdit,"onUpdate:show":t[16]||(t[16]=e=>V.isEdit=e),round:"",closeable:"",onClickCloseIcon:de,style:{height:"65%",width:"95%"}},{default:p((()=>[m("div",N,[m("div",S,v(f(z)("添加商品")),1),c(u,null,{default:p((()=>[m("div",B,v(f(z)("product.25")),1),c(l,{class:"input-field",inset:""},{default:p((()=>[c(a,{modelValue:ae.value.percent,"onUpdate:modelValue":t[0]||(t[0]=e=>ae.value.percent=e),type:"number",placeholder:f(z)("百分比"),rules:[{required:!0,message:f(z)("请填写百分比"),max:ue.max,min:ue.min}]},{button:p((()=>[E])),_:1},8,["modelValue","placeholder","rules"])])),_:1}),m("div",P,[b(v(e.$t("将选中的商品发布到你的店铺，并填写商品利润比例，推荐比例"))+": ",1),m("span",null,v(ue.min)+"%-"+v(ue.max)+"%",1)]),m("div",A,v(e.$t("折扣开始日期")),1),c(l,{class:"input-field",inset:"",style:{position:"relative"}},{default:p((()=>[ae.value.startTime?(r(),d("div",{key:0,class:"time-clear",onClick:t[1]||(t[1]=e=>ae.value.startTime="")},[c(o,{name:"cross"})])):h("v-if",!0),c(a,{onClickInput:t[2]||(t[2]=e=>oe(1)),modelValue:ae.value.startTime,"onUpdate:modelValue":t[3]||(t[3]=e=>ae.value.startTime=e),placeholder:f(z)("折扣开始日期")},null,8,["modelValue","placeholder"]),c(n,{show:L.value,"onUpdate:show":t[7]||(t[7]=e=>L.value=e),round:"",position:"bottom"},{default:p((()=>[c(s,{"min-date":W.value,"cancel-button-text":e.$t("取消"),"confirm-button-text":e.$t("确定"),modelValue:X.value,"onUpdate:modelValue":t[4]||(t[4]=e=>X.value=e),type:"date",title:f(z)("选择完整时间"),onConfirm:t[5]||(t[5]=e=>se(1)),onCancel:t[6]||(t[6]=e=>ne(1))},null,8,["min-date","cancel-button-text","confirm-button-text","modelValue","title"])])),_:1},8,["show"])])),_:1}),m("div",H,v(e.$t("折扣结束日期")),1),c(l,{class:"input-field",inset:"",style:{position:"relative"}},{default:p((()=>[ae.value.endTime?(r(),d("div",{key:0,class:"time-clear",onClick:t[8]||(t[8]=e=>ae.value.endTime="")},[c(o,{name:"cross"})])):h("v-if",!0),c(a,{onClickInput:t[9]||(t[9]=e=>oe(2)),modelValue:ae.value.endTime,"onUpdate:modelValue":t[10]||(t[10]=e=>ae.value.endTime=e),placeholder:f(z)("折扣结束日期")},null,8,["modelValue","placeholder"])])),_:1}),m("div",q,v(e.$t("折扣比例")),1),c(l,{class:"input-field",inset:""},{default:p((()=>[c(a,{modelValue:ae.value.discount,"onUpdate:modelValue":t[11]||(t[11]=e=>ae.value.discount=e),type:"number",placeholder:f(z)("折扣比例")},{button:p((()=>[J])),_:1},8,["modelValue","placeholder"])])),_:1}),c(n,{show:Q.value,"onUpdate:show":t[15]||(t[15]=e=>Q.value=e),round:"",position:"bottom"},{default:p((()=>[c(s,{"min-date":W.value,"cancel-button-text":e.$t("取消"),"confirm-button-text":e.$t("确定"),modelValue:Z.value,"onUpdate:modelValue":t[12]||(t[12]=e=>Z.value=e),type:"date",title:f(z)("选择完整时间"),onConfirm:t[13]||(t[13]=e=>se(2)),onCancel:t[14]||(t[14]=e=>ne(2))},null,8,["min-date","cancel-button-text","confirm-button-text","modelValue","title"])])),_:1},8,["show"]),m("div",O,[c(i,{disabled:K.value,block:"",class:"btn-content",type:"primary",onClick:ie,"native-type":"submit"},{default:p((()=>[b(v(f(z)("product.26")),1)])),_:1},8,["disabled"])])])),_:1})])])),_:1},8,["show"]),h(" <fx-header fixed>\n      <template #title>编辑商品</template>\n    </fx-header> "),h(' <van-popup v-model:show="show" position="bottom">\n      <div>\n        <van-date-picker v-model="fromData.recTime" title="选择日期"   ></van-date-picker>\n\n      </div>\n    </van-popup> ')],2)}}},[["__scopeId","data-v-9e40474f"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/product/components/editProfit.vue"]]);export{z as e};
