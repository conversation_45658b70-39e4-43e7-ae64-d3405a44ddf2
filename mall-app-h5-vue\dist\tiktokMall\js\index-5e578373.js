import{_ as a,d as e,i as t,u as s,r as i,m as l,s as o,b4 as n,av as _,c as r,e as d,w as p,T as m,b5 as v,b6 as g,o as c,f as u,t as h,a as f,F as j,y as E,n as x,A as D,x as T,b7 as b,D as R,E as L}from"./index-3d21abf8.js";import{P as I}from"./index-fc51b7d2.js";import{L as V}from"./index-40e83579.js";import{E as A}from"./index-5d897066.js";import{T as O,a as P}from"./index-222e676a.js";/* empty css              *//* empty css              *//* empty css               */import{B as y}from"./index-2406f514.js";import{_ as w}from"./dynamic-import-helper-353f3700.js";import{u as N}from"./index-54dce367.js";import{t as k,p as C}from"./config-0489190f.js";import{F as $}from"./index-8c1841f6.js";import"./use-id-a0619e01.js";import"./use-route-cd41a893.js";import"./index-0d6a7179.js";import"./use-refs-b86d6bcd.js";import"./index-7dfcb82a.js";const U=e({name:"shopPromotion",setup(){const a=t(),{t:e}=s(),_=new URL("/www/png/name-20d65991.png",self.location),r=i([...C]),{toClipboard:d}=N(),p=i(0),c=l((()=>k[p.value].key)),u=i([]),h=i(!1),f=i(!0),j=i(!1),E=i({pageNo:1,pageSize:10}),x=async()=>{const a={...E.value,level:c.value};await v(a).then((async a=>{const e=a||[],t=await(async a=>{const e=[];for(let t=0;t<a.length;t++){const s={...a[t]};if(isNaN(Number(a[t].avatar)))s.avatarImg=a[t].avatar,e.push(s);else{const i=a[t].avatar||"d";await w(Object.assign({"../../../assets/image/avatar/head_0.jpg":()=>b((()=>import("./head_0-13bc653d.js")),[]),"../../../assets/image/avatar/head_1.jpg":()=>b((()=>import("./head_1-22d9944e.js")),[]),"../../../assets/image/avatar/head_10.jpg":()=>b((()=>import("./head_10-368e0071.js")),[]),"../../../assets/image/avatar/head_11.jpg":()=>b((()=>import("./head_11-a274e2c3.js")),[]),"../../../assets/image/avatar/head_12.jpg":()=>b((()=>import("./head_12-e917044c.js")),[]),"../../../assets/image/avatar/head_13.jpg":()=>b((()=>import("./head_13-c5a043d7.js")),[]),"../../../assets/image/avatar/head_14.jpg":()=>b((()=>import("./head_14-8367209e.js")),[]),"../../../assets/image/avatar/head_15.jpg":()=>b((()=>import("./head_15-abcc6ff5.js")),[]),"../../../assets/image/avatar/head_16.jpg":()=>b((()=>import("./head_16-22218ac0.js")),[]),"../../../assets/image/avatar/head_17.jpg":()=>b((()=>import("./head_17-b669c555.js")),[]),"../../../assets/image/avatar/head_18.jpg":()=>b((()=>import("./head_18-61399948.js")),[]),"../../../assets/image/avatar/head_19.jpg":()=>b((()=>import("./head_19-37e10b27.js")),[]),"../../../assets/image/avatar/head_2.jpg":()=>b((()=>import("./head_2-fe990859.js")),[]),"../../../assets/image/avatar/head_20.jpg":()=>b((()=>import("./head_20-ccc6700e.js")),[]),"../../../assets/image/avatar/head_3.jpg":()=>b((()=>import("./head_3-7add788f.js")),[]),"../../../assets/image/avatar/head_4.jpg":()=>b((()=>import("./head_4-5f4b5902.js")),[]),"../../../assets/image/avatar/head_5.jpg":()=>b((()=>import("./head_5-35ab6dbe.js")),[]),"../../../assets/image/avatar/head_6.jpg":()=>b((()=>import("./head_6-c9e5a862.js")),[]),"../../../assets/image/avatar/head_7.jpg":()=>b((()=>import("./head_7-950f62ed.js")),[]),"../../../assets/image/avatar/head_8.jpg":()=>b((()=>import("./head_8-1b07024a.js")),[]),"../../../assets/image/avatar/head_9.jpg":()=>b((()=>import("./head_9-65895fab.js")),[]),"../../../assets/image/avatar/head_d.jpg":()=>b((()=>import("./head_d-c93fb7eb.js")),[])}),`../../../../../../mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/assets/image/avatar/head_${i}.jpg`).then((a=>{s.avatarImg=a.default,e.push(s)}))}}return e})(e);u.value=1===E.value.pageNo?t:[...u.value,...t],E.value.pageNo++,f.value=!1,h.value=!1,t.length<E.value.pageSize&&(j.value=!0)})).catch((()=>{h.value=!1,f.value=!1,j.value=!0}))},D=()=>{j.value=!1,f.value=!0,E.value.pageNo=1,x()},{locale:T}=s(),R=i(T.value),L=i(0),I=i(0),V=i(0);return o((()=>{m.loading({duration:0,message:e("loading"),forbidClick:!0}),g().then((a=>{r.value[0].value=a.download&&a.code?`${a.download}/#?usercode=${a.code}&lang=${T.value}`:"",r.value[1].value=a.code||"",L.value=Number((100*Number(a.promoRate1)).toFixed(2)),I.value=Number((100*Number(a.promoRate2)).toFixed(2)),V.value=Number((100*Number(a.promoRate3)).toFixed(2)),m.clear()})).catch((()=>{m.clear()}))})),{promotionDataRef:r,navActive:p,teamNav:k,refreshing:h,loading:f,finished:j,listData:u,empytImg:_,lang:R,t:e,formatZoneDate:n,levelOne:L,levelTwo:I,levelThree:V,isArLang:a,copyHandle:async a=>{try{await d(a),m(e("copySuccess"))}catch(t){}},navChange:()=>{u.value=[],D()},getLevelData:x,onRefresh:D}}}),F={class:"shop-promotion page-main-content shop-promotion-page"},H=(a=>(R("data-v-18227136"),a=a(),L(),a))((()=>f("div",{class:"header-spacer",style:{height:"46px"}},null,-1))),z={class:"promotion-info"},S={class:"intro-content"},M=["innerHTML"],Z={class:"team-content"},B={key:0,class:"list-content"},q={class:"avatar"},G=["src"],J={class:"info"},K={class:"name"},Q={class:"rebate"};const W=a(U,[["render",function(a,e,t,s,i,l){const o=_("fx-header"),n=y,m=$,v=O,g=P,b=A,R=V,L=I;return c(),r("div",F,[d(o,{fixed:!0},{title:p((()=>[u(h(a.t("shopPromotion")),1)])),_:1}),H,d(L,{modelValue:a.refreshing,"onUpdate:modelValue":e[2]||(e[2]=e=>a.refreshing=e),"pulling-text":a.t("pullingText"),"loosing-text":a.t("loosingText"),"loading-text":a.t("loading"),onRefresh:a.onRefresh},{default:p((()=>[d(R,{loading:a.loading,"onUpdate:loading":e[1]||(e[1]=e=>a.loading=e),finished:a.finished,"loading-text":a.t("loading"),"finished-text":a.listData.length?a.t("product.3"):"",onLoad:a.getLevelData},{default:p((()=>[f("div",z,[(c(!0),r(j,null,E(a.promotionDataRef,(e=>(c(),r("div",{key:e.key,class:x(["item",{"is-ar":a.isArLang}])},[f("p",null,h(a.t(e.title)),1),d(m,{modelValue:e.value,"onUpdate:modelValue":a=>e.value=a,center:"",label:"",placeholder:""},{button:p((()=>[d(n,{onClick:t=>a.copyHandle(e.value),size:"small",type:"primary"},{default:p((()=>[u(h(a.t("copy")),1)])),_:2},1032,["onClick"])])),_:2},1032,["modelValue","onUpdate:modelValue"])],2)))),128))]),f("div",S,[f("p",{innerHTML:a.t("promotionIntro",{level1:a.levelOne,level2:a.levelTwo,level3:a.levelThree})},null,8,M),f("p",null,h(a.t("以下是分成计算公式：")),1),f("p",null,[u(h(a.t("一级"))+" "+h(a.t("好友分成计算公式：佣金 = 商品销售利润 x")),1),f("span",null,h(a.levelOne)+"%",1)]),f("p",null,[u(h(a.t("二级"))+" "+h(a.t("好友分成计算公式：佣金 = 商品销售利润 x")),1),f("span",null,h(a.levelTwo)+"%",1)]),f("p",null,[u(h(a.t("三级"))+" "+h(a.t("好友分成计算公式：佣金 = 商品销售利润 x")),1),f("span",null,h(a.levelThree)+"%",1)]),f("p",null,h(a.t("我们提供详细的分成计算公式，以便您清晰了解佣金的计算方式。我们鼓励您了解平台的邀请制度规则，以便更好地管理和规划您的佣金收入。我们感谢您的参与，并期待与您共同发展。")),1)]),f("div",Z,[d(g,{active:a.navActive,"onUpdate:active":e[0]||(e[0]=e=>a.navActive=e),"title-inactive-color":"#999999",onChange:a.navChange},{default:p((()=>[(c(!0),r(j,null,E(a.teamNav,(e=>(c(),D(v,{key:e.key,title:a.t(e.title)},null,8,["title"])))),128))])),_:1},8,["active","onChange"]),a.listData.length?(c(),r("div",B,[(c(!0),r(j,null,E(a.listData,((e,t)=>(c(),r("div",{key:t,class:"item"},[f("div",q,[f("img",{src:e.avatarImg,alt:""},null,8,G)]),f("div",J,[f("p",K,h(e.name),1),f("p",Q,h(`${a.t("shopRebate")}：${e.income||0}`),1),f("div",null,[f("p",null,h(`${a.t("shopCountOrder")}：${e.orderCount}`),1),f("p",null,h(`${a.t("shopRegTime")}：${a.formatZoneDate(e.createTime)}`),1)])])])))),128))])):T("v-if",!0),a.listData.length||a.loading?T("v-if",!0):(c(),D(b,{key:1,image:a.empytImg.href,description:a.t("noData")},null,8,["image","description"]))])])),_:1},8,["loading","finished","loading-text","finished-text","onLoad"])])),_:1},8,["modelValue","pulling-text","loosing-text","loading-text","onRefresh"])])}],["__scopeId","data-v-18227136"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/shop/promotion/index.vue"]]);export{W as default};
