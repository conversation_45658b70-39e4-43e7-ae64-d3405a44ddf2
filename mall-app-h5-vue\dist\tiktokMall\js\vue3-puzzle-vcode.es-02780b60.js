import{d as e,q as a,J as t,r as i,p as n,g as s,m as o,o as l,A as c,a as h,K as r,L as d,n as u,b as v,t as g,M as p}from"./index-3d21abf8.js";const f=["width","height"],m=["width","height"],w=["width","height"],X=[h("div",{class:"loading-gif_"},[h("span"),h("span"),h("span"),h("span"),h("span")],-1)],x={class:"auth-control_"},T={class:"range-text"},Y=[h("div",null,null,-1),h("div",null,null,-1),h("div",null,null,-1)],y=e({__name:"App",props:{canvasWidth:{type:Number,default:310},canvasHeight:{type:Number,default:160},show:{type:Boolean,default:!1},puzzleScale:{type:Number,default:1},sliderSize:{type:Number,default:50},range:{type:Number,default:10},imgs:{type:Array,default:null},successText:{type:String,default:"验证通过！"},failText:{type:String,default:"验证失败，请重试"},sliderText:{type:String,default:"拖动滑块完成拼图"}},emits:["success","fail","close"],setup(e,{emit:y}){const S=e;a((()=>{document.addEventListener("mousemove",R,!1),document.addEventListener("mouseup",O,!1),document.addEventListener("touchmove",R,{passive:!1}),document.addEventListener("touchend",O,!1),S.show&&(document.body.classList.add("vue-puzzle-overflow"),J())})),t((()=>{M.timer1&&clearTimeout(M.timer1),document.removeEventListener("mousemove",R,!1),document.removeEventListener("mouseup",O,!1),document.removeEventListener("touchmove",R),document.removeEventListener("touchend",O,!1)}));const b=i(),B=i(),C=i(),A=i(),M=n({mouseDown:!1,startWidth:50,startX:0,newX:0,pinX:0,pinY:0,loading:!1,isCanSlide:!1,error:!1,infoBoxShow:!1,infoText:"",infoBoxFail:!1,timer1:void 0,closeDown:!1,isSuccess:!1,imgIndex:-1,isSubmting:!1});s((()=>S.show),(e=>{e?(document.body.classList.add("vue-puzzle-overflow"),J()):(M.isSubmting=!1,M.isSuccess=!1,M.infoBoxShow=!1,document.body.classList.remove("vue-puzzle-overflow"))}));const H=o((()=>{const e=M.startWidth+M.newX-M.startX;return e<I.value?I.value:e>S.canvasWidth?S.canvasWidth:e})),W=o((()=>Math.round(52.5*Math.max(Math.min(S.puzzleScale,2),.2)+6))),I=o((()=>Math.max(Math.min(Math.round(S.sliderSize),Math.round(.5*S.canvasWidth)),10))),D=()=>{M.closeDown=!0},L=()=>{M.closeDown&&(!M.mouseDown&&!M.isSubmting&&(M.timer1&&clearTimeout(M.timer1),y("close"))),M.closeDown=!1},z=e=>{var a;M.isCanSlide&&(M.mouseDown=!0,M.startWidth=(null==(a=b.value)?void 0:a.clientWidth)||0,M.newX=e.clientX||e.changedTouches[0].clientX,M.startX=e.clientX||e.changedTouches[0].clientX)},R=e=>{M.mouseDown&&(e.preventDefault(),M.newX=e.clientX||e.changedTouches[0].clientX)},O=()=>{M.mouseDown&&(M.mouseDown=!1,Q())},P=(e=!1)=>{var a;if(M.loading&&!e)return;M.loading=!0,M.isCanSlide=!1;const t=B.value,i=C.value,n=A.value,s=null==t?void 0:t.getContext("2d",{willReadFrequently:!0}),o=null==i?void 0:i.getContext("2d"),l=null==n?void 0:n.getContext("2d");if(!s||!o||!l)return;const c=navigator.userAgent.indexOf("Firefox")>=0&&navigator.userAgent.indexOf("Windows")>=0,h=document.createElement("img");if(s.fillStyle="rgba(255,255,255,1)",l.fillStyle="rgba(255,255,255,1)",s.clearRect(0,0,S.canvasWidth,S.canvasHeight),o.clearRect(0,0,S.canvasWidth,S.canvasHeight),M.pinX=F(W.value+20,S.canvasWidth-W.value-10),M.pinY=F(20,S.canvasHeight-W.value-10),h.crossOrigin="anonymous",h.onload=()=>{const[e,a,t,i]=k(h);s.save(),E(s),s.closePath(),c?(s.clip(),s.save(),s.shadowOffsetX=0,s.shadowOffsetY=0,s.shadowColor="#000",s.shadowBlur=3,s.fill(),s.restore()):(s.shadowOffsetX=0,s.shadowOffsetY=0,s.shadowColor="#000",s.shadowBlur=3,s.fill(),s.clip()),s.drawImage(h,e,a,t,i),l.fillRect(0,0,S.canvasWidth,S.canvasHeight),l.drawImage(h,e,a,t,i),s.globalCompositeOperation="source-atop",E(s),s.arc(M.pinX+Math.ceil(W.value/2),M.pinY+Math.ceil(W.value/2),1.2*W.value,0,2*Math.PI,!0),s.closePath(),s.shadowColor="rgba(255, 255, 255, .8)",s.shadowOffsetX=-1,s.shadowOffsetY=-1,s.shadowBlur=Math.min(Math.ceil(8*S.puzzleScale),12),s.fillStyle="#ffffaa",s.fill();const n=s.getImageData(M.pinX-3,M.pinY-20,M.pinX+W.value+5,M.pinY+W.value+5);o.putImageData(n,0,M.pinY-20),s.restore(),s.clearRect(0,0,S.canvasWidth,S.canvasHeight),s.save(),E(s),s.globalAlpha=.8,s.fillStyle="#ffffff",s.fill(),s.restore(),s.save(),s.globalCompositeOperation="source-atop",E(s),s.arc(M.pinX+Math.ceil(W.value/2),M.pinY+Math.ceil(W.value/2),1.2*W.value,0,2*Math.PI,!0),s.shadowColor="#000",s.shadowOffsetX=2,s.shadowOffsetY=2,s.shadowBlur=16,s.fill(),s.restore(),s.save(),s.globalCompositeOperation="destination-over",s.drawImage(h,e,a,t,i),s.restore(),M.loading=!1,M.isCanSlide=!0},h.onerror=()=>{P(!0)},!e&&null!=(a=S.imgs)&&a.length){let e=F(0,S.imgs.length-1);e===M.imgIndex&&(e===S.imgs.length-1?e=0:e++),M.imgIndex=e,h.src=S.imgs[e]}else h.src=Z()},F=(e,a)=>Math.ceil(Math.random()*(a-e)+e),k=e=>{const a=e.width/e.height;let t=0,i=0,n=0,s=0;return a>S.canvasWidth/S.canvasHeight?(s=S.canvasHeight,n=a*s,i=0,t=(S.canvasWidth-n)/2):(n=S.canvasWidth,s=n/a,t=0,i=(S.canvasHeight-s)/2),[t,i,n,s]},E=e=>{const a=Math.ceil(15*S.puzzleScale);e.beginPath(),e.moveTo(M.pinX,M.pinY),e.lineTo(M.pinX+a,M.pinY),e.arcTo(M.pinX+a,M.pinY-a/2,M.pinX+a+a/2,M.pinY-a/2,a/2),e.arcTo(M.pinX+a+a,M.pinY-a/2,M.pinX+a+a,M.pinY,a/2),e.lineTo(M.pinX+a+a+a,M.pinY),e.lineTo(M.pinX+a+a+a,M.pinY+a),e.arcTo(M.pinX+a+a+a+a/2,M.pinY+a,M.pinX+a+a+a+a/2,M.pinY+a+a/2,a/2),e.arcTo(M.pinX+a+a+a+a/2,M.pinY+a+a,M.pinX+a+a+a,M.pinY+a+a,a/2),e.lineTo(M.pinX+a+a+a,M.pinY+a+a+a),e.lineTo(M.pinX,M.pinY+a+a+a),e.lineTo(M.pinX,M.pinY+a+a),e.arcTo(M.pinX+a/2,M.pinY+a+a,M.pinX+a/2,M.pinY+a+a/2,a/2),e.arcTo(M.pinX+a/2,M.pinY+a,M.pinX,M.pinY+a,a/2),e.lineTo(M.pinX,M.pinY)},Z=()=>{const e=document.createElement("canvas"),a=e.getContext("2d");if(!a)return"";e.width=S.canvasWidth,e.height=S.canvasHeight,a.fillStyle=`rgb(${F(100,255)},${F(100,255)},${F(100,255)})`,a.fillRect(0,0,S.canvasWidth,S.canvasHeight);for(let t=0;t<12;t++)if(a.fillStyle=`rgb(${F(100,255)},${F(100,255)},${F(100,255)})`,a.strokeStyle=`rgb(${F(100,255)},${F(100,255)},${F(100,255)})`,F(0,2)>1)a.save(),a.rotate(F(-90,90)*Math.PI/180),a.fillRect(F(-20,e.width-20),F(-20,e.height-20),F(10,e.width/2+10),F(10,e.height/2+10)),a.restore();else{a.beginPath();const t=F(-Math.PI,Math.PI);a.arc(F(0,e.width),F(0,e.height),F(10,e.height/2+10),t,t+1.5*Math.PI),a.closePath(),a.fill()}return e.toDataURL("image/png")},Q=()=>{M.isSubmting=!0;const e=Math.abs(M.pinX-(H.value-I.value)+(W.value-I.value)*((H.value-I.value)/(S.canvasWidth-I.value))-3);e<S.range?(M.infoText=S.successText,M.infoBoxFail=!1,M.infoBoxShow=!0,M.isCanSlide=!1,M.isSuccess=!0,M.timer1&&clearTimeout(M.timer1),M.timer1=setTimeout((()=>{M.isSubmting=!1,y("success",e)}),800)):(M.infoText=S.failText,M.infoBoxFail=!0,M.infoBoxShow=!0,M.isCanSlide=!1,y("fail",e),M.timer1&&clearTimeout(M.timer1),M.timer1=setTimeout((()=>{M.isSubmting=!1,J()}),800))},J=()=>{M.isSubmting||(M.infoBoxFail=!1,M.infoBoxShow=!1,M.isCanSlide=!1,M.isSuccess=!1,M.startWidth=I.value,M.startX=0,M.newX=0,P())};return(a,t)=>(l(),c(p,{to:"body"},[h("div",{class:u(["vue-puzzle-vcode",{show_:e.show}]),onMousedown:D,onMouseup:L,onTouchstart:D,onTouchend:L},[h("div",{class:"vue-auth-box_",onMousedown:t[2]||(t[2]=r((()=>{}),["stop"])),onTouchstart:t[3]||(t[3]=r((()=>{}),["stop"]))},[h("div",{class:"auth-body_",style:d(`height: ${e.canvasHeight}px`)},[h("canvas",{ref_key:"canvas1",ref:B,width:e.canvasWidth,height:e.canvasHeight,style:d(`width:${e.canvasWidth}px;height:${e.canvasHeight}px`)},null,12,f),h("canvas",{ref_key:"canvas3",ref:A,class:u(["auth-canvas3_",{show:M.isSuccess}]),width:e.canvasWidth,height:e.canvasHeight,style:d(`width:${e.canvasWidth}px;height:${e.canvasHeight}px`)},null,14,m),h("canvas",{ref_key:"canvas2",ref:C,class:"auth-canvas2_",width:v(W),height:e.canvasHeight,style:d(`width:${v(W)}px;height:${e.canvasHeight}px;transform:translateX(${v(H)-v(I)-(v(W)-v(I))*((v(H)-v(I))/(e.canvasWidth-v(I)))}px)`)},null,12,w),h("div",{class:u(["loading-box_",{hide_:!M.loading}])},X,2),h("div",{class:u(["info-box_",{show:M.infoBoxShow},{fail:M.infoBoxFail}])},g(M.infoText),3),h("div",{class:u(["flash_",{show:M.isSuccess}]),style:d(`transform: translateX(${M.isSuccess?`${e.canvasWidth+.578*e.canvasHeight}px`:`-${.578*e.canvasHeight}px`}) skew(-30deg, 0);`)},null,6),h("img",{class:"reset_",onClick:J,src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAELklEQVRYR+2YW2wUZRTH//9vtlCoF9IoIklT3PqgPGi326hoetuaGEhIr9SgCYkkgt2WGOQVCca+GavWdr0GjD4YhG3RB3hply1LQA1tEQIxEXapGI2pEkys9LIzx2ylYWfY6e5sF0oi+7hzzvl+3/9855xvhrjNf7zN+XAHcL4Z+n8o6JWTeYt++W25S596AIZy6TB+n3yo+Nchlk8vmIIVowdXU9c3Q1gDSilBlQwjgBAYFGDvdF58/4milqvZwDpOcXWsb5Uh8hmBqkwXFMhlCN8aX5LXNbRy/T+Z+iXsHAFWRXs3QGQPyLucLDJrK5DgUXdTsxPfjAEro8E3Ce50EtxsKxPTwCPH3U2jTmJkBJgWTnAMxDeGMEoa0xQ+LJQnCD4HYFkCyAC3RdwN3U7gMkpxRTTYrMD91sCJIgCxV5R6O1Jcfy7VwonqLoj9/CqB2kF341qncGkBvRe+ureAWpRgoalCBecMFzcdK24YymZRJz5zprgq1tsJwXYL3CVZGvdGHmwZc7JQtra2gE+f712ep2QUYP714DJhaJrXLqXZQszlZwtYdSHoB9ljVk/ePVrSZFL0ZkAlxzQBVseCT8WhZhRThtFB8plk9Zi/qCi8cv0fNxvKFrDy4oF11NXXIFy2EII4iBcG3Y03VLZT8OqRd5aFPduvOEpxRayvXolxAKB2g6NgEhobBlc1HHYKY7WvHf5wtVAPgegIlbbZ9seUZ7AyFnwewi9pGoUyDmhrB931kfnC1ZwOeKlLP8GZJi6QLSFP2yep4toXSbT3ZQAfX3O6omt8Nhd9r/aHQAUMOQywYBZo5uZD2ThQ2rbPCjlnH6yI9rUryE5DU75ctJaake46Be4DuDjF8dFBNA94/AdtiySVxIlpMlTS8td801o70vMigM9huTda2lhcKHVHPO2HZv/P6LIwX7hk/+qzPSvUJGMkrg8AQYTkroRdXMlE+HH/twsG6BsOdJHYZlaO/lBZ6weOiiSXqs3Gqj0TeAxx+T75DIpgwjC0onD51pQD4JaluPrkR/cpFT9DcoVp84LOgTL/DjtBbglgou+puHwB8lEznPxJw1XSX77VtgizBvQNBw4RMqB7xt4Lc3c8lQKJaQHoO4R8ydz0/7MWoCXk8c85MrMC9J3qaafw/WtQlwXST+F3BnAeYB4obgJ1BJIuG+YtiKAjVOZ/Pd1ZdwzoG+4uBtSPpjaRbhXLcwF3hzytb2TilgVgT5BkYybBrTYC+Rvg5nRpdTRJrIs8+VPXPQXj2i4ItxC4O2NQQUQnN4U9rRcz9nH64p4ceM2lziX5Y4s3KHCdUHwE77ecMkMEp6BwhIa2Z6DslZRvfulgHafYLuCas58WLp2aLCFUga70qxOFU6dPFL2W1feYeaU43Y5z/TxnCuYabMEuC043ckdBp4pZ7f8FE5psOI1g6fwAAAAASUVORK5CYII="})],4),h("div",x,[h("div",{class:"range-box",style:d(`height:${v(I)}px`)},[h("div",T,g(e.sliderText),1),h("div",{class:"range-slider",ref_key:"rangeSlider",ref:b,style:d(`width:${v(H)}px`)},[h("div",{class:u(["range-btn",{isDown:M.mouseDown}]),style:d(`width:${v(I)}px`),onMousedown:t[0]||(t[0]=e=>z(e)),onTouchstart:t[1]||(t[1]=e=>z(e))},Y,38)],4)],4)])],32)],34)]))}});export{y as g};
