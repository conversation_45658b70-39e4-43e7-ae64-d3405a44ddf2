import{d as e,_ as l,u as a,r as o,m as u,o as t,c as n,a as s,f as d,t as i,b as r,e as c,h as v,c1 as m,F as p,y as b,w as h,n as f,T as x,D as w,E as k,I as y,j as V,l as g,i as _,q as U,g as C,av as A,bn as T,x as D,A as B,ak as S,aF as j}from"./index-3d21abf8.js";import{A as N}from"./index-3d6106f5.js";import{P as q,N as R}from"./index-2c2a5a87.js";import{B as W}from"./index-2406f514.js";import{P as F}from"./index-573e22f7.js";/* empty css              *//* empty css               */import{E as I,i as E}from"./index-402f5114.js";import{b as $,e as z,a as Z,c as M,d as P}from"./exchange.api-23bc91cd.js";import{E as H}from"./index-9c8e9dca.js";import{F as L}from"./index-8c1841f6.js";import{F as O}from"./index-4ac00735.js";import{D as G}from"./function-call-78245787.js";import"./use-route-cd41a893.js";import"./index-7dfcb82a.js";import"./use-id-a0619e01.js";import"./use-placeholder-c97cb410.js";const J={class:"form-content"},K={class:"title-content"},Q={class:"content"},X={class:"item"},Y={class:"select-item"},ee=["value"],le={class:"item"},ae={class:"select-item"},oe=["value"],ue={class:"item"},te={class:"item tips"},ne=(e=>(w("data-v-94360798"),e=e(),k(),e))((()=>s("div",{class:"bg"},null,-1))),se=e({name:"AddressBindDialog"}),de=l(Object.assign(se,{props:{modelValue:{type:Boolean,default:!1},chainData:{type:Array,default:()=>[]}},emits:["update:modelValue","bind-done"],setup(e,{emit:l}){const w=e,{t:k}=a(),V=()=>{l("update:modelValue",!1)},g=o("USDT"),_=u((()=>{if(w.chainData.length){const e=[...new Set(w.chainData.map((e=>e.coin)).reverse())];if(e.length)return g.value=e[0],e.map((e=>({label:e,value:e})))}return[{label:"USDT",value:"USDT"}]})),U=o("TRC20"),C=u((()=>{if(w.chainData.length){const e=w.chainData.filter((e=>e.coin===g.value));if(e.length)return U.value=e[0].blockchain_name,e.map((e=>({label:e.blockchain_name,value:e.blockchain_name})))}return[{label:"TRC20",value:"TRC20"},{label:"ERC20",value:"ERC20"}]})),A=o(""),T=e=>/^[0-9A-Za-z]+$/.test(e),D=o(!1),B=()=>{if(!/^[0-9A-Za-z]+$/.test(A.value))return void x(k("提现地址只能包含数字和字母"));D.value=!0;const e={coin:g.value,blockchain_name:U.value,channel_address:A.value};$(e).then((()=>{l("bind-done",e)})).finally((()=>{D.value=!1}))};return(l,a)=>{const o=y,u=L,x=W;return t(),n("div",{class:f([{active:e.modelValue},"dialog-content"])},[s("div",J,[s("div",K,[d(i(r(k)("提款地址绑定"))+" ",1),c(o,{name:"cross",class:"close",onClick:V})]),s("div",Q,[s("div",X,[s("p",null,i(r(k)("取款方式")),1),s("div",Y,[d(i(g.value)+" ",1),v(s("select",{"onUpdate:modelValue":a[0]||(a[0]=e=>g.value=e)},[(t(!0),n(p,null,b(r(_),((e,l)=>(t(),n("option",{key:l,value:e.value},i(e.label),9,ee)))),128))],512),[[m,g.value]])])]),s("div",le,[s("p",null,i(r(k)("链接协议")),1),s("div",ae,[d(i(U.value)+" ",1),v(s("select",{"onUpdate:modelValue":a[1]||(a[1]=e=>U.value=e)},[(t(!0),n(p,null,b(r(C),((e,l)=>(t(),n("option",{key:l,value:e.value},i(e.label),9,oe)))),128))],512),[[m,U.value]])])]),s("div",ue,[s("p",null,i(r(k)("收款钱包地址")),1),c(u,{clearable:"",modelValue:A.value,"onUpdate:modelValue":a[2]||(a[2]=e=>A.value=e),rules:[{validator:T,message:r(k)("提现地址只能包含数字和字母"),trigger:"onChange"}],class:"input-item"},null,8,["modelValue","rules"])]),s("div",te,i(r(k)("仅能绑定一个收款地址！")),1)]),c(x,{class:"w-full btn-content",type:"primary",loading:D.value,onClick:B},{default:h((()=>[d(i(r(k)("确认绑定")),1)])),_:1},8,["loading"])]),ne],2)}}}),[["__scopeId","data-v-94360798"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/withdraw/components/AddressBindDialog.vue"]]),ie={class:"page-main-content withdraw-page"},re=(e=>(w("data-v-1a4fc61c"),e=e(),k(),e))((()=>s("div",{class:"header-spacer",style:{height:"46px"}},null,-1))),ce={class:"icon-type-content"},ve={class:"mb-2.5 text-xs required-txt",style:{color:"#333"}},me={key:0,class:"icon-type-content"},pe={class:"mb-2.5 text-xs required-txt",style:{color:"#333"}},be={class:"mb-2.5 text-xs required-txt",style:{color:"#333"}},he={class:"mb-5 border border-solid rounded",style:{"border-color":"#eeeeee"}},fe={class:"mb-2.5 text-xs required-txt",style:{color:"#333"}},xe={class:"mb-5 nationality-select"},we={class:"mb-2.5 text-xs required-txt",style:{color:"#333"}},ke={class:"mb-5 border border-solid rounded",style:{"border-color":"#eeeeee"}},ye={class:"mb-2.5 text-xs required-txt",style:{color:"#333"}},Ve={class:"mb-5 border border-solid rounded",style:{"border-color":"#eeeeee"}},ge={class:"mb-2.5 text-xs required-txt",style:{color:"#333"}},_e={class:"mb-5 border border-solid rounded",style:{"border-color":"#eeeeee"}},Ue={class:"mb-2.5 text-xs",style:{color:"#333"}},Ce={class:"mb-5 border border-solid rounded",style:{"border-color":"#eeeeee"}},Ae={class:"mb-2.5 text-xs",style:{color:"#333"}},Te={class:"mb-5 border border-solid rounded",style:{"border-color":"#eeeeee"}},De={class:"mb-2.5 text-xs",style:{color:"#333"}},Be={class:"mb-5 border border-solid rounded",style:{"border-color":"#eeeeee"}},Se={class:"mb-2.5 text-xs",style:{color:"#333"}},je={class:"mb-5 border border-solid rounded",style:{"border-color":"#eeeeee"}},Ne={key:11,class:"current-balance"},qe={key:12,class:"current-balance"},Re={class:"tips"},We={style:{height:"22rem"}},Fe=l({__name:"index",setup(e){const l=V(),v=g(),{t:m}=a(),p="tiktokMall",b=_(),w=o(""),k=o(""),y=o(""),$=o(""),J=o("0.00"),K=o(!1),Q=o(!1),X=e=>[null,void 0,""].includes(e),Y=u((()=>["argos"].includes(p))),ee=u((()=>["shop2u"].includes(p))),le=o(""),ae=o(""),oe=e=>{ae.value=e.countryName},ue=e=>/^[0-9A-Za-z]+$/.test(e),te=()=>{z({channel:"icon"===nl.value?Me.value:"bank"}).then((e=>{const{withdraw_fee:l}=e;J.value=(100*l).toFixed(2)}))},ne=o([]),se=o(""),Fe=u((()=>{var e,l;let a=[];if(se.value)a=[{label:se.value,value:se.value}];else{a=ne.value.filter((e=>e.coin===Me.value)).map((e=>({...e,label:e.blockchain_name,value:e.blockchain_name})))}return a.length&&(w.value=null!=(l=null==(e=a[0])?void 0:e.value)?l:""),null!=a?a:[]})),Ie=o(!1),Ee=o(!1),$e=o(!1),ze=o(""),Ze=async()=>{var e;await M({session_token:null==(e=null==l?void 0:l.userInfo)?void 0:e.token}).then((e=>{Ie.value=Boolean(e.openWithdrawAddressBinding),Ee.value=Boolean(e.openWithdrawAddressBinding)&&(!e.chainName||!e.existWithdrawAddress),$e.value=Boolean(e.openWithdrawAddressBinding)&&(!e.chainName||!e.existWithdrawAddress),ze.value=e.session_token,Boolean(e.openWithdrawAddressBinding)&&(Me.value=e.coinType,k.value=e.existWithdrawAddress,se.value=e.chainName)}))},Me=o(""),Pe=o(!1),He=u((()=>{if(ne.value.length){const e=[...new Set(ne.value.map((e=>e.coin)).reverse())];if(e.length)return e.map((e=>({text:e,value:e})))}return[{text:"USDT",value:"USDT"}]})),Le=()=>{Ie.value||(Pe.value=!0)},Oe=({value:e})=>{Me.value=e,Pe.value=!1,te()};U((async()=>{x.loading({duration:0,message:m("loading"),forbidClick:!0}),await(async()=>{await Z().then((e=>{if(e.length){const l=[...new Set(e.map((e=>e.coin)).reverse())];Me.value=l[0]}else Me.value="USDT";ne.value=e||[]})).catch((()=>{x.clear()}))})(),await Ze(),await te(),x.clear()}));const Ge=u((()=>{const e=$.value,l=J.value,a=E.divide(E.minus(100,l),100),o=E.times(e,a),u=String(o);if(u.includes(".")){return 1===u.split(".")[1].length?u+"0":u}return u+".00"})),Je=o(2),Ke=u((()=>l.userInfo.money)),Qe=u((()=>{const e=Ke.value?Number(Ke.value):0,l=ne.value.find((e=>e.coin===Me.value&&e.blockchain_name===w.value));return l?(Je.value="BTC"===Me.value||"ETH"===Me.value?6:2,Number(e/Number(l.fee))):0})),Xe=()=>{if(!$e.value){const e=Number(j(Qe.value,Je.value).replace(/,/g,""));$.value=e}},Ye=()=>{v.push({name:"WithdrawRecord"})},el=()=>{var e;if("icon"===nl.value){if(!/^[0-9A-Za-z]+$/.test(k.value))return void x(m("提现地址只能包含数字和字母"));if(X(w.value))return void x(m("blockchainNetworkRequire"));if(X(k.value))return void x(m("withdrawalAddressRequire"))}if("bank"===nl.value){if(ee.value&&X(ae.value))return void x(m("selectNation"));if(X(il.value))return void x(m("请输入开户行名称"));if(X(rl.value))return void x(m("entryName"));if(X(cl.value))return void x(m("请输入卡号"))}X($.value)?x(m("withdrawalAmountRequire")):1===(null==(e=null==l?void 0:l.userInfo)?void 0:e.safeword)?(K.value=!0,al.value=!0):G.confirm({title:m("dialogTips"),message:m("shopSafeWord"),cancelButtonText:m("cancel"),confirmButtonText:m("gotoSet"),confirmButtonColor:"#1552F0",cancelButtonColor:"#999"}).then((()=>{S("/personalInfo")})).catch((()=>{}))},ll=e=>{Me.value=e.coin,w.value=e.blockchain_name,k.value=e.channel_address,$e.value=!1,Ee.value=!1},al=o(!0);C((()=>y.value),(e=>{6===e.length&&(x.loading({duration:0,forbidClick:!0}),(async()=>{const e=Fe.value.find((e=>e.blockchain_name===w.value)),a=e?e.coin:"USDT",o={safeword:y.value,amount:$.value,from:k.value,channel:a,session_token:ze.value};"bank"===nl.value&&(o.channel="bank",o.bankName=il.value,o.bankUserName=rl.value,o.bankCardNo=cl.value,Y.value&&(o.swiftCode=ml.value||"",o.routingNum=ml.value||"",o.accountAddress=pl.value||"",o.bankAddress=bl.value||""),ee.value&&(o.countryName=ae.value||"")),Q.value=!0,P(o).then((async()=>{await l.getUserInfo(!0),x(m("withdrawalApplySuccess")),Ze(),setTimeout((()=>{Ye()}),1500)})).finally((()=>{Q.value=!1,Ze()}))})(),K.value=!1,y.value="")}));const ol=u((()=>!["tiktok-wholesale"].includes("tiktokMall")));let ul="虚拟币";["familyShop","sm"].includes(p)&&(ul="加密货币"),["argos2"].includes(p)&&(ul="货币");const tl=o(m(ul)),nl=o("icon"),sl=o([{text:m(ul),value:"icon"}]);ol.value&&sl.value.push({text:m("银行卡"),value:"bank"});const dl=o(!1),il=o(""),rl=o(""),cl=o(""),vl=o(""),ml=o(""),pl=o(""),bl=o(""),hl=e=>{"bank"===e.value&&["familyShop","sm"].includes(p)?v.push({path:"/customerService"}):(tl.value=e.text,nl.value=e.value,dl.value=!1,$.value="",te())};return(e,l)=>{const a=A("fx-header"),o=L,u=F,v=T,p=O,x=A("country-select"),V=W,g=q,_=R,U=N;return t(),n("div",ie,[c(a,{fixed:""},{title:h((()=>[s("div",null,i(r(m)("withdrawal")),1)])),right:h((()=>[s("div",{onClick:Ye},i(r(m)("withdrawalRecord")),1)])),_:1}),re,s("div",{class:f(["withdraw",{"is-ar":r(b)}])},[s("div",ce,[s("div",ve,i(r(m)("取款方式")),1),c(o,{modelValue:tl.value,"onUpdate:modelValue":l[0]||(l[0]=e=>tl.value=e),readonly:"",name:"picker",onClick:l[1]||(l[1]=e=>dl.value=!0)},null,8,["modelValue"]),c(v,{show:dl.value,"onUpdate:show":l[3]||(l[3]=e=>dl.value=e),position:"bottom"},{default:h((()=>[c(u,{columns:sl.value,"cancel-button-text":r(m)("cancel"),"confirm-button-text":r(m)("confirm"),onConfirm:hl,onCancel:l[2]||(l[2]=e=>dl.value=!1)},null,8,["columns","cancel-button-text","confirm-button-text"])])),_:1},8,["show"])]),"icon"===nl.value?(t(),n("div",me,[s("div",pe,i(r(m)("选择币种")),1),c(o,{modelValue:Me.value,"onUpdate:modelValue":l[4]||(l[4]=e=>Me.value=e),readonly:"",name:"picker",onClick:Le},null,8,["modelValue"]),c(v,{show:Pe.value,"onUpdate:show":l[6]||(l[6]=e=>Pe.value=e),position:"bottom"},{default:h((()=>[c(u,{columns:r(He),"cancel-button-text":r(m)("cancel"),"confirm-button-text":r(m)("confirm"),onConfirm:Oe,onCancel:l[5]||(l[5]=e=>Pe.value=!1)},null,8,["columns","cancel-button-text","confirm-button-text"])])),_:1},8,["show"])])):D("v-if",!0),"icon"===nl.value?(t(),B(I,{key:1,list:r(Fe),label:r(m)("blockchainNetwork"),disabled:Ie.value,modelValue:w.value,"onUpdate:modelValue":l[7]||(l[7]=e=>w.value=e)},null,8,["list","label","disabled","modelValue"])):D("v-if",!0),"icon"===nl.value?(t(),B(p,{key:2},{default:h((()=>[s("div",be,i(r(m)("withdrawalAddress")),1),s("div",he,[c(o,{clearable:"",modelValue:k.value,"onUpdate:modelValue":l[8]||(l[8]=e=>k.value=e),disabled:Ie.value,placeholder:r(m)("withdrawalAddressTips"),rules:[{validator:ue,message:r(m)("提现地址只能包含数字和字母"),trigger:"onChange"}]},null,8,["modelValue","disabled","placeholder","rules"])])])),_:1})):D("v-if",!0),"bank"===nl.value&&r(ee)?(t(),B(p,{key:3},{default:h((()=>[s("div",fe,i(r(m)("国家")),1),s("div",xe,[c(x,{modelValue:le.value,"onUpdate:modelValue":l[9]||(l[9]=e=>le.value=e),"get-name":"true",onDone:oe},null,8,["modelValue"])])])),_:1})):D("v-if",!0),"bank"===nl.value?(t(),B(p,{key:4},{default:h((()=>[s("div",we,i(r(m)("开户行")),1),s("div",ke,[c(o,{clearable:"",modelValue:il.value,"onUpdate:modelValue":l[10]||(l[10]=e=>il.value=e),placeholder:r(m)("请输入开户行名称")},null,8,["modelValue","placeholder"])])])),_:1})):D("v-if",!0),"bank"===nl.value?(t(),B(p,{key:5},{default:h((()=>[s("div",ye,i(r(m)("姓名")),1),s("div",Ve,[c(o,{clearable:"",modelValue:rl.value,"onUpdate:modelValue":l[11]||(l[11]=e=>rl.value=e),placeholder:r(m)("entryName")},null,8,["modelValue","placeholder"])])])),_:1})):D("v-if",!0),"bank"===nl.value?(t(),B(p,{key:6},{default:h((()=>[s("div",ge,i(r(m)("卡号")),1),s("div",_e,[c(o,{clearable:"",modelValue:cl.value,"onUpdate:modelValue":l[12]||(l[12]=e=>cl.value=e),placeholder:r(m)("请输入卡号")},null,8,["modelValue","placeholder"])])])),_:1})):D("v-if",!0),"bank"===nl.value&&r(Y)?(t(),B(p,{key:7},{default:h((()=>[s("div",Ue,i(r(m)("国际代码")),1),s("div",Ce,[c(o,{clearable:"",modelValue:vl.value,"onUpdate:modelValue":l[13]||(l[13]=e=>vl.value=e),placeholder:r(m)("请输入国际代码")},null,8,["modelValue","placeholder"])])])),_:1})):D("v-if",!0),"bank"===nl.value&&r(Y)?(t(),B(p,{key:8},{default:h((()=>[s("div",Ae,i(r(m)("路由号码")),1),s("div",Te,[c(o,{clearable:"",modelValue:ml.value,"onUpdate:modelValue":l[14]||(l[14]=e=>ml.value=e),placeholder:r(m)("请输入路由号码")},null,8,["modelValue","placeholder"])])])),_:1})):D("v-if",!0),"bank"===nl.value&&r(Y)?(t(),B(p,{key:9},{default:h((()=>[s("div",De,i(r(m)("账户地址")),1),s("div",Be,[c(o,{clearable:"",modelValue:pl.value,"onUpdate:modelValue":l[15]||(l[15]=e=>pl.value=e),placeholder:r(m)("请输入账户地址")},null,8,["modelValue","placeholder"])])])),_:1})):D("v-if",!0),"bank"===nl.value&&r(Y)?(t(),B(p,{key:10},{default:h((()=>[s("div",Se,i(r(m)("银行地址")),1),s("div",je,[c(o,{clearable:"",modelValue:bl.value,"onUpdate:modelValue":l[16]||(l[16]=e=>bl.value=e),placeholder:r(m)("请输入银行地址")},null,8,["modelValue","placeholder"])])])),_:1})):D("v-if",!0),c(H,{label:r(m)("withdrawalAmount"),placeholderText:r(m)("withdrawalAmountTips"),modelValue:$.value,"onUpdate:modelValue":l[17]||(l[17]=e=>$.value=e),maxLength:16,disabled:$e.value,required:"",typeText:"number"},{rightBtn:h((()=>[s("div",{class:f(["withdraw-all",{"is-ar":r(b)}]),onClick:Xe,style:{"min-width":"40px"}},i(r(m)("total")),3)])),_:1},8,["label","placeholderText","modelValue","disabled"]),(r(Ke)||0===r(Ke))&&Me.value&&"icon"===nl.value?(t(),n("div",Ne,[d(i(r(m)("当前余额"))+": ",1),s("span",null,i(r(j)(r(Ke))),1),d("USDT ≈ "),s("span",null,i(r(j)(r(Qe),Je.value)),1),d(i(Me.value),1)])):D("v-if",!0),"icon"!==nl.value?(t(),n("div",qe,[d(i(r(m)("当前余额"))+": ",1),s("span",null,i(r(j)(r(Ke))),1),d("USD ")])):D("v-if",!0),s("div",Re,[s("span",null,[d(i(r(m)("realWithdrawalAccount"))+": "+i(r(Ge)),1),s("span",null,i("icon"===nl.value?` ${Me.value}`:" USD"),1)]),s("span",null,i(r(m)("withdrawalFee"))+": "+i(J.value)+"%",1)]),c(V,{class:"w-full btn-content",type:"primary",disabled:$e.value,onClick:el},{default:h((()=>[d(i(r(m)("submit")),1)])),_:1},8,["disabled"]),c(U,{show:K.value,"onUpdate:show":l[21]||(l[21]=e=>K.value=e),title:r(m)("请输入交易密码")},{default:h((()=>[s("div",We,[c(g,{length:6,value:y.value,focused:al.value,onFocus:l[18]||(l[18]=e=>al.value=!0)},null,8,["value","focused"]),c(_,{modelValue:y.value,"onUpdate:modelValue":l[19]||(l[19]=e=>y.value=e),show:al.value,onBlur:l[20]||(l[20]=e=>al.value=!1)},null,8,["modelValue","show"])])])),_:1},8,["show","title"])],2),c(de,{modelValue:Ee.value,"onUpdate:modelValue":l[22]||(l[22]=e=>Ee.value=e),"chain-data":ne.value,onBindDone:ll},null,8,["modelValue","chain-data"])])}}},[["__scopeId","data-v-1a4fc61c"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/withdraw/index.vue"]]);export{Fe as default};
