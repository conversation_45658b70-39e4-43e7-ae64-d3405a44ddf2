System.register(["./index-legacy-46a00900.js","./use-placeholder-legacy-f22ccc27.js"],(function(a,e){"use strict";var t,n,r,i,o,l,v,s,c,b,d,f,g=document.createElement("style");return g.textContent=":root{--van-nav-bar-height: 46px;--van-nav-bar-background-color: var(--van-background-color-light);--van-nav-bar-arrow-size: 16px;--van-nav-bar-icon-color: var(--van-primary-color);--van-nav-bar-text-color: var(--van-primary-color);--van-nav-bar-title-font-size: var(--van-font-size-lg);--van-nav-bar-title-text-color: var(--van-text-color);--van-nav-bar-z-index: 1 }.van-nav-bar{position:relative;z-index:var(--van-nav-bar-z-index);line-height:var(--van-line-height-lg);text-align:center;background:var(--van-nav-bar-background-color);-webkit-user-select:none;-moz-user-select:none;user-select:none}.van-nav-bar--fixed{position:fixed;top:0;left:0;width:100%}.van-nav-bar--safe-area-inset-top{padding-top:constant(safe-area-inset-top);padding-top:env(safe-area-inset-top)}.van-nav-bar .van-icon{color:var(--van-nav-bar-icon-color)}.van-nav-bar__content{position:relative;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;height:var(--van-nav-bar-height)}.van-nav-bar__arrow{margin-right:var(--van-padding-base);font-size:var(--van-nav-bar-arrow-size)}.van-nav-bar__title{max-width:60%;margin:0 auto;color:var(--van-nav-bar-title-text-color);font-weight:var(--van-font-weight-bold);font-size:var(--van-nav-bar-title-font-size)}.van-nav-bar__left,.van-nav-bar__right{position:absolute;top:0;bottom:0;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;padding:0 var(--van-padding-md);font-size:var(--van-font-size-md)}.van-nav-bar__left{left:0}.van-nav-bar__right{right:0}.van-nav-bar__text{color:var(--van-nav-bar-text-color)}\n",document.head.appendChild(g),{setters:[a=>{t=a.P,n=a.S,r=a.ai,i=a.d,o=a.r,l=a.a$,v=a.e,s=a.b0,c=a.b1,b=a.I,d=a.X},a=>{f=a.u}],execute:function(){const[e,g]=t("nav-bar"),x={title:String,fixed:Boolean,zIndex:n,border:r,leftText:String,rightText:String,leftArrow:Boolean,placeholder:Boolean,safeAreaInsetTop:Boolean};var p=i({name:e,props:x,emits:["click-left","click-right"],setup(a,{emit:e,slots:t}){const n=o(),r=f(n,g),i=a=>e("click-left",a),d=a=>e("click-right",a),x=()=>{const{title:e,fixed:r,border:o,zIndex:f}=a,x=l(f),p=a.leftArrow||a.leftText||t.left,h=a.rightText||t.right;return v("div",{ref:n,style:x,class:[g({fixed:r}),{[c]:o,"van-safe-area-top":a.safeAreaInsetTop}]},[v("div",{class:g("content")},[p&&v("div",{class:[g("left"),s],onClick:i},[t.left?t.left():[a.leftArrow&&v(b,{class:g("arrow"),name:"arrow-left"},null),a.leftText&&v("span",{class:g("text")},[a.leftText])]]),v("div",{class:[g("title"),"van-ellipsis"]},[t.title?t.title():e]),h&&v("div",{class:[g("right"),s],onClick:d},[t.right?t.right():v("span",{class:g("text")},[a.rightText])])])])};return()=>a.fixed&&a.placeholder?r(x):x()}});a("N",d(p))}}}));
