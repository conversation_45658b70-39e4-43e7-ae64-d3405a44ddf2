import{_ as e,u as a,Y as s,l as r,r as l,c as t,a as c,e as i,b as o,o as u}from"./index-3d21abf8.js";import{N as n}from"./index-cfdda867.js";import"./use-placeholder-c97cb410.js";const d={class:"iframe-content"},f=["src"],p=e({__name:"thirdPartyRecharge",setup(e){const{t:p}=a(),v=s(),m=r(),h=l("");h.value=v.query.u||"",h.value||_();const _=()=>{m.go(-2)};return(e,a)=>{const s=n;return u(),t("div",null,[c("div",null,[i(s,{ref:"navEl",title:o(p)("recharge"),"left-arrow":"",onClickLeft:_,fixed:""},null,8,["title"])]),c("div",d,[c("iframe",{src:h.value,class:"flex-1"},null,8,f)])])}}},[["__scopeId","data-v-d0ac1100"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/recharge/thirdPartyRecharge.vue"]]);export{p as default};
