import{aU as e,aD as t,b8 as a,b9 as n,aM as l,aN as o,aR as i,P as r,d as s,r as d,aB as c,p as u,m as v,ba as f,Q as p,a$ as b,g as h,ac as g,e as m,S as x,R as y,a4 as w,aO as k,aT as I,X as B,ai as S,ad as C,bb as $,bc as R,q as z,a9 as T,Z as j,aH as A,aI as L,s as O,at as Z,aL as N,a8 as W,bd as H,a0 as M,be as F,ag as X,aY as D,bf as P,a5 as V,a6 as q,aP as E,aa as Q,h as U,aS as Y}from"./index-3d21abf8.js";import{u as G}from"./use-id-a0619e01.js";import{a as J,r as K}from"./use-route-cd41a893.js";import{T as _}from"./index-40e83579.js";import{a as ee,S as te}from"./index-0d6a7179.js";import{u as ae}from"./use-refs-b86d6bcd.js";const[ne,le]=r("sticky");const oe=B(s({name:ne,props:{zIndex:x,position:y("top"),container:Object,offsetTop:w(0),offsetBottom:w(0)},emits:["scroll","change"],setup(e,{emit:a,slots:r}){const s=d(),x=c(s),y=u({fixed:!1,width:0,height:0,transform:0}),w=v((()=>f("top"===e.position?e.offsetTop:e.offsetBottom))),B=v((()=>{const{fixed:e,height:t,width:a}=y;if(e)return{width:`${a}px`,height:`${t}px`}})),S=v((()=>{if(!y.fixed)return;const t=p(b(e.zIndex),{width:`${y.width}px`,height:`${y.height}px`,[e.position]:`${w.value}px`});return y.transform&&(t.transform=`translate3d(0, ${y.transform}px, 0)`),t})),C=()=>{if(!s.value||k(s))return;const{container:n,position:l}=e,o=I(s),i=t(window);if(y.width=o.width,y.height=o.height,"top"===l)if(n){const e=I(n),t=e.bottom-w.value-y.height;y.fixed=w.value>o.top&&e.bottom>0,y.transform=t<0?t:0}else y.fixed=w.value>o.top;else{const{clientHeight:e}=document.documentElement;if(n){const t=I(n),a=e-t.top-w.value-y.height;y.fixed=e-w.value<o.bottom&&e>t.top,y.transform=a<0?-a:0}else y.fixed=e-w.value<o.bottom}(e=>{a("scroll",{scrollTop:e,isFixed:y.fixed})})(i)};return h((()=>y.fixed),(e=>a("change",e))),g("scroll",C,{target:x,passive:!0}),function(e,t){if(!n||!window.IntersectionObserver)return;const a=new IntersectionObserver((e=>{t(e[0].intersectionRatio>0)}),{root:document.body}),r=()=>{e.value&&a.unobserve(e.value)};l(r),o(r),i((()=>{e.value&&a.observe(e.value)}))}(s,C),()=>{var e;return m("div",{ref:s,style:B.value},[m("div",{class:le({fixed:y.fixed}),style:S.value},[null==(e=r.default)?void 0:e.call(r)])])}}})),[ie,re]=r("tab");var se=s({name:ie,props:{id:String,dot:Boolean,type:String,color:String,title:String,badge:x,shrink:Boolean,isActive:Boolean,disabled:Boolean,controls:String,scrollable:Boolean,activeColor:String,inactiveColor:String,showZeroBadge:S},setup(e,{slots:t}){const a=v((()=>{const t={},{type:a,color:n,disabled:l,isActive:o,activeColor:i,inactiveColor:r}=e;n&&"card"===a&&(t.borderColor=n,l||(o?t.backgroundColor=n:t.color=n));const s=o?i:r;return s&&(t.color=s),t})),n=()=>{const a=m("span",{class:re("text",{ellipsis:!e.scrollable})},[t.title?t.title():e.title]);return e.dot||C(e.badge)&&""!==e.badge?m($,{dot:e.dot,content:e.badge,showZero:e.showZeroBadge},{default:()=>[a]}):a};return()=>m("div",{id:e.id,role:"tab",class:[re([e.type,{grow:e.scrollable&&!e.shrink,shrink:e.shrink,active:e.isActive,disabled:e.disabled}])],style:a.value,tabindex:e.disabled?void 0:e.isActive?0:-1,"aria-selected":e.isActive,"aria-disabled":e.disabled||void 0,"aria-controls":e.controls},[n()])}});const[de,ce]=r("tabs");var ue=s({name:de,props:{count:R(Number),inited:Boolean,animated:Boolean,duration:R(x),swipeable:Boolean,lazyRender:Boolean,currentIndex:R(Number)},emits:["change"],setup(e,{emit:t,slots:a}){const n=d(),l=e=>t("change",e),o=()=>{var t;const o=null==(t=a.default)?void 0:t.call(a);return e.animated||e.swipeable?m(ee,{ref:n,loop:!1,class:ce("track"),duration:1e3*+e.duration,touchable:e.swipeable,lazyRender:e.lazyRender,showIndicators:!1,onChange:l},{default:()=>[o]}):o},i=t=>{const a=n.value;a&&a.state.active!==t&&a.swipeTo(t,{immediate:!e.inited})};return h((()=>e.currentIndex),i),z((()=>{i(e.currentIndex)})),T({swipeRef:n}),()=>m("div",{class:ce("content",{animated:e.animated||e.swipeable})},[o()])}});const[ve,fe]=r("tabs"),pe={type:y("line"),color:String,border:Boolean,sticky:Boolean,shrink:Boolean,active:w(0),duration:w(.3),animated:Boolean,ellipsis:S,swipeable:Boolean,scrollspy:Boolean,offsetTop:w(0),background:String,lazyRender:S,lineWidth:x,lineHeight:x,beforeChange:Function,swipeThreshold:w(5),titleActiveColor:String,titleInactiveColor:String},be=Symbol(ve);var he=s({name:ve,props:pe,emits:["click","change","scroll","disabled","rendered","click-tab","update:active"],setup(n,{emit:l,slots:o}){var r,s;null==(s=null==(r=j())?void 0:r.vnode)||s.props;let p,b,x;const y=d(),w=d(),B=d(),S=d(),$=G(),R=c(y),[z,V]=ae(),{children:q,linkChildren:E}=A(be),Q=u({inited:!1,position:"",lineStyle:{},currentIndex:-1}),U=v((()=>q.length>n.swipeThreshold||!n.ellipsis||n.shrink)),Y=v((()=>({borderColor:n.color,background:n.background}))),K=(e,t)=>{var a;return null!=(a=e.name)?a:t},_=v((()=>{const e=q[Q.currentIndex];if(e)return K(e,Q.currentIndex)})),ee=v((()=>f(n.offsetTop))),te=v((()=>n.sticky?ee.value+p:0)),ne=t=>{const a=w.value,l=z.value;if(!(U.value&&a&&l&&l[Q.currentIndex]))return;const o=l[Q.currentIndex].$el;!function(t,a,n){let l=0;const o=t.scrollLeft,i=0===n?1:Math.round(1e3*n/16);!function n(){t.scrollLeft+=(a-o)/i,++l<i&&e(n)}()}(a,o.offsetLeft-(a.offsetWidth-o.offsetWidth)/2,t?0:+n.duration)},le=()=>{const e=Q.inited;O((()=>{const t=z.value;if(!t||!t[Q.currentIndex]||"line"!==n.type||k(y.value))return;const a=t[Q.currentIndex].$el,{lineWidth:l,lineHeight:o}=n,i=a.offsetLeft+a.offsetWidth/2,r={width:W(l),backgroundColor:n.color,transform:`translateX(${i}px) translateX(-50%)`};if(e&&(r.transitionDuration=`${n.duration}s`),C(o)){const e=W(o);r.height=e,r.borderRadius=e}Q.lineStyle=r}))},ie=(e,t)=>{const a=(e=>{const t=e<Q.currentIndex?-1:1;for(;e>=0&&e<q.length;){if(!q[e].disabled)return e;e+=t}})(e);if(!C(a))return;const o=q[a],i=K(o,a),r=null!==Q.currentIndex;Q.currentIndex!==a&&(Q.currentIndex=a,t||ne(),le()),i!==n.active&&(l("update:active",i),r&&l("change",i,o.title)),x&&!n.scrollspy&&M(Math.ceil(F(y.value)-ee.value))},re=(e,t)=>{const a=q.find(((t,a)=>K(t,a)===e)),n=a?q.indexOf(a):0;ie(n,t)},de=(l=!1)=>{if(n.scrollspy){const o=q[Q.currentIndex].$el;if(o&&R.value){const i=F(o,R.value)-te.value;b=!0,function(n,l,o,i){let r=t(n);const s=r<l,d=0===o?1:Math.round(1e3*o/16),c=(l-r)/d;!function t(){r+=c,(s&&r>l||!s&&r<l)&&(r=l),a(n,r),s&&r<l||!s&&r>l?e(t):i&&e(i)}()}(R.value,i,l?0:+n.duration,(()=>{b=!1}))}}},ce=e=>{x=e.isFixed,l("scroll",e)},ve=()=>q.map(((e,t)=>m(se,X({key:e.id,id:`${$}-${t}`,ref:V(t),type:n.type,color:n.color,style:e.titleStyle,class:e.titleClass,shrink:n.shrink,isActive:t===Q.currentIndex,controls:e.id,scrollable:U.value,activeColor:n.titleActiveColor,inactiveColor:n.titleInactiveColor,onClick:a=>((e,t,a)=>{const{title:o,disabled:i}=q[t],r=K(q[t],t);i?l("disabled",r,o):(P(n.beforeChange,{args:[r],done:()=>{ie(t),de()}}),l("click",r,o),J(e)),l("click-tab",{name:r,title:o,event:a,disabled:i})})(e,t,a)},D(e,["dot","badge","title","disabled","showZeroBadge"])),{title:e.$slots.title}))),pe=()=>{if("line"===n.type&&q.length)return m("div",{class:fe("line"),style:Q.lineStyle},null)},he=()=>{var e,t,a;const{type:l,border:i,sticky:r}=n,s=[m("div",{ref:r?void 0:B,class:[fe("wrap"),{[H]:"line"===l&&i}]},[m("div",{ref:w,role:"tablist",class:fe("nav",[l,{shrink:n.shrink,complete:U.value}]),style:Y.value,"aria-orientation":"horizontal"},[null==(e=o["nav-left"])?void 0:e.call(o),ve(),pe(),null==(t=o["nav-right"])?void 0:t.call(o)])]),null==(a=o["nav-bottom"])?void 0:a.call(o)];return r?m("div",{ref:B},[s]):s};h([()=>n.color,L],le),h((()=>n.active),(e=>{e!==_.value&&re(e)})),h((()=>q.length),(()=>{Q.inited&&(re(n.active),le(),O((()=>{ne(!0)})))}));return T({resize:()=>{le(),O((()=>{var e,t;return null==(t=null==(e=S.value)?void 0:e.swipeRef.value)?void 0:t.resize()}))},scrollTo:e=>{O((()=>{re(e),de(!0)}))}}),Z(le),N(le),i((()=>{re(n.active,!0),O((()=>{Q.inited=!0,B.value&&(p=I(B.value).height),ne(!0)}))})),g("scroll",(()=>{if(n.scrollspy&&!b){const e=(()=>{for(let e=0;e<q.length;e++){const{top:t}=I(q[e].$el);if(t>te.value)return 0===e?0:e-1}return q.length-1})();ie(e)}}),{target:R,passive:!0}),E({id:$,props:n,setLine:le,onRendered:(e,t)=>l("rendered",e,t),currentName:_,scrollIntoView:ne}),()=>m("div",{ref:y,class:fe([n.type])},[n.sticky?m(oe,{container:y.value,offsetTop:ee.value,onScroll:ce},{default:()=>[he()]}):he(),m(ue,{ref:S,count:q.length,inited:Q.inited,animated:n.animated,duration:n.duration,swipeable:n.swipeable,lazyRender:n.lazyRender,currentIndex:Q.currentIndex,onChange:ie},{default:()=>{var e;return[null==(e=o.default)?void 0:e.call(o)]}})])}});const[ge,me]=r("tab");const xe=B(s({name:ge,props:p({},K,{dot:Boolean,name:x,badge:x,title:String,disabled:Boolean,titleClass:V,titleStyle:[String,Object],showZeroBadge:S}),setup(e,{slots:t}){const a=G(),n=d(!1),{parent:l,index:o}=q(be);if(!l)return;const i=()=>{var t;return null!=(t=e.name)?t:o.value},r=v((()=>{const t=i()===l.currentName.value;return t&&!n.value&&(n.value=!0,l.props.lazyRender&&O((()=>{l.onRendered(i(),e.title)}))),t})),s=d(!r.value);return h(r,(e=>{e?s.value=!1:E((()=>{s.value=!0}))})),h((()=>e.title),(()=>{l.setLine(),l.scrollIntoView()})),Q(_,r),()=>{var e;const i=`${l.id}-${o.value}`,{animated:d,swipeable:c,scrollspy:u,lazyRender:v}=l.props;if(!t.default&&!d)return;const f=u||r.value;if(d||c)return m(te,{id:a,role:"tabpanel",class:me("panel-wrapper",{inactive:s.value}),tabindex:r.value?0:-1,"aria-hidden":!r.value,"aria-labelledby":i},{default:()=>{var e;return[m("div",{class:me("panel")},[null==(e=t.default)?void 0:e.call(t)])]}});const p=n.value||u||!v?null==(e=t.default)?void 0:e.call(t):null;return T({id:a}),U(m("div",{id:a,role:"tabpanel",class:me("panel"),tabindex:f?0:-1,"aria-labelledby":i},[p]),[[Y,f]])}}})),ye=B(he);export{xe as T,ye as a};
