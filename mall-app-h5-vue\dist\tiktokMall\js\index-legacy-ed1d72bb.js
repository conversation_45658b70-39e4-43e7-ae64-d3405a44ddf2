System.register(["./index-legacy-46a00900.js","./index-legacy-72e00c5f.js","./index-legacy-71866ecf.js","./index-legacy-f9c0699e.js","./index-legacy-df3e01aa.js","./use-route-legacy-be86ac1c.js"],(function(e,t){"use strict";var a,l,n,i,s,c,u,d,o,r,f,g,v,p,_,m,x,b,k,y,C,j,h,w,A=document.createElement("style");return A.textContent=".btn[data-v-ee5ef2fb]{margin:var(--van-cell-group-inset-padding);margin-top:2rem}[data-v-ee5ef2fb] .van-button{border-radius:4px}[data-v-ee5ef2fb] .van-cell__title,[data-v-ee5ef2fb] .van-cell__value{color:#333}.is-ar-cell-group[data-v-ee5ef2fb] .van-cell__value{text-align:left;padding-left:5px}\n",document.head.appendChild(A),{setters:[e=>{a=e._,l=e.u,n=e.l,i=e.r,s=e.s,c=e.k,u=e.m,d=e.av,o=e.c,r=e.e,f=e.w,g=e.n,v=e.b,p=e.a,_=e.j,m=e.o,x=e.f,b=e.t,k=e.A,y=e.x,C=e.T},e=>{j=e.B},e=>{h=e.C},()=>{},e=>{w=e.C},()=>{}],execute:function(){const t={class:"page-main-content"},A={class:"btn"};e("default",a({__name:"index",setup(e){const{t:a}=l(),B=n(),M=i(!1);s((()=>{M.value=["argos","argos2"].includes("tiktokMall")}));const T=c(),$=u((()=>T.isArLang)),D=()=>{_().logout()},E=()=>{C.loading({message:a("加载中"),forbidClick:!0}),setTimeout((()=>{C({message:a("当前已是最新版本，无需更新~"),duration:2e3})}),2e3)},I=()=>{B.push("/setting/cancellation")};return(e,l)=>{const n=d("fx-header"),i=w,s=h,c=j;return m(),o("div",t,[r(n,null,{title:f((()=>[x(b(e.$t("setting")),1)])),_:1}),r(s,{class:g({"is-ar-cell-group":v($)})},{default:f((()=>[r(i,{title:v(a)("清除缓存"),value:"0MB"},null,8,["title"]),r(i,{onClick:E,title:v(a)("检查更新"),"is-link":"",value:"V1.0.2"},null,8,["title"]),M.value?(m(),k(i,{key:0,onClick:I,title:v(a)("账号注销"),"is-link":""},null,8,["title"])):y("v-if",!0)])),_:1},8,["class"]),p("div",A,[r(c,{type:"danger",block:"",onClick:D},{default:f((()=>[x(b(e.$t("退出")),1)])),_:1})])])}}},[["__scopeId","data-v-ee5ef2fb"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/setting/index.vue"]]))}}}));
