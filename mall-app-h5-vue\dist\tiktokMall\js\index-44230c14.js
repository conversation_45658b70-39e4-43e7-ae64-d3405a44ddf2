import{_ as e,Y as a,l,u as t,r as o,j as s,q as n,av as i,c as r,e as u,w as d,a as c,b as p,T as m,cM as v,o as f,f as x,t as h}from"./index-3d21abf8.js";import{B as b}from"./index-2406f514.js";import{E as j}from"./index-9c8e9dca.js";import{n as y}from"./nationalityList-c6365b2b.js";import{c as g}from"./countryList-016fc82e.js";import"./use-route-cd41a893.js";/* empty css              *//* empty css               */import"./index-8c1841f6.js";import"./index-7dfcb82a.js";import"./use-id-a0619e01.js";import"./index-3d6106f5.js";import"./index-179203f3.js";const _={class:"bindVerify h-full bg-white"},$={class:"content"},w={style:{"margin-top":"22px"}},T={style:{"margin-top":"22px"}},V=e({__name:"index",setup(e){const V=a(),C=l(),{t:k}=t(),N=o(""),P=o(""),U=o(0),I=o(""),q=s(),A=o(!1),B=o("修改手机号码");n((()=>{const e=q.userInfo.phone;if(e&&!q.phoneverif){const a=e.split(" ");B.value="bindPhone",U.value=Number(a[0]),N.value=a[1];for(const e in g)g[e].dialCode===Number(a[0])&&(I.value=g[e].code)}}));const L=()=>{""!=N.value?/^[0-9]+$/.test(N.value)?""!=P.value?(A.value=!0,S()):m(k("请输入登录密码")):m(k("请输入正确的手机号码")):m(k("entryPhone"))},S=()=>{v({target:`${U.value} ${N.value}`,phone:`${U.value} ${N.value}`,password:P.value}).then((e=>{(async()=>{await q.getUserInfo(!0),m(k("bindSuccess")),A.value=!1,setTimeout((()=>{V.query.reset?C.go(-2):C.back()}),1e3)})()})).catch((e=>{A.value=!1}))},D=(e,a,l)=>{I.value=a,U.value=l},E=o(null),G=()=>{E.value.open()};return(e,a)=>{const l=i("fx-header"),t=b;return f(),r("div",_,[u(l,null,{title:d((()=>[x(h(p(k)(B.value)),1)])),_:1}),c("div",$,[c("div",w,[u(j,{label:p(k)("phoneNum"),placeholderText:p(k)("entryPhone"),modelValue:N.value,"onUpdate:modelValue":a[0]||(a[0]=e=>N.value=e),area:!0,onSelectArea:G,dialCode:U.value,icon:I.value},null,8,["label","placeholderText","modelValue","dialCode","icon"])]),c("div",T,[u(j,{label:e.$t("登录密码"),placeholderText:e.$t("请输入登录密码"),modelValue:P.value,"onUpdate:modelValue":a[1]||(a[1]=e=>P.value=e),typeText:"password",clearBtn:!1},null,8,["label","placeholderText","modelValue"])]),u(t,{class:"w-full",style:{"margin-top":"30px"},type:"primary",loading:A.value,onClick:L},{default:d((()=>[x(h(e.$t("confirm")),1)])),_:1},8,["loading"])]),u(y,{ref_key:"controlChildRef",ref:E,title:e.$t("selectArea"),onGetName:D},null,8,["title"])])}}},[["__scopeId","data-v-403e7485"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/changePhone/index.vue"]]);export{V as default};
