System.register(["./index-legacy-46a00900.js"],(function(e,a){"use strict";var o,d,n,i,t,m,r,l,c,p,h,C,s,F,u,b,g=document.createElement("style");return g.textContent=".area-code-dialog[data-v-8be7fad4]{width:100%;height:100vh;pointer-events:none;position:fixed;top:0;left:0;z-index:999998;opacity:0}.area-code-dialog>div[data-v-8be7fad4]{position:fixed;left:0}.area-code-dialog>div.code-content[data-v-8be7fad4]{width:100%;height:80vh;bottom:0;background-color:#fff;border-top-left-radius:16px;border-top-right-radius:16px;z-index:999999;-webkit-animation-duration:.75s;animation-duration:.75s}.area-code-dialog>div.code-content.pc[data-v-8be7fad4]{width:600px;left:50%;bottom:10vh;border-radius:16px;margin-left:-300px}.area-code-dialog>div.code-content>.title[data-v-8be7fad4]{width:100%;height:60px;text-align:center;line-height:60px;color:#000;font-size:18px}.area-code-dialog>div.code-content>.search-content[data-v-8be7fad4]{padding:0 15px;margin-bottom:15px}.area-code-dialog>div.code-content>.search-content>.content[data-v-8be7fad4]{height:44px;border-radius:44px;background-color:#f8f8f8;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;padding:0 20px;color:#000;font-size:14px}.area-code-dialog>div.code-content>.search-content>.content>input[data-v-8be7fad4]{-webkit-box-flex:1;-webkit-flex:1 1;flex:1 1;color:#000;width:100%;border:none;background-color:transparent}.area-code-dialog>div.code-content>.scroll-content[data-v-8be7fad4]{height:calc(80vh - 119px);overflow-y:scroll}.area-code-dialog>div.code-content>.scroll-content .countries-content.is-ar>.item>.name[data-v-8be7fad4]{padding-left:10px;padding-right:0}.area-code-dialog>div.code-content>.scroll-content .countries-content>.item[data-v-8be7fad4]{width:100%;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;padding:8px 15px}.area-code-dialog>div.code-content>.scroll-content .countries-content>.item>.name[data-v-8be7fad4]{-webkit-box-flex:1;-webkit-flex:1 1;flex:1 1;padding-right:10px;font-size:14px;color:#666}.area-code-dialog>div.code-content>.scroll-content .countries-content>.item>.code[data-v-8be7fad4]{font-size:16px;color:#000}.area-code-dialog>div.code-bg[data-v-8be7fad4]{top:0;width:100%;height:100vh;background-color:rgba(0,0,0,.4);z-index:999998;opacity:0;-webkit-transition:all .3s ease;transition:all .3s ease}.area-code-dialog.active[data-v-8be7fad4]{pointer-events:auto;opacity:1}.area-code-dialog.active>.code-content[data-v-8be7fad4]{-webkit-animation-name:bounceInUp;animation-name:bounceInUp}.area-code-dialog.active>.code-bg[data-v-8be7fad4]{opacity:1}.no-data[data-v-8be7fad4]{width:100%;height:200px;color:#333;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center}.input-item[data-v-efb302ad]{width:100%;height:44px;border:1px solid #ddd;border-radius:4px;padding:0 10px;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.input-item.active[data-v-efb302ad]{border-color:#fff}.input-item.turn[data-v-efb302ad]{background-color:snow;border-radius:44px;border-color:#f2db9c;padding:0 20px}.input-item.turn .van-cell[data-v-efb302ad] .van-field__control{color:#333}.input-item.turn .van-cell[data-v-efb302ad]::-webkit-input-placeholder{color:#ccc}.input-item.turn .van-cell[data-v-efb302ad]::-moz-placeholder{color:#ccc}.input-item.turn .van-cell[data-v-efb302ad]::placeholder{color:#ccc}.input-item.turn .icon[data-v-efb302ad]{color:#ccc}.input-item.is-ar[data-v-efb302ad] .van-field__control{text-align:right!important}.input-item.is-ar>.area-code[data-v-efb302ad]{padding-right:0;padding-left:10px}.input-item.is-ar>.area-code>p[data-v-efb302ad]{margin-left:8px;margin-right:0}.input-item>.area-code[data-v-efb302ad]{height:44px;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;font-size:14px;padding-right:10px}.input-item>.area-code>p[data-v-efb302ad]{margin-right:8px}.input-item>.area-code>.iconfont[data-v-efb302ad]{font-size:14px}.input-item .van-cell[data-v-efb302ad]{padding:0;background-color:transparent;border:none;font-size:14px;line-height:1;-webkit-box-flex:1;-webkit-flex:1 1;flex:1 1}.input-item .van-cell[data-v-efb302ad]:after{border:none}.input-item .van-cell[data-v-efb302ad] .van-field__control{color:#fff}.input-item .van-cell[data-v-efb302ad]::-webkit-input-placeholder{color:rgba(255,255,255,.6)}.input-item .van-cell[data-v-efb302ad]::-moz-placeholder{color:rgba(255,255,255,.6)}.input-item .van-cell[data-v-efb302ad]::placeholder{color:rgba(255,255,255,.6)}.input-item>.icon[data-v-efb302ad]{width:26px;height:26px;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center}\n",document.head.appendChild(g),{setters:[e=>{o=e._,d=e.u,n=e.i,i=e.r,t=e.m,m=e.o,r=e.c,l=e.a,c=e.t,p=e.b,h=e.h,C=e.v,s=e.n,F=e.F,u=e.y,b=e.H}],execute:function(){const a=[{code:"af",name:"Afghanistan (‫افغانستان‬‎)",dialCode:93,phoneFormat:"************"},{code:"al",name:"Albania (Shqipëri)",dialCode:355,phoneFormat:"************"},{code:"dz",name:"Algeria (‫الجزائر‬‎)",dialCode:213,phoneFormat:"0551 23 45 67"},{code:"as",name:"American Samoa",dialCode:1684,phoneFormat:"(*************"},{code:"ad",name:"Andorra",dialCode:376,phoneFormat:"312 345"},{code:"ao",name:"Angola",dialCode:244,phoneFormat:"***********"},{code:"ai",name:"Anguilla",dialCode:1264,phoneFormat:"(*************"},{code:"ag",name:"Antigua and Barbuda",dialCode:1268,phoneFormat:"(*************"},{code:"ar",name:"Argentina",dialCode:54,phoneFormat:"011 15-2345-6789"},{code:"am",name:"Armenia (Հայաստան)",dialCode:374,phoneFormat:"**********"},{code:"aw",name:"Aruba",dialCode:297,phoneFormat:"560 1234"},{code:"au",name:"Australia",dialCode:61,phoneFormat:"0412 345 678"},{code:"at",name:"Austria (Österreich)",dialCode:43,phoneFormat:"0664 123456"},{code:"az",name:"Azerbaijan (Azərbaycan)",dialCode:994,phoneFormat:"040 123 45 67"},{code:"bs",name:"Bahamas",dialCode:1242,phoneFormat:"(*************"},{code:"bh",name:"Bahrain (‫البحرين‬‎)",dialCode:973,phoneFormat:"3600 1234"},{code:"bd",name:"Bangladesh (বাংলাদেশ)",dialCode:880,phoneFormat:"************"},{code:"bb",name:"Barbados",dialCode:1246,phoneFormat:"(*************"},{code:"by",name:"Belarus (Беларусь)",dialCode:375,phoneFormat:"8 029 491-19-11"},{code:"be",name:"Belgium (België)",dialCode:32,phoneFormat:"0470 12 34 56"},{code:"bz",name:"Belize",dialCode:501,phoneFormat:"622-1234"},{code:"bj",name:"Benin (Bénin)",dialCode:229,phoneFormat:"90 01 12 34"},{code:"bm",name:"Bermuda",dialCode:1441,phoneFormat:"(*************"},{code:"bt",name:"Bhutan (འབྲུག)",dialCode:975,phoneFormat:"17 12 34 56"},{code:"bo",name:"Bolivia",dialCode:591,phoneFormat:"71234567"},{code:"ba",name:"Bosnia and Herzegovina (Босна и Херцеговина)",dialCode:387,phoneFormat:"061 123 456"},{code:"bw",name:"Botswana",dialCode:267,phoneFormat:"71 123 456"},{code:"br",name:"Brazil (Brasil)",dialCode:55,phoneFormat:"(11) 96123-4567"},{code:"io",name:"British Indian Ocean Territory",dialCode:246,phoneFormat:"380 1234"},{code:"vg",name:"British Virgin Islands",dialCode:1284,phoneFormat:"(*************"},{code:"bn",name:"Brunei",dialCode:673,phoneFormat:"712 3456"},{code:"bg",name:"Bulgaria (България)",dialCode:359,phoneFormat:"048 123 456"},{code:"bf",name:"Burkina Faso",dialCode:226,phoneFormat:"70 12 34 56"},{code:"bi",name:"Burundi (Uburundi)",dialCode:257,phoneFormat:"79 56 12 34"},{code:"kh",name:"Cambodia (កម្ពុជា)",dialCode:855,phoneFormat:"091 234 567"},{code:"cm",name:"Cameroon (Cameroun)",dialCode:237,phoneFormat:"6 71 23 45 67"},{code:"ca",name:"Canada",dialCode:1,phoneFormat:"(*************"},{code:"cv",name:"Cape Verde (Kabu Verdi)",dialCode:238,phoneFormat:"991 12 34"},{code:"bq",name:"Caribbean Netherlands",dialCode:599,phoneFormat:"318 1234"},{code:"ky",name:"Cayman Islands",dialCode:1345,phoneFormat:"(*************"},{code:"cf",name:"Central African Republic (République centrafricaine)",dialCode:236,phoneFormat:"70 01 23 45"},{code:"td",name:"Chad (Tchad)",dialCode:235,phoneFormat:"63 01 23 45"},{code:"cl",name:"Chile",dialCode:56,phoneFormat:"09 6123 4567"},{code:"cn",name:"China (中国)",dialCode:86,phoneFormat:"131 2345 6789"},{code:"cx",name:"Christmas Island",dialCode:61,phoneFormat:"0412 345 678"},{code:"cc",name:"Cocos (Keeling) Islands",dialCode:61,phoneFormat:"0412 345 678"},{code:"co",name:"Colombia",dialCode:57,phoneFormat:"321 1234567"},{code:"km",name:"Comoros (‫جزر القمر‬‎)",dialCode:269,phoneFormat:"321 23 45"},{code:"cd",name:"Congo (DRC) (Jamhuri ya Kidemokrasia ya Kongo)",dialCode:243,phoneFormat:"0991 234 567"},{code:"cg",name:"Congo (Republic) (Congo-Brazzaville)",dialCode:242,phoneFormat:"06 123 4567"},{code:"ck",name:"Cook Islands",dialCode:682,phoneFormat:"71 234"},{code:"cr",name:"Costa Rica",dialCode:506,phoneFormat:"8312 3456"},{code:"ci",name:"Côte d’Ivoire",dialCode:225,phoneFormat:"01 23 45 67"},{code:"hr",name:"Croatia (Hrvatska)",dialCode:385,phoneFormat:"************"},{code:"cu",name:"Cuba",dialCode:53,phoneFormat:"05 1234567"},{code:"cw",name:"Curaçao",dialCode:599,phoneFormat:"9 518 1234"},{code:"cy",name:"Cyprus (Κύπρος)",dialCode:357,phoneFormat:"96 123456"},{code:"cz",name:"Czech Republic (Česká republika)",dialCode:420,phoneFormat:"601 123 456"},{code:"dk",name:"Denmark (Danmark)",dialCode:45,phoneFormat:"20 12 34 56"},{code:"dj",name:"Djibouti",dialCode:253,phoneFormat:"77 83 10 01"},{code:"dm",name:"Dominica",dialCode:1767,phoneFormat:"(*************"},{code:"do",name:"Dominican Republic (República Dominicana)",dialCode:1,phoneFormat:"(*************"},{code:"ec",name:"Ecuador",dialCode:593,phoneFormat:"************"},{code:"eg",name:"Egypt (‫مصر‬‎)",dialCode:20,phoneFormat:"0100 123 4567"},{code:"sv",name:"El Salvador",dialCode:503,phoneFormat:"7012 3456"},{code:"gq",name:"Equatorial Guinea (Guinea Ecuatorial)",dialCode:240,phoneFormat:"222 123 456"},{code:"er",name:"Eritrea",dialCode:291,phoneFormat:"07 123 456"},{code:"ee",name:"Estonia (Eesti)",dialCode:372,phoneFormat:"5123 4567"},{code:"et",name:"Ethiopia",dialCode:251,phoneFormat:"************"},{code:"fk",name:"Falkland Islands (Islas Malvinas)",dialCode:500,phoneFormat:"51234"},{code:"fo",name:"Faroe Islands (Føroyar)",dialCode:298,phoneFormat:"211234"},{code:"fj",name:"Fiji",dialCode:679,phoneFormat:"701 2345"},{code:"fi",name:"Finland (Suomi)",dialCode:358,phoneFormat:"041 2345678"},{code:"fr",name:"France",dialCode:33,phoneFormat:"06 12 34 56 78"},{code:"gf",name:"French Guiana (Guyane française)",dialCode:594,phoneFormat:"0694 20 12 34"},{code:"pf",name:"French Polynesia (Polynésie française)",dialCode:689,phoneFormat:"87 12 34 56"},{code:"ga",name:"Gabon",dialCode:241,phoneFormat:"06 03 12 34"},{code:"gm",name:"Gambia",dialCode:220,phoneFormat:"301 2345"},{code:"ge",name:"Georgia (საქართველო)",dialCode:995,phoneFormat:"555 12 34 56"},{code:"de",name:"Germany (Deutschland)",dialCode:49,phoneFormat:"01512 3456789"},{code:"gh",name:"Ghana (Gaana)",dialCode:233,phoneFormat:"************"},{code:"gi",name:"Gibraltar",dialCode:350,phoneFormat:"57123456"},{code:"gr",name:"Greece (Ελλάδα)",dialCode:30,phoneFormat:"************"},{code:"gl",name:"Greenland (Kalaallit Nunaat)",dialCode:299,phoneFormat:"22 12 34"},{code:"gd",name:"Grenada",dialCode:1473,phoneFormat:"(*************"},{code:"gp",name:"Guadeloupe",dialCode:590,phoneFormat:"0690 30-1234"},{code:"gu",name:"Guam",dialCode:1671,phoneFormat:"(*************"},{code:"gt",name:"Guatemala",dialCode:502,phoneFormat:"5123 4567"},{code:"gg",name:"Guernsey",dialCode:44,phoneFormat:"07781 123456"},{code:"gn",name:"Guinea (Guinée)",dialCode:224,phoneFormat:"601 12 34 56"},{code:"gw",name:"Guinea-Bissau (Guiné Bissau)",dialCode:245,phoneFormat:"955 012 345"},{code:"gy",name:"Guyana",dialCode:592,phoneFormat:"609 1234"},{code:"ht",name:"Haiti",dialCode:509,phoneFormat:"34 10 1234"},{code:"hn",name:"Honduras",dialCode:504,phoneFormat:"9123-4567"},{code:"hk",name:"Hong Kong (香港)",dialCode:852,phoneFormat:"5123 4567"},{code:"hu",name:"Hungary (Magyarország)",dialCode:36,phoneFormat:"(20) 123 4567"},{code:"is",name:"Iceland (Ísland)",dialCode:354,phoneFormat:"611 1234"},{code:"in",name:"India (भारत)",dialCode:91,phoneFormat:"099876 54321"},{code:"id",name:"Indonesia",dialCode:62,phoneFormat:"0812-345-678"},{code:"ir",name:"Iran (‫ایران‬‎)",dialCode:98,phoneFormat:"0912 345 6789"},{code:"iq",name:"Iraq (‫العراق‬‎)",dialCode:964,phoneFormat:"0791 234 5678"},{code:"ie",name:"Ireland",dialCode:353,phoneFormat:"************"},{code:"im",name:"Isle of Man",dialCode:44,phoneFormat:"07924 123456"},{code:"il",name:"Israel (‫ישראל‬‎)",dialCode:972,phoneFormat:"************"},{code:"it",name:"Italy (Italia)",dialCode:39,phoneFormat:"************"},{code:"jm",name:"Jamaica",dialCode:1876,phoneFormat:"(*************"},{code:"jp",name:"Japan (日本)",dialCode:81,phoneFormat:"090-1234-5678"},{code:"je",name:"Jersey",dialCode:44,phoneFormat:"07797 123456"},{code:"jo",name:"Jordan (‫الأردن‬‎)",dialCode:962,phoneFormat:"07 9012 3456"},{code:"kz",name:"Kazakhstan (Казахстан)",dialCode:7,phoneFormat:"8 (*************"},{code:"ke",name:"Kenya",dialCode:254,phoneFormat:"0712 123456"},{code:"ki",name:"Kiribati",dialCode:686,phoneFormat:"72012345"},{code:"xk",name:"Kosovo",dialCode:383,phoneFormat:""},{code:"kw",name:"Kuwait (‫الكويت‬‎)",dialCode:965,phoneFormat:"500 12345"},{code:"kg",name:"Kyrgyzstan (Кыргызстан)",dialCode:996,phoneFormat:"0700 123 456"},{code:"la",name:"Laos (ລາວ)",dialCode:856,phoneFormat:"020 23 123 456"},{code:"lv",name:"Latvia (Latvija)",dialCode:371,phoneFormat:"21 234 567"},{code:"lb",name:"Lebanon (‫لبنان‬‎)",dialCode:961,phoneFormat:"71 123 456"},{code:"ls",name:"Lesotho",dialCode:266,phoneFormat:"5012 3456"},{code:"lr",name:"Liberia",dialCode:231,phoneFormat:"************"},{code:"ly",name:"Libya (‫ليبيا‬‎)",dialCode:218,phoneFormat:"091-2345678"},{code:"li",name:"Liechtenstein",dialCode:423,phoneFormat:"660 234 567"},{code:"lt",name:"Lithuania (Lietuva)",dialCode:370,phoneFormat:"(8-612) 34567"},{code:"lu",name:"Luxembourg",dialCode:352,phoneFormat:"628 123 456"},{code:"mo",name:"Macau (澳門)",dialCode:853,phoneFormat:"6612 3456"},{code:"mk",name:"Macedonia (FYROM) (Македонија)",dialCode:389,phoneFormat:"072 345 678"},{code:"mg",name:"Madagascar (Madagasikara)",dialCode:261,phoneFormat:"032 12 345 67"},{code:"mw",name:"Malawi",dialCode:265,phoneFormat:"0991 23 45 67"},{code:"my",name:"Malaysia",dialCode:60,phoneFormat:"************"},{code:"mv",name:"Maldives",dialCode:960,phoneFormat:"771-2345"},{code:"ml",name:"Mali",dialCode:223,phoneFormat:"65 01 23 45"},{code:"mt",name:"Malta",dialCode:356,phoneFormat:"9696 1234"},{code:"mh",name:"Marshall Islands",dialCode:692,phoneFormat:"235-1234"},{code:"mq",name:"Martinique",dialCode:596,phoneFormat:"0696 20 12 34"},{code:"mr",name:"Mauritania (‫موريتانيا‬‎)",dialCode:222,phoneFormat:"22 12 34 56"},{code:"mu",name:"Mauritius (Moris)",dialCode:230,phoneFormat:"5251 2345"},{code:"yt",name:"Mayotte",dialCode:262,phoneFormat:"0639 12 34 56"},{code:"mx",name:"Mexico (México)",dialCode:52,phoneFormat:"044 ************"},{code:"fm",name:"Micronesia",dialCode:691,phoneFormat:"350 1234"},{code:"md",name:"Moldova (Republica Moldova)",dialCode:373,phoneFormat:"0621 12 345"},{code:"mc",name:"Monaco",dialCode:377,phoneFormat:"06 12 34 56 78"},{code:"mn",name:"Mongolia (Монгол)",dialCode:976,phoneFormat:"8812 3456"},{code:"me",name:"Montenegro (Crna Gora)",dialCode:382,phoneFormat:"067 622 901"},{code:"ms",name:"Montserrat",dialCode:1664,phoneFormat:"(*************"},{code:"ma",name:"Morocco (‫المغرب‬‎)",dialCode:212,phoneFormat:"0650-123456"},{code:"mz",name:"Mozambique (Moçambique)",dialCode:258,phoneFormat:"82 123 4567"},{code:"mm",name:"Myanmar (Burma) (မြန်မာ)",dialCode:95,phoneFormat:"09 212 3456"},{code:"na",name:"Namibia (Namibië)",dialCode:264,phoneFormat:"************"},{code:"nr",name:"Nauru",dialCode:674,phoneFormat:"555 1234"},{code:"np",name:"Nepal (नेपाल)",dialCode:977,phoneFormat:"984-1234567"},{code:"nl",name:"Netherlands (Nederland)",dialCode:31,phoneFormat:"06 12345678"},{code:"nc",name:"New Caledonia (Nouvelle-Calédonie)",dialCode:687,phoneFormat:"75.12.34"},{code:"nz",name:"New Zealand",dialCode:64,phoneFormat:"************"},{code:"ni",name:"Nicaragua",dialCode:505,phoneFormat:"8123 4567"},{code:"ne",name:"Niger (Nijar)",dialCode:227,phoneFormat:"93 12 34 56"},{code:"ng",name:"Nigeria",dialCode:234,phoneFormat:"0802 123 4567"},{code:"nu",name:"Niue",dialCode:683,phoneFormat:"1234"},{code:"nf",name:"Norfolk Island",dialCode:672,phoneFormat:"3 81234"},{code:"kp",name:"North Korea (조선 민주주의 인민 공화국)",dialCode:850,phoneFormat:"0192 123 4567"},{code:"mp",name:"Northern Mariana Islands",dialCode:1670,phoneFormat:"(*************"},{code:"no",name:"Norway (Norge)",dialCode:47,phoneFormat:"406 12 345"},{code:"om",name:"Oman (‫عُمان‬‎)",dialCode:968,phoneFormat:"9212 3456"},{code:"pk",name:"Pakistan (‫پاکستان‬‎)",dialCode:92,phoneFormat:"0301 2345678"},{code:"pw",name:"Palau",dialCode:680,phoneFormat:"620 1234"},{code:"ps",name:"Palestine (‫فلسطين‬‎)",dialCode:970,phoneFormat:"0599 123 456"},{code:"pa",name:"Panama (Panamá)",dialCode:507,phoneFormat:"6001-2345"},{code:"pg",name:"Papua New Guinea",dialCode:675,phoneFormat:"681 2345"},{code:"py",name:"Paraguay",dialCode:595,phoneFormat:"0961 456789"},{code:"pe",name:"Peru (Perú)",dialCode:51,phoneFormat:"912 345 678"},{code:"ph",name:"Philippines",dialCode:63,phoneFormat:"0905 123 4567"},{code:"pl",name:"Poland (Polska)",dialCode:48,phoneFormat:"512 345 678"},{code:"pt",name:"Portugal",dialCode:351,phoneFormat:"912 345 678"},{code:"pr",name:"Puerto Rico",dialCode:1,phoneFormat:"(*************"},{code:"qa",name:"Qatar (‫قطر‬‎)",dialCode:974,phoneFormat:"3312 3456"},{code:"re",name:"Réunion (La Réunion)",dialCode:262,phoneFormat:"0692 12 34 56"},{code:"ro",name:"Romania (România)",dialCode:40,phoneFormat:"0712 345 678"},{code:"ru",name:"Russia (Россия)",dialCode:7,phoneFormat:"8 (912) 345-67-89"},{code:"rw",name:"Rwanda",dialCode:250,phoneFormat:"0720 123 456"},{code:"bl",name:"Saint Barthélemy (Saint-Barthélemy)",dialCode:590,phoneFormat:"0690 30-1234"},{code:"sh",name:"Saint Helena",dialCode:290,phoneFormat:"51234"},{code:"kn",name:"Saint Kitts and Nevis",dialCode:1869,phoneFormat:"(*************"},{code:"lc",name:"Saint Lucia",dialCode:1758,phoneFormat:"(*************"},{code:"mf",name:"Saint Martin (Saint-Martin (partie française))",dialCode:590,phoneFormat:"0690 30-1234"},{code:"pm",name:"Saint Pierre and Miquelon (Saint-Pierre-et-Miquelon)",dialCode:508,phoneFormat:"055 12 34"},{code:"vc",name:"Saint Vincent and the Grenadines",dialCode:1784,phoneFormat:"(*************"},{code:"ws",name:"Samoa",dialCode:685,phoneFormat:"601234"},{code:"sm",name:"San Marino",dialCode:378,phoneFormat:"66 66 12 12"},{code:"st",name:"São Tomé and Príncipe (São Tomé e Príncipe)",dialCode:239,phoneFormat:"981 2345"},{code:"sa",name:"Saudi Arabia (‫المملكة العربية السعودية‬‎)",dialCode:966,phoneFormat:"************"},{code:"sn",name:"Senegal (Sénégal)",dialCode:221,phoneFormat:"70 123 45 67"},{code:"rs",name:"Serbia (Србија)",dialCode:381,phoneFormat:"060 1234567"},{code:"sc",name:"Seychelles",dialCode:248,phoneFormat:"2 510 123"},{code:"sl",name:"Sierra Leone",dialCode:232,phoneFormat:"(025) 123456"},{code:"sg",name:"Singapore",dialCode:65,phoneFormat:"8123 4567"},{code:"sx",name:"Sint Maarten",dialCode:1721,phoneFormat:"(*************"},{code:"sk",name:"Slovakia (Slovensko)",dialCode:421,phoneFormat:"0912 123 456"},{code:"si",name:"Slovenia (Slovenija)",dialCode:386,phoneFormat:"031 234 567"},{code:"sb",name:"Solomon Islands",dialCode:677,phoneFormat:"74 21234"},{code:"so",name:"Somalia (Soomaaliya)",dialCode:252,phoneFormat:"7 1123456"},{code:"za",name:"South Africa",dialCode:27,phoneFormat:"************"},{code:"kr",name:"South Korea (대한민국)",dialCode:82,phoneFormat:"010-0000-0000"},{code:"ss",name:"South Sudan (‫جنوب السودان‬‎)",dialCode:211,phoneFormat:"0977 123 456"},{code:"es",name:"Spain (España)",dialCode:34,phoneFormat:"612 34 56 78"},{code:"lk",name:"Sri Lanka (ශ්‍රී ලංකාව)",dialCode:94,phoneFormat:"************"},{code:"sd",name:"Sudan (‫السودان‬‎)",dialCode:249,phoneFormat:"************"},{code:"sr",name:"Suriname",dialCode:597,phoneFormat:"741-2345"},{code:"sj",name:"Svalbard and Jan Mayen",dialCode:47,phoneFormat:"412 34 567"},{code:"sz",name:"Swaziland",dialCode:268,phoneFormat:"7612 3456"},{code:"se",name:"Sweden (Sverige)",dialCode:46,phoneFormat:"070-123 45 67"},{code:"ch",name:"Switzerland (Schweiz)",dialCode:41,phoneFormat:"078 123 45 67"},{code:"sy",name:"Syria (‫سوريا‬‎)",dialCode:963,phoneFormat:"0944 567 890"},{code:"tw",name:"Taiwan (台灣)",dialCode:886,phoneFormat:"0912 345 678"},{code:"tj",name:"Tajikistan",dialCode:992,phoneFormat:"(8) 917 12 3456"},{code:"tz",name:"Tanzania",dialCode:255,phoneFormat:"0621 234 567"},{code:"th",name:"Thailand (ไทย)",dialCode:66,phoneFormat:"************"},{code:"tl",name:"Timor-Leste",dialCode:670,phoneFormat:"7721 2345"},{code:"tg",name:"Togo",dialCode:228,phoneFormat:"90 11 23 45"},{code:"tk",name:"Tokelau",dialCode:690,phoneFormat:"7290"},{code:"to",name:"Tonga",dialCode:676,phoneFormat:"771 5123"},{code:"tt",name:"Trinidad and Tobago",dialCode:1868,phoneFormat:"(*************"},{code:"tn",name:"Tunisia (‫تونس‬‎)",dialCode:216,phoneFormat:"20 123 456"},{code:"tr",name:"Turkey (Türkiye)",dialCode:90,phoneFormat:"0501 234 56 78"},{code:"tm",name:"Turkmenistan",dialCode:993,phoneFormat:"8 66 123456"},{code:"tc",name:"Turks and Caicos Islands",dialCode:1649,phoneFormat:"(*************"},{code:"tv",name:"Tuvalu",dialCode:688,phoneFormat:"901234"},{code:"us",name:"United States",dialCode:1,phoneFormat:"(*************"},{code:"gb",name:"United Kingdom",dialCode:44,phoneFormat:"07400 123456"},{code:"vi",name:"U.S. Virgin Islands",dialCode:1340,phoneFormat:"(*************"},{code:"ug",name:"Uganda",dialCode:256,phoneFormat:"0712 345678"},{code:"ua",name:"Ukraine (Україна)",dialCode:380,phoneFormat:"************"},{code:"ae",name:"United Arab Emirates (‫الإمارات العربية المتحدة‬‎)",dialCode:971,phoneFormat:"************"},{code:"uy",name:"Uruguay",dialCode:598,phoneFormat:"094 231 234"},{code:"uz",name:"Uzbekistan (Oʻzbekiston)",dialCode:998,phoneFormat:"8 91 234 56 78"},{code:"vu",name:"Vanuatu",dialCode:678,phoneFormat:"591 2345"},{code:"va",name:"Vatican City (Città del Vaticano)",dialCode:39,phoneFormat:"************"},{code:"ve",name:"Venezuela",dialCode:58,phoneFormat:"0412-1234567"},{code:"vn",name:"Vietnam (Việt Nam)",dialCode:84,phoneFormat:"091 234 56 78"},{code:"wf",name:"Wallis and Futuna",dialCode:681,phoneFormat:"50 12 34"},{code:"eh",name:"Western Sahara (‫الصحراء الغربية‬‎)",dialCode:212,phoneFormat:"0650-123456"},{code:"ye",name:"Yemen (‫اليمن‬‎)",dialCode:967,phoneFormat:"0712 345 678"},{code:"zm",name:"Zambia",dialCode:260,phoneFormat:"095 5123456"},{code:"zw",name:"Zimbabwe",dialCode:263,phoneFormat:"************"},{code:"ax",name:"Åland Islands",dialCode:358,phoneFormat:"041 2345678"}],g={class:"title"},v={class:"search-content"},f={class:"content"},k=["placeholder"],x={class:"scroll-content"},y=["onClick"],w={class:"name"},S={class:"code"},z={key:1,class:"no-data"},M={__name:"index",props:{modelValue:{type:Boolean,default:!1}},emits:["update:modelValue","done"],setup(e,{emit:o}){const{t:M}=d(),B=n(),I=i(""),G=t((()=>{const e=a.filter((e=>{const a=I.value.toLowerCase(),o=e.name.toLowerCase(),d=String(e.dialCode);return isNaN(a)?o.indexOf(a)>-1:d===a}));return I.value?e:a})),N=()=>{o("update:modelValue",!1)};return(a,d)=>(m(),r("div",{class:s([{active:e.modelValue},"area-code-dialog"])},[l("div",{class:s([{pc:!p(b)()},"code-content"])},[l("div",g,c(p(M)("selectArea")),1),l("div",v,[l("div",f,[h(l("input",{"onUpdate:modelValue":d[0]||(d[0]=e=>I.value=e),type:"text",placeholder:p(M)("entrynational")},null,8,k),[[C,I.value]])])]),l("div",x,[l("div",{class:s(["countries-content",{"is-ar":p(B)}])},[p(G).length?(m(!0),r(F,{key:0},u(p(G),(e=>(m(),r("div",{key:e.code,class:"item",onClick:a=>(o("done",e),void N())},[l("div",w,c(e.name),1),l("div",S,c(e.dialCode),1)],8,y)))),128)):(m(),r("div",z,c(p(M)("noData")),1))],2)])],2),l("div",{class:"code-bg",onClick:N})],2))}};e("A",o(M,[["__scopeId","data-v-8be7fad4"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/components/area-code-dialog/index.vue"]]))}}}));
