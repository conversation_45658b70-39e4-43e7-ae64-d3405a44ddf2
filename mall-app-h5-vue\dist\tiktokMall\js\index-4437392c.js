import{_ as e,r as a,av as l,c as t,e as s,w as o,a as i,b as n,aV as r,L as d,o as u,f as m,D as p,E as c}from"./index-3d21abf8.js";import{B as f}from"./index-2406f514.js";import{n as b}from"./nationalityList-c6365b2b.js";import"./use-route-cd41a893.js";import"./index-3d6106f5.js";/* empty css              *//* empty css               */import"./index-179203f3.js";import"./index-8c1841f6.js";import"./index-7dfcb82a.js";import"./use-id-a0619e01.js";import"./countryList-016fc82e.js";const v={class:"h-full w-full bg-white"},x={class:"main"},j=(e=>(p("data-v-b0ed097b"),e=e(),c(),e))((()=>i("div",null,"Mobile number",-1))),y=e({__name:"index",setup(e){let p=a(null),c=a(0);const y=a(null);let _=a("");const h=()=>{},C=()=>{y.value.open()},w=(e,a,l)=>{_.value=a,c.value=l};return(e,a)=>{const g=l("fx-header"),k=l("ExInput"),F=f;return u(),t("div",v,[s(g,null,{title:o((()=>[m(" Mobile number ")])),_:1}),i("div",x,[j,i("div",null,[s(k,{style:{"padding-bottom":"0!important"},placeholderText:"Please enter your number",modelValue:n(p),"onUpdate:modelValue":a[0]||(a[0]=e=>r(p)?p.value=e:p=e),dialCode:n(c),onSelectArea:C,area:"",icon:n(_)},null,8,["modelValue","dialCode","icon"])]),s(F,{class:"w-full",type:n(p)?"primary ":"",style:d({marginTop:"10px",backgroundColor:n(p)?"#1552F0":"#F6F6F6",color:n(p)?"#fff":"#999"}),onClick:h},{default:o((()=>[m("Save")])),_:1},8,["type","style"]),s(b,{ref_key:"controlChildRef",ref:y,title:e.$t("selectArea"),onGetName:w},null,8,["title"])])])}}},[["__scopeId","data-v-b0ed097b"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/number/index.vue"]]);export{y as default};
