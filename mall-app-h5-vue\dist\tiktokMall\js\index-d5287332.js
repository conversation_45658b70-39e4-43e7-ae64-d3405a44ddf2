import{_ as a,u as s,l as e,r as t,s as l,k as i,m as n,av as o,c as r,e as u,w as c,n as d,b as f,a as m,j as p,o as g,f as v,t as k,A as x,x as _,T as b}from"./index-3d21abf8.js";import{B as j}from"./index-2406f514.js";import{C}from"./index-6aaac5d3.js";/* empty css              */import{C as h}from"./index-7dfcb82a.js";import"./use-route-cd41a893.js";const w={class:"page-main-content"},y={class:"btn"},A=a({__name:"index",setup(a){const{t:A}=s(),B=e(),M=t(!1);l((()=>{M.value=["argos","argos2"].includes("tiktokMall")}));const T=i(),$=n((()=>T.isArLang)),D=()=>{p().logout()},I=()=>{b.loading({message:A("加载中"),forbidClick:!0}),setTimeout((()=>{b({message:A("当前已是最新版本，无需更新~"),duration:2e3})}),2e3)},L=()=>{B.push("/setting/cancellation")};return(a,s)=>{const e=o("fx-header"),t=h,l=C,i=j;return g(),r("div",w,[u(e,null,{title:c((()=>[v(k(a.$t("setting")),1)])),_:1}),u(l,{class:d({"is-ar-cell-group":f($)})},{default:c((()=>[u(t,{title:f(A)("清除缓存"),value:"0MB"},null,8,["title"]),u(t,{onClick:I,title:f(A)("检查更新"),"is-link":"",value:"V1.0.2"},null,8,["title"]),M.value?(g(),x(t,{key:0,onClick:L,title:f(A)("账号注销"),"is-link":""},null,8,["title"])):_("v-if",!0)])),_:1},8,["class"]),m("div",y,[u(i,{type:"danger",block:"",onClick:D},{default:c((()=>[v(k(a.$t("退出")),1)])),_:1})])])}}},[["__scopeId","data-v-ee5ef2fb"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/setting/index.vue"]]);export{A as default};
