import{_ as a,d as e,Y as s,l as t,j as o,u as n,m as l,r,q as i,av as c,c as u,e as p,w as d,a as m,o as f,f as v,t as h,b as k,D as g,E as w}from"./index-3d21abf8.js";import{l as x}from"./config-6c4e82a2.js";const S=(a=>(g("data-v-2edf1d0e"),a=a(),w(),a))((()=>m("div",{style:{height:"46px"}},null,-1))),b=["src"],_=e({name:"ShopContractSign"}),I=a(Object.assign(_,{setup(a){const e=s(),g=t(),w=o(),{t:_,locale:I}=n(),$=x.find((a=>"tiktokMall"===a.key)),j=$?$.name:"Argos",y=l((()=>{var a;return(null==(a=w.userInfo)?void 0:a.token)||""})),q=r(""),{hostname:C,origin:D}=window.location,T="localhost"===C?"https://tkittkit.com/promote/#/pact/":`${D}/promote/#/pact/`;return sessionStorage.removeItem("SellToken"),sessionStorage.setItem("SellToken",y.value),q.value=`${T}?token=${y.value}&lang=${I.value}&name=${j}`,i((()=>{const{back:a}=e.query,s=!!a&&Boolean(Number(a));window.closePopup=async()=>{await w.getUserInfo(!0),s?g.back():g.push("/shop")}})),(a,e)=>{const s=c("fx-header");return f(),u("div",null,[p(s,{fixed:!0},{title:d((()=>[v(h(k(_)("签订电子合同")),1)])),_:1}),S,m("iframe",{src:q.value,class:"iframe-content"},null,8,b)])}}}),[["__scopeId","data-v-2edf1d0e"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/shop/contractSign/index.vue"]]);export{I as default};
