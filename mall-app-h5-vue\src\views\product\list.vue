<template>
  <div class="product page-main-content has-fixed-header">
    <fx-header fixed>
      <template #title>{{t('product.2')}}</template>
      <template #right>
        <img @click="search" class="nav_filtering_icon" :src="searchIconTopUrl" />
      </template>
    </fx-header>
    <div class="fixed-header-spacer"></div>

    <!-- 分类选择器 -->
    <div class="dropdown">
      <div class="dropdownitem one h-10.5" @click="classifyOneShow = true">
        <div class="p-2.5 bg-white flex justify-between items-center w-full h-full">
          <span class="text-xs">{{ title1 }}</span>
          <div class="triangle"></div>
        </div>
      </div>
      <div class="dropdownitem one h-10.5" @click="classifyTwoShow = true">
        <div class="p-2.5 bg-white flex justify-between items-center w-full h-full">
          <span class="text-xs">{{ title2 }}</span>
          <div class="triangle"></div>
        </div>
      </div>
    </div>

    <!-- 一级分类弹窗 -->
    <van-popup v-model:show="classifyOneShow" round>
      <div class="classify-pop-content">
        <div class="title">{{ t('一级分类') }}</div>
        <div class="content">
          <div v-for="item in categoryOneArry" :key="item.categoryId" class="classify-item" @click="classifyHandle(item, true)">
            {{ item.name }}
            <van-icon v-if="classifyOneActive === item.categoryId" name="success" class="yes"/>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 二级分类弹窗 -->
    <van-popup v-model:show="classifyTwoShow" round>
      <div class="classify-pop-content">
        <div class="title">{{ t('二级分类') }}</div>
        <div class="content">
          <div v-for="item in categoryTwoArry" :key="item.categoryId" class="classify-item" @click="classifyHandle(item, false)">
            {{ item.name }}
            <van-icon v-if="classifyTwoActive === item.categoryId" name="success" class="yes"/>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 商品列表 -->
    <div class="list mt-2 mb-4" :class="isArLang? 'ml-4' : 'mr-4'">
      <van-pull-refresh
        ref="pullRefreshRef"
        :loading-text="$t('刷新中')"
        :loosing-text="$t('释放以刷新')"
        :pulling-text="$t('下拉以刷新')"
        :head-height="50"
        :pull-distance="150"
        :success-text="$t('刷新成功')"
        :success-duration="1000"
        :disabled="pullRefreshDisabled"
        v-model="refreshing"
        @refresh="onRefresh"
        @touchstart="onTouchStart"
        @touchmove="onTouchMove"
        @touchend="onTouchEnd">
        <van-list
          v-model:loading="loading"
          :loading-text="$t('加载中')"
          :finished="finished"
          :finished-text="t('product.3')"
          @load="onLoad">
          <div class="item pb-3 pt-3 flex" v-for="(item, index) in list" :key="index">
            <div class="pl-3 pr-3">
              <div class="check-icon" @click="item.check = !item.check" :class="[item.check ? 'check-true ' : 'check']">
                <i class="iconfont icon-duigoux"></i>
              </div>
            </div>
            <div class="flex-1 flex left">
              <div class="product-img-wrap">
                <img class="w-20 h-20" style="object-fit: contain" :src="item.imgUrl1" />
              </div>
              <div class="product-info" :class="{'is-ar': isArLang}">
                <div class="name">{{ item.name }}</div>
                <div class="Specification">
                  <span>{{ item.categoryName }}</span>
                </div>
                <div class="money">${{ numberStrFormat(item.systemPrice) }}</div>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>

      <!-- 底部操作栏 -->
      <div v-if="bottomShow" class="flex fixed-wrap" :class="{'pl-3': !isArLang && !bottomDisabledType, 'pr-3': isArLang && !bottomDisabledType}">
        <div v-if="!bottomDisabledType" class="flex-1 flex" @click="allCheck">
          <div class="check-icon" :class="[isAll ? 'check-true ' : 'check']">
            <i class="iconfont icon-duigoux"></i>
          </div>
          <div :class="isArLang? 'pr-2' : 'pl-2'">
            {{t('product.5')}}: {{ selectNumber() }}
          </div>
        </div>
        <div v-if="!bottomDisabledType" class="submit-but" @click="openEdit">{{t('product.6')}}</div>
        <div v-else class="submit-but disabled" @click="openSetPage">{{ bottomDisabledType === 1 ? t('product.34') : t('product.35') }}</div>
      </div>
    </div>

    <!-- 编辑利润弹窗 -->
    <edit-profit :isEdit="isEdit" @update="updateInfo" :productArry="productArry" @close="close"></edit-profit>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { sellerInfo } from '@/service/shop.api.js'
import { _getIdentify } from '@/service/user.api.js'
import { getSystemGoods, categoryListTree } from "@/service/product.api";
import editProfit from './components/editProfit.vue';
import { openPage, numberStrFormat } from '@/utils'
import { Toast } from 'vant';
import { arLangCheck } from '@/utils/arLangCheck'
import searchIconTopUrl from '@/assets/imgs/product/search-icon-top.png'

const isArLang = arLangCheck()
const { t } = useI18n();
const router = useRouter()

// 基础状态
const pageNum = ref(1)
const list = ref([])
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const pullRefreshDisabled = ref(false)
const pullRefreshRef = ref(null)

// 触摸相关状态
let touchStartY = 0
let touchStartTime = 0
let isValidPullGesture = false

// 选择相关
const isAll = ref(false)
const isEdit = ref(false)
const productArry = ref([])

// 底部操作栏
const bottomShow = ref(false)
const bottomDisabledType = ref(0)

// 分类相关
const categoryOneArry = ref([])
const title1 = ref(t('一级分类'))
const title2 = ref(t('二级分类'))
const classifyOneShow = ref(false)
const classifyTwoShow = ref(false)
const classifyOneActive = ref('')
const classifyTwoActive = ref('')

let scrollCleanup = null

onMounted(() => {
  sessionStorage.setItem('productReload', true)
  getShopInfo()
  getCategory()
  scrollCleanup = setupScrollListener()
})

onUnmounted(() => {
  if (scrollCleanup) {
    scrollCleanup()
  }
})

// 设置滚动监听
const setupScrollListener = () => {
  let scrollTimer = null

  const handleScroll = () => {
    if (scrollTimer) {
      clearTimeout(scrollTimer)
    }

    scrollTimer = setTimeout(() => {
      const scrollTop = document.documentElement.scrollTop || document.body.scrollTop

      // 只有在页面顶部（滚动位置小于3px）时才允许下拉刷新
      const isAtTop = scrollTop <= 3
      pullRefreshDisabled.value = !isAtTop

      // 如果页面滚动了，重置触摸手势状态
      if (scrollTop > 3) {
        isValidPullGesture = false
      }
    }, 16)
  }

  window.addEventListener('scroll', handleScroll, { passive: true })

  // 组件卸载时移除监听
  const cleanup = () => {
    window.removeEventListener('scroll', handleScroll)
    if (scrollTimer) {
      clearTimeout(scrollTimer)
    }
  }

  // 返回清理函数
  return cleanup
}

const getShopInfo = async () => {
  let dataInfo = {}
  // 基础信息
  await sellerInfo().then(res => {
    dataInfo = res || {}
  })

  // 认证信息
  await _getIdentify().then(res => {
    const status = Number(res.status)
    let statusInfo = status
    if (status === 2) {
      statusInfo = 3
    } else if (status === 3) {
      statusInfo = 2
    }
    dataInfo.authStatus = statusInfo
  })

  const { avatar, name, authStatus } = dataInfo
  if (!avatar || !name) {
    bottomDisabledType.value = 1
  } else if (authStatus === 0 || authStatus === 1 || authStatus === 2) {
    bottomDisabledType.value = 2
  }

  bottomShow.value = true
}

const openSetPage = () => {
  const href = bottomDisabledType.value === 1 ? '/shop/settings' : '/name'
  openPage(href)
}

const close = () => {
  console.log('close')
  isEdit.value = false
}

const openEdit = () => {
  productArry.value = []
  list.value.map((item) => {
    if (item.check) {
      productArry.value.push(item.id)
    }
  })
  if (productArry.value.length) {
    isEdit.value = true
  } else {
    Toast(t('请选择商品'))
  }
}

// 触摸开始
const onTouchStart = (event) => {
  const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
  touchStartY = event.touches[0].clientY
  touchStartTime = Date.now()

  // 只有在页面顶部才允许下拉刷新手势
  isValidPullGesture = scrollTop <= 5

  console.log('触摸开始:', { scrollTop, isValidPullGesture, touchStartY })
}

// 触摸移动
const onTouchMove = (event) => {
  if (!isValidPullGesture) {
    return
  }

  const currentY = event.touches[0].clientY
  const deltaY = currentY - touchStartY
  const scrollTop = document.documentElement.scrollTop || document.body.scrollTop

  // 如果页面已经滚动了，取消下拉刷新手势
  if (scrollTop > 5) {
    isValidPullGesture = false
    pullRefreshDisabled.value = true
    console.log('页面滚动了，取消下拉刷新手势')
    return
  }

  // 只有向下拉动且距离足够大才认为是有效的下拉刷新手势
  if (deltaY < 30) {
    pullRefreshDisabled.value = true
  } else {
    pullRefreshDisabled.value = false
  }

  console.log('触摸移动:', { deltaY, scrollTop, disabled: pullRefreshDisabled.value })
}

// 触摸结束
const onTouchEnd = (event) => {
  const touchEndTime = Date.now()
  const touchDuration = touchEndTime - touchStartTime

  console.log('触摸结束:', { touchDuration, isValidPullGesture })

  // 重置状态
  setTimeout(() => {
    isValidPullGesture = false
  }, 100)
}

// 下拉刷新
const onRefresh = () => {
  console.log('触发下拉刷新')

  // 最终检查：只有在页面顶部且是有效手势时才执行刷新
  const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
  if (scrollTop > 5 || !isValidPullGesture) {
    console.log('取消刷新:', { scrollTop, isValidPullGesture })
    refreshing.value = false
    return
  }

  pageNum.value = 1
  finished.value = false
  loadData(true)
}

// 上拉加载更多
const onLoad = () => {
  console.log('触发上拉加载', { loading: loading.value, refreshing: refreshing.value })
  if (refreshing.value) {
    loading.value = false
    return
  }
  loadData(false)
}

// 统一的数据加载函数
const loadData = (isRefresh = false) => {
  const params = {
    pageNum: pageNum.value,
    pageSize: 20
  }

  if (classifyTwoActive.value) {
    params.secondaryCategoryId = classifyTwoActive.value
  } else if (classifyOneActive.value) {
    params.categoryId = classifyOneActive.value
  }

  getSystemGoods(params).then((res) => {
    const newData = res.pageList || []

    // 为新数据添加选择状态
    newData.forEach(item => {
      item.check = false
    })

    if (isRefresh) {
      // 刷新：替换数据
      list.value = newData
    } else {
      // 加载更多：追加数据
      list.value.push(...newData)
    }

    // 更新页码
    pageNum.value++

    // 检查是否还有更多数据
    if (newData.length === 0) {
      finished.value = true
    }

    // 结束加载状态
    loading.value = false
    refreshing.value = false

    // 清除Toast
    Toast.clear()
  }).catch((error) => {
    console.error('加载数据失败:', error)
    loading.value = false
    refreshing.value = false
    Toast.clear()
  })
}

// 获取分类数据
const getCategory = () => {
  categoryListTree().then(res => {
    const data = res || []
    data.unshift({
      name: t('全部分类'),
      categoryId: '',
      subList: []
    })
    categoryOneArry.value = data.filter(item => item.name)
  })
}

// 二级分类计算属性
const categoryTwoArry = computed(() => {
  const oneObj = categoryOneArry.value.find(item => item.categoryId === classifyOneActive.value)
  let dataArr = [{
    name: t('全部分类'),
    categoryId: ''
  }]
  if (oneObj && oneObj.subList && oneObj.subList.length) {
    const data = oneObj.subList.filter(item => item.name)
    dataArr = [...dataArr, ...data]
  }
  return dataArr
})

// 分类选择处理
const classifyHandle = (data, flag) => {
  if (flag) { // 一级分类
    title1.value = data.name
    classifyOneActive.value = data.categoryId
    classifyOneShow.value = false
    title2.value = t('二级分类')
    classifyTwoActive.value = ''
  } else { // 二级分类
    title2.value = data.name
    classifyTwoActive.value = data.categoryId
    classifyTwoShow.value = false
  }

  // 显示加载提示
  Toast.loading({
    duration: 0,
    forbidClick: true
  })

  // 重置状态并重新加载数据
  isAll.value = false
  pageNum.value = 1
  list.value = []
  finished.value = false
  loadData(true)
}

// 全选/取消全选
const allCheck = () => {
  isAll.value = !isAll.value
  list.value.forEach(item => {
    item.check = isAll.value
  })
}

// 计算选中数量
const selectNumber = () => {
  return list.value.filter(item => item.check).length
}

// 搜索
const search = () => {
  router.push('/search?id=2')
}

// 更新信息（编辑利润后回调）
const updateInfo = () => {
  getCategory()
  onRefresh()
}
</script>

<style scoped lang="scss">
.product {
  padding-top: 0;
  padding-bottom: 50px;
  min-height: 100vh;
  background: #EFF2F6;
  overflow: auto;
  -webkit-overflow-scrolling: touch;

  .list {
    overflow: visible;

    /* 确保触摸事件正常工作 */
    .van-pull-refresh {
      touch-action: pan-y;
      -webkit-overflow-scrolling: touch;
    }

    .item {
      border-radius: 4px;
      align-items: center;

      .left {
        align-items: center;
        background: #fff;
        padding: 12px;
        border-radius: 4px;

        .product-info {
          padding-left: 10px;
          flex: 1;

          &.is-ar {
            padding-left: 0;
            padding-right: 10px;
          }

          .name {
            font-size: 14px;
            color: #333333;
            line-height: 16px;
            font-weight: bold;
            word-break: break-all;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            margin-bottom: 5px;
          }

          .Specification {
            font-size: 12px;
            color: #999999;
          }

          .money {
            color: var(--site-main-color);
            font-weight: bold;
          }
        }
      }
    }

    .product-img-wrap {
      position: relative;
    }
  }

  .fixed-wrap {
    height: 50px;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background: #fff;
    align-items: center;

    .submit-but {
      min-width: 130px;
      padding: 0 15px;
      background: var(--site-main-color);
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #fff;

      &.disabled {
        width: 100%;
        background-color: #666;
      }
    }
  }
}

.check-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #ccc;
  display: flex;
  align-items: center;
  justify-content: center;

  > .iconfont {
    color: #fff;
    font-size: 12px;
    opacity: 0;
  }

  &.check-true {
    border-color: var(--site-main-color);
    background-color: var(--site-main-color);

    > .iconfont {
      opacity: 1;
    }
  }
}

.nav_filtering_icon {
  width: 18px;
  height: 18px;
}

.dropdown {
  padding: 0 15px;
  margin: 10px 0;
  display: flex;
  justify-content: space-between;
  width: 100%;

  .dropdownitem {
    width: 48%;
    border-radius: 5px;
    overflow: hidden;
  }
}

.triangle {
  width: 0;
  height: 0;
  border: 6px solid transparent;
  border-top-color: #000;
  position: relative;
  top: 3px;
}

.classify-pop-content {
  width: 18rem;
  min-height: 3rem;

  .title {
    width: 100%;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #333;
    font-weight: bold;
    border-bottom: 1px solid #eaeaea;
  }

  .content {
    max-height: 60vh;
    overflow-y: scroll;

    .classify-item {
      width: 100%;
      height: 3rem;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      border-bottom: 1px solid #eaeaea;

      &:last-child {
        border-bottom: none;
      }

      .yes {
        color: var(--site-main-color);
        position: absolute;
        right: 30px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
}
</style>
