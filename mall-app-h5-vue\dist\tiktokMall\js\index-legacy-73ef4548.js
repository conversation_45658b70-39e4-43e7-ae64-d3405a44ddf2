System.register(["./index-legacy-46a00900.js"],(function(e,t){"use strict";var i,n,o,a,r,l,s,d,c,v,u,f,g,h,x,p,m,_,T,z=document.createElement("style");return z.textContent=":root{--van-list-text-color: var(--van-text-color-2);--van-list-text-font-size: var(--van-font-size-md);--van-list-text-line-height: 50px;--van-list-loading-icon-size: 16px }.van-list__loading,.van-list__finished-text,.van-list__error-text{color:var(--van-list-text-color);font-size:var(--van-list-text-font-size);line-height:var(--van-list-text-line-height);text-align:center}.van-list__placeholder{height:0;pointer-events:none}.van-list__loading-icon .van-loading__spinner{width:var(--van-list-loading-icon-size);height:var(--van-list-loading-icon-size)}\n",document.head.appendChild(z),{setters:[e=>{i=e.aW,n=e.P,o=e.a4,a=e.R,r=e.ai,l=e.d,s=e.r,d=e.aB,c=e.g,v=e.aX,u=e.q,f=e.a9,g=e.ac,h=e.e,x=e.s,p=e.aT,m=e.aO,_=e.W,T=e.X}],execute:function(){const t=e("T",Symbol()),[z,b,y]=n("list"),C={error:Boolean,offset:o(300),loading:Boolean,finished:Boolean,errorText:String,direction:a("down"),loadingText:String,finishedText:String,immediateCheck:r};var S=l({name:z,props:C,emits:["load","update:error","update:loading"],setup(e,{emit:n,slots:o}){const a=s(!1),r=s(),l=s(),T=i(t,null),z=d(r),C=()=>{x((()=>{if(a.value||e.finished||e.error||!1===(null==T?void 0:T.value))return;const{offset:t,direction:i}=e,o=p(z);if(!o.height||m(r))return;let s=!1;const d=p(l);s="up"===i?o.top-d.top<=t:d.bottom-o.bottom<=t,s&&(a.value=!0,n("update:loading",!0),n("load"))}))},S=()=>{if(e.finished){const t=o.finished?o.finished():e.finishedText;if(t)return h("div",{class:b("finished-text")},[t])}},k=()=>{n("update:error",!1),C()},B=()=>{if(e.error){const t=o.error?o.error():e.errorText;if(t)return h("div",{role:"button",class:b("error-text"),tabindex:0,onClick:k},[t])}},w=()=>{if(a.value&&!e.finished)return h("div",{class:b("loading")},[o.loading?o.loading():h(_,{class:b("loading-icon")},{default:()=>[e.loadingText||y("loading")]})])};return c((()=>[e.loading,e.finished,e.error]),C),T&&c(T,(e=>{e&&C()})),v((()=>{a.value=e.loading})),u((()=>{e.immediateCheck&&C()})),f({check:C}),g("scroll",C,{target:z,passive:!0}),()=>{var t;const i=null==(t=o.default)?void 0:t.call(o),n=h("div",{ref:l,class:b("placeholder")},null);return h("div",{ref:r,role:"feed",class:b(),"aria-busy":a.value},["down"===e.direction?i:n,w(),S(),B(),"up"===e.direction?i:n])}}});e("L",T(S))}}}));
