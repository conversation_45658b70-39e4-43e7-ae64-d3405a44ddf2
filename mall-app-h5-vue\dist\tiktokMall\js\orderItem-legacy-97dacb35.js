System.register(["./index-legacy-46a00900.js","./config-legacy-39cba370.js","./index-legacy-b248d96d.js"],(function(t,e){"use strict";var o,i,s,n,a,l,p,c,r,d,b,f,u,g,x,m,h,v,y,k,_,w=document.createElement("style");return w.textContent=".goods_list[data-v-a00c59fb]{background:#fff;padding:10px;box-sizing:border-box;border-radius:5px;position:relative;margin-bottom:10px}.goods_list.is-ar p i[data-v-a00c59fb]{padding-right:0;padding-left:10px}.goods_list .bottom-content[data-v-a00c59fb]{width:100%;border-top:1px solid #f6f6f6;padding-top:10px;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between}.goods_list .bottom-content .money[data-v-a00c59fb]{font-size:13px;color:#666;text-align:center}.goods_list .bottom-content .money p[data-v-a00c59fb]{font-size:18px;font-weight:700;color:var(--site-main-color)}.goods_list .bottom-content .btn-content[data-v-a00c59fb]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.goods_list .bottom-content .btn-content .btn[data-v-a00c59fb]{width:80px;height:30px;line-height:30px;text-align:center;background:var(--site-main-color);color:#fff;right:10px;bottom:10px;font-size:13px;border-radius:3px;margin-right:10px}.goods_list .bottom-content .btn-content .btn[data-v-a00c59fb]:last-child{margin-right:0}.goods_list p[data-v-a00c59fb]{height:30px;line-height:30px}.goods_list p span[data-v-a00c59fb]{color:#666;font-size:13px}.goods_list p i[data-v-a00c59fb]{padding-right:10px}.goods_list .title[data-v-a00c59fb]{font-weight:700}\n",document.head.appendChild(w),{setters:[t=>{o=t.N,i=t.O,s=t._,n=t.u,a=t.i,l=t.o,p=t.c,c=t.a,r=t.t,d=t.e,b=t.b,f=t.b4,u=t.f,g=t.L,x=t.x,m=t.aF,h=t.K,v=t.n,y=t.I},t=>{k=t.l},t=>{_=t.c}],execute:function(){t("a",(t=>o({url:"/wap/seller/orders!list.action",method:i.POST,data:t})));const e={class:"title mb-2"},w={class:"mb-2"},$={class:"mb-2"},z={class:"mb-2"},j={class:"mb-2"},C={class:"bottom-content"},S={class:"money"},I={class:"btn-content"},q=["onClick"];t("o",s({__name:"orderItem",props:{info:{type:Object,default:()=>{}}},setup(t){const o=t,{t:i}=n(),s=a(),N=_(k);["argos"].includes("tiktokMall")&&N.forEach((t=>{4===t.id&&(t.txt="订单已完成"),5===t.id&&(t.txt="买家已签收")}));const O=t=>{const e=N.find((e=>e.id===t));return i(e?e.txt:"全部")},E=()=>{router.push({path:"/qr_order",query:{id:o.info.id,title:"采购确定"}})},T=()=>{router.push({path:"/orderdeails",query:{id:o.info.id,title:"订单详情"}})};return(t,i)=>{const n=y;return l(),p("div",{class:v(["goods_list",{"is-ar":b(s)}]),onClick:T},[c("p",e,r(o.info.id),1),c("p",w,[d(n,{size:"20px",name:"notes-o"}),c("span",null,r(t.$t("下单日期"))+"："+r(b(f)(o.info.createTime)),1)]),c("p",$,[d(n,{size:"20px",name:"debit-pay"}),c("span",null,[u(r(t.$t("支付状态"))+"： ",1),c("span",{style:g({color:1==o.info.payStatus?"#2369f6":"#dc2626"})},r(1==o.info.payStatus?t.$t("买家已付款"):t.$t("等待买家付款")),5),x(' <span style="color: #f00">已支付</span> ')])]),c("p",z,[d(n,{size:"20px",name:"cart-o"}),c("span",null,r(t.$t("采购状态"))+"："+r(1==o.info.purchStatus?`${t.$t("已采购")}`:t.$t("待采购")),1)]),c("p",j,[d(n,{size:"20px",name:"logistics"}),c("span",null,r(t.$t("物流状态"))+"："+r(O(o.info.status)),1)]),c("div",C,[c("div",S,[c("p",null,"$"+r(b(m)(o.info.totalCost)),1),c("span",null,"("+r(t.$t("利润"))+"$"+r(b(m)(o.info.profit))+")",1)]),c("div",I,[x(' <span v-if="props.info.purchStatus / 1 !== 1 && props.info.status / 1 !== -1 && props.info.status / 1 !== 0 && props.info.status / 1 !== 6" class="btn" @click.stop="buy">{{ $t(\'采购\') }}</span> '),1===Number(o.info.purchStatus)||[-1,0,6].includes(Number(o.info.status))?x("v-if",!0):(l(),p("span",{key:0,class:"btn",onClick:h(E,["stop"])},r(t.$t("采购")),9,q))])])],2)}}},[["__scopeId","data-v-a00c59fb"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/order/orderItem.vue"]]))}}}));
