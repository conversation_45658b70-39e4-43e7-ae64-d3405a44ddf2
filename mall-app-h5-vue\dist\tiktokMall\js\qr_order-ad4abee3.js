import{_ as e,i as s,c0 as a,j as l,u as t,Y as u,r as i,m as o,q as n,T as r,c4 as c,c5 as v,g as d,av as p,c as m,e as f,w as h,a as $,F as y,y as g,b as x,n as w,t as P,f as _,x as b,aV as j,o as D,aF as I,D as N,E as k,c6 as q}from"./index-3d21abf8.js";import{B as C}from"./index-2406f514.js";import{A as E}from"./index-3d6106f5.js";import{P as V,N as F}from"./index-2c2a5a87.js";import"./use-route-cd41a893.js";const T=(e=>(N("data-v-7f0433a8"),e=e(),k(),e))((()=>$("div",{style:{height:"46px"}},null,-1))),U={class:"listitem"},z={class:"pic"},B=["src"],L={class:"name"},O={class:"title"},S={class:"col"},A={class:"title money"},R={class:"line"},Y={class:"line-item"},G={class:"count"},H={class:"line-item"},J={class:"count"},K={key:0,class:"price-content"},M={class:"line-item"},Q={class:"count"},W={class:"line-item"},X={class:"count"},Z={class:"line-item"},ee={class:"count"},se={class:"line-item"},ae={class:"count"},le={class:"line-item"},te={class:"count"},ue={style:{height:"22rem"}},ie={key:1,class:"btn"},oe=e({__name:"qr_order",setup(e){const N=s(),k=a(),oe=l(),{t:ne}=t(),re=u();let ce=i(!0),ve=i(0),de=i(0),pe=i(0),me=i(0),fe=i([]),he=i(0),$e=i(0);const ye=i("");let ge=i(!1);const xe=i(!0),we=o((()=>{var e;return null==(e=oe.userInfo)?void 0:e.safeword})),Pe=()=>{we.value?(ge.value=!0,ce.value=!0):(r(ne("请设置资金密码")),setTimeout((()=>{router.push({path:"/fundsPasswordSettings"})}),1500))};n((()=>{_e()}));const _e=()=>{je()},be=i({}),je=async()=>{xe.value=!0;const e=re.query.id;r.loading({duration:0,message:ne("loading"),forbidClick:!0}),await c({orderId:e}).then((e=>{fe.value=e.pageList,fe.value.forEach((e=>{ve.value+=e.systemPrice,de.value+=e.goodsNum,he.value+=e.fees,$e.value+=e.tax,pe.value+=e.profit})),me.value=ve.value+he.value+$e.value})),await v({orderId:e}).then((e=>{be.value=e.orderInfo,xe.value=!1,r.clear()})).catch((()=>{r.clear()}))};d((()=>ye.value),(e=>{6===e.length&&(r.loading(""),(()=>{const e={orderId:re.query.id,safeword:ye.value};q(e).then((s=>{_e(),k.decrement(),document.dispatchEvent(new CustomEvent("reloadOrderList")),setTimeout((()=>{router.push({path:"/passsuess",query:{id:e.orderId}})}),1e3)}))})(),ge.value=!1,ye.value="")}));const De=o((()=>{I((be.sellerDiscountPrice?be.sellerDiscountPrice:be.systemPrice)/1+be.fees/1+be.tax/1);const{sellerDiscountPrice:e,systemPrice:s,fees:a,tax:l}=be.value;return I(Number(e||s)+Number(a)+Number(l))}));return(e,s)=>{const a=p("fx-header"),l=V,t=F,u=E,i=C;return D(),m("div",null,[f(a,{fixed:""},{title:h((()=>[_(P(e.$t("采购确认")),1)])),_:1}),T,$("div",{class:w(["list",{"is-ar":x(N)}])},[(D(!0),m(y,null,g(x(fe),(s=>(D(),m("div",U,[$("div",z,[$("img",{style:{"object-fit":"contain"},src:s.goodsIcon},null,8,B)]),$("div",L,[$("p",O,P(s.goodsName),1),$("p",S,P(e.$t("采购数量")),1),$("p",A,"$"+P(x(I)(s.systemPrice)),1),$("span",null,"x"+P(s.goodsNum),1)])])))),256))],2),Object.keys(be.value).length>0?(D(),m("div",{key:0,class:w(["line-content",{"is-ar":x(N)}])},[$("div",R,[$("div",Y,[$("p",null,P(e.$t("买家付款")),1),$("p",G,"$"+P(x(I)(be.value.prizeReal)),1)]),$("div",H,[$("p",null,P(e.$t("采购金额")),1),$("p",J,"$"+P(x(I)(be.value.systemPrice)),1)]),be.value.sellerDiscountPrice?(D(),m("div",K,[_(P(x(ne)("优惠价")),1),$("span",null,"$"+P(x(I)(be.value.sellerDiscountPrice)),1),_(", "+P(x(ne)("采购优惠")),1),$("span",null,P(100*Number(be.value.sellerDiscount))+"%",1)])):b("v-if",!0),$("div",M,[$("p",null,P(e.$t("采购数量")),1),$("p",Q,P(be.value.goodsCount),1)]),$("div",W,[$("p",null,P(e.$t("利润")),1),$("p",X,"$"+P(x(I)(be.value.profit)),1)]),$("div",Z,[$("p",null,P(e.$t("运费")),1),$("p",ee,"$"+P(x(I)(be.value.fees)),1)]),$("div",se,[$("p",null,P(e.$t("税")),1),$("p",ae,"$"+P(x(I)(be.value.tax)),1)]),$("div",le,[$("p",null,P(e.$t("合计")),1),$("p",te,"$"+P(x(De)),1)])])],2)):b("v-if",!0),f(u,{show:x(ge),"onUpdate:show":s[3]||(s[3]=e=>j(ge)?ge.value=e:ge=e),title:x(ne)("请输入交易密码")},{default:h((()=>[$("div",ue,[f(l,{length:6,value:ye.value,focused:x(ce),onFocus:s[0]||(s[0]=e=>j(ce)?ce.value=!0:ce=!0)},null,8,["value","focused"]),f(t,{modelValue:ye.value,"onUpdate:modelValue":s[1]||(s[1]=e=>ye.value=e),show:x(ce),onBlur:s[2]||(s[2]=e=>j(ce)?ce.value=!1:ce=!1)},null,8,["modelValue","show"])])])),_:1},8,["show","title"]),xe.value||be.value.purchStatus/1==1||be.value.status/1==-1||be.value.status/1==0||be.value.status/1==6?b("v-if",!0):(D(),m("div",ie,[f(i,{type:"primary",size:"large",onClick:Pe},{default:h((()=>[_(P(`${e.$t("立即支付")} $${x(De)}`),1)])),_:1})]))])}}},[["__scopeId","data-v-7f0433a8"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/order/qr_order.vue"]]);export{oe as default};
