System.register(["./index-legacy-46a00900.js","./index-legacy-8cf82a64.js","./index-legacy-6b2aee05.js","./index-legacy-73ef4548.js","./index-legacy-f9c0699e.js","./index-legacy-f9c0699e2.js","./index-legacy-ff56f089.js","./index-legacy-2aab34ff.js","./index-legacy-20dd4294.js","./orderItem-legacy-97dacb35.js","./stringify-legacy-93b715ae.js","./use-id-legacy-df76950f.js","./index-legacy-0ade4760.js","./index-legacy-df3e01aa.js","./use-route-legacy-be86ac1c.js","./index-legacy-15165887.js","./use-refs-legacy-0eab8d10.js","./config-legacy-39cba370.js","./index-legacy-b248d96d.js","./___vite-browser-external_commonjs-proxy-legacy-70ac8ee8.js"],(function(e,t){"use strict";var i,s,a,o,n,l,r,d,h,c,u,p,f,x,g,b,v,m,w,y,C,k,j,S,_,I,L,N,O,R,U=document.createElement("style");return U.textContent='@charset "UTF-8";#order[data-v-69fb50eb]{width:100%;height:100vh;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column}#order.is-ar[data-v-69fb50eb] .van-field__control{text-align:right;padding-right:10px}.main-list[data-v-69fb50eb]{overflow:scroll;margin-top:10px}.yes[data-v-69fb50eb]{color:var(--site-main-color);position:absolute;right:30px;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.triangle[data-v-69fb50eb]{width:0;height:0;border:6px solid transparent;border-top-color:#000;position:relative;top:3px}.main[data-v-69fb50eb]{padding:5px 15px 15px;box-sizing:border-box;height:calc(100vh - 95px);display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column}.ios-device .main[data-v-69fb50eb]{height:calc(100vh - 95px - var(--ios-safe-area-top));padding-top:0;margin-top:0}.van-search[data-v-69fb50eb]{padding:0!important;border-radius:5px;height:44px}[data-v-69fb50eb] .van-search__content{border-radius:20px}.seach[data-v-69fb50eb]{position:relative}.seach .seach_list[data-v-69fb50eb]{position:absolute;top:35px;left:0px;z-index:999;background:#fff;width:100%;max-height:200px;overflow-y:scroll;padding:0 10px;box-sizing:border-box}.seach .seach_list div[data-v-69fb50eb]{height:25px;line-height:25px}.dropdown[data-v-69fb50eb]{margin:10px 0;top:10px;position:relative;-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between;display:-webkit-box;display:-webkit-flex;display:flex}.dropdown .dropdownitem[data-v-69fb50eb]{width:48%;border-radius:5px;overflow:hidden}.cenger[data-v-69fb50eb]{display:inline-block;width:50px}.goods[data-v-69fb50eb]{height:calc(100% - 200px);overflow:auto}\n',document.head.appendChild(U),{setters:[e=>{i=e._,s=e.i,a=e.u,o=e.c0,n=e.T,l=e.I,r=e.bn,d=e.av,h=e.c,c=e.e,u=e.w,p=e.a,f=e.t,x=e.A,g=e.n,b=e.o,v=e.F,m=e.y,w=e.f,y=e.x,C=e.D,k=e.E},e=>{j=e.E},e=>{S=e.P},e=>{_=e.L},()=>{},()=>{},e=>{I=e.S},e=>{L=e.T,N=e.a},()=>{},e=>{O=e.o,R=e.a},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{}],execute:function(){const t=s(),U={name:"Order",beforeRouteEnter(e,t,i){i((e=>{const t=JSON.parse(window.sessionStorage.getItem("position"));document.querySelector(".main-list").scrollTop=t}))},beforeRouteLeave(e,t,i){const s=document.querySelector(".main-list").scrollTop;window.sessionStorage.setItem("position",JSON.stringify(s)),i()},components:{orderItem:O},data:()=>({loading:!1,refreshing:!1,finished:!1,showCenter:!1,showCenter2:!1,t:a().t,activeName:"",payStatus:"-1",showSeach:!1,list:[],option1:[],option2:[],title1:"",title2:"",orderId:"",purchStatus:"",status:"-2",pageNum:1,isArLang:t}),activated(){},created(){window.sessionStorage.setItem("position",JSON.stringify(0)),this.title1="支付状态",this.title2="物流状态",this.option1=[{text:"全部",value:-1,isCurrent:!0},{text:"未支付",value:0,isCurrent:!1},{text:"已支付",value:1,isCurrent:!1}],this.option2=[{text:"全部",value:-2,isCurrent:!0},{text:"订单已取消",value:-1,isCurrent:!1},{text:"等待买家付款",value:0,isCurrent:!1},{text:"买家已付款",value:1,isCurrent:!1},{text:"供应商已接单",value:2,isCurrent:!1},{text:"物流运输中",value:3,isCurrent:!1},{text:"买家已签收",value:4,isCurrent:!1},{text:"订单已完成",value:5,isCurrent:!1},{text:"已退款",value:6,isCurrent:!1}],["argos"].includes("tiktokMall")&&this.option2.forEach((e=>{4===e.value&&(e.text="订单已完成"),5===e.value&&(e.text="买家已签收")})),this.onRefresh(),document.addEventListener("reloadOrderList",(()=>{this.onRefresh()}),!1)},computed:{hasNoPushNum:()=>{const e=o();return Boolean(e.num)}},methods:{onLoad(){},onRefresh(){this.init(),this.getOrderList()},handleChoose(e){let t=this.payStatus/1,i=this.option1.findIndex((e=>e.value===t));this.option1.splice(i,1,{...this.option1[i],isCurrent:!1});let s=this.option1.findIndex((t=>t.value===e.value));this.option1.splice(s,1,{...this.option1[s],isCurrent:!0}),this.payStatus=e.value,this.title1=e.text,this.showCenter=!1,this.init(),this.getOrderList()},handleChoose2(e){let t=this.status/1,i=this.option2.findIndex((e=>e.value===t));this.option2.splice(i,1,{...this.option2[i],isCurrent:!1});let s=this.option2.findIndex((t=>t.value===e.value));this.option2.splice(s,1,{...this.option2[s],isCurrent:!0}),this.status=e.value,this.title2=e.text,this.showCenter2=!1,this.init(),this.getOrderList()},handleShow(e){1===e?this.showCenter=!0:this.showCenter2=!0},init(){this.pageNum=1,this.list=[],this.loading=!1,this.refreshing=!1,this.finished=!1},getOrderList(){const e={orderId:this.orderId,payStatus:this.payStatus,purchStatus:this.activeName,status:this.status,begin:"",end:"",pageNum:this.pageNum};Object.keys(e).forEach((t=>{e[t]||0===e[t]||delete e[t]})),e.payStatus/1==-1&&delete e.payStatus,e.status/1==-2&&delete e.status,n.loading({message:this.t("加载中"),forbidClick:!0,duration:0}),R(e).then((e=>{this.refreshing&&(this.refreshing=!1),this.pageNum++,this.loading=!1,0==e.pageList.length&&(this.finished=!0),this.list=this.list.concat(e.pageList)})).catch((e=>{}))},tab(e,t){this.activeName=e,this.onRefresh()},onSearch(){this.showSeach=!0,this.$router.push("/order_search")},change(e){this.value=e,this.showSeach=!1},onConfirm2(e){this.title2=this.option2[e].text,this.getOrderList()}}},E=e=>(C("data-v-69fb50eb"),e=e(),k(),e),$={class:"main"},V={class:"seach",style:{"border-radius":"25px"}},T={class:"dropdown"},z={class:"p-2.5 bg-white flex justify-between items-center w-full h-full"},A={class:"text-xs"},D=E((()=>p("div",{class:"triangle"},null,-1))),J={class:"p-2.5 bg-white flex justify-between items-center w-full h-full"},P={class:"text-xs"},q=E((()=>p("div",{class:"triangle"},null,-1))),F=["onClick"],M=["onClick"],Y={class:"main-list"},B={class:"goods"},G=E((()=>p("div",{class:"footer-padding"},null,-1)));e("default",i(U,[["render",function(e,t,i,s,a,o){const n=L,C=N,k=I,O=l,R=r,U=d("orderItem"),E=_,H=S,K=j;return b(),h("div",{id:"order",class:g({"is-ar":a.isArLang})},[c(C,{active:a.activeName,"onUpdate:active":t[0]||(t[0]=e=>a.activeName=e),sticky:"",onClick:o.tab},{default:u((()=>[c(n,{title:a.t("total"),name:""},null,8,["title"]),c(n,{title:a.t("待采购"),name:"0",dot:o.hasNoPushNum},null,8,["title","dot"]),c(n,{title:a.t("已采购"),name:"1"},null,8,["title"])])),_:1},8,["active","onClick"]),p("div",$,[p("div",V,[c(k,{style:{"border-radius":"25px"},readonly:"",onClick:o.onSearch,modelValue:a.orderId,"onUpdate:modelValue":t[1]||(t[1]=e=>a.orderId=e),placeholder:a.t("搜索")},null,8,["onClick","modelValue","placeholder"])]),p("div",T,[p("div",{class:"dropdownitem one h-10.5",onClick:t[2]||(t[2]=e=>o.handleShow(1))},[p("div",z,[p("span",A,f(a.t(a.title1)),1),D])]),p("div",{class:"dropdownitem one h-10.5",onClick:t[3]||(t[3]=e=>o.handleShow(2))},[p("div",J,[p("span",P,f(a.t(a.title2)),1),q])])]),c(R,{show:a.showCenter,"onUpdate:show":t[4]||(t[4]=e=>a.showCenter=e),round:""},{default:u((()=>[(b(!0),h(v,null,m(a.option1,((e,t)=>(b(),h("div",{onClick:t=>o.handleChoose(e),class:"font-3.5 w-72 h-12 flex justify-center items-center border-bottom relative"},[w(f(a.t(e.text))+" ",1),e.isCurrent?(b(),x(O,{key:0,name:"success",class:"yes"})):y("v-if",!0)],8,F)))),256))])),_:1},8,["show"]),c(R,{show:a.showCenter2,"onUpdate:show":t[5]||(t[5]=e=>a.showCenter2=e),round:""},{default:u((()=>[(b(!0),h(v,null,m(a.option2,((e,t)=>(b(),h("div",{onClick:t=>o.handleChoose2(e),class:"text-xs w-72 h-12 flex justify-center items-center border-bottom relative",key:t+"option2"},[w(f(a.t(e.text))+" ",1),e.isCurrent?(b(),x(O,{key:0,name:"success",class:"yes"})):y("v-if",!0)],8,M)))),128))])),_:1},8,["show"]),p("div",Y,[a.list.length>0?(b(),x(H,{key:0,modelValue:a.refreshing,"onUpdate:modelValue":t[7]||(t[7]=e=>a.refreshing=e),onRefresh:o.onRefresh,"loading-text":e.$t("加载中"),"loosing-text":e.$t("释放以刷新"),"pulling-text":e.$t("下拉以刷新")},{default:u((()=>[c(E,{"immediate-check":!1,loading:a.loading,"onUpdate:loading":t[6]||(t[6]=e=>a.loading=e),"loading-text":e.$t("加载中"),finished:a.finished,"finished-text":e.$t("noMore"),onLoad:o.getOrderList},{default:u((()=>[p("div",B,[(b(!0),h(v,null,m(a.list,(e=>(b(),x(U,{info:e},null,8,["info"])))),256))])])),_:1},8,["loading","loading-text","finished","finished-text","onLoad"])])),_:1},8,["modelValue","onRefresh","loading-text","loosing-text","pulling-text"])):(b(),x(K,{key:1,description:e.$t("noData")},null,8,["description"])),G])])],2)}],["__scopeId","data-v-69fb50eb"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/order/index.vue"]]))}}}));
