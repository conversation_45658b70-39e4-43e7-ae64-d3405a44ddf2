import{N as s,O as t,_ as a,u as o,i as n,o as i,c as e,a as p,t as l,e as r,b as c,b4 as u,f,L as d,x as m,aF as b,K as $,n as x,I as h}from"./index-3d21abf8.js";import{l as v}from"./config-22c2c72a.js";import{c as y}from"./index-3ab60a77.js";const _=a=>s({url:"/wap/seller/orders!list.action",method:t.POST,data:a}),k={class:"title mb-2"},S={class:"mb-2"},C={class:"mb-2"},g={class:"mb-2"},j={class:"mb-2"},z={class:"bottom-content"},I={class:"money"},q={class:"btn-content"},N=["onClick"],O=a({__name:"orderItem",props:{info:{type:Object,default:()=>{}}},setup(s){const t=s,{t:a}=o(),_=n(),O=y(v);["argos"].includes("tiktokMall")&&O.forEach((s=>{4===s.id&&(s.txt="订单已完成"),5===s.id&&(s.txt="买家已签收")}));const w=s=>{const t=O.find((t=>t.id===s));return a(t?t.txt:"全部")},T=()=>{router.push({path:"/qr_order",query:{id:t.info.id,title:"采购确定"}})},D=()=>{router.push({path:"/orderdeails",query:{id:t.info.id,title:"订单详情"}})};return(s,a)=>{const o=h;return i(),e("div",{class:x(["goods_list",{"is-ar":c(_)}]),onClick:D},[p("p",k,l(t.info.id),1),p("p",S,[r(o,{size:"20px",name:"notes-o"}),p("span",null,l(s.$t("下单日期"))+"："+l(c(u)(t.info.createTime)),1)]),p("p",C,[r(o,{size:"20px",name:"debit-pay"}),p("span",null,[f(l(s.$t("支付状态"))+"： ",1),p("span",{style:d({color:1==t.info.payStatus?"#2369f6":"#dc2626"})},l(1==t.info.payStatus?s.$t("买家已付款"):s.$t("等待买家付款")),5),m(' <span style="color: #f00">已支付</span> ')])]),p("p",g,[r(o,{size:"20px",name:"cart-o"}),p("span",null,l(s.$t("采购状态"))+"："+l(1==t.info.purchStatus?`${s.$t("已采购")}`:s.$t("待采购")),1)]),p("p",j,[r(o,{size:"20px",name:"logistics"}),p("span",null,l(s.$t("物流状态"))+"："+l(w(t.info.status)),1)]),p("div",z,[p("div",I,[p("p",null,"$"+l(c(b)(t.info.totalCost)),1),p("span",null,"("+l(s.$t("利润"))+"$"+l(c(b)(t.info.profit))+")",1)]),p("div",q,[m(' <span v-if="props.info.purchStatus / 1 !== 1 && props.info.status / 1 !== -1 && props.info.status / 1 !== 0 && props.info.status / 1 !== 6" class="btn" @click.stop="buy">{{ $t(\'采购\') }}</span> '),1===Number(t.info.purchStatus)||[-1,0,6].includes(Number(t.info.status))?m("v-if",!0):(i(),e("span",{key:0,class:"btn",onClick:$(T,["stop"])},l(s.$t("采购")),9,N))])])],2)}}},[["__scopeId","data-v-a00c59fb"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/order/orderItem.vue"]]);export{_ as a,O as o};
