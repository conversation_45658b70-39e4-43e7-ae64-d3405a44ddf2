System.register(["./index-legacy-46a00900.js","./index-legacy-df3e01aa.js","./use-id-legacy-df76950f.js"],(function(e,t){"use strict";var a,r,l,n,i,o,s,u,c,d,g,f,p,m,v,h,y,b,x,k,w,S,C,M,V,B,I,A,P,T,E,$,z,j,W,q,L,F;return{setters:[e=>{a=e.$,r=e.a0,l=e.a1,n=e.a2,i=e.a3,o=e.P,s=e.S,u=e.R,c=e.a4,d=e.Q,g=e.a5,f=e.d,p=e.p,m=e.r,v=e.a6,h=e.a7,y=e.m,b=e.a8,x=e.a9,k=e.aa,w=e.ab,S=e.g,C=e.s,M=e.q,V=e.ac,B=e.e,I=e.ad,A=e.ae,P=e.af,T=e.V,E=e.I,$=e.ag,z=e.f,j=e.ah,W=e.X},e=>{q=e.c,L=e.C},e=>{F=e.u}],execute:function(){function t(e){return Array.isArray(e)?!e.length:0!==e&&!e}function H(e,t){const{message:a}=t;return i(a)?a(e,t):a||""}function D({target:e}){e.composing=!0}function O({target:e}){e.composing&&(e.composing=!1,e.dispatchEvent(new Event("input")))}function K(e){return[...e].length}const[Q,R]=o("field"),X=e("f",{id:String,name:String,leftIcon:String,rightIcon:String,autofocus:Boolean,clearable:Boolean,maxlength:s,formatter:Function,clearIcon:u("clear"),modelValue:c(""),inputAlign:String,placeholder:String,autocomplete:String,errorMessage:String,enterkeyhint:String,clearTrigger:u("focus"),formatTrigger:u("onChange"),error:{type:Boolean,default:null},disabled:{type:Boolean,default:null},readonly:{type:Boolean,default:null}}),G=d({},q,X,{rows:s,type:u("text"),rules:Array,autosize:[Boolean,Object],labelWidth:s,labelClass:g,labelAlign:String,showWordLimit:Boolean,errorMessageAlign:String,colon:{type:Boolean,default:null}});var J=f({name:Q,props:G,emits:["blur","focus","clear","keypress","click-input","end-validate","start-validate","click-left-icon","click-right-icon","update:modelValue"],setup(e,{emit:i,slots:o}){const s=F(),u=p({status:"unvalidated",focused:!1,validateMessage:""}),c=m(),d=m(),g=m(),{parent:f}=v(h),W=()=>{var t;return String(null!=(t=e.modelValue)?t:"")},q=t=>I(e[t])?e[t]:f&&I(f.props[t])?f.props[t]:void 0,Q=y((()=>{const t=q("readonly");if(e.clearable&&!t){const t=""!==W(),a="always"===e.clearTrigger||"focus"===e.clearTrigger&&u.focused;return t&&a}return!1})),X=y((()=>g.value&&o.input?g.value():e.modelValue)),G=e=>e.reduce(((e,a)=>e.then((()=>{if("failed"===u.status)return;let{value:e}=X;if(a.formatter&&(e=a.formatter(e,a)),!function(e,a){if(t(e)){if(a.required)return!1;if(!1===a.validateEmpty)return!0}return!(a.pattern&&!a.pattern.test(String(e)))}(e,a))return u.status="failed",void(u.validateMessage=H(e,a));if(a.validator){if(t(e)&&!1===a.validateEmpty)return;return function(e,t){return new Promise((a=>{const r=t.validator(e,t);n(r)?r.then(a):a(r)}))}(e,a).then((t=>{t&&"string"==typeof t?(u.status="failed",u.validateMessage=t):!1===t&&(u.status="failed",u.validateMessage=H(e,a))}))}}))),Promise.resolve()),J=()=>{u.status="unvalidated",u.validateMessage=""},N=()=>i("end-validate",{status:u.status}),U=(t=e.rules)=>new Promise((a=>{J(),t?(i("start-validate"),G(t).then((()=>{"failed"===u.status?(a({name:e.name,message:u.validateMessage}),N()):(u.status="passed",a(),N())}))):a()})),Y=t=>{if(f&&e.rules){const{validateTrigger:a}=f.props,r=A(a).includes(t),l=e.rules.filter((e=>e.trigger?A(e.trigger).includes(t):r));l.length&&U(l)}},Z=(t,a="onChange")=>{if(t=(t=>{const{maxlength:a}=e;if(I(a)&&K(t)>a){const e=W();return e&&K(e)===+a?e:function(e,t){return[...e].slice(0,t).join("")}(t,+a)}return t})(t),"number"===e.type||"digit"===e.type){const a="number"===e.type;t=P(t,a,a)}e.formatter&&a===e.formatTrigger&&(t=e.formatter(t)),c.value&&c.value.value!==t&&(c.value.value=t),t!==e.modelValue&&i("update:modelValue",t)},_=e=>{e.target.composing||Z(e.target.value)},ee=()=>{var e;return null==(e=c.value)?void 0:e.blur()},te=()=>{const t=c.value;"textarea"===e.type&&e.autosize&&t&&function(e,t){const n=a();e.style.height="auto";let i=e.scrollHeight;if(l(t)){const{maxHeight:e,minHeight:a}=t;void 0!==e&&(i=Math.min(i,e)),void 0!==a&&(i=Math.max(i,a))}i&&(e.style.height=`${i}px`,r(n))}(t,e.autosize)},ae=e=>{u.focused=!0,i("focus",e),C(te),q("readonly")&&ee()},re=e=>{q("readonly")||(u.focused=!1,Z(W(),"onBlur"),i("blur",e),Y("onBlur"),C(te),j())},le=e=>i("click-input",e),ne=e=>i("click-left-icon",e),ie=e=>i("click-right-icon",e),oe=y((()=>"boolean"==typeof e.error?e.error:!(!f||!f.props.showError||"failed"!==u.status)||void 0)),se=y((()=>{const e=q("labelWidth");if(e)return{width:b(e)}})),ue=t=>{13===t.keyCode&&(f&&f.props.submitOnEnter||"textarea"===e.type||T(t),"search"===e.type&&ee()),i("keypress",t)},ce=()=>e.id||`${s}-input`,de=()=>{const t=R("control",[q("inputAlign"),{error:oe.value,custom:!!o.input,"min-height":"textarea"===e.type&&!e.autosize}]);if(o.input)return B("div",{class:t,onClick:le},[o.input()]);const a={id:ce(),ref:c,name:e.name,rows:void 0!==e.rows?+e.rows:void 0,class:t,disabled:q("disabled"),readonly:q("readonly"),autofocus:e.autofocus,placeholder:e.placeholder,autocomplete:e.autocomplete,enterkeyhint:e.enterkeyhint,"aria-labelledby":e.label?`${s}-label`:void 0,onBlur:re,onFocus:ae,onInput:_,onClick:le,onChange:O,onKeypress:ue,onCompositionend:O,onCompositionstart:D};return"textarea"===e.type?B("textarea",a,null):B("input",$("number"===(r=e.type)?{type:"text",inputmode:"decimal"}:"digit"===r?{type:"tel",inputmode:"numeric"}:{type:r},a),null);var r},ge=()=>{const t=o["right-icon"];if(e.rightIcon||t)return B("div",{class:R("right-icon"),onClick:ie},[t?t():B(E,{name:e.rightIcon,classPrefix:e.iconPrefix},null)])},fe=()=>{if(e.showWordLimit&&e.maxlength){const t=K(W());return B("div",{class:R("word-limit")},[B("span",{class:R("word-num")},[t]),z("/"),e.maxlength])}},pe=()=>{if(f&&!1===f.props.showErrorMessage)return;const t=e.errorMessage||u.validateMessage;if(t){const e=o["error-message"],a=q("errorMessageAlign");return B("div",{class:R("error-message",a)},[e?e({message:t}):t])}},me=()=>[B("div",{class:R("body")},[de(),Q.value&&B(E,{ref:d,name:e.clearIcon,class:R("clear")},null),ge(),o.button&&B("div",{class:R("button")},[o.button()])]),fe(),pe()];return x({blur:ee,focus:()=>{var e;return null==(e=c.value)?void 0:e.focus()},validate:U,formValue:X,resetValidation:J,getValidationStatus:()=>u.status}),k(w,{customValue:g,resetValidation:J,validateWithTrigger:Y}),S((()=>e.modelValue),(()=>{Z(W()),J(),Y("onChange"),C(te)})),M((()=>{Z(W(),e.formatTrigger),C(te)})),V("touchstart",(e=>{T(e),i("update:modelValue",""),i("clear",e)}),{target:y((()=>{var e;return null==(e=d.value)?void 0:e.$el}))}),()=>{const t=q("disabled"),a=q("labelAlign"),r=(()=>{const t=q("colon")?":":"";return o.label?[o.label(),t]:e.label?B("label",{id:`${s}-label`,for:ce()},[e.label+t]):void 0})(),l=(()=>{const t=o["left-icon"];if(e.leftIcon||t)return B("div",{class:R("left-icon"),onClick:ne},[t?t():B(E,{name:e.leftIcon,classPrefix:e.iconPrefix},null)])})();return B(L,{size:e.size,icon:e.leftIcon,class:R({error:oe.value,disabled:t,[`label-${a}`]:a}),center:e.center,border:e.border,isLink:e.isLink,clickable:e.clickable,titleStyle:se.value,valueClass:R("value"),titleClass:[R("label",[a,{required:e.required}]),e.labelClass],arrowDirection:e.arrowDirection},{icon:l?()=>l:null,title:r?()=>r:null,value:me,extra:o.extra})}}});e("F",W(J))}}}));
