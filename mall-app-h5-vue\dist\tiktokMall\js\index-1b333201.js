import{_ as n,l as a,j as e,r as w,av as s,I as l,c as o,e as c,w as t,a as i,F as p,y as g,b as f,o as d,f as m,t as v,x as r,D as u,E as U,T as b,cj as L}from"./index-3d21abf8.js";const R=[new URL("/www/png/name-06e7e6f2.png",self.location),new URL("/www/png/name-6e79b4d8.png",self.location),new URL("/www/png/name-b2bb3866.png",self.location),new URL("/www/png/name-e5c455cc.png",self.location),new URL("/www/png/name-c4f095b9.png",self.location),new URL("/www/png/name-61e1a338.png",self.location),new URL("/www/png/name-9a6f541c.png",self.location),new URL("/www/png/name-195c213d.png",self.location),new URL("/www/png/name-36248e5c.png",self.location),new URL("/www/png/name-eb40a9be.png",self.location),new URL("/www/png/name-219d8586.png",self.location),new URL("/www/png/name-d5dbab23.png",self.location),new URL("/www/png/name-fb2f3616.png",self.location),new URL("/www/png/name-c3062cb0.png",self.location),new URL("/www/png/name-76e71516.png",self.location),new URL("/www/png/name-656cd634.png",self.location),new URL("/www/png/name-693679bf.png",self.location),new URL("/www/png/name-3e40215a.png",self.location),new URL("/www/png/name-6f4809d9.png",self.location),new URL("/www/png/name-abbd7e16.png",self.location)],k=(n=>(u("data-v-ddc11a54"),n=n(),U(),n))((()=>i("div",{style:{height:"46px"}},null,-1))),x={class:"avatar-content"},h=["onClick"],_=["src"],y={key:0,class:"check"},C=n({__name:"index",setup(n){const u=a(),U=e(),C=w(1);if(U.userInfo.token){const n=Number(U.userInfo.avatar);isNaN(n)||(C.value=n)}else u.push("/login");const I=async()=>{b.loading({duration:0,forbidClick:!0}),await L({idx:C.value}),await U.getUserInfo(!0),u.back()};return(n,a)=>{const e=s("fx-header"),w=l;return d(),o("div",null,[c(e,{fixed:!0},{title:t((()=>[m(v(n.$t("avatar")),1)])),right:t((()=>[i("div",{onClick:I,class:"save-btn"},v(n.$t("save")),1)])),_:1}),k,i("div",x,[(d(!0),o(p,null,g(f(R),((n,a)=>(d(),o("div",{key:a,class:"item",onClick:n=>(n=>{n+1!==C.value&&(C.value=n+1)})(a)},[i("img",{src:n,alt:""},null,8,_),a+1===C.value?(d(),o("div",y,[c(w,{name:"success"})])):r("v-if",!0)],8,h)))),128))])])}}},[["__scopeId","data-v-ddc11a54"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/changeAvatar/index.vue"]]);export{C as default};
