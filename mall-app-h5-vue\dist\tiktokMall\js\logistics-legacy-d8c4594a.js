System.register(["./index-legacy-46a00900.js","./index-legacy-8cf82a64.js","./use-id-legacy-df76950f.js"],(function(t,e){"use strict";var i,a,n,o,s,l,c,d,r,p,f,u,v,g,x,h,m,b,y,k,_,w,j,C,I=document.createElement("style");return I.textContent='.logistics-container[data-v-d1280af0]{padding:15px}.logistics-container>.content[data-v-d1280af0]{padding:15px;background-color:#fff;border-radius:4px}.logistics-container>.content.single>.item[data-v-d1280af0]:before{display:none}.logistics-container>.content>.item[data-v-d1280af0]{position:relative;padding-bottom:20px;padding-left:20px;overflow:hidden}.logistics-container>.content>.item[data-v-d1280af0]:after{content:"";display:block;width:10px;height:10px;border-radius:50%;background-color:var(--site-main-color);position:absolute;top:7px;left:0}.logistics-container>.content>.item[data-v-d1280af0]:before{content:"";display:block;width:2px;height:100%;background-color:var(--site-main-color);position:absolute;top:0;left:4px}.logistics-container>.content>.item[data-v-d1280af0]:first-child:before{top:8px}.logistics-container>.content>.item[data-v-d1280af0]:last-child:before{height:15px}.logistics-container>.content>.item>.info[data-v-d1280af0]{font-size:14px;line-height:21px}.logistics-container>.content>.item>.info>span[data-v-d1280af0]{color:#1552f0}.logistics-container>.content>.item>.time[data-v-d1280af0]{font-size:14px;color:#333;margin-top:5px}\n',document.head.appendChild(I),{setters:[t=>{i=t._,a=t.d,n=t.u,o=t.Y,s=t.r,l=t.T,c=t.c8,d=t.av,r=t.c,p=t.e,f=t.w,u=t.a,v=t.A,g=t.b,x=t.x,h=t.n,m=t.F,b=t.y,y=t.o,k=t.f,_=t.t,w=t.D,j=t.E},t=>{C=t.E},()=>{}],execute:function(){const e=(t=>(w("data-v-d1280af0"),t=t(),j(),t))((()=>u("div",{style:{height:"46px"}},null,-1))),I={class:"logistics-container"},T={class:"info"},D={class:"time"},E=a({name:"OrderLogistics"});t("default",i(Object.assign(E,{setup(t){const{t:i}=n(),a=o(),w=s(!0),j=s([]),E=s(!1);return a.query.id?(l.loading({forbidClick:!0,duration:0}),c({orderId:a.query.id}).then((t=>{if(t.length){const e=t.map((t=>{const e=t.log.split(t.orderId);return{...t,tipsTxt:e[1]}})).reverse();j.value=e}E.value=1===t.length,w.value=!1,l.clear()})).catch((()=>{w.value=!1,l.clear()}))):w.value=!1,(t,a)=>{const n=d("fx-header"),o=C;return y(),r("div",null,[p(n,{fixed:!0},{title:f((()=>[k(_(g(i)("查看物流")),1)])),_:1}),e,u("div",I,[w.value||j.value.length?x("v-if",!0):(y(),v(o,{key:0,description:g(i)("noData")},null,8,["description"])),!w.value&&j.value.length?(y(),r("div",{key:1,class:h(["content",{single:E.value}])},[(y(!0),r(m,null,b(j.value,(t=>(y(),r("div",{key:t.id,class:"item"},[u("p",T,[k(_(g(i)("订单")),1),u("span",null,"#"+_(t.orderId)+"#",1),k(_(g(i)(t.tipsTxt)),1)]),u("p",D,_(t.updateTime),1)])))),128))],2)):x("v-if",!0)])])}}}),[["__scopeId","data-v-d1280af0"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/order/logistics.vue"]]))}}}));
