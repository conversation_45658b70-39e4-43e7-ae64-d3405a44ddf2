System.register(["./index-legacy-46a00900.js","./index-legacy-6156faa3.js","./index-legacy-20dd4294.js","./index-legacy-f9c0699e3.js","./index-legacy-1e1b2807.js","./index-legacy-8ad4c0d7.js","./im.api-legacy-55d32ce5.js","./upload.api-legacy-ce6f3ca0.js","./index-legacy-15165887.js","./index-legacy-bbd15202.js","./use-placeholder-legacy-f22ccc27.js"],(function(e,t){"use strict";var a,n,i,o,l,s,r,c,d,g,f,m,p,v,x,b,u,A,w,h,k,y,U,S,C,B,j,R,E,J,M,L,K,Q,I,T=document.createElement("style");return T.textContent='@charset "UTF-8";.service-box[data-v-864b89f2]{width:100%;box-sizing:border-box;position:relative}.service-box[data-v-864b89f2] .van-hairline--bottom:after{border-color:#f3f3f3}.service-box.ios-device[data-v-864b89f2]{padding-bottom:env(safe-area-inset-bottom);padding-bottom:var(--ios-safe-area-bottom, 34px)}.service-header[data-v-864b89f2]{position:relative;z-index:100}.chat-container[data-v-864b89f2]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column;-webkit-box-pack:start;-webkit-justify-content:flex-start;justify-content:flex-start}.chat-container .history-btn[data-v-864b89f2]{-webkit-flex-shrink:0;flex-shrink:0;margin-bottom:8px;font-size:12px;padding:8px 0}.chat-container .message-list[data-v-864b89f2]{-webkit-box-flex:1;-webkit-flex:1 1;flex:1 1;overflow-y:auto;-webkit-overflow-scrolling:touch;padding-bottom:10px}.chat-container .message-list .message-item[data-v-864b89f2]{margin:8px 0}.chat-container .message-list .message-item[data-v-864b89f2]:first-child{margin-top:0}.chat-container .message-list .message-item[data-v-864b89f2]:last-child{margin-bottom:0}.chat-container .message-list .message-item .time-divider[data-v-864b89f2]{margin:4px 0;font-size:11px;opacity:.7}.content[data-v-864b89f2]{padding-top:46px;padding-bottom:0;min-height:calc(100vh - 46px);max-height:calc(100vh - 46px)}.ios-device .content[data-v-864b89f2]{padding-top:calc(46px + env(safe-area-inset-top,0px));padding-bottom:0;min-height:calc(100vh - 46px - env(safe-area-inset-top,0px));max-height:calc(100vh - 46px - env(safe-area-inset-top,0px))}.send-bg[data-v-864b89f2]{background-color:#ffead1!important}.break-word[data-v-864b89f2]{word-wrap:break-word}.max-w-230[data-v-864b89f2]{max-width:115px}.responser[data-v-864b89f2]{position:relative}.responser[data-v-864b89f2]:after{content:"";width:0;height:0;border-top:5px solid transparent;border-bottom:5px solid transparent;border-right:10px solid #f3f3f3;position:absolute;left:-10px;top:10px}.borderTop[data-v-864b89f2]{border-top:1px solid #f3f3f3}.bottomBox[data-v-864b89f2]{min-height:46px;max-height:76px;z-index:1000;padding:6px 12px}.bottomBox .send-msg-content[data-v-864b89f2]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;min-height:32px}.bottomBox .send-msg-content textarea[data-v-864b89f2]{min-height:32px;max-height:56px;line-height:1.4;padding:6px 12px;border-radius:16px;border:1px solid #e5e5e5;font-size:14px}.bottomBox .send-msg-content textarea[data-v-864b89f2]:focus{outline:none;border-color:var(--site-main-color, #007aff)}.bottomBox.ios-bottom-input[data-v-864b89f2]{bottom:env(safe-area-inset-bottom,0px);padding-bottom:20px!important;min-height:calc(46px + env(safe-area-inset-bottom,0px))}.black[data-v-864b89f2]{color:#1f2025}.chatBg[data-v-864b89f2]{border-radius:10.0022px 10.0022px 0;max-width:50vw}.responser-content>.res-content[data-v-864b89f2]{border-radius:10.0022px 10.0022px 10.0022px 0}.chat-res-content[data-v-864b89f2]{padding:10px;border-radius:10.0022px 10.0022px 10.0022px 0;background-color:#fff;max-width:70vw;word-break:break-all}.chat-res-content.img[data-v-864b89f2]{max-width:50vw}.chat-res-content.img>img[data-v-864b89f2]{width:100%;height:auto;margin:0}.chat-res-content>p[data-v-864b89f2]{margin:0;line-height:1.4}.send-msg-content[data-v-864b89f2]{padding:10px 0}.send-msg-content>textarea[data-v-864b89f2]{width:100%;padding:10px 0;margin:0}.upload-btn[data-v-864b89f2]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;padding:4px;border-radius:50%;-webkit-transition:all .2s ease;transition:all .2s ease}.upload-btn[data-v-864b89f2]:active{background-color:rgba(0,0,0,.05);-webkit-transform:scale(.95);transform:scale(.95)}.upload-btn img[data-v-864b89f2]{border-radius:4px}.icon-fasong[data-v-864b89f2]{color:var(--site-main-color, #007aff);font-size:24px;padding:6px;border-radius:50%;-webkit-transition:all .2s ease;transition:all .2s ease}.icon-fasong[data-v-864b89f2]:active{background-color:rgba(0,122,255,.1);-webkit-transform:scale(.95);transform:scale(.95)}\n',document.head.appendChild(T),{setters:[e=>{a=e._,n=e.i,i=e.ci,o=e.r,l=e.q,s=e.u,r=e.Y,c=e.l,d=e.m,g=e.ao,f=e.j,m=e.s,p=e.J,v=e.c,x=e.a,b=e.e,u=e.L,A=e.t,w=e.F,h=e.y,k=e.x,y=e.w,U=e.h,S=e.v,C=e.n,B=e.b,j=e.T,R=e.o,E=e.b4},e=>{J=e.I},()=>{},()=>{},e=>{M=e.U},e=>{L=e.N},e=>{K=e.a,Q=e.b},e=>{I=e.u},()=>{},()=>{},()=>{}],execute:function(){const t={class:"service-header"},T={class:"flex flex-col px-16 box-border h-full chat-container",style:{background:"#f5f5f5"}},Y={key:0,class:"font-12 text-center py-1 text-grey time-divider"},z=["src"],Z={class:"text-xs text-grey mb-1"},P={key:0,class:"chat-res-content text-sm"},G=["innerHTML"],H={key:1,class:"chat-res-content img text-sm"},W=["src","onClick"],V={class:"text-xs text-grey mb-1",style:{"text-align":"right"}},N=["innerHTML"],q={key:1,class:"chat-res-content img text-sm"},D=["src","onClick"],X=["src"],F={class:"relative bottom bottomBox flex justify-between items-center w-full fixed bottom-0 borderTop px-3 py-2 box-border bgBottom bg-white ios-bottom-input"},O=["src"],_={class:"flex-1 mx-3 h-full border-none bgBottom textColor send-msg-content"},$=["placeholder"],ee={__name:"index",setup(e){const a=n(),{height:ee}=i(),te=o(!1);l((()=>{const e=navigator.userAgent.toLowerCase();te.value=/iphone|ipad|ipod/.test(e)||!0===window.navigator.standalone,te.value&&document.body.classList.add("ios-device")}));const ae=o(new URL("data:image/png;base64,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",self.location).href),{t:ne,locale:ie}=s(),oe=r(),le=c(),se=o([]),re=o(""),ce=o(""),de=o(null);o(0);const ge=o(!1),fe=o(null),me=o(null),pe=o(0),ve=o(!1),xe={responser:new URL("data:image/png;base64,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",self.location),argos:new URL("/www/png/name-d7f7a48e.png",self.location),argos2:new URL("/www/png/name-d7f7a48e.png",self.location),familyShop:new URL("/www/png/name-f6e12968.png",self.location),hive:new URL("/www/png/name-3a1dd991.png",self.location),inchoi:new URL("/www/png/name-924c0133.png",self.location),mbuy:new URL("/www/png/name-5d5887cc.png",self.location),greenMall:new URL("/www/png/name-b8e3926e.png",self.location),tiktokMall:new URL("/www/png/name-54adb70a.png",self.location),shop2u:new URL("/www/png/name-4c3635af.png",self.location),sm:new URL("/www/png/name-1eca6583.png",self.location),iceland:new URL("/www/png/name-4d16d45f.png",self.location),int:new URL("/www/png/name-84219bb9.png",self.location),"tiktok-wholesale":new URL("/www/png/name-eca5f455.png",self.location),antMall:new URL("/www/png/name-fb841661.png",self.location),simon:new URL("/www/png/name-e93c092e.png",self.location),texm:new URL("/www/png/name-5baf10a3.png",self.location),"alibaba-wholesale":new URL("/www/png/name-02067a4b.png",self.location),photo:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAYAAABV7bNHAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAJvSURBVHgB7ZtNbhoxGIZfAwvUVY+QGzRdsiqzaPc5QbkBvQHDDeAG6TVSVUMWFeqq6Q1yhCwqStNkHH8RlrLANg6e0czwPtLIlj5rJD/y8PkPgBBCCCGEEEJI3ahQg6L49fYB26mpjndPB1A3Gnr5KRtdBlv6gkWxPns0hQbO0EFM52/7QJZlo1tXm57vBV2WI0jfpI/ylbjaOAVdFT8mXZZjkT7eY/PFFXcKUuhPcSL00PvgjjnR5zgdxq7AAJF8zEbBzNdkvhVrHdO+B+KFggJQUAAKCkBBAaKzmA+7bjNp7nmSKVN5U14OMFxm2fs7tJBkI0jk/Nfblanmdga+K/MH/PNO55tMMkEl7qdK4d3+qD73TeebTDJBGuXEF++j9xktJKEg/8K2rQvfZILkB9kX14F4U0k5gr7646U3fijfi58zs56aoSaSCRpgu5CtzH2xUuP3Bm8WOBKRU6LMTTWvS1IyQWbf8m6Av5mpzu3ntPvs5hs1HF8cOQ96IcdSi6SkE0WRZIp89yRjjxyLSJItmDkqovFLDY8cS6UjqdGCDpBjqUxSbYJis0+EHEslkmoRFJt9XiHHklxS5YJis88Rcg56fyyVCgpkn1lE+1hyJKIyQbHZJ6GcpCSdB1kis48c3KGJcoRKBMVmH9MeTYV70gEoKAAFBaCgABQUoJIs1vYbIC/hCApAQQEoKAAFBaCgANFZLPaOX9vxjCC1wsmgb1wR3zXga5wIGmrpijkFyUlpW8/TY5A++v7U4hQkh4CPpuiyJOmb9NHX5qAlwVWxniioaYdu36/Mc/0Hw8VFS68GEkIIIYQQQkiXeQJ31xQ8cq8AoAAAAABJRU5ErkJggg==",self.location)},be=o(null),ue=o(null),Ae=o(!1),we=d((()=>{const e="tiktokMall";let t=xe.responser.href;return xe[e]&&(t=xe[e].href),t})),he=g(),ke=d((()=>he.shopInfo?.avatar||""));l((async()=>{pe.value=me.value.$el.getBoundingClientRect().height,ve.value=!1;const e=navigator.userAgent;fe.value=e.indexOf("Android")>-1||e.indexOf("Linux")>-1;const{avatar:t,token:a,lang:n}=oe.query;if(ke.value&&(ae.value=ke.value),t&&(ae.value=t),n&&(ie.value=n,localStorage.setItem("lang",n)),a){const e=f();await e.getUserInfo(!0,a)}Ae.value=!0,Be()}));const ye=e=>{j(ne("fileMaxLimit"))},Ue=e=>{J([e])},Se=e=>{let t=!1;return 0===e&&(t=!0),e>0&&se.value[e].createtime.split(" ")[0]!==se.value[e-1].createtime.split(" ")[0]&&(t=!0),t},Ce=e=>{j.loading({duration:0}),I({file:e.file,moduleName:"customerService"}).then((e=>{j.clear(),Me("img",e)})).catch((()=>{j.clear()}))},Be=(e="")=>{K({message_id:e}).then((t=>{if(ce.value||(ce.value=t.length&&t[t.length-1].id),t.length){let a=[];if(t.forEach((e=>{if("img"===e.type){const t=e.content,a="imagePath=";e.imgUrl=t;const n=t.indexOf(a);if(n>0){const i=t.slice(n+a.length);e.imgUrl=i}}})),e)ce.value=t[t.length-1].id,a=je([...t.reverse(),...se.value]);else{a=je([...se.value,...t.reverse()]);let e={};a=a.reduce((function(t,a){return e[a.id]||(e[a.id]=t.push(a)),t}),[])}se.value=a,t.length<10&&(ge.value=!0)}Ae.value&&m((()=>{be.value.scrollTop=ue.value.offsetHeight,Ae.value=!1})),ve.value||(ve.value=!0,de.value=setInterval((()=>{Be()}),1e3))}))},je=e=>{let t=[];return t=e.filter((e=>-1===Number(e.delete_status))).map((e=>e.id)),e.filter((e=>!t.includes(e.id)))},Re=()=>{Be(ce.value)},Ee=()=>{try{window.history.length>1?window.history.back():le.push("/")}catch(e){le.push("/")}},Je=o(!1),Me=(e="text",t="")=>{Je.value||(t?(j.loading({duration:0,forbidClick:!0}),Je.value=!0,Q(e,t).then((e=>{re.value="",Je.value=!1,j.clear(),Ae.value=!0,Be()})).catch((()=>{Je.value=!1,j.clear()}))):j(ne("entryMessageContent")))};return p((()=>{de.value&&(clearInterval(de.value),de.value=null)})),(e,n)=>{const i=L,o=M;return R(),v("div",{class:C(["service-box flex flex-col has-nav-bar",{"ios-device":te.value}]),style:u({height:B(ee)+"px"})},[x("div",t,[b(i,{fixed:!0,ref_key:"navEl",ref:me,title:e.$t("onLineService"),"left-arrow":"",onClickLeft:Ee},null,8,["title"])]),x("div",{ref_key:"msgContent",ref:be,class:"content flex-1 overflow-auto",style:{background:"#f5f5f5"}},[x("div",T,[x("div",{class:"w-full py-2 text-grey text-center history-btn",onClick:Re,style:u({display:ge.value?"none":"block"})},A(e.$t("historyMessage")),5),x("ul",{ref_key:"msgTxtContent",ref:ue,class:"flex flex-col pt-2 message-list"},[(R(!0),v(w,null,h(se.value,((e,t)=>(R(),v("li",{key:e.id,class:"flex flex-col my-2 message-item"},[Se(t)?(R(),v("p",Y,A(B(E)(e.createtime,"YYYY-MM-DD")),1)):k("v-if",!0),x("div",{class:C(["flex responser-content items-center","send"===e.send_receive?"justify-end":""])},["receive"===e.send_receive?(R(),v(w,{key:0},[x("img",{src:B(we),class:C(["w-9 h-9",B(a)?"ml-3":"mr-3"]),style:{"border-radius":"50%"}},null,10,z),x("div",null,[x("div",Z,A(B(E)(e.createtime,"YYYY-MM-DD HH:mm")),1),k(' <div v-if="item.type === \'text\'" class="chat-res-content text-sm">{{ item.content }}</div> '),"text"===e.type?(R(),v("div",P,[x("p",{innerHTML:e.content},null,8,G)])):(R(),v("div",H,[x("img",{src:e.imgUrl,onClick:t=>Ue(e.imgUrl)},null,8,W)]))])],64)):(R(),v(w,{key:1},[x("div",null,[x("div",V,A(B(E)(e.createtime,"YYYY-MM-DD HH:mm")),1),k(" <div v-if=\"item.type === 'text'\" class=\"chat-res-content text-sm\" :class=\"item.send_receive === 'send' ? 'send-bg' : ''\">{{ item.content }}</div> "),"text"===e.type?(R(),v("div",{key:0,class:C(["chat-res-content text-sm","send"===e.send_receive?"send-bg":""])},[x("p",{innerHTML:e.content},null,8,N)],2)):(R(),v("div",q,[x("img",{src:`${e.imgUrl}`,onClick:t=>Ue(e.imgUrl)},null,8,D)]))]),x("img",{src:ae.value,class:C(["w-9 h-9",B(a)?"mr-3":"ml-3"]),style:{"border-radius":"50%"}},null,10,X)],64))],2)])))),128))],512)])],512),x("div",F,[k(' <van-uploader :max-size="10000 * 1024" @oversize="onOversize" :after-read="afterRead" :capture="androidAttrs ? \'camera\' : null"> '),b(o,{"max-size":1024e4,onOversize:ye,"after-read":Ce,class:"upload-btn"},{default:y((()=>[x("img",{src:xe.photo,class:"w-8 h-8"},null,8,O)])),_:1}),x("div",_,[U(x("textarea",{"onUpdate:modelValue":n[0]||(n[0]=e=>re.value=e),placeholder:e.$t("entryYouMessage"),class:"flex-1 mx-3 h-full border-none bgBottom textColor",style:{resize:"none","background-color":"#fff"}},null,8,$),[[S,re.value]])]),x("i",{class:"iconfont icon-fasong",onClick:n[1]||(n[1]=e=>Me("text",re.value))})])],6)}}};e("default",a(ee,[["__scopeId","data-v-864b89f2"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/customerService/index.vue"]]))}}}));
