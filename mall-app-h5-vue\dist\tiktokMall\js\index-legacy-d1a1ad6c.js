System.register(["./index-legacy-46a00900.js","./index-legacy-72e00c5f.js","./index-legacy-e952cf7f.js","./nationalityList-legacy-4b6c8c35.js","./countryList-legacy-94cb363f.js","./use-route-legacy-be86ac1c.js","./index-legacy-f9c0699e.js","./index-legacy-f9c0699e2.js","./index-legacy-0ade4760.js","./index-legacy-df3e01aa.js","./use-id-legacy-df76950f.js","./index-legacy-322f21cd.js","./index-legacy-ff56f089.js"],(function(e,a){"use strict";var t,i,l,o,n,d,r,p,c,s,u,x,v,g,b,h,f,m,y,w,k,j,_=document.createElement("style");return _.textContent=".bindVerify[data-v-403e7485]{width:100%;min-height:100vh;box-sizing:border-box}.content[data-v-403e7485]{font-size:12px;padding:0 16px}.iptbox[data-v-403e7485]{height:44px;margin-top:8px;padding:0 10px 0 20px;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;align-items:center;border-radius:3px}.iptbox input[data-v-403e7485]{-webkit-box-flex:1;-webkit-flex:1 1;flex:1 1;height:100%;border:none}.iptbox input[data-v-403e7485]::-webkit-input-placeholder{color:#868c9a}.iptbox span[data-v-403e7485]{color:#1d91ff}.imgbox[data-v-403e7485]{border:1px solid #E5E7ED;padding:5px;width:182px;height:182px;box-sizing:border-box}.imgbox img[data-v-403e7485]{width:100%;height:100%}.code[data-v-403e7485]{font-size:15px;font-weight:300;line-height:18px;margin-top:22px;height:18px}.code img[data-v-403e7485]{width:14px;height:14px;margin-left:5px}.tips[data-v-403e7485]{margin-top:10px;color:#999}.copy[data-v-403e7485]{border-radius:4px;width:132px;height:40px;margin-top:16px;border:1px solid #E5E7ED;line-height:40px}.bottom[data-v-403e7485]{padding:20px 16px 7px}.bottom p[data-v-403e7485]{padding-bottom:13px}.van-password-input[data-v-403e7485]{margin:0}.van-password-input__security li[data-v-403e7485],[data-v-403e7485] .van-password-input__security li{background:#F5F5F5;width:50px;height:50px;color:#333}[data-v-403e7485] .van-button--primary{background-color:var(--site-main-color);border-color:var(--site-main-color);border-radius:4px}[data-v-403e7485] .inputBackground{background-color:#fff!important;border-radius:4px;border:1px solid #eee}\n",document.head.appendChild(_),{setters:[e=>{t=e._,i=e.Y,l=e.l,o=e.u,n=e.r,d=e.j,r=e.q,p=e.av,c=e.c,s=e.e,u=e.w,x=e.a,v=e.b,g=e.T,b=e.cM,h=e.o,f=e.f,m=e.t},e=>{y=e.B},e=>{w=e.E},e=>{k=e.n},e=>{j=e.c},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{}],execute:function(){const a={class:"bindVerify h-full bg-white"},_={class:"content"},$={style:{"margin-top":"22px"}},C={style:{"margin-top":"22px"}};e("default",t({__name:"index",setup(e){const t=i(),E=l(),{t:V}=o(),T=n(""),z=n(""),N=n(0),P=n(""),U=d(),B=n(!1),D=n("修改手机号码");r((()=>{const e=U.userInfo.phone;if(e&&!U.phoneverif){const a=e.split(" ");D.value="bindPhone",N.value=Number(a[0]),T.value=a[1];for(const e in j)j[e].dialCode===Number(a[0])&&(P.value=j[e].code)}}));const F=()=>{""!=T.value?/^[0-9]+$/.test(T.value)?""!=z.value?(B.value=!0,I()):g(V("请输入登录密码")):g(V("请输入正确的手机号码")):g(V("entryPhone"))},I=()=>{b({target:`${N.value} ${T.value}`,phone:`${N.value} ${T.value}`,password:z.value}).then((e=>{(async()=>{await U.getUserInfo(!0),g(V("bindSuccess")),B.value=!1,setTimeout((()=>{t.query.reset?E.go(-2):E.back()}),1e3)})()})).catch((e=>{B.value=!1}))},S=(e,a,t)=>{P.value=a,N.value=t},q=n(null),A=()=>{q.value.open()};return(e,t)=>{const i=p("fx-header"),l=y;return h(),c("div",a,[s(i,null,{title:u((()=>[f(m(v(V)(D.value)),1)])),_:1}),x("div",_,[x("div",$,[s(w,{label:v(V)("phoneNum"),placeholderText:v(V)("entryPhone"),modelValue:T.value,"onUpdate:modelValue":t[0]||(t[0]=e=>T.value=e),area:!0,onSelectArea:A,dialCode:N.value,icon:P.value},null,8,["label","placeholderText","modelValue","dialCode","icon"])]),x("div",C,[s(w,{label:e.$t("登录密码"),placeholderText:e.$t("请输入登录密码"),modelValue:z.value,"onUpdate:modelValue":t[1]||(t[1]=e=>z.value=e),typeText:"password",clearBtn:!1},null,8,["label","placeholderText","modelValue"])]),s(l,{class:"w-full",style:{"margin-top":"30px"},type:"primary",loading:B.value,onClick:F},{default:u((()=>[f(m(e.$t("confirm")),1)])),_:1},8,["loading"])]),s(k,{ref_key:"controlChildRef",ref:q,title:e.$t("selectArea"),onGetName:S},null,8,["title"])])}}},[["__scopeId","data-v-403e7485"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/changePhone/index.vue"]]))}}}));
