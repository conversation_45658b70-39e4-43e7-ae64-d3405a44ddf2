System.register(["./index-legacy-46a00900.js"],(function(e,n){"use strict";var t,a,o,i,c,s,l,r,v,d,h,g,b,p,m,u,x,f=document.createElement("style");return f.textContent=':root{--van-action-sheet-max-height: 80%;--van-action-sheet-header-height: 48px;--van-action-sheet-header-font-size: var(--van-font-size-lg);--van-action-sheet-description-color: var(--van-text-color-2);--van-action-sheet-description-font-size: var(--van-font-size-md);--van-action-sheet-description-line-height: var(--van-line-height-md);--van-action-sheet-item-background: var(--van-background-color-light);--van-action-sheet-item-font-size: var(--van-font-size-lg);--van-action-sheet-item-line-height: var(--van-line-height-lg);--van-action-sheet-item-text-color: var(--van-text-color);--van-action-sheet-item-disabled-text-color: var(--van-text-color-3);--van-action-sheet-subname-color: var(--van-text-color-2);--van-action-sheet-subname-font-size: var(--van-font-size-sm);--van-action-sheet-subname-line-height: var(--van-line-height-sm);--van-action-sheet-close-icon-size: 22px;--van-action-sheet-close-icon-color: var(--van-gray-5);--van-action-sheet-close-icon-padding: 0 var(--van-padding-md);--van-action-sheet-cancel-text-color: var(--van-gray-7);--van-action-sheet-cancel-padding-top: var(--van-padding-xs);--van-action-sheet-cancel-padding-color: var(--van-background-color);--van-action-sheet-loading-icon-size: 22px }.van-action-sheet{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column;max-height:var(--van-action-sheet-max-height);overflow:hidden;color:var(--van-action-sheet-item-text-color)}.van-action-sheet__content{-webkit-box-flex:1;-webkit-flex:1 1 auto;flex:1 1 auto;overflow-y:auto;-webkit-overflow-scrolling:touch}.van-action-sheet__item,.van-action-sheet__cancel{display:block;width:100%;padding:14px var(--van-padding-md);font-size:var(--van-action-sheet-item-font-size);background:var(--van-action-sheet-item-background);border:none;cursor:pointer}.van-action-sheet__item:active,.van-action-sheet__cancel:active{background-color:var(--van-active-color)}.van-action-sheet__item{line-height:var(--van-action-sheet-item-line-height)}.van-action-sheet__item--loading,.van-action-sheet__item--disabled{color:var(--van-action-sheet-item-disabled-text-color)}.van-action-sheet__item--loading:active,.van-action-sheet__item--disabled:active{background-color:var(--van-action-sheet-item-background)}.van-action-sheet__item--disabled{cursor:not-allowed}.van-action-sheet__item--loading{cursor:default}.van-action-sheet__cancel{-webkit-flex-shrink:0;flex-shrink:0;box-sizing:border-box;color:var(--van-action-sheet-cancel-text-color)}.van-action-sheet__subname{margin-top:var(--van-padding-xs);color:var(--van-action-sheet-subname-color);font-size:var(--van-action-sheet-subname-font-size);line-height:var(--van-action-sheet-subname-line-height)}.van-action-sheet__gap{display:block;height:var(--van-action-sheet-cancel-padding-top);background:var(--van-action-sheet-cancel-padding-color)}.van-action-sheet__header{-webkit-flex-shrink:0;flex-shrink:0;font-weight:var(--van-font-weight-bold);font-size:var(--van-action-sheet-header-font-size);line-height:var(--van-action-sheet-header-height);text-align:center}.van-action-sheet__description{position:relative;-webkit-flex-shrink:0;flex-shrink:0;padding:20px var(--van-padding-md);color:var(--van-action-sheet-description-color);font-size:var(--van-action-sheet-description-font-size);line-height:var(--van-action-sheet-description-line-height);text-align:center}.van-action-sheet__description:after{position:absolute;box-sizing:border-box;content:" ";pointer-events:none;right:var(--van-padding-md);bottom:0;left:var(--van-padding-md);border-bottom:1px solid var(--van-border-color);-webkit-transform:scaleY(.5);transform:scaleY(.5)}.van-action-sheet__loading-icon .van-loading__spinner{width:var(--van-action-sheet-loading-icon-size);height:var(--van-action-sheet-loading-icon-size)}.van-action-sheet__close{position:absolute;top:0;right:0;padding:var(--van-action-sheet-close-icon-padding);color:var(--van-action-sheet-close-icon-color);font-size:var(--van-action-sheet-close-icon-size);line-height:inherit}\n',document.head.appendChild(f),{setters:[e=>{t=e.P,a=e.Q,o=e.bk,i=e.ai,c=e.bl,s=e.R,l=e.bm,r=e.d,v=e.e,d=e.ag,h=e.aY,g=e.bn,b=e.I,p=e.b0,m=e.W,u=e.s,x=e.X}],execute:function(){const[n,f]=t("action-sheet"),_=a({},o,{title:String,round:i,actions:c(),closeIcon:s("cross"),closeable:i,cancelText:String,description:String,closeOnPopstate:i,closeOnClickAction:Boolean,safeAreaInsetBottom:i}),k=[...l,"round","closeOnPopstate","safeAreaInsetBottom"];var z=r({name:n,props:_,emits:["select","cancel","update:show"],setup(e,{slots:n,emit:t}){const a=e=>t("update:show",e),o=()=>{a(!1),t("cancel")},i=()=>{if(e.title)return v("div",{class:f("header")},[e.title,e.closeable&&v(b,{name:e.closeIcon,class:[f("close"),p],onClick:o},null)])},c=()=>{if(n.cancel||e.cancelText)return[v("div",{class:f("gap")},null),v("button",{type:"button",class:f("cancel"),onClick:o},[n.cancel?n.cancel():e.cancelText])]},s=(e,t)=>e.loading?v(m,{class:f("loading-icon")},null):n.action?n.action({action:e,index:t}):[v("span",{class:f("name")},[e.name]),e.subname&&v("div",{class:f("subname")},[e.subname])],l=(n,o)=>{const{color:i,loading:c,callback:l,disabled:r,className:d}=n;return v("button",{type:"button",style:{color:i},class:[f("item",{loading:c,disabled:r}),d],onClick:()=>{r||c||(l&&l(n),e.closeOnClickAction&&a(!1),u((()=>t("select",n,o))))}},[s(n,o)])},r=()=>{if(e.description||n.description){const t=n.description?n.description():e.description;return v("div",{class:f("description")},[t])}};return()=>v(g,d({class:f(),position:"bottom","onUpdate:show":a},h(e,k)),{default:()=>{var t;return[i(),r(),v("div",{class:f("content")},[e.actions.map(l),null==(t=n.default)?void 0:t.call(n)]),c()]}})}});e("A",x(z))}}}));
