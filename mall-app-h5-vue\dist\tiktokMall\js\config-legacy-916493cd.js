System.register(["./index-legacy-46a00900.js"],(function(t,e){"use strict";var i,a,r,l;return{setters:[t=>{i=t._,a=t.o,r=t.c,l=t.t}],execute:function(){let e=0;const s="webkit moz ms o".split(" ");let n,o;if("undefined"==typeof window)n=function(){},o=function(){};else{let t;n=window.requestAnimationFrame,o=window.cancelAnimationFrame;for(let e=0;e<s.length&&(!n||!o);e++)t=s[e],n=n||window[t+"RequestAnimationFrame"],o=o||window[t+"CancelAnimationFrame"]||window[t+"CancelRequestAnimationFrame"];n&&o||(n=function(t){const i=(new Date).getTime(),a=Math.max(0,16-(i-e)),r=window.setTimeout((()=>{t(i+a)}),a);return e=i+a,r},o=function(t){window.clearTimeout(t)})}const u={props:{startVal:{type:Number,required:!1,default:0},endVal:{type:Number,required:!1,default:2017},duration:{type:Number,required:!1,default:3e3},autoplay:{type:Boolean,required:!1,default:!0},decimals:{type:Number,required:!1,default:0,validator:t=>t>=0},decimal:{type:String,required:!1,default:"."},separator:{type:String,required:!1,default:","},prefix:{type:String,required:!1,default:""},suffix:{type:String,required:!1,default:""},useEasing:{type:Boolean,required:!1,default:!0},easingFn:{type:Function,default:(t,e,i,a)=>i*(1-Math.pow(2,-10*t/a))*1024/1023+e}},data(){return{localStartVal:this.startVal,displayValue:this.formatNumber(this.startVal),printVal:null,paused:!1,localDuration:this.duration,startTime:null,timestamp:null,remaining:null,rAF:null}},computed:{countDown(){return this.startVal>this.endVal}},watch:{startVal(){this.autoplay&&this.start()},endVal(){this.autoplay&&this.start()}},mounted(){this.autoplay&&this.start(),this.$emit("mountedCallback")},methods:{start(){this.localStartVal=this.startVal,this.startTime=null,this.localDuration=this.duration,this.paused=!1,this.rAF=n(this.count)},pauseResume(){this.paused?(this.resume(),this.paused=!1):(this.pause(),this.paused=!0)},pause(){o(this.rAF)},resume(){this.startTime=null,this.localDuration=+this.remaining,this.localStartVal=+this.printVal,n(this.count)},reset(){this.startTime=null,o(this.rAF),this.displayValue=this.formatNumber(this.startVal)},count(t){this.startTime||(this.startTime=t),this.timestamp=t;const e=t-this.startTime;this.remaining=this.localDuration-e,this.useEasing?this.countDown?this.printVal=this.localStartVal-this.easingFn(e,0,this.localStartVal-this.endVal,this.localDuration):this.printVal=this.easingFn(e,this.localStartVal,this.endVal-this.localStartVal,this.localDuration):this.countDown?this.printVal=this.localStartVal-(this.localStartVal-this.endVal)*(e/this.localDuration):this.printVal=this.localStartVal+(this.endVal-this.localStartVal)*(e/this.localDuration),this.countDown?this.printVal=this.printVal<this.endVal?this.endVal:this.printVal:this.printVal=this.printVal>this.endVal?this.endVal:this.printVal,this.displayValue=this.formatNumber(this.printVal),e<this.localDuration?this.rAF=n(this.count):this.$emit("callback")},isNumber:t=>!isNaN(parseFloat(t)),formatNumber(t){t=t.toFixed(this.decimals);const e=(t+="").split(".");let i=e[0];const a=e.length>1?this.decimal+e[1]:"",r=/(\d+)(\d{3})/;if(this.separator&&!this.isNumber(this.separator))for(;r.test(i);)i=i.replace(r,"$1"+this.separator+"$2");return this.prefix+i+a+this.suffix}},destroyed(){o(this.rAF)}},h=i(u,[["render",function(t,e,i,s,n,o){return a(),r("span",null,l(n.displayValue),1)}],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/node_modules/vue-count-to/src/vue-countTo.vue"]]);function c(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function d(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,a)}return i}h.unmounted=h.destroyed,Reflect.deleteProperty(h,"destroyed"),t("C",function(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?d(Object(i),!0).forEach((function(e){c(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):d(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}({name:"CountTo",emits:["callback","mountedCallback"]},h)),t("r",[{title:"待到账金额",number:0,color:"#41A3FF",prefix:"$",decimals:2,key:"willIncome"},{title:"总销售额",number:0,color:"#54C1FF",prefix:"$",decimals:2,key:"totalSales"},{title:"总利润",number:0,color:"#3CCDC4",prefix:"$",decimals:2,key:"totalProfit"},{title:"总订单",number:0,color:"#FF6F4F",prefix:"",decimals:0,key:"orderNum"},{title:"取消订单",number:0,color:"#757F8F",prefix:"",decimals:0,key:"orderCancel"},{title:"退款订单",number:0,color:"#DD4E4E",prefix:"",decimals:0,key:"orderReturns"}]),t("a",[{name:"全部",value:0},{name:"今日",value:1},{name:"昨日",value:2},{name:"本周",value:3},{name:"本月",value:4},{name:"本年",value:5}])}}}));
