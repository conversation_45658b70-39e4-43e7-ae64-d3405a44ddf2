System.register(["./index-legacy-46a00900.js","./use-route-legacy-be86ac1c.js"],(function(e,l){"use strict";var t,i,r,n,a,s,o,c,u,d,b,v;return{setters:[e=>{t=e.P,i=e.S,r=e.ai,n=e.a5,a=e.Q,s=e.d,o=e.e,c=e.ad,u=e.I,d=e.X},e=>{b=e.r,v=e.u}],execute:function(){const[l,f]=t("cell"),g=e("c",{icon:String,size:String,title:i,value:i,label:i,center:Boolean,isLink:Boolean,border:r,required:Boolean,iconPrefix:String,valueClass:n,labelClass:n,titleClass:n,titleStyle:null,arrowDirection:String,clickable:{type:Boolean,default:null}}),C=a({},g,b);var S=s({name:l,props:C,setup(e,{slots:l}){const t=v(),i=()=>{if(l.label||c(e.label))return o("div",{class:[f("label"),e.labelClass]},[l.label?l.label():e.label])},r=()=>{if(l.title||c(e.title))return o("div",{class:[f("title"),e.titleClass],style:e.titleStyle},[l.title?l.title():o("span",null,[e.title]),i()])},n=()=>{const t=l.value||l.default;if(t||c(e.value)){const i=l.title||c(e.title);return o("div",{class:[f("value",{alone:!i}),e.valueClass]},[t?t():o("span",null,[e.value])])}},a=()=>{if(l["right-icon"])return l["right-icon"]();if(e.isLink){const l=e.arrowDirection?`arrow-${e.arrowDirection}`:"arrow";return o(u,{name:l,class:f("right-icon")},null)}};return()=>{var i,s;const{size:c,center:d,border:b,isLink:v,required:g}=e,C=null!=(i=e.clickable)?i:v,S={center:d,required:g,clickable:C,borderless:!b};return c&&(S[c]=!!c),o("div",{class:f(S),role:C?"button":void 0,tabindex:C?0:void 0,onClick:t},[l.icon?l.icon():e.icon?o(u,{name:e.icon,class:f("left-icon"),classPrefix:e.iconPrefix},null):void 0,r(),n(),a(),null==(s=l.extra)?void 0:s.call(l)])}}});e("C",d(S))}}}));
