import{P as e,e as t,d as s,S as l,r as a,aC as o,V as r,W as n,R as i,a4 as u,ai as c,m as d,g as h,bo as v,h as m,aS as p,a$ as f,bp as x,bq as y,M as g,b0 as w,X as b,U as z,a8 as B,br as V}from"./index-3d21abf8.js";const[T,S]=e("key"),k=t("svg",{class:S("collapse-icon"),viewBox:"0 0 30 24"},[t("path",{d:"M26 13h-2v2h2v-2zm-8-3h2V8h-2v2zm2-4h2V4h-2v2zm2 4h4V4h-2v4h-2v2zm-7 14 3-3h-6l3 3zM6 13H4v2h2v-2zm16 0H8v2h14v-2zm-12-3h2V8h-2v2zM28 0l1 1 1 1v15l-1 2H1l-1-2V2l1-1 1-1zm0 2H2v15h26V2zM6 4v2H4V4zm10 2h2V4h-2v2zM8 9v1H4V8zm8 0v1h-2V8zm-6-5v2H8V4zm4 0v2h-2V4z",fill:"currentColor"},null)]),A=t("svg",{class:S("delete-icon"),viewBox:"0 0 32 22"},[t("path",{d:"M28 0a4 4 0 0 1 4 4v14a4 4 0 0 1-4 4H10.4a2 2 0 0 1-1.4-.6L1 13.1c-.6-.5-.9-1.3-.9-2 0-1 .3-1.7.9-2.2L9 .6a2 2 0 0 1 1.4-.6zm0 2H10.4l-8.2 8.3a1 1 0 0 0-.3.7c0 .3.1.5.3.7l8.2 8.4H28a2 2 0 0 0 2-2V4c0-1.1-.9-2-2-2zm-5 4a1 1 0 0 1 .7.3 1 1 0 0 1 0 1.4L20.4 11l3.3 3.3c.2.2.3.5.3.7 0 .3-.1.5-.3.7a1 1 0 0 1-.7.3 1 1 0 0 1-.7-.3L19 12.4l-3.4 3.3a1 1 0 0 1-.6.3 1 1 0 0 1-.7-.3 1 1 0 0 1-.3-.7c0-.2.1-.5.3-.7l3.3-3.3-3.3-3.3A1 1 0 0 1 14 7c0-.3.1-.5.3-.7A1 1 0 0 1 15 6a1 1 0 0 1 .6.3L19 9.6l3.3-3.3A1 1 0 0 1 23 6z",fill:"currentColor"},null)]);var H=s({name:T,props:{type:String,text:l,color:String,wider:Boolean,large:Boolean,loading:Boolean},emits:["press"],setup(e,{emit:s,slots:l}){const i=a(!1),u=o(),c=e=>{u.start(e),i.value=!0},d=e=>{u.move(e),u.direction.value&&(i.value=!1)},h=t=>{i.value&&(l.default||r(t),i.value=!1,s("press",e.text,e.type))},v=()=>{if(e.loading)return t(n,{class:S("loading-icon")},null);const s=l.default?l.default():e.text;switch(e.type){case"delete":return s||A;case"extra":return s||k;default:return s}};return()=>t("div",{class:S("wrapper",{wider:e.wider}),onTouchstartPassive:c,onTouchmovePassive:d,onTouchend:h,onTouchcancel:h},[t("div",{role:"button",tabindex:0,class:S([e.color,{large:e.large,active:i.value,delete:"delete"===e.type}])},[v()])])}});const[P,K]=e("number-keyboard");const M=b(s({name:P,props:{show:Boolean,title:String,theme:i("default"),zIndex:l,teleport:[String,Object],maxlength:u(1/0),modelValue:i(""),transition:c,blurOnClose:c,showDeleteKey:c,randomKeyOrder:Boolean,closeButtonText:String,deleteButtonText:String,closeButtonLoading:Boolean,hideOnClickOutside:c,safeAreaInsetBottom:c,extraKey:{type:[String,Array],default:""}},emits:["show","hide","blur","input","close","delete","update:modelValue"],setup(e,{emit:s,slots:l}){const o=a(),r=()=>{const t=Array(9).fill("").map(((e,t)=>({text:t+1})));return e.randomKeyOrder&&function(e){for(let t=e.length-1;t>0;t--){const s=Math.floor(Math.random()*(t+1)),l=e[t];e[t]=e[s],e[s]=l}}(t),t},n=d((()=>"custom"===e.theme?(()=>{const t=r(),{extraKey:s}=e,l=Array.isArray(s)?s:[s];return 1===l.length?t.push({text:0,wider:!0},{text:l[0],type:"extra"}):2===l.length&&t.push({text:l[0],type:"extra"},{text:0},{text:l[1],type:"extra"}),t})():[...r(),{text:e.extraKey,type:"extra"},{text:0},{text:e.showDeleteKey?e.deleteButtonText:"",type:e.showDeleteKey?"delete":""}])),i=()=>{e.show&&s("blur")},u=()=>{s("close"),e.blurOnClose&&i()},c=()=>s(e.show?"show":"hide"),b=(t,l)=>{if(""===t)return void("extra"===l&&i());const a=e.modelValue;"delete"===l?(s("delete"),s("update:modelValue",a.slice(0,a.length-1))):"close"===l?u():a.length<e.maxlength&&(s("input",t),s("update:modelValue",a+t))},z=()=>{if("custom"===e.theme)return t("div",{class:K("sidebar")},[e.showDeleteKey&&t(H,{large:!0,text:e.deleteButtonText,type:"delete",onPress:b},{delete:l.delete}),t(H,{large:!0,text:e.closeButtonText,type:"close",color:"blue",loading:e.closeButtonLoading,onPress:b},null)])};return h((()=>e.show),(t=>{e.transition||s(t?"show":"hide")})),e.hideOnClickOutside&&v(o,i,{eventName:"touchstart"}),()=>{const s=(()=>{const{title:s,theme:a,closeButtonText:o}=e,r=l["title-left"],n=o&&"default"===a;if(s||n||r)return t("div",{class:K("header")},[r&&t("span",{class:K("title-left")},[r()]),s&&t("h2",{class:K("title")},[s]),n&&t("button",{type:"button",class:[K("close"),w],onClick:u},[o])])})(),a=t(y,{name:e.transition?"van-slide-up":""},{default:()=>[m(t("div",{ref:o,style:f(e.zIndex),class:K({unfit:!e.safeAreaInsetBottom,"with-title":!!s}),onAnimationend:c,onTouchstartPassive:x},[s,t("div",{class:K("body")},[t("div",{class:K("keys")},[n.value.map((e=>{const s={};return"delete"===e.type&&(s.default=l.delete),"extra"===e.type&&(s.default=l["extra-key"]),t(H,{key:e.text,text:e.text,type:e.type,wider:e.wider,color:e.color,onPress:b},s)}))]),z()])]),[[p,e.show]])]});return e.teleport?t(g,{to:e.teleport},{default:()=>[a]}):a}}})),[O,C]=e("password-input");const L=b(s({name:O,props:{info:String,mask:c,value:i(""),gutter:l,length:u(6),focused:Boolean,errorInfo:String},emits:["focus"],setup(e,{emit:s}){const l=e=>{e.stopPropagation(),s("focus",e)},a=()=>{const s=[],{mask:l,value:a,length:o,gutter:r,focused:n}=e;for(let e=0;e<o;e++){const o=a[e],i=0!==e&&!r,u=n&&e===a.length;let c;0!==e&&r&&(c={marginLeft:B(r)}),s.push(t("li",{class:[{[V]:i},C("item",{focus:u})],style:c},[l?t("i",{style:{visibility:o?"visible":"hidden"}},null):o,u&&t("div",{class:C("cursor")},null)]))}return s};return()=>{const s=e.errorInfo||e.info;return t("div",{class:C()},[t("ul",{class:[C("security"),{[z]:!e.gutter}],onTouchstartPassive:l},[a()]),s&&t("div",{class:C(e.errorInfo?"error-info":"info")},[s])])}}}));export{M as N,L as P};
