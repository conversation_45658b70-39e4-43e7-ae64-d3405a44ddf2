System.register(["./index-legacy-46a00900.js","./index-legacy-15165887.js","./index-legacy-bbd15202.js"],(function(e,o){"use strict";var a,i,t,n,s,r,l,c,m,v,g,d,u,h,p,b,f,w,x,y,z,C,k,I,_,X,P,Y,Z,S,W,H,R,T,$,j,D,N,B,O=document.createElement("style");return O.textContent=":root{--van-image-placeholder-text-color: var(--van-text-color-2);--van-image-placeholder-font-size: var(--van-font-size-md);--van-image-placeholder-background-color: var(--van-background-color);--van-image-loading-icon-size: 32px;--van-image-loading-icon-color: var(--van-gray-4);--van-image-error-icon-size: 32px;--van-image-error-icon-color: var(--van-gray-4) }.van-image{position:relative;display:inline-block}.van-image--round{overflow:hidden;border-radius:var(--van-border-radius-max)}.van-image--round .van-image__img{border-radius:inherit}.van-image--block{display:block}.van-image__img,.van-image__error,.van-image__loading{display:block;width:100%;height:100%}.van-image__error,.van-image__loading{position:absolute;top:0;left:0;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;color:var(--van-image-placeholder-text-color);font-size:var(--van-image-placeholder-font-size);background:var(--van-image-placeholder-background-color)}.van-image__loading-icon{color:var(--van-image-loading-icon-color);font-size:var(--van-image-loading-icon-size)}.van-image__error-icon{color:var(--van-image-error-icon-color);font-size:var(--van-image-error-icon-size)}\n",document.head.appendChild(O),{setters:[e=>{a=e.P,i=e.d,t=e.bc,n=e.S,s=e.p,r=e.aC,l=e.r,c=e.m,m=e.g,v=e.ac,g=e.e,d=e.V,u=e.aQ,h=e.W,p=e.a9,b=e.q,f=e.aI,w=e.aJ,x=e.s,y=e.ag,z=e.aY,C=e.bn,k=e.ai,I=e.bl,_=e.a4,X=e.a5,P=e.R,Y=e.aT,Z=e.b0,S=e.I,W=e.bf,H=e.b9,R=e.Q,T=e.X,$=e.bv,j=e.bw},e=>{D=e.S,N=e.a},e=>{B=e.I}],execute:function(){const o=e=>Math.sqrt((e[0].clientX-e[1].clientX)**2+(e[0].clientY-e[1].clientY)**2),O=a("image-preview")[1];var M=i({props:{src:String,show:Boolean,active:Number,minZoom:t(n),maxZoom:t(n),rootWidth:t(Number),rootHeight:t(Number)},emits:["scale","close"],setup(e,{emit:a}){const i=s({scale:1,moveX:0,moveY:0,moving:!1,zooming:!1,imageRatio:0,displayWidth:0,displayHeight:0}),t=r(),n=l(),p=c((()=>{const{rootWidth:o,rootHeight:a}=e,t=a/o;return i.imageRatio>t})),b=c((()=>{const{scale:e,moveX:o,moveY:a,moving:t,zooming:n}=i,s={transitionDuration:n||t?"0s":".3s"};if(1!==e){const i=o/e,t=a/e;s.transform=`scale(${e}, ${e}) translate(${i}px, ${t}px)`}return s})),f=c((()=>{if(i.imageRatio){const{rootWidth:o,rootHeight:a}=e,t=p.value?a/i.imageRatio:o;return Math.max(0,(i.scale*t-o)/2)}return 0})),w=c((()=>{if(i.imageRatio){const{rootWidth:o,rootHeight:a}=e,t=p.value?a:o*i.imageRatio;return Math.max(0,(i.scale*t-a)/2)}return 0})),x=o=>{(o=u(o,+e.minZoom,+e.maxZoom+1))!==i.scale&&(i.scale=o,a("scale",{scale:o,index:e.active}))},y=()=>{x(1),i.moveX=0,i.moveY=0};let z,C,k,I,_,X,P;const Y=e=>{const{touches:a}=e,{offsetX:n}=t;t.start(e),z=a.length,C=i.moveX,k=i.moveY,P=Date.now(),i.moving=1===z&&1!==i.scale,i.zooming=2===z&&!n.value,i.zooming&&(I=i.scale,_=o(e.touches))},Z=()=>{if(z>1)return;const{offsetX:e,offsetY:o}=t,n=Date.now()-P;e.value<5&&o.value<5&&n<250&&(X?(clearTimeout(X),X=null,(()=>{const e=i.scale>1?1:2;x(e),i.moveX=0,i.moveY=0})()):X=setTimeout((()=>{a("close"),X=null}),250))},S=o=>{let a=!1;(i.moving||i.zooming)&&(a=!0,i.moving&&C===i.moveX&&k===i.moveY&&(a=!1),o.touches.length||(i.zooming&&(i.moveX=u(i.moveX,-f.value,f.value),i.moveY=u(i.moveY,-w.value,w.value),i.zooming=!1),i.moving=!1,C=0,k=0,I=1,i.scale<1&&y(),i.scale>e.maxZoom&&(i.scale=+e.maxZoom))),d(o,a),Z(),t.reset()},W=e=>{const{naturalWidth:o,naturalHeight:a}=e.target;i.imageRatio=a/o};return m((()=>e.active),y),m((()=>e.show),(e=>{e||y()})),v("touchmove",(e=>{const{touches:a}=e;if(t.move(e),(i.moving||i.zooming)&&d(e,!0),i.moving){const{deltaX:e,deltaY:o}=t,a=e.value+C,n=o.value+k;i.moveX=u(a,-f.value,f.value),i.moveY=u(n,-w.value,w.value)}if(i.zooming&&2===a.length){const e=o(a);x(I*e/_)}}),{target:c((()=>{var e;return null==(e=n.value)?void 0:e.$el}))}),()=>{const o={loading:()=>g(h,{type:"spinner"},null)};return g(D,{ref:n,class:O("swipe-item"),onTouchstartPassive:Y,onTouchend:S,onTouchcancel:S},{default:()=>[g(B,{src:e.src,fit:"contain",class:O("image",{vertical:p.value}),style:b.value,onLoad:W},o)]})}}});const[q,A]=a("image-preview"),Q=["show","transition","overlayStyle","closeOnPopstate"],U={show:Boolean,loop:k,images:I(),minZoom:_(1/3),maxZoom:_(3),overlay:k,closeable:Boolean,showIndex:k,className:X,closeIcon:P("clear"),transition:String,beforeClose:Function,overlayClass:X,overlayStyle:Object,swipeDuration:_(300),startPosition:_(0),showIndicators:Boolean,closeOnPopstate:k,closeIconPosition:P("top-right")};var E=i({name:q,props:U,emits:["scale","close","closed","change","update:show"],setup(e,{emit:o,slots:a}){const i=l(),t=s({active:0,rootWidth:0,rootHeight:0}),n=()=>{if(i.value){const e=Y(i.value.$el);t.rootWidth=e.width,t.rootHeight=e.height,i.value.resize()}},r=e=>o("scale",e),c=e=>o("update:show",e),v=()=>{W(e.beforeClose,{args:[t.active],done:()=>c(!1)})},d=e=>{e!==t.active&&(t.active=e,o("change",e))},u=()=>{if(e.showIndex)return g("div",{class:A("index")},[a.index?a.index({index:t.active}):`${t.active+1} / ${e.images.length}`])},h=()=>{if(a.cover)return g("div",{class:A("cover")},[a.cover()])},k=()=>{if(e.closeable)return g(S,{role:"button",name:e.closeIcon,class:[A("close-icon",e.closeIconPosition),Z],onClick:v},null)},I=()=>o("closed"),_=(e,o)=>{var a;return null==(a=i.value)?void 0:a.swipeTo(e,o)};return p({swipeTo:_}),b(n),m([f,w],n),m((()=>e.startPosition),(e=>d(+e))),m((()=>e.show),(a=>{const{images:i,startPosition:s}=e;a?(d(+s),x((()=>{n(),_(+s,{immediate:!0})}))):o("close",{index:t.active,url:i[t.active]})})),()=>g(C,y({class:[A(),e.className],overlayClass:[A("overlay"),e.overlayClass],onClosed:I,"onUpdate:show":c},z(e,Q)),{default:()=>[k(),g(N,{ref:i,lazyRender:!0,loop:e.loop,class:A("swipe"),duration:e.swipeDuration,initialSwipe:e.startPosition,showIndicators:e.showIndicators,indicatorColor:"white",onChange:d},{default:()=>[e.images.map((o=>g(M,{src:o,show:e.show,active:t.active,maxZoom:e.maxZoom,minZoom:e.minZoom,rootWidth:t.rootWidth,rootHeight:t.rootHeight,onScale:r,onClose:v},null)))]}),u(),h()]})}});let F;const J={loop:!0,images:[],maxZoom:3,minZoom:1/3,onScale:void 0,onClose:void 0,onChange:void 0,teleport:"body",className:"",showIndex:!0,closeable:!1,closeIcon:"clear",transition:void 0,beforeClose:void 0,overlayStyle:void 0,overlayClass:void 0,startPosition:0,swipeDuration:300,showIndicators:!1,closeOnPopstate:!0,closeIconPosition:"top-right"},L=e("I",((e,o=0)=>{if(H)return F||({instance:F}=$({setup(){const{state:e,toggle:o}=j(),a=()=>{e.images=[]};return()=>g(E,y(e,{onClosed:a,"onUpdate:show":o}),null)}})),e=Array.isArray(e)?{images:e,startPosition:o}:e,F.open(R({},J,e)),F}));L.Component=T(E),L.install=e=>{e.use(L.Component)}}}}));
