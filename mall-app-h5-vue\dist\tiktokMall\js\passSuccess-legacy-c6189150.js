System.register(["./index-legacy-46a00900.js","./index-legacy-72e00c5f.js","./success-legacy-1fd9e8c8.js","./use-route-legacy-be86ac1c.js"],(function(t,e){"use strict";var i,a,n,o,s,d,c,f,l,r,x,b,p,g,m=document.createElement("style");return m.textContent=".resetSuccess[data-v-fddfa7f1]{width:100%;height:100vh;box-sizing:border-box;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column;background-color:#f8f9fa}.content[data-v-fddfa7f1]{-webkit-box-flex:1;-webkit-flex:1 1;flex:1 1;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;align-items:center;padding:20px;text-align:center;padding-bottom:calc(20px + env(safe-area-inset-bottom))}.success-container[data-v-fddfa7f1]{-webkit-box-flex:1;-webkit-flex:1 1;flex:1 1;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;align-items:center;max-width:300px;margin:0 auto}.imgBox[data-v-fddfa7f1]{width:80px;height:80px;margin-bottom:24px}.imgBox img[data-v-fddfa7f1]{width:100%;height:100%;object-fit:contain}.title[data-v-fddfa7f1]{font-size:20px;font-weight:600;color:#333;margin-bottom:12px;line-height:1.4}.subtitle[data-v-fddfa7f1]{font-size:14px;color:#666;line-height:1.5;margin-bottom:20px}.login-btn[data-v-fddfa7f1]{width:100%;max-width:300px;height:44px;border-radius:22px;font-size:16px;font-weight:500;margin-top:auto;margin-bottom:20px}@media (max-height: 600px){.content[data-v-fddfa7f1]{-webkit-box-pack:start;-webkit-justify-content:flex-start;justify-content:flex-start;padding-top:40px}.success-container[data-v-fddfa7f1]{-webkit-box-flex:0;-webkit-flex:none;flex:none}.imgBox[data-v-fddfa7f1]{width:60px;height:60px;margin-bottom:16px}.title[data-v-fddfa7f1]{font-size:18px;margin-bottom:8px}.subtitle[data-v-fddfa7f1]{font-size:13px;margin-bottom:16px}}@supports (padding: max(0px)){.content[data-v-fddfa7f1]{padding-left:max(20px,env(safe-area-inset-left));padding-right:max(20px,env(safe-area-inset-right));padding-bottom:max(20px,env(safe-area-inset-bottom))}}\n",document.head.appendChild(m),{setters:[t=>{i=t._,a=t.l,n=t.j,o=t.av,s=t.c,d=t.e,c=t.a,f=t.b,l=t.t,r=t.w,x=t.o,b=t.f},t=>{p=t.B},t=>{g=t.s},()=>{}],execute:function(){const e={class:"resetSuccess"},m={class:"content"},u={class:"success-container"},w={class:"imgBox"},k=["src"],h={class:"title"},v={class:"subtitle"};t("default",i({__name:"passSuccess",setup(t){const i=a(),y=n(),j=()=>{y.userInfo={},i.push("/login")};return(t,i)=>{const a=o("fx-header"),n=p;return x(),s("div",e,[d(a,{back:!1,onBack:j}),c("div",m,[c("div",u,[c("div",w,[c("img",{src:f(g),alt:""},null,8,k)]),c("div",h,l(t.$t("passwordChangeSuccess")),1),c("div",v,l(t.$t("useNewPasswordLogin"))+"!",1)]),d(n,{class:"login-btn",type:"primary",onClick:j},{default:r((()=>[b(l(t.$t("login")),1)])),_:1})])])}}},[["__scopeId","data-v-fddfa7f1"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/forget/passSuccess.vue"]]))}}}));
