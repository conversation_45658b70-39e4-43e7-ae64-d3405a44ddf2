import{_ as e,d as a,u as s,i as t,r as n,s as i,av as l,c as o,e as u,w as r,o as d,f as p,t as c,b as v,F as m,y as f,a as g,n as y,x,A as h,D as T,E as b,cI as _,cJ as k,ak as L,cK as z}from"./index-3d21abf8.js";import{P as M}from"./index-fc51b7d2.js";import{L as S}from"./index-40e83579.js";import{E as w}from"./index-5d897066.js";import{m as H}from"./config-6f1990d6.js";import"./use-id-a0619e01.js";const j={class:"page-main-content has-fixed-header"},N=(e=>(T("data-v-8d741b7f"),e=e(),b(),e))((()=>g("div",{class:"fixed-header-spacer"},null,-1))),I={key:0,class:"list-content"},A=["onClick"],E={class:"title-content"},O={class:"title"},P={key:0,class:"txt"},J=["innerHTML"],R=["innerHTML"],U=["innerHTML"],$=["innerHTML"],C=["innerHTML"],V={key:0},D={key:1,class:"txt"},F=a({name:"MessageSystem"}),q=e(Object.assign(F,{setup(e){const{t:a}=s(),T=t(),b=new URL("/www/png/name-20d65991.png",self.location),F=n([]),q=n(!1),K=n(!0),B=n(!1),G=n({pageSize:20,pageNum:1}),Q=()=>{const e={...G.value,totalElements:-1,type:3,status:0,module:1};_(e).then((e=>{const s=e.elements||[];s.forEach((e=>{const s={};(e.varInfo?JSON.parse(e.varInfo):[]).forEach((e=>{s[e.code]=["complaintReason"].includes(e.code)?a(e.value):e.value})),e.timeStr=k(e.sendTime,!0),e.varObj=s})),F.value=1===G.value.pageNum?s:[...F.value,...s],K.value=!1,q.value=!1,e.totalPage!==G.value.pageNum&&e.totalPage||(B.value=!0),e.totalPage>G.value.pageNum&&(G.value.pageNum+=1)})).catch((()=>{B.value=!0,K.value=!1,q.value=!1}))},W=()=>{B.value=!1,K.value=!0,G.value.pageNum=1,Q()},X=(e,a)=>{const s=(e.varInfo?JSON.parse(e.varInfo):[]).find((e=>e.code===a));return s?s.value:"0"},Y=(e,a)=>{const s=(e.varInfo?JSON.parse(e.varInfo):[]).find((e=>e.code===a));return s?s.value:""};return i((()=>{document.addEventListener("systemListRefresh",(()=>{W()}),!1)})),(e,s)=>{const t=l("fx-header"),n=w,i=S,_=M;return d(),o("div",j,[u(t,{fixed:!0},{title:r((()=>[p(c(e.$t("系统消息")),1)])),_:1}),N,u(_,{modelValue:q.value,"onUpdate:modelValue":s[1]||(s[1]=e=>q.value=e),"loading-text":e.$t("加载中"),"loosing-text":e.$t("释放以刷新"),"pulling-text":e.$t("下拉以刷新"),onRefresh:W},{default:r((()=>[u(i,{loading:K.value,"onUpdate:loading":s[0]||(s[0]=e=>K.value=e),finished:B.value,"loading-text":v(a)("loading"),"finished-text":v(a)("product.3"),onLoad:Q},{default:r((()=>[F.value.length?(d(),o("div",I,[(d(!0),o(m,null,f(F.value,(e=>(d(),o("div",{key:e.id,class:"item-list",onClick:a=>{return L({path:"/message/details",query:{id:(s=e).id}}),void(2!==s.status&&z({ids:s.id}).then((()=>{s.status=2})));var s}},[g("div",E,[g("div",O,[1===e.status?(d(),o("div",{key:0,class:y(["tips",{"is-ar":v(T)}])},null,2)):x("v-if",!0),g("p",null,c(v(a)(v(H)[e.bizType].title)),1)]),g("p",null,c(e.timeStr),1)]),v(H)[e.bizType].key?(d(),o("p",P,["creditScore"===v(H)[e.bizType].key?(d(),o("span",{key:0,innerHTML:v(a)("systemMsgScore",{creditScore:X(e,v(H)[e.bizType].key)})},null,8,J)):"inbox_recharge_success"===e.bizType?(d(),o("span",{key:1,innerHTML:v(a)("rechargeSuccessTips",{orderAmount:X(e,"orderAmount")})},null,8,R)):"inbox_withdraw_success"===e.bizType?(d(),o("span",{key:2,innerHTML:v(a)("withdrawalSuccessTips",{orderAmount:X(e,"orderAmount")})},null,8,U)):"inbox_store_audit_fail"===e.bizType?(d(),o("span",{key:3,innerHTML:v(a)("storeAuthenticationFailedTips",{shop_name:Y(e,"shop_name"),reason:Y(e,"reason")})},null,8,$)):"inbox_store_audit_success"===e.bizType?(d(),o("span",{key:4,innerHTML:v(a)("storeAuthenticationPassedTips",{shop_name:Y(e,"shop_name")})},null,8,C)):(d(),o(m,{key:5},[g("span",null,c(v(a)(v(H)[e.bizType].txt)),1),g("span",null,c(X(e,v(H)[e.bizType].key)),1),v(H)[e.bizType].txt1?(d(),o("span",V,c(v(a)(v(H)[e.bizType].txt1)),1)):x("v-if",!0)],64))])):(d(),o("p",D,c(v(a)(v(H)[e.bizType].txt,e.varObj)),1))],8,A)))),128))])):x("v-if",!0),F.value.length||K.value?x("v-if",!0):(d(),h(n,{key:1,image:v(b).href},null,8,["image"]))])),_:1},8,["loading","finished","loading-text","finished-text"])])),_:1},8,["modelValue","loading-text","loosing-text","pulling-text"])])}}}),[["__scopeId","data-v-8d741b7f"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/message/system/index.vue"]]);export{q as default};
