@tailwind base;
@tailwind components;
@tailwind utilities;

/* overwite default */
:root {
    font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    width: 100%;
    color-scheme: light dark;
    /* color: rgba(0, 0, 0, 0.87); */
    background-color: #EFF2F6;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;
    
    /* iOS 安全区域变量 */
    --safe-area-inset-top: 0px;
    --safe-area-inset-right: 0px;
    --safe-area-inset-bottom: 0px;
    --safe-area-inset-left: 0px;
}

/* iOS设备全局顶部适配 - 恢复基础适配 */
html, body {
    padding-top: 0;
    margin-top: 0;
}

/* iOS设备基础安全区域设置 */
.ios-device {
    --ios-safe-area-top: 44px;
    --ios-safe-area-bottom: 34px;
}

/* 现代浏览器支持 */
@supports (padding-top: env(safe-area-inset-top)) {
    .ios-device {
        --ios-safe-area-top: env(safe-area-inset-top);
        --ios-safe-area-bottom: env(safe-area-inset-bottom);
    }
}

body, #app {
    width: 100%;
    color: #1F2025;
    background: #EFF2F6;
    min-height: 100vh;
}

/* 主要内容区域 */
.page-main-content {
    min-height: 100vh;
    background-color: #EFF2F6;
}

/* iOS设备页面内容适配策略 */

/* 1. 基础页面适配 - 为所有页面添加基础的iOS安全区域 */
.ios-device .page-main-content {
    padding-top: var(--ios-safe-area-top);
}

/* 2. 店铺首页特殊适配 - ShopHeader已经处理了安全区域，页面不需要额外padding */
.ios-device .page-main-content.shop-home-page {
    padding-top: 0;
}

/* 3. 商品管理页面适配 - 由组件内部处理，移除全局适配避免重复 */
.ios-device .page-main-content.product-home-page {
    padding-top: 0;
}

/* 4. 商品库页面适配 - 使用fx-header的页面 */
.ios-device .page-main-content.has-fixed-header {
    padding-top: 0; /* 移除padding，让fx-header组件自己处理 */
}

/* fx-header组件在iOS设备上的适配 */
.ios-device .fx-header.fixed {
    top: var(--ios-safe-area-top) !important;
    height: 46px; /* 保持原有高度，通过调整top值处理安全区域 */
}

/* 为有固定头部的页面添加正确的top空间 */
.ios-device .has-fixed-header .fixed-header-spacer {
    height: calc(46px + var(--ios-safe-area-top)); /* 修正高度计算 */
}

/* 确保商品库页面内容不会被固定头部遮挡 */
.ios-device .has-fixed-header .dropdown {
    margin-top: 10px; /* 确保下拉菜单与头部有间距 */
}

/* 固定定位元素的安全区域适配 */
.fixed-top {
    position: fixed;
    top: var(--safe-area-inset-top);
    left: 0;
    right: 0;
    z-index: 1000;
}

.fixed-bottom {
    position: fixed;
    bottom: var(--safe-area-inset-bottom);
    left: 0;
    right: 0;
    z-index: 1000;
}

/* iOS设备固定元素适配 */
.ios-device .van-nav-bar--fixed,
.ios-device .fx-header--fixed {
    top: var(--ios-safe-area-top) !important;
}

/* ShopHeader的iOS适配由组件内部样式处理，移除重复规则 */

/* 订单页面iOS适配 - 修复头部重叠 */
.ios-device #order {
    padding-top: var(--ios-safe-area-top);
}

.ios-device #order .van-tabs {
    position: relative;
    top: 0;
}

.ios-device #order .van-tabs__wrap {
    position: fixed !important;
    top: var(--ios-safe-area-top) !important;
    left: 0;
    right: 0;
    z-index: 99;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.ios-device #order .van-tabs__content {
    padding-top: 0px; /* 移除van-tabs内容区域的padding */
}

/* 移除重复的样式，已在下面的强制适配中处理 */

.ios-device #order .main {
    margin-top: 0; /* 移除额外margin */
    height: calc(100vh - var(--ios-safe-area-top) - 44px - 51px); /* 安全区域 + tabs + 底部导航 */
}

/* 客服页面iOS适配 - 修复显示问题 */
.ios-device .service-box {
    padding-top: 20px; /* 不需要额外padding，通过固定导航栏处理 */
    position: relative;
    height: 100vh;
    overflow: hidden;
}

.ios-device .service-box .van-nav-bar {
    position: fixed !important;
    top: var(--ios-safe-area-top) !important;
    left: 0;
    right: 0;
    z-index: 100;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.ios-device .service-box .content {
    padding-top: calc(var(--ios-safe-area-top) + 46px) !important; /* 安全区域 + 导航栏高度 */
    padding-bottom: 20px !important; /* 底部间距设置为0，最大化空间利用 */
    min-height: calc(100vh - var(--ios-safe-area-top) - 46px);
    max-height: calc(100vh - var(--ios-safe-area-top) - 46px);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.ios-device .service-box .bottomBox {
    position: fixed !important;
    bottom: var(--ios-safe-area-bottom) !important;
    left: 0;
    right: 0;
    z-index: 101;
    background: #fff;
    border-top: 1px solid #f3f3f3;
    padding-bottom: var(--ios-safe-area-bottom);
    min-height: calc(65px + var(--ios-safe-area-bottom));
}

/* iOS设备底部输入框特殊适配 */
.ios-device .service-box .ios-bottom-input {
    bottom: 0 !important;
    padding-bottom: var(--ios-safe-area-bottom) !important;
}

/* 页面固定头部占位高度 */
.fixed-header-spacer {
    height: 46px; /* fx-header默认高度 */
}

.ios-device .fixed-header-spacer {
    height: calc(46px + var(--ios-safe-area-top)) !important; /* 修正高度计算，强制生效 */
    background: transparent; /* 确保占位符透明 */
    position: relative;
    z-index: 1;
}

/* 确保商品库页面内容不被头部遮挡 */
.has-fixed-header .dropdown {
    margin-top: 10px; /* 确保下拉菜单有足够间距 */
}

.has-fixed-header .list {
    margin-top: 0; /* 确保商品列表位置正确 */
}

/* 针对特定iPhone机型的强制适配 */
@media only screen 
  and (device-width: 375px) 
  and (device-height: 812px) 
  and (-webkit-device-pixel-ratio: 3) {
    .ios-device {
        --ios-safe-area-top: 44px !important;
        --ios-safe-area-bottom: 34px !important;
    }
}

@media only screen 
  and (device-width: 414px) 
  and (device-height: 896px) {
    .ios-device {
        --ios-safe-area-top: 44px !important;
        --ios-safe-area-bottom: 34px !important;
    }
}

@media only screen 
  and (device-width: 390px) 
  and (device-height: 844px) 
  and (-webkit-device-pixel-ratio: 3) {
    .ios-device {
        --ios-safe-area-top: 44px !important;
        --ios-safe-area-bottom: 34px !important;
    }
}

input {
    outline: none;
}

input::-webkit-input-placeholder {
    color: #C0C4CC;
}

ul,
li {
    list-style: none;
}

p {
    padding: 0;
    margin: 0;
}

/* component css */
.van-list {
    min-height: calc(100vh - 120px - var(--safe-area-inset-top) - var(--safe-area-inset-bottom));
}

.safe-area-inset-bottom {
    height: constant(safe-area-inset-bottom);
    height: env(safe-area-inset-bottom);
}

.footer-padding {
    height: calc(50px + constant(safe-area-inset-bottom));
    height: calc(50px + env(safe-area-inset-bottom));
}

/* 更新后的安全区域类 */
.safe-area-top {
    padding-top: var(--safe-area-inset-top);
}

.safe-area-bottom {
    padding-bottom: var(--safe-area-inset-bottom);
}

.safe-area-left {
    padding-left: var(--safe-area-inset-left);
}

.safe-area-right {
    padding-right: var(--safe-area-inset-right);
}

.safe-area-all {
    padding-top: var(--safe-area-inset-top);
    padding-right: var(--safe-area-inset-right);
    padding-bottom: var(--safe-area-inset-bottom);
    padding-left: var(--safe-area-inset-left);
}

/* 高度相关的安全区域 */
.safe-height-top {
    height: var(--safe-area-inset-top);
}

.safe-height-bottom {
    height: var(--safe-area-inset-bottom);
}

/* 针对WebView的特殊处理 */
.webview-container {
    width: 100%;
    height: calc(100vh - var(--safe-area-inset-top) - var(--safe-area-inset-bottom));
    padding-top: var(--safe-area-inset-top);
    padding-bottom: var(--safe-area-inset-bottom);
}

.webview-iframe {
    width: 100%;
    height: 100%;
    border: none;
}

/* 消息中心页面iOS适配 */
.ios-device .page-main-content.has-fixed-header .iframe-content {
    height: calc(100vh - var(--ios-safe-area-top) - var(--ios-safe-area-bottom)) !important;
    padding-top: calc(46px + var(--ios-safe-area-top)) !important;
    padding-bottom: var(--ios-safe-area-bottom) !important;
}

.ios-device .page-main-content.has-fixed-header .iframe-content iframe {
    height: calc(100% - var(--ios-safe-area-bottom)) !important;
    border: none;
}

/* 修复iOS设备上的输入框焦点问题 */
.ios-device textarea,
.ios-device input[type="text"] {
    -webkit-appearance: none;
    border-radius: 0;
}

/* iOS设备上防止页面缩放 */
.ios-device {
    -webkit-text-size-adjust: 100%;
    -webkit-user-select: none;
    user-select: none;
}

/* iOS设备上的触摸滚动优化 */
.ios-device .content,
.ios-device .iframe-content {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
}

input[type="password"]::-ms-reveal{
    display: none;
}
input[type="password"]::-ms-clear{
    display: none;
}
input[type="password"]::-o-clear{
    display: none;
}

.range-box {
    direction: ltr !important;
}

.van-loading__text {
    margin-right: 8px;
}

@keyframes bounceInUp {
    0% {
        opacity: 0;
        -webkit-transform: translateY(2000px);
        -ms-transform: translateY(2000px);
        transform: translateY(2000px);
    }

    60% {
        opacity: 1;
        -webkit-transform: translateY(-30px);
        -ms-transform: translateY(-30px);
        transform: translateY(-30px);
    }
    80% {
        -webkit-transform: translateY(10px);
        -ms-transform: translateY(10px);
        transform: translateY(10px);
    }
    100% {
        -webkit-transform: translateY(0);
        -ms-transform: translateY(0);
        transform: translateY(0);
    }
}

/* 消息相关页面iOS适配 */
.ios-device .message-page {
    padding-top: var(--ios-safe-area-top);
}

.ios-device .message-page .van-nav-bar--fixed {
    top: var(--ios-safe-area-top) !important;
}

.ios-device .message-page .content {
    margin-top: 46px; /* 导航栏高度 */
}

/* 通用页面iOS适配 - 有固定导航栏的页面 */
.ios-device .has-nav-bar {
    padding-top: 0;
}

.ios-device .has-nav-bar .van-nav-bar--fixed {
    top: var(--ios-safe-area-top) !important;
}

.ios-device .has-nav-bar .page-content {
    margin-top: calc(var(--ios-safe-area-top) + 46px);
}

/* 底部导航栏适配 */
.ios-device .van-tabbar {
    padding-bottom: var(--ios-safe-area-bottom);
}

/* 弹窗和对话框适配 */
.ios-device .van-popup,
.ios-device .van-dialog {
    margin-top: var(--ios-safe-area-top);
}

/* 特定iPhone机型强制适配 */
@media only screen 
  and (device-width: 375px) 
  and (device-height: 812px) 
  and (-webkit-device-pixel-ratio: 3) {
    .ios-device {
        --ios-safe-area-top: 44px !important;
        --ios-safe-area-bottom: 34px !important;
    }
}

@media only screen 
  and (device-width: 414px) 
  and (device-height: 896px) {
    .ios-device {
        --ios-safe-area-top: 44px !important;
        --ios-safe-area-bottom: 34px !important;
    }
}

@media only screen 
  and (device-width: 390px) 
  and (device-height: 844px) 
  and (-webkit-device-pixel-ratio: 3) {
    .ios-device {
        --ios-safe-area-top: 44px !important;
        --ios-safe-area-bottom: 34px !important;
    }
}

/* ================================ */
/* 订单页面和商品库页面iOS适配检查 */
/* ================================ */

/* 订单页面 - 精确的iOS适配 */
.ios-device #order {
    position: relative;
    overflow: hidden;
}

/* 确保van-tabs在iOS设备上的正确定位 */
.ios-device #order .van-tabs--sticky .van-tabs__wrap {
    position: fixed !important;
    top: var(--ios-safe-area-top) !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 100 !important;
    background: #fff !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
}

/* 确保订单页面内容区域有正确的顶部空间 */
.ios-device #order .main {
    margin-top: 0 !important; /* 移除过多的margin */
    height: calc(100vh - var(--ios-safe-area-top) - 44px - 51px) !important; /* 总高度 - 安全区域 - tabs - 底部导航 */
    padding-top: 0px !important; /* 进一步减少顶部padding */
}

/* 商品库页面 - 精确的iOS适配 */
.ios-device .has-fixed-header {
    position: relative;
}

/* 确保fx-header在iOS设备上不会遮挡内容 */
.ios-device .has-fixed-header .fx-header--fixed {
    position: fixed !important;
    top: var(--ios-safe-area-top) !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 100 !important;
    background: transparent !important;
    box-shadow: none !important;
}

/* 额外确保fx-header的固定定位生效 */
.ios-device .fx-header.fx-header--fixed {
    position: fixed !important;
    top: var(--ios-safe-area-top) !important;
    height: 46px !important;
    width: 100% !important;
}

/* 商品库页面内容区域顶部空间 */
.ios-device .has-fixed-header .fixed-header-spacer {
    height: calc(46px + var(--ios-safe-area-top)) !important;
    background: transparent;
}

/* 强制确保商品库页面下拉菜单不被遮挡 */
.ios-device .has-fixed-header .dropdown {
    margin-top: 20px !important; /* 增加更多间距防止重叠 */
    position: relative;
    z-index: 1;
}

/* 额外确保商品库页面的商品列表不被遮挡 */
.ios-device .has-fixed-header .list {
    margin-top: 0 !important;
    padding-top: 25px !important; /* 增加顶部间距防止重叠 */
}

/* 强制确保订单页面搜索区域不被遮挡 */
.ios-device #order .main .seach {
    margin-top: 0px !important; /* 移除顶部边距 */
    position: relative;
    z-index: 1;
}

/* 强制确保订单页面下拉菜单不被遮挡 */
.ios-device #order .main .dropdown {
    margin-top: 0px !important; /* 移除顶部边距 */
    position: relative;
    z-index: 1;
}

/* ================================ */
/* 商品库页面强制适配规则 - 防止重叠 */
/* ================================ */

/* 强制修复商品库页面头部重叠问题 */
.ios-device .product.has-fixed-header {
    position: relative;
}

/* 确保fx-header完全固定在顶部 */
.ios-device .product.has-fixed-header .fx-header {
    position: fixed !important;
    top: var(--ios-safe-area-top) !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 999 !important;
    background: transparent !important;
    box-shadow: none !important;
    height: 46px !important;
}

/* 强制设置占位符高度 */
.ios-device .product.has-fixed-header .fixed-header-spacer {
    height: calc(46px + var(--ios-safe-area-top)) !important;
    min-height: calc(46px + var(--ios-safe-area-top)) !important;
    background: transparent !important;
    display: block !important;
}

/* 强制设置下拉菜单间距 */
.ios-device .product.has-fixed-header .dropdown {
    margin-top: 25px !important;
    margin-bottom: 20px !important;
    position: relative !important;
    z-index: 1 !important;
}

/* 强制设置商品列表间距 */
.ios-device .product.has-fixed-header .list {
    margin-top: 0 !important;
    padding-top: 30px !important;
    position: relative !important;
    z-index: 1 !important;
}

/* 确保第一个商品项不被遮挡 */
.ios-device .product.has-fixed-header .list .item:first-child {
    margin-top: 10px !important;
}

/* ================================ */
/* 订单详情页面iOS适配 - 防止重叠 */
/* ================================ */

/* 订单详情页面使用相同的has-fixed-header结构 */
.ios-device .page-main-content.has-fixed-header {
    position: relative;
}

/* 确保订单详情页面的fx-header固定定位 */
.ios-device .page-main-content.has-fixed-header .fx-header {
    position: fixed !important;
    top: var(--ios-safe-area-top) !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 999 !important;
    background: transparent !important;
    box-shadow: none !important;
    height: 46px !important;
    width: 100% !important;
}

/* 订单详情页面占位符强制高度 */
.ios-device .page-main-content.has-fixed-header .fixed-header-spacer {
    height: calc(46px + var(--ios-safe-area-top)) !important;
    min-height: calc(46px + var(--ios-safe-area-top)) !important;
    background: transparent !important;
    display: block !important;
}

/* 订单详情页面第一个section不被遮挡 */
.ios-device .page-main-content.has-fixed-header .section:first-of-type {
    margin-top: 25px !important;
}

/* 订单详情页面商品列表不被遮挡 */
.ios-device .page-main-content.has-fixed-header .listitem:first-of-type {
    margin-top: 25px !important;
}

/* ================================ */
/* 个人信息页面iOS适配 */
/* ================================ */

/* 个人信息页面基础适配 */
.ios-device .personal-info-page {
    padding-top: var(--ios-safe-area-top) !important;
    min-height: 100vh;
    background-color: #EFF2F6;
}

/* 个人信息页面fx-header适配 */
.ios-device .personal-info-page .fx-header {
    margin-top: var(--ios-safe-area-top);
    position: relative;
    z-index: 10;
}

/* 个人信息页面头像区域适配 */
.ios-device .personal-info-page .img {
    margin-top: 10px !important;
}

/* 个人信息页面cell-group适配 */
.ios-device .personal-info-page .van-cell-group {
    margin-top: 10px;
}

/* ================================ */
/* 全局头部透明适配 - 避免遮挡内容 */
/* ================================ */

/* fx-header组件透明背景 */
.fx-header {
    background: transparent !important;
}

/* van-nav-bar组件透明背景 */
.van-nav-bar {
    background: transparent !important;
}

/* 移除阴影避免视觉干扰 */
.fx-header,
.van-nav-bar {
    box-shadow: none !important;
}

/* 确保标题和按钮可见 */
.fx-header .fx-header__title,
.fx-header .fx-header__left,
.fx-header .fx-header__right,
.van-nav-bar .van-nav-bar__title,
.van-nav-bar .van-nav-bar__left,
.van-nav-bar .van-nav-bar__right {
    color: #333 !important;
}

/* 图标颜色调整 */
.fx-header .van-icon,
.van-nav-bar .van-icon {
    color: #333 !important;
}

/* 语言页面 iOS 适配 - 简化直接版本 */
.language-page {
  padding-top: 0 !important;
}

/* 通用iOS头部固定定位 - 完全透明 */
.ios-device .language-page .fx-header,
body.ios-device .language-page .fx-header {
  position: fixed !important;
  top: var(--ios-safe-area-inset-top, 44px) !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  z-index: 999 !important;
  background: transparent !important;
  box-shadow: none !important;
}

/* 头部占位符适配 */
.ios-device .language-page .header-spacer,
.ios-device .language-page > div[style*="height: 46px"],
body.ios-device .language-page .header-spacer,
body.ios-device .language-page > div[style*="height: 46px"] {
  height: calc(46px + var(--ios-safe-area-inset-top, 44px)) !important;
  min-height: calc(46px + var(--ios-safe-area-inset-top, 44px)) !important;
}

/* 最强优先级适配 - 确保语言页面在所有iOS设备上都能正确显示 */
.ios-device .language-page .header-spacer {
  height: calc(46px + var(--ios-safe-area-top, 44px)) !important;
  height: calc(46px + var(--ios-safe-area-inset-top, 44px)) !important;
  background: transparent !important;
  display: block !important;
}

.ios-device .language-page .fx-header {
  position: fixed !important;
  top: var(--ios-safe-area-top, 44px) !important;
  top: var(--ios-safe-area-inset-top, 44px) !important;
  width: 100vw !important;
  z-index: 9999 !important;
  background: transparent !important;
  box-shadow: none !important;
}

/* 确保语言页面头部文字和图标可见 */
.ios-device .language-page .fx-header .fx-header__title,
.ios-device .language-page .fx-header .fx-header__left,
.ios-device .language-page .fx-header .fx-header__right {
  color: #333 !important;
}

.ios-device .language-page .fx-header .van-icon {
  color: #333 !important;
}

/* 语言页面背景优化 */
.ios-device .language-page {
  background: #fff !important;
}

/* 强制透明头部 - 确保在所有情况下都生效 */
.language-page .fx-header,
body.ios-device .language-page .fx-header,
html.ios-device .language-page .fx-header {
  background: transparent !important;
  backdrop-filter: none !important;
  box-shadow: none !important;
}

/* 确保语言选项列表有合适的间距 */
.ios-device .language-page .lang-padding:first-of-type {
  margin-top: 10px;
  border-top: 1px solid #f0f0f0;
}

/* 语言页面最终优化 - 完整透明适配 */
.language-page .fx-header * {
  background: transparent !important;
}

/* 语言页面在非iOS设备上的优化 */
.language-page .header-spacer {
  background: transparent;
}

/* 确保语言列表在有头部的情况下不被遮挡 */
.language-page .lang-padding {
  position: relative;
  z-index: 1;
}

/* 语言页面头部在各种状态下都保持透明 */
.language-page .fx-header,
.language-page .fx-header.fixed,
.language-page .fx-header--fixed {
  background: transparent !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

/* 针对不同设备尺寸的直接适配 */
@media only screen and (max-height: 667px) {
  .ios-device .language-page .fx-header {
    top: 20px !important;
  }
  .ios-device .language-page .header-spacer {
    height: 66px !important; /* 20px + 46px */
  }
}

@media only screen and (min-height: 800px) {
  .ios-device .language-page .fx-header {
    top: 44px !important;
  }
  .ios-device .language-page .header-spacer {
    height: 90px !important; /* 44px + 46px */
  }
}

/* 资金记录页面 iOS 适配 */
.ios-device .funds-records-page {
  padding-top: var(--ios-safe-area-top, 44px) !important;
}

.ios-device .funds-records-page .fx-header {
  padding-top: var(--ios-safe-area-top, 44px) !important;
}

.ios-device .funds-records-page > div[style*="height: 46px"] {
  height: calc(46px + var(--ios-safe-area-top, 44px)) !important;
}

/* 财务报表页面 iOS 适配 */
.ios-device .financial-statements-page {
  padding-top: var(--ios-safe-area-top, 44px) !important;
}

.ios-device .financial-statements-page .fx-header {
  padding-top: var(--ios-safe-area-top, 44px) !important;
}

.ios-device .financial-statements-page > div[style*="height: 46px"] {
  height: calc(46px + var(--ios-safe-area-top, 44px)) !important;
}

.ios-device .financial-statements-page .report-content {
  margin-top: 10px;
}

/* 创业联盟页面 iOS 适配 - 强化版本 */
.ios-device .invitation-activity-page {
  padding-top: var(--ios-safe-area-inset-top, 44px) !important;
  min-height: 100vh;
  box-sizing: border-box !important;
}

.ios-device .invitation-activity-page .fx-header {
  position: fixed !important;
  top: var(--ios-safe-area-inset-top, 44px) !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  z-index: 999 !important;
  background: transparent !important;
  box-shadow: none !important;
}

/* 创业联盟页面header-spacer强化适配 - iOS设备隐藏spacer */
.ios-device .invitation-activity-page .header-spacer {
  display: none !important;
}

.ios-device .invitation-activity-page > div[style*="height: 46px"] {
  height: calc(46px + var(--ios-safe-area-inset-top, 44px)) !important;
  min-height: calc(46px + var(--ios-safe-area-inset-top, 44px)) !important;
}

.ios-device .invitation-activity-page .invitation-content {
  padding-top: 26vh !important;
  box-sizing: border-box !important;
}

.ios-device .invitation-activity-page .info-title {
  top: 16vw !important;
  position: absolute !important;
}

/* 强制适配 - 确保所有iOS设备都有正确的顶部间距 */
body.ios-device .invitation-activity-page,
html.ios-device .invitation-activity-page {
  padding-top: var(--ios-safe-area-inset-top, 44px) !important;
  box-sizing: border-box !important;
}

body.ios-device .invitation-activity-page .fx-header,
html.ios-device .invitation-activity-page .fx-header {
  position: fixed !important;
  top: var(--ios-safe-area-inset-top, 44px) !important;
  background: transparent !important;
  box-shadow: none !important;
}

/* 强制header-spacer隐藏 - iOS设备不需要spacer */
body.ios-device .invitation-activity-page .header-spacer,
html.ios-device .invitation-activity-page .header-spacer {
  display: none !important;
}

body.ios-device .invitation-activity-page > div[style*="height: 46px"],
html.ios-device .invitation-activity-page > div[style*="height: 46px"] {
  height: calc(46px + var(--ios-safe-area-inset-top, 44px)) !important;
  min-height: calc(46px + var(--ios-safe-area-inset-top, 44px)) !important;
}

body.ios-device .invitation-activity-page .info-title,
html.ios-device .invitation-activity-page .info-title {
  top: 16vw !important;
  position: absolute !important;
}

body.ios-device .invitation-activity-page .invitation-content,
html.ios-device .invitation-activity-page .invitation-content {
  padding-top: 26vh !important;
}

/* 确保创业联盟页面头部文字和图标可见 */
.ios-device .invitation-activity-page .fx-header .fx-header__title,
.ios-device .invitation-activity-page .fx-header .fx-header__left,
.ios-device .invitation-activity-page .fx-header .fx-header__right {
  color: #fff !important;
}

.ios-device .invitation-activity-page .fx-header .van-icon {
  color: #fff !important;
}

/* 强制透明头部 - 确保在所有情况下都生效 */
.invitation-activity-page .fx-header,
body.ios-device .invitation-activity-page .fx-header,
html.ios-device .invitation-activity-page .fx-header {
  background: transparent !important;
  backdrop-filter: none !important;
  box-shadow: none !important;
}

/* 创业联盟页面最终优化 - 完整透明适配 */
.invitation-activity-page .fx-header * {
  background: transparent !important;
}

/* 创业联盟页面头部在各种状态下都保持透明 */
.invitation-activity-page .fx-header,
.invitation-activity-page .fx-header.fixed,
.invitation-activity-page .fx-header--fixed {
  background: transparent !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

/* 创业联盟页面最终适配优化 */
.invitation-activity-page .fx-header:before,
.invitation-activity-page .fx-header:after {
  display: none !important;
}

/* 确保在背景图片上的文字清晰可见 */
.invitation-activity-page .fx-header .fx-header__title,
.invitation-activity-page .fx-header .fx-header__left,
.invitation-activity-page .fx-header .fx-header__right {
  color: #fff !important;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3) !important;
}

.invitation-activity-page .fx-header .van-icon {
  color: #fff !important;
  filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3)) !important;
}

/* 确保创业联盟页面在所有情况下头部都透明 */
body .invitation-activity-page .fx-header,
html .invitation-activity-page .fx-header,
.invitation-activity-page .fx-header * {
  background: transparent !important;
  background-color: transparent !important;
}

/* 创业联盟页面 - 特殊增强规则 */
.invitation-activity-page .info-title {
  width: 100% !important;
  padding: 0 50px !important;
  position: absolute !important;
  left: 0 !important;
  z-index: 100 !important;
}

.invitation-activity-page .invitation-content {
  background-attachment: fixed !important;
  box-sizing: border-box !important;
  min-height: 100vh !important;
  position: relative !important;
  z-index: 1 !important;
}

/* 确保创业联盟页面的完全透明适配 */
.invitation-activity-page .fx-header,
.invitation-activity-page .fx-header * {
  background: transparent !important;
  background-color: transparent !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

/* 为所有iPhone机型添加专门的适配规则 - 简化版 */
@media screen and (device-width: 375px) and (device-height: 667px) {
  .invitation-activity-page .header-spacer {
    display: none !important;
  }
  .shop-promotion-page .header-spacer {
    height: calc(46px + 20px) !important;
  }
  .withdraw-page .header-spacer {
    height: calc(46px + 20px) !important;
  }
  .marketing-page .header-spacer {
    height: calc(46px + 20px) !important;
  }
}

@media screen and (device-width: 375px) and (device-height: 812px) {
  .invitation-activity-page .header-spacer {
    display: none !important;
  }
  .shop-promotion-page .header-spacer {
    height: calc(46px + 44px) !important;
  }
  .withdraw-page .header-spacer {
    height: calc(46px + 44px) !important;
  }
  .marketing-page .header-spacer {
    height: calc(46px + 44px) !important;
  }
}

@media screen and (device-width: 414px) and (device-height: 896px) {
  .invitation-activity-page .header-spacer {
    display: none !important;
  }
  .shop-promotion-page .header-spacer {
    height: calc(46px + 44px) !important;
  }
  .withdraw-page .header-spacer {
    height: calc(46px + 44px) !important;
  }
  .marketing-page .header-spacer {
    height: calc(46px + 44px) !important;
  }
}

/* 强制适配规则 - 特定iPhone机型 */
@media only screen and (device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) {
  .funds-records-page, .financial-statements-page, .login-page {
    padding-top: 20px !important;
  }
  .funds-records-page .fx-header,
  .financial-statements-page .fx-header {
    padding-top: 20px !important;
  }
  .language-page .fx-header {
    top: 20px !important;
  }
  .language-page .header-spacer {
    height: calc(46px + 20px) !important;
  }
  .invitation-activity-page {
    padding-top: 20px !important;
  }
  .invitation-activity-page .fx-header {
    top: 20px !important;
    background: transparent !important;
    box-shadow: none !important;
    position: fixed !important;
  }
  .invitation-activity-page .header-spacer {
    display: none !important;
  }
  .invitation-activity-page .info-title {
    top: 16vw !important;
    position: absolute !important;
  }
  .invitation-activity-page .invitation-content {
    padding-top: 26vh !important;
    box-sizing: border-box !important;
  }
  .shop-promotion-page .fx-header {
    top: 20px !important;
  }
  .shop-promotion-page .header-spacer {
    height: calc(46px + 20px) !important;
  }
  .withdraw-page .fx-header {
    top: 20px !important;
  }
  .withdraw-page .header-spacer {
    height: calc(46px + 20px) !important;
  }
  .marketing-page .fx-header {
    top: 20px !important;
  }
  .marketing-page .header-spacer {
    height: calc(46px + 20px) !important;
  }
  .login-page .tiktok-header {
    margin-top: 0 !important;
    margin-bottom: 10px !important;
  }
  .login-page .tiktok-shop-login {
    padding-top: calc(16px + 20px) !important;
  }
}

@media only screen and (device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) {
  .funds-records-page, .financial-statements-page, .login-page {
    padding-top: 44px !important;
  }
  .funds-records-page .fx-header,
  .financial-statements-page .fx-header {
    padding-top: 44px !important;
  }
  .language-page .fx-header {
    top: 44px !important;
  }
  .language-page .header-spacer {
    height: calc(46px + 44px) !important;
  }
  .invitation-activity-page {
    padding-top: 44px !important;
  }
  .invitation-activity-page .fx-header {
    top: 44px !important;
    background: transparent !important;
    box-shadow: none !important;
    position: fixed !important;
  }
  .invitation-activity-page .header-spacer {
    display: none !important;
  }
  .invitation-activity-page .info-title {
    top: 16vw !important;
    position: absolute !important;
  }
  .invitation-activity-page .invitation-content {
    padding-top: 26vh !important;
    box-sizing: border-box !important;
  }
  .shop-promotion-page .fx-header {
    top: 44px !important;
  }
  .shop-promotion-page .header-spacer {
    height: calc(46px + 44px) !important;
  }
  .withdraw-page .fx-header {
    top: 44px !important;
  }
  .withdraw-page .header-spacer {
    height: calc(46px + 44px) !important;
  }
  .marketing-page .fx-header {
    top: 44px !important;
  }
  .marketing-page .header-spacer {
    height: calc(46px + 44px) !important;
  }
  .login-page .tiktok-header {
    margin-top: 0 !important;
    margin-bottom: 10px !important;
  }
  .login-page .tiktok-shop-login {
    padding-top: calc(16px + 44px) !important;
  }
}

@media only screen and (device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3) {
  .funds-records-page, .financial-statements-page, .login-page {
    padding-top: 44px !important;
  }
  .funds-records-page .fx-header,
  .financial-statements-page .fx-header {
    padding-top: 44px !important;
  }
  .language-page .fx-header {
    top: 44px !important;
  }
  .language-page .header-spacer {
    height: calc(46px + 44px) !important;
  }
  .invitation-activity-page {
    padding-top: 44px !important;
  }
  .invitation-activity-page .fx-header {
    top: 44px !important;
    background: transparent !important;
    box-shadow: none !important;
    position: fixed !important;
  }
  .invitation-activity-page .header-spacer {
    display: none !important;
  }
  .invitation-activity-page .info-title {
    top: 16vw !important;
    position: absolute !important;
  }
  .invitation-activity-page .invitation-content {
    padding-top: 26vh !important;
    box-sizing: border-box !important;
  }
  .shop-promotion-page .fx-header {
    top: 44px !important;
  }
  .shop-promotion-page .header-spacer {
    height: calc(46px + 44px) !important;
  }
  .withdraw-page .fx-header {
    top: 44px !important;
  }
  .withdraw-page .header-spacer {
    height: calc(46px + 44px) !important;
  }
  .marketing-page .fx-header {
    top: 44px !important;
  }
  .marketing-page .header-spacer {
    height: calc(46px + 44px) !important;
  }
  .login-page .tiktok-header {
    margin-top: 0 !important;
    margin-bottom: 10px !important;
  }
  .login-page .tiktok-shop-login {
    padding-top: calc(16px + 44px) !important;
  }
}

/* 登录页面 iOS 适配 */
.ios-device .login-page {
  padding-top: var(--ios-safe-area-top, 44px) !important;
}

.ios-device .login-page .tiktok-shop-login {
  padding-top: calc(16px + var(--ios-safe-area-top, 44px)) !important;
  min-height: calc(100vh - var(--ios-safe-area-top, 44px));
}

.ios-device .login-page .tiktok-header {
  margin-top: 0 !important;
  margin-bottom: 15px !important;
  padding-top: 0;
}

/* 登录页面特殊元素适配 */
.ios-device .login-page .login-illustration {
  margin-top: 15px;
  margin-bottom: 15px;
}

.ios-device .login-page .login-illustration img {
  width: 180px;
}

.ios-device .login-page .login-title {
  margin-top: 0;
  margin-bottom: 12px;
}

.ios-device .login-page .type-tab {
  margin-bottom: 15px;
}

/* ================================ */
/* 店铺推广页面iOS适配 */
/* ================================ */

/* 店铺推广页面基础适配 */
.ios-device .shop-promotion-page {
  padding-top: 0 !important;
  min-height: 100vh;
  background-color: #fff;
}

/* 店铺推广页面fx-header适配 */
.ios-device .shop-promotion-page .fx-header {
  position: fixed !important;
  top: var(--ios-safe-area-inset-top, 44px) !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  z-index: 999 !important;
  background: transparent !important;
  box-shadow: none !important;
}

/* 店铺推广页面header-spacer适配 */
.ios-device .shop-promotion-page .header-spacer,
.ios-device .shop-promotion-page > div[style*="height: 46px"],
body.ios-device .shop-promotion-page .header-spacer,
body.ios-device .shop-promotion-page > div[style*="height: 46px"] {
  height: calc(46px + var(--ios-safe-area-inset-top, 44px)) !important;
  min-height: calc(46px + var(--ios-safe-area-inset-top, 44px)) !important;
}

/* 店铺推广页面内容区域适配 */
.ios-device .shop-promotion-page .promotion-info {
  margin-top: 10px;
}

/* 强制适配 - 确保所有iOS设备都有正确的顶部间距 */
body.ios-device .shop-promotion-page,
html.ios-device .shop-promotion-page {
  padding-top: 0 !important;
}

body.ios-device .shop-promotion-page .fx-header,
html.ios-device .shop-promotion-page .fx-header {
  position: fixed !important;
  top: var(--ios-safe-area-inset-top, 44px) !important;
  background: transparent !important;
  box-shadow: none !important;
}

/* 强制header-spacer适配 */
body.ios-device .shop-promotion-page .header-spacer,
html.ios-device .shop-promotion-page .header-spacer,
body.ios-device .shop-promotion-page > div[style*="height: 46px"],
html.ios-device .shop-promotion-page > div[style*="height: 46px"] {
  height: calc(46px + var(--ios-safe-area-inset-top, 44px)) !important;
  min-height: calc(46px + var(--ios-safe-area-inset-top, 44px)) !important;
}

/* 确保店铺推广页面头部文字和图标可见 */
.ios-device .shop-promotion-page .fx-header .fx-header__title,
.ios-device .shop-promotion-page .fx-header .fx-header__left,
.ios-device .shop-promotion-page .fx-header .fx-header__right {
  color: #333 !important;
}

.ios-device .shop-promotion-page .fx-header .van-icon {
  color: #333 !important;
}

/* ================================ */
/* 提现页面iOS适配 */
/* ================================ */

/* 提现页面基础适配 */
.ios-device .withdraw-page {
  padding-top: 0 !important;
  min-height: 100vh;
  background-color: #fff;
}

/* 提现页面fx-header适配 */
.ios-device .withdraw-page .fx-header {
  position: fixed !important;
  top: var(--ios-safe-area-inset-top, 44px) !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  z-index: 999 !important;
  background: transparent !important;
  box-shadow: none !important;
}

/* 提现页面header-spacer适配 */
.ios-device .withdraw-page .header-spacer,
.ios-device .withdraw-page > div[style*="height: 46px"],
body.ios-device .withdraw-page .header-spacer,
body.ios-device .withdraw-page > div[style*="height: 46px"] {
  height: calc(46px + var(--ios-safe-area-inset-top, 44px)) !important;
  min-height: calc(46px + var(--ios-safe-area-inset-top, 44px)) !important;
}

/* 提现页面内容区域适配 */
.ios-device .withdraw-page .withdraw {
  margin-top: 10px;
  padding: 0 15px;
}

/* 强制适配 - 确保所有iOS设备都有正确的顶部间距 */
body.ios-device .withdraw-page,
html.ios-device .withdraw-page {
  padding-top: 0 !important;
}

body.ios-device .withdraw-page .fx-header,
html.ios-device .withdraw-page .fx-header {
  position: fixed !important;
  top: var(--ios-safe-area-inset-top, 44px) !important;
  background: transparent !important;
  box-shadow: none !important;
}

/* 强制header-spacer适配 */
body.ios-device .withdraw-page .header-spacer,
html.ios-device .withdraw-page .header-spacer,
body.ios-device .withdraw-page > div[style*="height: 46px"],
html.ios-device .withdraw-page > div[style*="height: 46px"] {
  height: calc(46px + var(--ios-safe-area-inset-top, 44px)) !important;
  min-height: calc(46px + var(--ios-safe-area-inset-top, 44px)) !important;
}

/* 确保提现页面头部文字和图标可见 */
.ios-device .withdraw-page .fx-header .fx-header__title,
.ios-device .withdraw-page .fx-header .fx-header__left,
.ios-device .withdraw-page .fx-header .fx-header__right {
  color: #333 !important;
}

.ios-device .withdraw-page .fx-header .van-icon {
  color: #333 !important;
}

/* ================================ */
/* 店铺营销/直通车页面iOS适配 */
/* ================================ */

/* 店铺营销页面基础适配 */
.ios-device .marketing-page {
  padding-top: 0 !important;
  min-height: 100vh;
  background-color: #EFF2F6;
}

/* 店铺营销页面fx-header适配 */
.ios-device .marketing-page .fx-header {
  position: fixed !important;
  top: var(--ios-safe-area-inset-top, 44px) !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  z-index: 999 !important;
  background: transparent !important;
  box-shadow: none !important;
}

/* 店铺营销页面header-spacer适配 */
.ios-device .marketing-page .header-spacer,
.ios-device .marketing-page > div[style*="height: 46px"],
body.ios-device .marketing-page .header-spacer,
body.ios-device .marketing-page > div[style*="height: 46px"] {
  height: calc(46px + var(--ios-safe-area-inset-top, 44px)) !important;
  min-height: calc(46px + var(--ios-safe-area-inset-top, 44px)) !important;
}

/* 店铺营销页面内容区域适配 */
.ios-device .marketing-page .marketing-content {
  margin-top: 10px;
}

/* 强制适配 - 确保所有iOS设备都有正确的顶部间距 */
body.ios-device .marketing-page,
html.ios-device .marketing-page {
  padding-top: 0 !important;
}

body.ios-device .marketing-page .fx-header,
html.ios-device .marketing-page .fx-header {
  position: fixed !important;
  top: var(--ios-safe-area-inset-top, 44px) !important;
  background: transparent !important;
  box-shadow: none !important;
}

/* 强制header-spacer适配 */
body.ios-device .marketing-page .header-spacer,
html.ios-device .marketing-page .header-spacer,
body.ios-device .marketing-page > div[style*="height: 46px"],
html.ios-device .marketing-page > div[style*="height: 46px"] {
  height: calc(46px + var(--ios-safe-area-inset-top, 44px)) !important;
  min-height: calc(46px + var(--ios-safe-area-inset-top, 44px)) !important;
}

/* 确保店铺营销页面头部文字和图标可见 */
.ios-device .marketing-page .fx-header .fx-header__title,
.ios-device .marketing-page .fx-header .fx-header__left,
.ios-device .marketing-page .fx-header .fx-header__right {
  color: #333 !important;
}

.ios-device .marketing-page .fx-header .van-icon {
  color: #333 !important;
}

