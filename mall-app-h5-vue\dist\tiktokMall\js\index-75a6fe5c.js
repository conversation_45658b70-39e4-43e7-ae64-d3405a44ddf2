import{_ as t,i as e,u as s,c0 as i,T as o,I as a,bn as n,av as r,c as l,e as d,w as h,a as u,t as c,A as p,n as m,o as f,F as x,y as v,f as g,x as C,D as w,E as b}from"./index-3d21abf8.js";import{E as y}from"./index-5d897066.js";import{P as j}from"./index-fc51b7d2.js";import{L as S}from"./index-40e83579.js";/* empty css              *//* empty css               */import{S as k}from"./index-179203f3.js";import{T as I,a as L}from"./index-222e676a.js";/* empty css              */import{o as N,a as _}from"./orderItem-7f0b3534.js";import"./stringify-d53955b2.js";import"./use-id-a0619e01.js";import"./index-8c1841f6.js";import"./index-7dfcb82a.js";import"./use-route-cd41a893.js";import"./index-0d6a7179.js";import"./use-refs-b86d6bcd.js";import"./config-22c2c72a.js";import"./index-3ab60a77.js";import"./___vite-browser-external_commonjs-proxy-9405bfc0.js";const O=e(),R={name:"Order",beforeRouteEnter(t,e,s){s((t=>{const e=JSON.parse(window.sessionStorage.getItem("position"));document.querySelector(".main-list").scrollTop=e}))},beforeRouteLeave(t,e,s){const i=document.querySelector(".main-list").scrollTop;window.sessionStorage.setItem("position",JSON.stringify(i)),s()},components:{orderItem:N},data:()=>({loading:!1,refreshing:!1,finished:!1,showCenter:!1,showCenter2:!1,t:s().t,activeName:"",payStatus:"-1",showSeach:!1,list:[],option1:[],option2:[],title1:"",title2:"",orderId:"",purchStatus:"",status:"-2",pageNum:1,isArLang:O}),activated(){},created(){window.sessionStorage.setItem("position",JSON.stringify(0)),this.title1="支付状态",this.title2="物流状态",this.option1=[{text:"全部",value:-1,isCurrent:!0},{text:"未支付",value:0,isCurrent:!1},{text:"已支付",value:1,isCurrent:!1}],this.option2=[{text:"全部",value:-2,isCurrent:!0},{text:"订单已取消",value:-1,isCurrent:!1},{text:"等待买家付款",value:0,isCurrent:!1},{text:"买家已付款",value:1,isCurrent:!1},{text:"供应商已接单",value:2,isCurrent:!1},{text:"物流运输中",value:3,isCurrent:!1},{text:"买家已签收",value:4,isCurrent:!1},{text:"订单已完成",value:5,isCurrent:!1},{text:"已退款",value:6,isCurrent:!1}],["argos"].includes("tiktokMall")&&this.option2.forEach((t=>{4===t.value&&(t.text="订单已完成"),5===t.value&&(t.text="买家已签收")})),this.onRefresh(),document.addEventListener("reloadOrderList",(()=>{this.onRefresh()}),!1)},computed:{hasNoPushNum:()=>{const t=i();return Boolean(t.num)}},methods:{onLoad(){},onRefresh(){this.init(),this.getOrderList()},handleChoose(t){let e=this.payStatus/1,s=this.option1.findIndex((t=>t.value===e));this.option1.splice(s,1,{...this.option1[s],isCurrent:!1});let i=this.option1.findIndex((e=>e.value===t.value));this.option1.splice(i,1,{...this.option1[i],isCurrent:!0}),this.payStatus=t.value,this.title1=t.text,this.showCenter=!1,this.init(),this.getOrderList()},handleChoose2(t){let e=this.status/1,s=this.option2.findIndex((t=>t.value===e));this.option2.splice(s,1,{...this.option2[s],isCurrent:!1});let i=this.option2.findIndex((e=>e.value===t.value));this.option2.splice(i,1,{...this.option2[i],isCurrent:!0}),this.status=t.value,this.title2=t.text,this.showCenter2=!1,this.init(),this.getOrderList()},handleShow(t){1===t?this.showCenter=!0:this.showCenter2=!0},init(){this.pageNum=1,this.list=[],this.loading=!1,this.refreshing=!1,this.finished=!1},getOrderList(){const t={orderId:this.orderId,payStatus:this.payStatus,purchStatus:this.activeName,status:this.status,begin:"",end:"",pageNum:this.pageNum};Object.keys(t).forEach((e=>{t[e]||0===t[e]||delete t[e]})),t.payStatus/1==-1&&delete t.payStatus,t.status/1==-2&&delete t.status,o.loading({message:this.t("加载中"),forbidClick:!0,duration:0}),_(t).then((t=>{this.refreshing&&(this.refreshing=!1),this.pageNum++,this.loading=!1,0==t.pageList.length&&(this.finished=!0),this.list=this.list.concat(t.pageList)})).catch((t=>{}))},tab(t,e){this.activeName=t,this.onRefresh()},onSearch(){this.showSeach=!0,this.$router.push("/order_search")},change(t){this.value=t,this.showSeach=!1},onConfirm2(t){this.title2=this.option2[t].text,this.getOrderList()}}},U=t=>(w("data-v-69fb50eb"),t=t(),b(),t),$={class:"main"},E={class:"seach",style:{"border-radius":"25px"}},V={class:"dropdown"},T={class:"p-2.5 bg-white flex justify-between items-center w-full h-full"},A={class:"text-xs"},D=U((()=>u("div",{class:"triangle"},null,-1))),J={class:"p-2.5 bg-white flex justify-between items-center w-full h-full"},P={class:"text-xs"},q=U((()=>u("div",{class:"triangle"},null,-1))),M=["onClick"],B=["onClick"],F={class:"main-list"},z={class:"goods"},G=U((()=>u("div",{class:"footer-padding"},null,-1)));const H=t(R,[["render",function(t,e,s,i,o,w){const b=I,N=L,_=k,O=a,R=n,U=r("orderItem"),H=S,K=j,Q=y;return f(),l("div",{id:"order",class:m({"is-ar":o.isArLang})},[d(N,{active:o.activeName,"onUpdate:active":e[0]||(e[0]=t=>o.activeName=t),sticky:"",onClick:w.tab},{default:h((()=>[d(b,{title:o.t("total"),name:""},null,8,["title"]),d(b,{title:o.t("待采购"),name:"0",dot:w.hasNoPushNum},null,8,["title","dot"]),d(b,{title:o.t("已采购"),name:"1"},null,8,["title"])])),_:1},8,["active","onClick"]),u("div",$,[u("div",E,[d(_,{style:{"border-radius":"25px"},readonly:"",onClick:w.onSearch,modelValue:o.orderId,"onUpdate:modelValue":e[1]||(e[1]=t=>o.orderId=t),placeholder:o.t("搜索")},null,8,["onClick","modelValue","placeholder"])]),u("div",V,[u("div",{class:"dropdownitem one h-10.5",onClick:e[2]||(e[2]=t=>w.handleShow(1))},[u("div",T,[u("span",A,c(o.t(o.title1)),1),D])]),u("div",{class:"dropdownitem one h-10.5",onClick:e[3]||(e[3]=t=>w.handleShow(2))},[u("div",J,[u("span",P,c(o.t(o.title2)),1),q])])]),d(R,{show:o.showCenter,"onUpdate:show":e[4]||(e[4]=t=>o.showCenter=t),round:""},{default:h((()=>[(f(!0),l(x,null,v(o.option1,((t,e)=>(f(),l("div",{onClick:e=>w.handleChoose(t),class:"font-3.5 w-72 h-12 flex justify-center items-center border-bottom relative"},[g(c(o.t(t.text))+" ",1),t.isCurrent?(f(),p(O,{key:0,name:"success",class:"yes"})):C("v-if",!0)],8,M)))),256))])),_:1},8,["show"]),d(R,{show:o.showCenter2,"onUpdate:show":e[5]||(e[5]=t=>o.showCenter2=t),round:""},{default:h((()=>[(f(!0),l(x,null,v(o.option2,((t,e)=>(f(),l("div",{onClick:e=>w.handleChoose2(t),class:"text-xs w-72 h-12 flex justify-center items-center border-bottom relative",key:e+"option2"},[g(c(o.t(t.text))+" ",1),t.isCurrent?(f(),p(O,{key:0,name:"success",class:"yes"})):C("v-if",!0)],8,B)))),128))])),_:1},8,["show"]),u("div",F,[o.list.length>0?(f(),p(K,{key:0,modelValue:o.refreshing,"onUpdate:modelValue":e[7]||(e[7]=t=>o.refreshing=t),onRefresh:w.onRefresh,"loading-text":t.$t("加载中"),"loosing-text":t.$t("释放以刷新"),"pulling-text":t.$t("下拉以刷新")},{default:h((()=>[d(H,{"immediate-check":!1,loading:o.loading,"onUpdate:loading":e[6]||(e[6]=t=>o.loading=t),"loading-text":t.$t("加载中"),finished:o.finished,"finished-text":t.$t("noMore"),onLoad:w.getOrderList},{default:h((()=>[u("div",z,[(f(!0),l(x,null,v(o.list,(t=>(f(),p(U,{info:t},null,8,["info"])))),256))])])),_:1},8,["loading","loading-text","finished","finished-text","onLoad"])])),_:1},8,["modelValue","onRefresh","loading-text","loosing-text","pulling-text"])):(f(),p(Q,{key:1,description:t.$t("noData")},null,8,["description"])),G])])],2)}],["__scopeId","data-v-69fb50eb"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/order/index.vue"]]);export{H as default};
