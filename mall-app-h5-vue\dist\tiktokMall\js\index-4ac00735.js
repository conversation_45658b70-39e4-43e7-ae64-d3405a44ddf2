import{P as e,S as a,ai as r,d as t,aH as n,a7 as o,a9 as l,e as s,V as i,X as d}from"./index-3d21abf8.js";const[m,u]=e("form");const c=d(t({name:m,props:{colon:<PERSON><PERSON><PERSON>,disabled:<PERSON><PERSON><PERSON>,readonly:<PERSON><PERSON><PERSON>,showError:<PERSON><PERSON><PERSON>,labelWidth:a,labelAlign:String,inputAlign:String,scrollToError:<PERSON>olean,validateFirst:<PERSON><PERSON>an,submitOnEnter:r,showErrorMessage:r,errorMessageAlign:String,validateTrigger:{type:[String,Array],default:"onBlur"}},emits:["submit","failed"],setup(e,{emit:a,slots:r}){const{children:t,linkChildren:d}=n(o),m=e=>e?t.filter((a=>e.includes(a.name))):t,c=a=>{return"string"==typeof a?(e=>{const a=t.find((a=>a.name===e));return a?new Promise(((e,r)=>{a.validate().then((a=>{a?r(a):e()}))})):Promise.reject()})(a):e.validateFirst?(r=a,new Promise(((e,a)=>{const t=[];m(r).reduce(((e,a)=>e.then((()=>{if(!t.length)return a.validate().then((e=>{e&&t.push(e)}))}))),Promise.resolve()).then((()=>{t.length?a(t):e()}))}))):(e=>new Promise(((a,r)=>{const t=m(e);Promise.all(t.map((e=>e.validate()))).then((e=>{(e=e.filter(Boolean)).length?r(e):a()}))})))(a);var r},g=(e,a)=>{t.some((r=>r.name===e&&(r.$el.scrollIntoView(a),!0)))},h=()=>t.reduce(((e,a)=>(e[a.name]=a.formValue.value,e)),{}),f=()=>{const r=h();c().then((()=>a("submit",r))).catch((t=>{a("failed",{values:r,errors:t}),e.scrollToError&&t[0].name&&g(t[0].name)}))},p=e=>{i(e),f()};return d({props:e}),l({submit:f,validate:c,getValues:h,scrollToField:g,resetValidation:e=>{"string"==typeof e&&(e=[e]);m(e).forEach((e=>{e.resetValidation()}))},getValidationStatus:()=>t.reduce(((e,a)=>(e[a.name]=a.getValidationStatus(),e)),{})}),()=>{var e;return s("form",{class:u(),onSubmit:p},[null==(e=r.default)?void 0:e.call(r)])}}}));export{c as F};
