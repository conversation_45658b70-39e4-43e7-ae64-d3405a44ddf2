import{_ as s,i as e,u as i,Y as t,r as a,q as l,av as c,c as d,e as r,w as p,a as v,b as o,n,t as u,x as m,o as f,f as x,aF as P,D as y,E as g}from"./index-3d21abf8.js";const h={class:"details page-main-content has-fixed-header"},_=(s=>(y("data-v-42a057c9"),s=s(),g(),s))((()=>v("div",{class:"fixed-header-spacer"},null,-1))),N={class:"list ml-4 mr-4 mt-4"},$={class:"item pl-3 pr-3 pb-3 pt-3 flex"},b={class:"flex-1 flex left"},k={class:"product-img-wrap w-20 h-20"},w=["src"],D={class:"name"},S={class:"Specification"},q={key:0},U={key:0,class:"money"},j={key:0,class:"pl-4 pr-4 font-14"},C={class:"flex details-item pl-3 pr-3"},E={class:"title"},F={class:"text"},I={class:"flex details-item pl-3 pr-3"},J={class:"title"},O={class:"text"},T={class:"flex details-item pl-3 pr-3"},Y={class:"title"},z={class:"text"},A={class:"flex details-item pl-3 pr-3"},B={class:"title"},G={class:"text"},H={class:"flex details-item pl-3 pr-3"},K={class:"title"},L={class:"text"},M={class:"flex details-item pl-3 pr-3"},Q={class:"title"},R={class:"text"},V=s({__name:"details",setup(s){const y=e(),{t:g}=i(),V=t();let W=a({});return l((()=>{W.value=JSON.parse(V.query.item)})),(s,e)=>{const i=c("fx-header");return f(),d("div",h,[r(i,{fixed:""},{title:p((()=>[x(u(o(g)("product.11")),1)])),_:1}),_,v("div",N,[v("div",$,[v("div",b,[v("div",k,[v("img",{class:"product-img",src:o(W).imgUrl1},null,8,w)]),v("div",{class:n(["product-info",{"is-ar":o(y)}])},[v("div",D,u(o(W).name),1),v("div",S,[m(" <span>{{t('product.4')}}: {{ info.unit }}</span> "),o(W).categoryName?(f(),d("span",q,u(o(W).categoryName),1)):m("v-if",!0),v("span",null,u(o(g)("product.9"))+": "+u(o(W).soldNum),1)]),o(W).systemPrice||0===o(W).systemPrice?(f(),d("div",U,"$"+u(o(W).discountPrice?o(P)(o(W).discountPrice):o(P)(o(W).sellingPrice)),1)):m("v-if",!0)],2)]),m(' <div class="number">\r\n              x1\r\n            </div> ')])]),o(W).systemPrice||0===o(W).systemPrice?(f(),d("div",j,[v("div",C,[v("div",E,u(o(g)("product.9")),1),v("div",F,u(o(W).soldNum),1)]),v("div",I,[v("div",J,u(o(g)("product.12")),1),v("div",O,"$"+u(o(W).discountPrice?o(P)(o(W).discountPrice):o(P)(o(W).sellingPrice)),1)]),m(' <div class="flex details-item pl-3 pr-3">\r\n        <div class="title">Discount Price</div>\r\n        <div class="text">$18.90</div>\r\n      </div> '),v("div",T,[v("div",Y,u(o(g)("product.13")),1),v("div",z,"$"+u(o(P)(o(W).systemPrice)),1)]),v("div",A,[v("div",B,u(o(g)("product.14")),1),v("div",G,"$"+u(o(W).discountPrice?o(P)(o(W).discountPrice-o(W).systemPrice):o(P)(o(W).sellingPrice-o(W).systemPrice)),1)]),v("div",H,[v("div",K,u(o(g)("product.15")),1),v("div",L,u(o(W).isShelf/1==1?o(g)("product.17"):o(g)("product.18")),1)]),v("div",M,[v("div",Q,u(o(g)("product.16")),1),v("div",R,u(o(W).recTime/1==1?o(g)("product.17"):o(g)("product.18")),1)])])):m("v-if",!0)])}}},[["__scopeId","data-v-42a057c9"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/product/details.vue"]]);export{V as default};
