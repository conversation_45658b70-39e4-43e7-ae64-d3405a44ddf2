import{_ as s,Y as a,l as A,av as e,c as i,e as l,w as c,a as d,o as t,f as n,D as p,E as v}from"./index-3d21abf8.js";import{B as g}from"./index-2406f514.js";import"./use-route-cd41a893.js";const o="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADEAAAAxCAYAAABznEEcAAAACXBIWXMAACE4AAAhOAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAItSURBVHgB7dlfTuJAHMDx36+46MsmHKFH2CPYG3iDdV8wZl92g9mExcT6gCT+CfpgiL7gEbwBegO9QY/go0Lsz/mpA00d6IDT6WD6TYjojA8fOkNLAViSOp2eH4qHasyDJYgBVK0MqlVvoIKgfNI+7G2iV/kJhvq/Uw/AQBIgnvpvf6FoOIyDsLkdyTkr49no+YK0Dg71EcCh/35ExhBnl5MaIBtDXsecRMwGyCYQN4/EGiR266zeIE4imn+3I8TnQECi7Nno6JGA+SAroBXdIuAV5Biv7eTbJseQTrcXEIj9QVP2B1I0OU8cXYTo4Z5qHgFctRr1X5BT7ZPLvvixAc+joPXv9116XEB8IgVEAIYYB4UvJwaIV3JTPGpQ+TZoH57/SM9RLq13QCjGCkVIgPxdG5IA8FhhiDRApgNJArhCENMAsixIEsBZR2QBZLMg6awidAEyCQm76s8RMmuIeQGT6Cy9fNJZQSwKIKD9VmMrzJqXOyJvAJcrwgaAyw1hC8DlgrAJ4DSvYvU7OLnogkUAZ/5IEGaenD78yycAXOFXsZ8FcIUiTAC4whCmAJzxja3TvIAw7Ndg9bGmHHxae7COWOQIrH4fbYjP2X3lYHV0axwhzg/3NOWuUQx0vdvYOgXDGUc0d+p/wHJLcWs/qxLhSiXClUqEK5UIV9I6YyPQevv4sg8FRXxLf8bXX5qXHegjLnLjy07lnnClEuFKX+stNob4xouX0OTF0QsGLAlKRQF0CgAAAABJRU5ErkJggg==",r=s=>(p("data-v-7271df13"),s=s(),v(),s),f={class:"list pb-20"},I=r((()=>d("div",{class:"bank-after pl-4"},[d("div",{class:"bank-name"},"银行卡"),d("div",{class:"py-2"},"James"),d("div",{class:"bank-no"},"4367 4214 2048 9044 633")],-1))),m=[r((()=>d("img",{class:"edit-img",src:o},null,-1)))],x=r((()=>d("div",{class:"AI-after pl-4"},[d("div",{class:"bank-name"},"AI-Rafidain QiServices"),d("div",{class:"py-2"},"James"),d("div",{class:"bank-no"},"4367 4214 2048 9044 633")],-1))),C=[r((()=>d("img",{class:"edit-img",src:o},null,-1)))],u={class:"px-4 pt-6 fixed-wrap"},E=s({__name:"list",setup(s){a();const p=A(),v=()=>{p.push("selectPay")},o=()=>{p.push("add")};return(s,a)=>{const A=e("fx-header"),p=g;return t(),i("div",f,[l(A,{fixed:""},{title:c((()=>[n("收款方式")])),_:1}),d("ul",{class:""},[d("li",{class:"flex px-4 justify-between pt-5 pb-10"},[I,d("div",{onClick:o},m)]),d("li",{class:"flex px-4 justify-between pt-5 pb-10"},[x,d("div",{onClick:o},C)])]),d("div",u,[l(p,{class:"w-full",type:"primary",onClick:v},{default:c((()=>[n("添加收款方式")])),_:1})])])}}},[["__scopeId","data-v-7271df13"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/payMentMethod/list.vue"]]);export{E as default};
