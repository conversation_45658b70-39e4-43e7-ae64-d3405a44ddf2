System.register(["./index-legacy-46a00900.js","./index-legacy-72e00c5f.js","./index-legacy-bfbc73de.js","./exchange.api-legacy-9c1a3b47.js","./index-legacy-b248d96d.js","./index-legacy-6156faa3.js","./index-legacy-20dd4294.js","./index-legacy-f9c0699e3.js","./use-route-legacy-be86ac1c.js","./index-legacy-15165887.js","./index-legacy-bbd15202.js"],(function(e,t){"use strict";var i,n,a,o,l,d,c,s,r,p,u,f,v,y,g,k,x,b,m,h,w,j,_,C,D,T,N,U,q,B=document.createElement("style");return B.textContent='.details-content[data-v-74d715a3]{padding:15px}.details-content.is-ar .content>.item>div[data-v-74d715a3]{padding-left:0;padding-right:20px}.details-content.is-ar .content>.item>div.copy>span[data-v-74d715a3]{text-align:left;padding-right:0;padding-left:10px}.details-content.is-ar .content>.item>div .unit[data-v-74d715a3]{padding-left:0;padding-right:5px}.details-content>.content[data-v-74d715a3]{background-color:#fff;border-radius:4px;overflow:hidden}.details-content>.content>.item[data-v-74d715a3]{padding:15px;position:relative;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between;min-height:50px;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.details-content>.content>.item>p[data-v-74d715a3]{color:#999;font-size:14px}.details-content>.content>.item>div[data-v-74d715a3]{color:#333;-webkit-box-flex:1;-webkit-flex:1 1;flex:1 1;padding-left:20px;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:end;-webkit-justify-content:flex-end;justify-content:flex-end;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.details-content>.content>.item>div.copy>span[data-v-74d715a3]{-webkit-box-flex:1;-webkit-flex:1 1;flex:1 1;text-align:right;padding-right:10px}.details-content>.content>.item>div.copy svg[data-v-74d715a3]{color:#999}.details-content>.content>.item>div>span[data-v-74d715a3]{font-size:14px;word-break:break-all}.details-content>.content>.item>div>span.unit[data-v-74d715a3]{padding-left:5px}.details-content>.content>.item>div>span.img[data-v-74d715a3]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;width:70px;height:70px;border-radius:4px;overflow:hidden;background-color:#ddd}.details-content>.content>.item>div>span.img>img[data-v-74d715a3]{width:100%;height:auto}.details-content>.content>.item>div>span.color-1[data-v-74d715a3]{color:#0ecb81}.details-content>.content>.item>div>span.color-2[data-v-74d715a3]{color:#ff3e3e}.details-content>.content>.item>div>span.color[data-v-74d715a3],.details-content>.content>.item>div>span.color-0[data-v-74d715a3]{color:#1552f0}.details-content>.content>.item[data-v-74d715a3]:last-child:after{display:none}.details-content>.content>.item[data-v-74d715a3]:after{content:"";display:block;width:calc(100% - 30px);height:1px;background-color:#eee;position:absolute;left:15px;bottom:0}.btn-content[data-v-74d715a3]{padding:15px}.btn-content[data-v-74d715a3] .van-button{width:100%;background-color:var(--site-main-color);border-color:var(--site-main-color)}\n',document.head.appendChild(B),{setters:[e=>{i=e._,n=e.d,a=e.i,o=e.r,l=e.Y,d=e.u,c=e.T,s=e.av,r=e.c,p=e.e,u=e.w,f=e.a,v=e.F,y=e.y,g=e.x,k=e.n,x=e.b,b=e.o,m=e.f,h=e.t,w=e.aF,j=e.ak,_=e.D,C=e.E},e=>{D=e.B},e=>{T=e.u},e=>{N=e.r},e=>{U=e.c},e=>{q=e.I},()=>{},()=>{},()=>{},()=>{},()=>{}],execute:function(){const t=e=>(_("data-v-74d715a3"),e=e(),C(),e),B=t((()=>f("div",{style:{height:"46px"}},null,-1))),E={key:0,class:"content"},I=["onClick"],S=["src"],z=["onClick"],F=[t((()=>f("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"},null,-1))),t((()=>f("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"},null,-1)))],H={key:0,class:"btn-content"},R=n({name:"RechageRecordDetails"});e("default",i(Object.assign(R,{setup(e){const t=a(),i=o(!0),n=[{title:"订单号",info:"",copy:!0,key:"order_no"},{title:"创建时间",info:"",key:"create_time"},{title:"充值数量",info:"",color:!0,key:"volume",isNum:!0,unit:"USDT"},{title:"实际到账",info:"",color:!0,key:"amount",isNum:!0,unit:"USDT"},{title:"订单状态",info:"",color:!0,key:"state"},{title:"币种协议",info:"",key:"coin_blockchain"},{title:"收款地址",info:"",copy:!0,key:"channel_address"},{title:"支付凭证",info:"",key:"img"}],_=o([]),C=l(),R=o(""),{t:M}=d(),{toClipboard:O}=T(),P=o(!1),V=o(null),Y=C.query.order_no,$=C.query.isThirdParty||0;if(P.value=Boolean(Number($)),P.value){const e=U(n);for(let t=0;t<e.length;t++)["coin_blockchain","channel_address","img"].includes(e[t].key)&&e.splice(t--,1);_.value=e}else _.value=n;if(C.query.r){R.value=C.query.r;const e=_.value.findIndex((e=>"state"===e.key));_.value.splice(e+1,0,{title:"失败原因",info:"",redColor:!0,key:"failure_msg"})}return Y?(i.value=!0,c.loading({duration:0,forbidClick:!0}),N({order_no:Y}).then((e=>{V.value=e,_.value.find((e=>"volume"===e.key)).unit=e.coin,_.value.forEach((t=>{t.info="failure_msg"===t.key?R.value:e[t.key]}))})).finally((()=>{c.clear(),i.value=!1}))):c(M("参数错误")),(e,n)=>{const a=s("fx-header"),o=D;return b(),r("div",null,[p(a,{fixed:""},{title:u((()=>[m(h(x(M)("充值详情")),1)])),_:1}),B,f("div",{class:k(["details-content",{"is-ar":x(t)}])},[i.value?g("v-if",!0):(b(),r("div",E,[(b(!0),r(v,null,y(_.value,(e=>{return b(),r("div",{key:e.key,class:"item"},[f("p",null,h(x(M)(e.title))+"：",1),f("div",{class:k({copy:e.copy})},["state"===e.key?(b(),r("span",{key:0,class:k(`color-${e.info}`)},h((t=e.info,{0:M("processing"),1:M("successful"),2:M("failure")}[t]??"")),3)):"img"===e.key?(b(),r("span",{key:1,class:"img",onClick:t=>{return i=e.info,void q({images:[i],showIndex:!1,loop:!1});var i}},[f("img",{src:e.info,alt:""},null,8,S)],8,I)):(b(),r("span",{key:2,class:k({color:e.color,"color-2":e.redColor})},h(e.isNum?x(w)(e.info,["BTC","ETH"].includes(e.unit)?6:2):e.info),3)),e.unit?(b(),r("span",{key:3,class:k([{color:e.color},"unit"])},h(e.unit),3)):g("v-if",!0),e.copy&&e.info?(b(),r("svg",{key:4,onClick:t=>(async e=>{try{await O(e),c(M("copySuccess"))}catch(t){}})(e.info),xmlns:"http://www.w3.org/2000/svg",width:"18",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"feather feather-copy"},F,8,z)):g("v-if",!0)],2)]);var t})),128))]))],2),P.value&&!i.value&&Number(0===V.value.state)&&V.value.payUrl?(b(),r("div",H,[p(o,{type:"primary",onClick:n[0]||(n[0]=e=>x(j)(V.value.payUrl,!0))},{default:u((()=>[m(h(x(M)("去支付")),1)])),_:1})])):g("v-if",!0)])}}}),[["__scopeId","data-v-74d715a3"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/recharge/recordDetails.vue"]]))}}}));
