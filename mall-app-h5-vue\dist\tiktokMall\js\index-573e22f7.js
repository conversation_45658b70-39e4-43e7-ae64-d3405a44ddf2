import{ad as e,a1 as t,P as n,d as a,bc as l,a5 as o,bB as s,bl as i,r as u,p as r,aC as c,a6 as d,a9 as m,g as f,ac as v,e as p,S as h,V as g,aQ as x,a4 as b,ai as y,Q as I,R as C,m as w,aH as H,ba as V,W as N,b0 as O,bC as T,X as S}from"./index-3d21abf8.js";function k(n){if(!e(n))return n;if(Array.isArray(n))return n.map((e=>k(e)));if(t(n)){const e={};return Object.keys(n).forEach((t=>{e[t]=k(n[t])})),e}return n}const[B,D]=n("picker-column");const K=Symbol(B),M=e=>t(e)&&e.disabled;var P=a({name:B,props:{textKey:l(String),readonly:<PERSON>olean,allowHtml:Boolean,className:o,itemHeight:l(Number),defaultIndex:s(0),swipeDuration:l(h),initialOptions:i(),visibleItemCount:l(h)},emits:["change"],setup(e,{emit:n,slots:a}){let l,o,s,i,h;const b=u(),y=u(),I=r({index:e.defaultIndex,offset:0,duration:0,options:k(e.initialOptions)}),C=c(),w=()=>I.options.length,H=()=>e.itemHeight*(+e.visibleItemCount-1)/2,V=(t,a)=>{const o=-(t=(e=>{for(let t=e=x(e,0,w());t<w();t++)if(!M(I.options[t]))return t;for(let t=e-1;t>=0;t--)if(!M(I.options[t]))return t})(t)||0)*e.itemHeight,s=()=>{t!==I.index&&(I.index=t,a&&n("change",t))};l&&o!==I.offset?h=s:s(),I.offset=o},N=t=>{JSON.stringify(t)!==JSON.stringify(I.options)&&(I.options=k(t),V(e.defaultIndex))},O=n=>t(n)&&e.textKey in n?n[e.textKey]:n,T=t=>x(Math.round(-t/e.itemHeight),0,w()-1),S=()=>{l=!1,I.duration=0,h&&(h(),h=null)},B=t=>{if(!e.readonly){if(C.start(t),l){const e=function(e){const{transform:t}=window.getComputedStyle(e),n=t.slice(7,t.length-1).split(", ")[5];return Number(n)}(y.value);I.offset=Math.min(0,e-H()),o=I.offset}else o=I.offset;I.duration=0,s=Date.now(),i=o,h=null}},P=()=>{if(e.readonly)return;const t=I.offset-i,n=Date.now()-s;if(n<300&&Math.abs(t)>15)return void((t,n)=>{const a=Math.abs(t/n);t=I.offset+a/.003*(t<0?-1:1);const l=T(t);I.duration=+e.swipeDuration,V(l,!0)})(t,n);const a=T(I.offset);I.duration=200,V(a,!0),setTimeout((()=>{l=!1}),0)},$=()=>{const t={height:`${e.itemHeight}px`};return I.options.map(((n,o)=>{const s=O(n),i=M(n),u={role:"button",style:t,tabindex:i?-1:0,class:D("item",{disabled:i,selected:o===I.index}),onClick:()=>(t=>{l||e.readonly||(h=null,I.duration=200,V(t,!0))})(o)},r={class:"van-ellipsis",[e.allowHtml?"innerHTML":"textContent"]:s};return p("li",u,[a.option?a.option(n):p("div",r,null)])}))};return V(I.index),d(K),m({state:I,setIndex:V,getValue:()=>I.options[I.index],setValue:e=>{const{options:t}=I;for(let n=0;n<t.length;n++)if(O(t[n])===e)return V(n)},setOptions:N,hasOptions:()=>I.options.length,stopMomentum:S}),f((()=>e.initialOptions),N),f((()=>e.defaultIndex),(e=>V(e))),v("touchmove",(t=>{if(e.readonly)return;C.move(t),C.isVertical()&&(l=!0,g(t,!0)),I.offset=x(o+C.deltaY.value,-w()*e.itemHeight,e.itemHeight);const n=Date.now();n-s>300&&(s=n,i=I.offset)}),{target:b}),()=>p("div",{ref:b,class:[D(),e.className],onTouchstartPassive:B,onTouchend:P,onTouchcancel:P},[p("ul",{ref:y,style:{transform:`translate3d(0, ${I.offset+H()}px, 0)`,transitionDuration:`${I.duration}ms`,transitionProperty:I.duration?"all":"none"},class:D("wrapper"),onTransitionend:S},[$()])])}});const[$,j,E]=n("picker"),A={title:String,loading:Boolean,readonly:Boolean,allowHtml:Boolean,itemHeight:b(44),showToolbar:y,swipeDuration:b(1e3),visibleItemCount:b(6),cancelButtonText:String,confirmButtonText:String};const F=S(a({name:$,props:I({},A,{columns:i(),valueKey:String,defaultIndex:b(0),toolbarPosition:C("top"),columnsFieldNames:Object}),emits:["confirm","cancel","change"],setup(e,{emit:t,slots:n}){n.default,e.valueKey;const a=u(!1),l=u(),o=u([]),s=w((()=>{const{columnsFieldNames:t}=e;return{text:(null==t?void 0:t.text)||e.valueKey||"text",values:(null==t?void 0:t.values)||"values",children:(null==t?void 0:t.children)||"children"}})),{children:i,linkChildren:r}=H(K);r();const c=w((()=>V(e.itemHeight))),d=w((()=>{const t=e.columns[0];if("object"==typeof t){if(s.value.children in t)return"cascade";if(s.value.values in t)return"object"}return"plain"})),h=()=>i.map((e=>e.state.index)),x=(e,t)=>{const n=i[e];n&&(n.setOptions(t),a.value=!0)},b=t=>{let n={[s.value.children]:e.columns};const a=h();for(let e=0;e<=t;e++)n=n[s.value.children][a[e]];for(;n&&n[s.value.children];)t++,x(t,n[s.value.children]),n=n[s.value.children][n.defaultIndex||0]},y=e=>i[e],I=e=>{const t=y(e);if(t)return t.getValue()},C=(e,t)=>{const n=y(e);n&&(n.setValue(t),"cascade"===d.value&&b(e))},S=e=>{const t=y(e);if(t)return t.state.index},k=(e,t)=>{const n=y(e);n&&(n.setIndex(t),"cascade"===d.value&&b(e))},B=()=>i.map((e=>e.getValue())),D=e=>{"plain"===d.value?t(e,I(0),S(0)):t(e,B(),h())},M=()=>{i.forEach((e=>e.stopMomentum())),D("confirm")},$=()=>D("cancel"),A=()=>{const t=e.cancelButtonText||E("cancel");return p("button",{type:"button",class:[j("cancel"),O],onClick:$},[n.cancel?n.cancel():t])},F=()=>{const t=e.confirmButtonText||E("confirm");return p("button",{type:"button",class:[j("confirm"),O],onClick:M},[n.confirm?n.confirm():t])},J=()=>{if(e.showToolbar){const t=n.toolbar||n.default;return p("div",{class:j("toolbar")},[t?t():[A(),n.title?n.title():e.title?p("div",{class:[j("title"),"van-ellipsis"]},[e.title]):void 0,F()]])}},Q=()=>o.value.map(((a,l)=>{var o;return p(P,{textKey:s.value.text,readonly:e.readonly,allowHtml:e.allowHtml,className:a.className,itemHeight:c.value,defaultIndex:null!=(o=a.defaultIndex)?o:+e.defaultIndex,swipeDuration:e.swipeDuration,initialOptions:a[s.value.values],visibleItemCount:e.visibleItemCount,onChange:()=>(e=>{"cascade"===d.value&&b(e),"plain"===d.value?t("change",I(0),S(0)):t("change",B(),e)})(l)},{option:n.option})})),z=e=>{if(a.value){const t={height:`${c.value}px`},n={backgroundSize:`100% ${(e-c.value)/2}px`};return[p("div",{class:j("mask"),style:n},null),p("div",{class:[T,j("frame")],style:t},null)]}},L=()=>{const t=c.value*+e.visibleItemCount,n={height:`${t}px`};return p("div",{ref:l,class:j("columns"),style:n},[Q(),z(t)])};return f((()=>e.columns),(()=>{const{columns:t}=e;"plain"===d.value?o.value=[{[s.value.values]:t}]:"cascade"===d.value?(()=>{var t;const n=[];let a={[s.value.children]:e.columns};for(;a&&a[s.value.children];){const l=a[s.value.children];let o=null!=(t=a.defaultIndex)?t:+e.defaultIndex;for(;l[o]&&l[o].disabled;){if(!(o<l.length-1)){o=0;break}o++}n.push({[s.value.values]:a[s.value.children],className:a.className,defaultIndex:o}),a=l[o]}o.value=n})():o.value=t,a.value=o.value.some((e=>e[s.value.values]&&0!==e[s.value.values].length))||i.some((e=>e.hasOptions))}),{immediate:!0}),v("touchmove",g,{target:l}),m({confirm:M,getValues:B,setValues:e=>{e.forEach(((e,t)=>{C(t,e)}))},getIndexes:h,setIndexes:e=>{e.forEach(((e,t)=>{k(t,e)}))},getColumnIndex:S,setColumnIndex:k,getColumnValue:I,setColumnValue:C,getColumnValues:e=>{const t=y(e);if(t)return t.state.options},setColumnValues:x}),()=>{var t,a;return p("div",{class:j()},["top"===e.toolbarPosition?J():null,e.loading?p(N,{class:j("loading")},null):null,null==(t=n["columns-top"])?void 0:t.call(n),L(),null==(a=n["columns-bottom"])?void 0:a.call(n),"bottom"===e.toolbarPosition?J():null])}}}));export{F as P,A as p};
