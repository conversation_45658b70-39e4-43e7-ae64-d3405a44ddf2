import{_ as e,i as a,u as l,l as s,r as t,q as c,J as i,B as o,an as n,m as u,av as d,I as v,bn as r,c as m,e as p,w as f,x as h,a as g,t as b,n as k,b as x,ak as A,T as y,o as j,f as w,F as I,y as Z,A as D,aF as R,D as E,E as S}from"./index-3d21abf8.js";import{P as T}from"./index-fc51b7d2.js";import{L as B}from"./index-40e83579.js";import{c as G,g as Q}from"./product.api-7fdfc848.js";import{e as J}from"./editProfit-723f706e.js";import"./index-cfaf3bc2.js";import"./stringify-d53955b2.js";import"./___vite-browser-external_commonjs-proxy-9405bfc0.js";import"./index-2406f514.js";import"./use-route-cd41a893.js";import"./index-573e22f7.js";import"./index-6aaac5d3.js";/* empty css              *//* empty css               */import"./index-d27cfcd1.js";import"./index-4ac00735.js";import"./function-call-78245787.js";import"./use-placeholder-c97cb410.js";import"./index-8c1841f6.js";import"./index-7dfcb82a.js";import"./use-id-a0619e01.js";const N=e=>(E("data-v-4cde41b8"),e=e(),S(),e),U={class:"product page-main-content has-fixed-header"},C=["src"],M=N((()=>g("div",{class:"fixed-header-spacer"},null,-1))),W={class:"dropdown"},X={class:"p-2.5 bg-white flex justify-between items-center w-full h-full"},z={class:"text-xs"},F=N((()=>g("div",{class:"triangle"},null,-1))),L={class:"p-2.5 bg-white flex justify-between items-center w-full h-full"},Y={class:"text-xs"},V=N((()=>g("div",{class:"triangle"},null,-1))),H={class:"classify-pop-content"},O={class:"title"},P={class:"content"},_=["onClick"],q={class:"classify-pop-content"},K={class:"title"},$={class:"content"},ee=["onClick"],ae={class:"pl-3 pr-3"},le=["onClick"],se=[N((()=>g("i",{class:"iconfont icon-duigoux"},null,-1)))],te={class:"flex-1 flex left"},ce={class:"product-img-wrap"},ie=["src"],oe={class:"name"},ne={class:"Specification"},ue={class:"money"},de=[N((()=>g("i",{class:"iconfont icon-duigoux"},null,-1)))],ve=e({__name:"list",setup(e){const E=a(),{t:S}=l(),N=s(),ve=t(1),re=t([]),me=t(!1),pe=t(!1),fe=t(!1),he=t(!1),ge=t(null);let be=0,ke=0,xe=!1;const Ae=t(!1),ye=t(!1),je=t([]),we=t(!1),Ie=t(0),Ze=t([]),De=t(S("一级分类")),Re=t(S("二级分类")),Ee=t(!1),Se=t(!1),Te=t(""),Be=t("");let Ge=null;c((()=>{sessionStorage.setItem("productReload",!0),Je(),Ye(),Ge=Qe()})),i((()=>{Ge&&Ge()}));const Qe=()=>{let e=null;const a=()=>{e&&clearTimeout(e),e=setTimeout((()=>{const e=document.documentElement.scrollTop||document.body.scrollTop,a=e<=3;he.value=!a,e>3&&(xe=!1)}),16)};window.addEventListener("scroll",a,{passive:!0});return()=>{window.removeEventListener("scroll",a),e&&clearTimeout(e)}},Je=async()=>{let e={};await o().then((a=>{e=a||{}})),await n().then((a=>{const l=Number(a.status);let s=l;2===l?s=3:3===l&&(s=2),e.authStatus=s}));const{avatar:a,name:l,authStatus:s}=e;a&&l?0!==s&&1!==s&&2!==s||(Ie.value=2):Ie.value=1,we.value=!0},Ne=()=>{const e=1===Ie.value?"/shop/settings":"/name";A(e)},Ue=()=>{ye.value=!1},Ce=()=>{je.value=[],re.value.map((e=>{e.check&&je.value.push(e.id)})),je.value.length?ye.value=!0:y(S("请选择商品"))},Me=e=>{const a=document.documentElement.scrollTop||document.body.scrollTop;be=e.touches[0].clientY,ke=Date.now(),xe=a<=5},We=e=>{if(!xe)return;const a=e.touches[0].clientY-be;if((document.documentElement.scrollTop||document.body.scrollTop)>5)return xe=!1,void(he.value=!0);he.value=a<30},Xe=e=>{Date.now();setTimeout((()=>{xe=!1}),100)},ze=()=>{(document.documentElement.scrollTop||document.body.scrollTop)>5||!xe?pe.value=!1:(ve.value=1,fe.value=!1,Le(!0))},Fe=()=>{pe.value?me.value=!1:Le(!1)},Le=(e=!1)=>{const a={pageNum:ve.value,pageSize:20};Be.value?a.secondaryCategoryId=Be.value:Te.value&&(a.categoryId=Te.value),Q(a).then((a=>{const l=a.pageList||[];l.forEach((e=>{e.check=!1})),e?re.value=l:re.value.push(...l),ve.value++,0===l.length&&(fe.value=!0),me.value=!1,pe.value=!1,y.clear()})).catch((e=>{me.value=!1,pe.value=!1,y.clear()}))},Ye=()=>{G().then((e=>{const a=e||[];a.unshift({name:S("全部分类"),categoryId:"",subList:[]}),Ze.value=a.filter((e=>e.name))}))},Ve=u((()=>{const e=Ze.value.find((e=>e.categoryId===Te.value));let a=[{name:S("全部分类"),categoryId:""}];if(e&&e.subList&&e.subList.length){const l=e.subList.filter((e=>e.name));a=[...a,...l]}return a})),He=(e,a)=>{a?(De.value=e.name,Te.value=e.categoryId,Ee.value=!1,Re.value=S("二级分类"),Be.value=""):(Re.value=e.name,Be.value=e.categoryId,Se.value=!1),y.loading({duration:0,forbidClick:!0}),Ae.value=!1,ve.value=1,re.value=[],fe.value=!1,Le(!0)},Oe=()=>{Ae.value=!Ae.value,re.value.forEach((e=>{e.check=Ae.value}))},Pe=()=>{N.push("/search?id=2")},_e=()=>{Ye(),ze()};return(e,a)=>{const l=d("fx-header"),s=v,t=r,c=B,i=T;return j(),m("div",U,[p(l,{fixed:""},{title:f((()=>[w(b(x(S)("product.2")),1)])),right:f((()=>[g("img",{onClick:Pe,class:"nav_filtering_icon",src:x("data:image/png;base64,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")},null,8,C)])),_:1}),M,h(" 分类选择器 "),g("div",W,[g("div",{class:"dropdownitem one h-10.5",onClick:a[0]||(a[0]=e=>Ee.value=!0)},[g("div",X,[g("span",z,b(De.value),1),F])]),g("div",{class:"dropdownitem one h-10.5",onClick:a[1]||(a[1]=e=>Se.value=!0)},[g("div",L,[g("span",Y,b(Re.value),1),V])])]),h(" 一级分类弹窗 "),p(t,{show:Ee.value,"onUpdate:show":a[2]||(a[2]=e=>Ee.value=e),round:""},{default:f((()=>[g("div",H,[g("div",O,b(x(S)("一级分类")),1),g("div",P,[(j(!0),m(I,null,Z(Ze.value,(e=>(j(),m("div",{key:e.categoryId,class:"classify-item",onClick:a=>He(e,!0)},[w(b(e.name)+" ",1),Te.value===e.categoryId?(j(),D(s,{key:0,name:"success",class:"yes"})):h("v-if",!0)],8,_)))),128))])])])),_:1},8,["show"]),h(" 二级分类弹窗 "),p(t,{show:Se.value,"onUpdate:show":a[3]||(a[3]=e=>Se.value=e),round:""},{default:f((()=>[g("div",q,[g("div",K,b(x(S)("二级分类")),1),g("div",$,[(j(!0),m(I,null,Z(x(Ve),(e=>(j(),m("div",{key:e.categoryId,class:"classify-item",onClick:a=>He(e,!1)},[w(b(e.name)+" ",1),Be.value===e.categoryId?(j(),D(s,{key:0,name:"success",class:"yes"})):h("v-if",!0)],8,ee)))),128))])])])),_:1},8,["show"]),h(" 商品列表 "),g("div",{class:k(["list mt-2 mb-4",x(E)?"ml-4":"mr-4"])},[p(i,{ref_key:"pullRefreshRef",ref:ge,"loading-text":e.$t("刷新中"),"loosing-text":e.$t("释放以刷新"),"pulling-text":e.$t("下拉以刷新"),"head-height":50,"pull-distance":150,"success-text":e.$t("刷新成功"),"success-duration":1e3,disabled:he.value,modelValue:pe.value,"onUpdate:modelValue":a[5]||(a[5]=e=>pe.value=e),onRefresh:ze,onTouchstart:Me,onTouchmove:We,onTouchend:Xe},{default:f((()=>[p(c,{loading:me.value,"onUpdate:loading":a[4]||(a[4]=e=>me.value=e),"loading-text":e.$t("加载中"),finished:fe.value,"finished-text":x(S)("product.3"),onLoad:Fe},{default:f((()=>[(j(!0),m(I,null,Z(re.value,((e,a)=>(j(),m("div",{class:"item pb-3 pt-3 flex",key:a},[g("div",ae,[g("div",{class:k(["check-icon",[e.check?"check-true ":"check"]]),onClick:a=>e.check=!e.check},se,10,le)]),g("div",te,[g("div",ce,[g("img",{class:"w-20 h-20",style:{"object-fit":"contain"},src:e.imgUrl1},null,8,ie)]),g("div",{class:k(["product-info",{"is-ar":x(E)}])},[g("div",oe,b(e.name),1),g("div",ne,[g("span",null,b(e.categoryName),1)]),g("div",ue,"$"+b(x(R)(e.systemPrice)),1)],2)])])))),128))])),_:1},8,["loading","loading-text","finished","finished-text"])])),_:1},8,["loading-text","loosing-text","pulling-text","success-text","disabled","modelValue"]),h(" 底部操作栏 "),we.value?(j(),m("div",{key:0,class:k(["flex fixed-wrap",{"pl-3":!x(E)&&!Ie.value,"pr-3":x(E)&&!Ie.value}])},[Ie.value?h("v-if",!0):(j(),m("div",{key:0,class:"flex-1 flex",onClick:Oe},[g("div",{class:k(["check-icon",[Ae.value?"check-true ":"check"]])},de,2),g("div",{class:k(x(E)?"pr-2":"pl-2")},b(x(S)("product.5"))+": "+b(re.value.filter((e=>e.check)).length),3)])),Ie.value?(j(),m("div",{key:2,class:"submit-but disabled",onClick:Ne},b(1===Ie.value?x(S)("product.34"):x(S)("product.35")),1)):(j(),m("div",{key:1,class:"submit-but",onClick:Ce},b(x(S)("product.6")),1))],2)):h("v-if",!0)],2),h(" 编辑利润弹窗 "),p(J,{isEdit:ye.value,onUpdate:_e,productArry:je.value,onClose:Ue},null,8,["isEdit","productArry"])])}}},[["__scopeId","data-v-4cde41b8"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/product/list.vue"]]);export{ve as default};
