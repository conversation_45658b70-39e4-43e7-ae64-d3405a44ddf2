import{d as a,u as s,aj as t,i as o,m as e,aF as r,_ as i,ak as l,o as d,c as n,a as m,t as g,x as c,n as u}from"./index-3d21abf8.js";const p=a({name:"GoodsItem",props:{goodsData:{type:Object,default:()=>{}}},setup(a){const{t:i}=s(),{goodsData:d}=t(a),n=o();return{t:i,getdDtails:()=>{l({path:"/productPage/details",query:{item:JSON.stringify(d.value)}})},showBrowse:e((()=>!["tiktok-wholesale"].includes("tiktokMall"))),numberStrFormat:r,isArLang:n}}}),v={class:"poster"},D=["src"],f={class:"name"},b={key:0},k={class:"price"};const w=i(p,[["render",function(a,s,t,o,e,r){return d(),n("div",{class:"goods-item",onClick:s[0]||(s[0]=(...s)=>a.getdDtails&&a.getdDtails(...s))},[m("div",v,[m("img",{src:a.goodsData.imgUrl1||a.goodsData.imgUrl2||a.goodsData.imgUrl3||a.goodsData.imgUrl4,alt:""},null,8,D)]),m("div",{class:u(["info-content",{"is-ar":a.isArLang}])},[m("p",f,g(a.goodsData.name),1),m("div",null,[a.showBrowse?(d(),n("div",b,g(a.t("browse"))+": "+g(a.numberStrFormat(a.goodsData.viewsNum,0)),1)):c("v-if",!0),m("div",null,g(a.t("sales"))+": "+g(a.numberStrFormat(a.goodsData.soldNum,0)),1)]),m("p",k,"$"+g(a.numberStrFormat(a.goodsData.sellingPrice)),1)],2)])}],["__scopeId","data-v-a0b32eee"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/shop/components/GoodsItem.vue"]]);export{w as G};
