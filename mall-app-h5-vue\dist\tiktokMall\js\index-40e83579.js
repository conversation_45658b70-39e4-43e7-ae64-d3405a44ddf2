import{aW as e,P as a,a4 as i,R as o,ai as r,d as s,r as t,aB as n,g as l,aX as d,q as c,a9 as f,ac as u,e as g,s as h,aT as p,aO as v,W as m,X as x}from"./index-3d21abf8.js";const T=Symbol(),[b,k,B]=a("list");const S=x(s({name:b,props:{error:Boolean,offset:i(300),loading:<PERSON>olean,finished:<PERSON><PERSON>an,errorText:String,direction:o("down"),loadingText:String,finishedText:String,immediateCheck:r},emits:["load","update:error","update:loading"],setup(a,{emit:i,slots:o}){const r=t(!1),s=t(),x=t(),b=e(T,null),S=n(s),C=()=>{h((()=>{if(r.value||a.finished||a.error||!1===(null==b?void 0:b.value))return;const{offset:e,direction:o}=a,t=p(S);if(!t.height||v(s))return;let n=!1;const l=p(x);n="up"===o?t.top-l.top<=e:l.bottom-t.bottom<=e,n&&(r.value=!0,i("update:loading",!0),i("load"))}))},w=()=>{if(a.finished){const e=o.finished?o.finished():a.finishedText;if(e)return g("div",{class:k("finished-text")},[e])}},y=()=>{i("update:error",!1),C()},W=()=>{if(a.error){const e=o.error?o.error():a.errorText;if(e)return g("div",{role:"button",class:k("error-text"),tabindex:0,onClick:y},[e])}},X=()=>{if(r.value&&!a.finished)return g("div",{class:k("loading")},[o.loading?o.loading():g(m,{class:k("loading-icon")},{default:()=>[a.loadingText||B("loading")]})])};return l((()=>[a.loading,a.finished,a.error]),C),b&&l(b,(e=>{e&&C()})),d((()=>{r.value=a.loading})),c((()=>{a.immediateCheck&&C()})),f({check:C}),u("scroll",C,{target:S,passive:!0}),()=>{var e;const i=null==(e=o.default)?void 0:e.call(o),t=g("div",{ref:x,class:k("placeholder")},null);return g("div",{ref:s,role:"feed",class:k(),"aria-busy":r.value},["down"===a.direction?i:t,X(),w(),W(),"up"===a.direction?i:t])}}}));export{S as L,T};
