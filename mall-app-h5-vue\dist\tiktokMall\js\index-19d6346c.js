import{_ as e,u as a,aj as l,r as t,m as s,o as i,c as d,a as u,t as n,b as o,h as r,v as A,F as c,y as m,n as v,s as g,T as f,cz as p,e as x,l as h,Y as y,i as w,j as b,q as B,an as C,cg as U,av as I,w as V,x as E,A as F,cA as O,f as R,D,E as K}from"./index-3d21abf8.js";import{B as j}from"./index-2406f514.js";import"./index-7d1632e5.js";/* empty css              *//* empty css               */import{U as k}from"./index-1f1846c6.js";import{N as z}from"./index-2ef790cf.js";import{u as G}from"./upload.api-28f256be.js";import{c as T}from"./countryList-016fc82e.js";import{E as N}from"./index-9c8e9dca.js";import"./use-route-cd41a893.js";import"./index-0d6a7179.js";import"./index-a439655d.js";/* empty css              *//* empty css               */import"./index-8c1841f6.js";import"./index-7dfcb82a.js";import"./use-id-a0619e01.js";const S={class:"code-content"},Q={class:"title"},H={class:"search-content"},P={class:"content"},J=["placeholder"],M={class:"scroll-content"},L={class:"countries-content"},W=["onClick"],Y={class:"name"},X=e({__name:"CountryDialog",props:{modelValue:{type:Boolean,default:!1},countries:{type:Array,default:()=>[]}},emits:["update:modelValue","done"],setup(e,{emit:g}){const f=e,{t:p}=a(),{countries:x}=l(f),h=t(""),y=s((()=>{const e=x.value.filter((e=>{const a=h.value.toLowerCase();return e.countryName.toLowerCase().indexOf(a)>-1}));return h.value?e:x.value})),w=()=>{g("update:modelValue",!1)};return(a,l)=>(i(),d("div",{class:v([{active:e.modelValue},"area-code-dialog"])},[u("div",S,[u("div",Q,n(o(p)("selectNation")),1),u("div",H,[u("div",P,[r(u("input",{"onUpdate:modelValue":l[0]||(l[0]=e=>h.value=e),type:"text",placeholder:o(p)("entrynational")},null,8,J),[[A,h.value]])])]),u("div",M,[u("div",L,[(i(!0),d(c,null,m(o(y),(e=>(i(),d("div",{key:e.code,class:"item",onClick:a=>(g("done",e),void w())},[u("div",Y,n(e.countryName),1)],8,W)))),128))])])]),u("div",{class:"code-bg",onClick:w})],2))}},[["__scopeId","data-v-e93d9969"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/components/country-select/components/CountryDialog.vue"]]),q=e({__name:"index",props:{modelValue:{type:String,default:""},disabled:{type:Boolean,default:!1}},emits:["update:modelValue","done"],setup(e,{emit:r}){const A=e,{t:c}=a(),{modelValue:m}=l(A),h=t([]),y=t(!1),w=t(!1),b=s((()=>{if(y.value){const e=h.value.find((e=>e.id===m.value));if(e)return e.countryName;for(const a in T)if(Number(T[a].dialCode)===Number(m.value))return T[a].name;return""}return""})),B=e=>{r("update:modelValue",e.id),r("done",e)};return g((async()=>{f.loading({duration:0,message:c("loading"),forbidClick:!0}),await p().then((e=>{h.value=e.data||[],y.value=!0,f.clear()})).catch((()=>{f.clear()}))})),(a,l)=>(i(),d("div",null,[x(X,{modelValue:w.value,"onUpdate:modelValue":l[0]||(l[0]=e=>w.value=e),countries:h.value,onDone:B},null,8,["modelValue","countries"]),u("div",{class:v([{empty:!o(b),disabled:e.disabled},"input-item"]),onClick:l[1]||(l[1]=e=>w.value=!0)},n(o(b)||o(c)("selectNation")),3)]))}},[["__scopeId","data-v-20a90537"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/components/country-select/index.vue"]]),Z=e=>(D("data-v-964f3e05"),e=e(),K(),e),_=Z((()=>u("div",{style:{height:"46px"}},null,-1))),$={key:0,class:"identity pl-15 pr-15 font-12 bg-white",style:{"padding-bottom":"30px"}},ee={key:0,class:"flex justify-between items-center text-sm pt-6",style:{color:"#000"}},ae={class:"flex items-center"},le=["src"],te=["src"],se=["src"],ie={key:1,style:{"margin-top":"10px"}},de={style:{"padding-top":"20px"}},ue={class:"mb-5"},ne={key:0},oe={class:"flex mt-4 mb-6 justify-between"},re={class:"flex-1 flex flex-col text-center justify-center items-center"},Ae={class:"upload-wrap"},ce={class:"mt-3 font-13 h-5 textColor"},me={class:"flex-1 flex flex-col text-center justify-center items-center"},ve={class:"upload-wrap"},ge={class:"mt-3 font-13 h-5 textColor"},fe={key:0,class:"flex-1 flex flex-col text-center justify-center items-center"},pe={class:"upload-wrap"},xe={class:"mt-3 font-13 h-5 textColor"},he={class:"mb-4 textColor"},ye={class:"flex items-center justify-between"},we=Z((()=>u("div",{class:"flex-1 flex justify-center"},[u("img",{src:"data:image/png;base64,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",alt:"",class:"mb-4",style:{width:"6.2175rem",height:"4rem"}}),E(' <img src="../../assets/imgs/me/kyc-true.png" style="width: 1.1875rem; height: 1.1875rem; margin: 0 auto" alt=""> ')],-1))),be=Z((()=>u("div",{class:"flex-1 flex justify-center"},[u("img",{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGcAAABECAYAAACceSthAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAASDSURBVHgB7Z27cttGFIYPFgBBSvLIoj3yTNIoKVJm0iUzKSLnFfICcZUyTfrYj+BHyHOkcJp0mUneQCqUIhmTokyTAElccv4FV5FlXixehEPyfDMQVrtnDxb7E8DuAlh4NOby8vIhr34siuJ7Xp+Qct+cc93/Zox5cXR0dI4ID39YmBNOeFWv1z+Ooug1GxxztE/KfZEzCZMOBoO253lPIZAVp91un7EwJ41Gg5RqGQ6Haa/Xu2g2m594fNScslKvDg8PSZFBt9slPoKeGj6cvgjDkBQ5BEFAfGk5NRx+yEcOKfIIbkfkeUHDtKDA92g0yqlR9ykZ5pRxXKNhaDgqqGAbxPeTjMNEUWT4MMxtes7/I2+alT5gY4xnw0gz/HNAWr1mpvrt9TNrF4b/+014vcdpzi9s/MCzflw59vf8d7aHvLAB8A1/5T6S3Tdnj7xIS7Pc5q1xnjgp8xqvrAeEXVnjOLf2KCO2AVAHnNXWHcq0Ct7zwvvAO55Syhu5vBrZOPzf6gz5YlXYsIvv8BrxsHXprrBuDRvnL05SG3/1ZrZfxCEMW4RRmW/fpmO/2bUN8twsx+3tORvnG/6QH+Vw9vCLMOIQRtlQB4hD2NneLKuzd9vAAr+u7lbFaiRW1oKKIxgVRzAqjmBUHMG815RGcxBN1oCbjg8OymQ0b9E0RRzCtXGT9GA/IB6Ts/GHDwK7RlMUhDdswqDMi+E6pKMJO8svfMEOPsowmsnB2K9/bXO7HLe352yQgrUrk+8XnFbuL/xGUU5RDWWBbWHrAHl9lM031hZ5XVmRZu3HYVcuV3erwmu1Ws95TO1nHVeTQxzHWF7oaU0wKo5gVBzBqDiCUXEEo+IIRsURjIojmGCuRfwPFa//IHtH6s7e98g7/pLXB6TcndnipD1Kf/+B7yBd0MI8+ZqCr15OTEoGCwi+ZRi/vPM6iZniFMM3LMzftBTds6lJ/7YGtOtEfEv7yeNoYtqca04xXpZh2fy7izYIBKPiCEbFEcz8pvQa+ei4Tsp0KhUnCPRJ01noaU0wlR45V90R7Tp4TuFgb7IMM8XxTMh/eCmWqMRgf2rSVXd1j65uKuiELiQORY/IfP4TFRe/0kKdyaBB5rNnU5NX9cD3JhOG06+7s8XxDJmT74iwrIHjKcMWSon+dAWj4ghGxRGMiiOYSvs508Crhat8Q0wyC/dzqgKv8+1KH2jxfk5F4K2CXekDLd7PqQi8rqF9IG0QiEbFEYzI09o80GDYFoxPdiKKSWykOO3O0M7+sQ0s8WiUUiUbeeQ8flSjPKOtZyPFwTnabGTJ74ae1gQz9/eHqbYwQ5KyHpYaW8NcY3qvf30sNbbm7dA4VxUsNbaG6UJWOWWI8uHoISEYFUcwKo5gVBzBqDiCgTgdN5GcIgtMl/9Xlu3AKOIGkaYppsv/030FBJ9nOdXZCqtnMBhQv98/azabn9prjud5z5IkOe/1eviOS4ej9FC6X2y9x3GcQxjW41tEXo8d4ANHbPCcE76h7fjy1KZdSHHt/4VPZy/dl6cUwfwHj2Agsa83+xcAAAAASUVORK5CYII=",alt:"",class:"mb-4",style:{width:"6.2175rem",height:"4rem"}}),E(' <img src="../../assets/imgs/me/kyc-false.png" style="width: 1.1875rem; height: 1.1875rem; margin: 0 auto" alt=""> ')],-1))),Be={key:0,class:"flex-1 flex justify-center"},Ce=Z((()=>u("img",{src:"/www/png/name-16274f89.png",alt:"",class:"mb-4",style:{width:"6.2175rem",height:"4rem"}},null,-1))),Ue=e({__name:"index",setup(e){const{t:l}=a(),s=h();y();const r=w(),A=t(""),m=t(""),g=t(""),p=t(!0),D=t([]),K=t([]),T=t([]);t("frontFile"),t(-1),t([]);const S=new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAI2SURBVHgBxVe9btRAEP5mdWdAgDiJgtYooqBBlyfIXYEEFXFPcSVdeIPgN0g6ynuDQIGCRBHT0CGuo0Nb0KQ7ISSUPeFhdn02l4udsxP/fNLKZ+/czjffjL2zhJLgo9EA3s1d+bUjYyiPfBmD5fRchgZoBuYIC/OegmheZl3aZMDHz3z8jffEcrLisMzSUxgKKfioL7Uqmkgi7r8Rkz1cB4QDnJmwSJFcAi5qjk/ATuY6oGHUOE8Ntf6APzwd1uzcwocXn7i113BOgQYiX8cFJTICLuc3vG8NOl8hYbbTmvifAltwVZ0/DIBbD1ARvgS6n944Ak76qtV+/wnw+BXw6CUqg/Gaj6zPVIGY99E2+rELWLncgydoG8QT61tJ7nfRCWiAvvdCUqB20BlopJYbS0f+eWiL0EdnYN8SqLDD1Q0a9HBd3N3a/C34+Qn4c5o7ZQnYT2J1FRa/k+u9rWRcht4d4PvbnAmeWwJaRvVC/PUD+BrK4rc3255+KZgg3XNt1FXfhMKFy4JnUoTxZ3QFpkjBLN4hqYP2Ic2rcvsyYYrWwVPrO9kNyRyibZhFaC+OAD2PtOte2wLjQKLXGQEHaZ2FhEbz0JL7ML3JCCS1YMYNk7D94Hj1jHCuLXepYA4aIqFF+yCVPvOZZ8nHI2nPvZoPJi5yvT6h8qydEmdmu5bClIJbtuE619fG/1s14r40rTRBeczF8VSK7bDIcWkCGZHksGr7R2nhKP94zjyDokjUK308/wdHf9fo2a4eoQAAAABJRU5ErkJggg==",self.location),Q=new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAKASURBVHgBzVe7bhNBFD0zaxwDQl5LFEARNkIiFcguEUgmEl2QID+AUlAT0UFl/AfhDwI9BQV1qGkSyaJIxSoVNGA3YDs7M9y72GbteB/eh5MjbbEzs3POzp25c65AQtgHLRsneKohmhCiTk0ON4+6u/S4xphDSPHZsvTHbqPdTTKviBtAxI72xA4N3Q4QxoLE7J0o0+7fa7tII4D/WCv5BgY7yAANs1sqmXbYiogQcofI94ncQR4wcIdKb8xbDTnbcPlLq54rOUPAKVtyn+c+3RVA7n8+izkrIQLkHPODwsgDIuQF3RjviUkIRhvOQdGgcHieaP1/xfioyW9YIoaeXuNQ+CugldXCklEuCf94Cz/2nvyF5aMrS3pNKkqvOBvYSsknUkiriQLw+kYTnbsvULVWwgdp81BS0q4jZzD5K3qqskICKqHjhECdN6GDHDEm73l9bB69w/GwFzFaOCwg8oZ7cMXB6kqyS3CWvPPnR9wntozqZeJP68/8J05ECnIfLCDUOBwPuuj8/o7Vsh0pIi05c7MAN2rE5tH7SBEZyAmGMqEgGxWBnuqHishGzq4Jh9bF582aESIyGQ2Mhw8/v+JR9RZuV67icW0dN0nMy+v3U5P7kGJ3nIr5Iord6nymeQXuXLrmv2ciZ/6Srsl/97LZS/JBMBxZydm0Mnfq65hTbE8NkBZT1zEpcdm9LjJBFnLmGtuySSJi60z2xEXRIEvmc40wEcDxkJbeKFTEyJQGa4SpVMyhUFpvFSKCyBX01mxtcP4KEwavBIWjsejGnAeeg214WI2YrDhl02rMNpLDzy1Dz7xNXZzOEWKzf/QtnNF1NhOYKs+Ny7l90fL8L2vvgzOHEc83AAAAAElFTkSuQmCC",self.location),H=new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAJBSURBVHgBzVcxTisxEH12WgpKOvIlDrB0v0D8/I4OuAE3oKaCFPSJ4AC0SBRwAQiIHg6AxJZUsAUFooiZZ++KJdm1nSWLeNJqDRnPe57xeMcKkTC93qK8tmDMP3kSGXflWcx/zuRJ5f/3UGoErS/UaJTF+FUhAyHuYjzeleFOiTBCsTlBp9MXISmaCLArHo8PZLiL72EgEenXRUTVkHPVV3BhngdSEfG/Khq6gjyZMzmsL/FpfU9ATZB3WyAvYyoSqkTOnN+1SF4WsVrsic8UuA3XRftglPeLP2wE8tA/4ieh9R+mwkXAmP3ghJUVYGEBUUiSsI07W6Dy3L94jdfWgMND4OFBTgWZ9/pab7u3B2xsAEdHwNmZz2vGKDACWwiBxE9PLgrDYX0kCnLa3t6GvHLhm9qe7SHQIVfuE1EmL2xDMKan8w9LGD4RTcgJpRJl1teZ//iPzNKSI+ebqeHThNwhowCDWVEWQTQjt9BoAhJx5QVYFb7KCAiIahy+gDlnaVJITHXUI6OAdJYZUxsuVB1+yEmo1H20edVujynROkgLRwHXUca+UmsqQvpHpuAcoX3AfIdKbVIE7UOQ5rXTT9O3g+Vl1tPfWsPnZ+D9HTg+9pcaK4FHMN+Xl/7KkKZV3dyc/o7Pcd4iDfBzGBRtmS4p6mPWkmyGNOdytMXA9mjSMKJdEUVTmk0JyEXQYBvtiLC+J+8Gv+9iYlW5SKxiPhtzkLfhaSVXaLaNBptWY3YQD+b4RIiHjS+nFUJauZ5/AIbLSDTY4R25AAAAAElFTkSuQmCC",self.location),P=b(),J=t(!1);B((()=>{J.value=["inchoi","hive","antMall"].includes("tiktokMall"),L()}));const M=t({}),L=()=>{f.loading({duration:0,message:l("loading"),forbidClick:!0}),p.value=!0,C().then((e=>{const a=Number(e.status);e.statusTxt=(e=>{switch(e){case 1:return"审核中";case 2:return"已认证";case 3:return"审核失败"}})(a),e.disabled=[1,2].includes(a),e.showStatus=[1,2,3].includes(a),e.showImg=2!==a,0!==e.status&&(A.value=e.nationality||"",m.value=e.idnumber&&[1,2].includes(a)?U(e.idnumber,!1):e.idnumber||"",g.value=e.name&&[1,2].includes(a)?U(e.name,!1):e.name||"",D.value=e.idimg_1?[{url:e.idimg_1}]:[],K.value=e.idimg_2?[{url:e.idimg_2}]:[],T.value=e.idimg_3?[{url:e.idimg_3}]:[]),M.value=e,p.value=!1})).catch((()=>{p.value=!1}))},W=(e,a,t)=>{e.status="uploading",e.message=l("uploading"),G({file:e.file,moduleName:t}).then((t=>{e.status="success",e.message=l("uploadSuccess"),e.resURL=t,a.value=[e]})).catch((()=>{e.message=l("上传失败"),e.status="failed"}))},Y=t(!1),X=()=>{if(!A.value)return void f(l("selectNation"));if(!g.value)return void f(l("entryName"));if(!/^[\u4e00-\u9fa5a-zA-Z-，,\s]+$/.test(g.value))return void f(l("真实姓名格式有误"));if(!m.value)return void f(l("entryCredent"));if(!D.value.length)return void f(`${l("请上传")}${l("credentFront")}`);if(!K.value.length)return void f(`${l("请上传")}${l("credentObverse")}`);if(!T.value.length&&!J.value)return void f(`${l("请上传")}${l("handCredent")}`);const e={name:g.value,idnumber:m.value,frontFile:D.value,reverseFile:K.value,fileList:T.value,nationality:A.value};Y.value=!0,O(e,J.value).then((async()=>{await P.getUserInfo(!0),f(l("submitSuccess")),Y.value=!1,document.dispatchEvent(new CustomEvent("headerRefresh")),setTimeout((()=>{s.back()}),300)})).catch((e=>{Y.value=!1,message=e.message?e.message:e||l("上传失败"),f(message)}))};return(e,a)=>{const t=I("fx-header"),s=z,f=k,h=j;return i(),d("div",null,[x(t,{fixed:!0},{title:V((()=>[R(n(o(l)("realNameVertify")),1)])),_:1}),_,E(" 申请身份认证 "),p.value?E("v-if",!0):(i(),d("div",$,[M.value.showStatus?(i(),d("div",ee,[u("div",null,n(o(l)("authVerify")),1),u("div",ae,[1===M.value.status?(i(),d("img",{key:0,class:v(["w-4 h-4",o(r)?"ml-0.5":"mr-0.5"]),src:o(S),alt:""},null,10,le)):E("v-if",!0),2===M.value.status?(i(),d("img",{key:1,class:v(["w-4 h-4",o(r)?"ml-0.5":"mr-0.5"]),src:o(Q),alt:""},null,10,te)):E("v-if",!0),3===M.value.status?(i(),d("img",{key:2,class:v(["w-4 h-4",o(r)?"ml-0.5":"mr-0.5"]),src:o(H),alt:""},null,10,se)):E("v-if",!0),u("div",null,n(o(l)(M.value.statusTxt)),1)])])):E("v-if",!0),3===M.value.status&&M.value.msg?(i(),d("div",ie,[x(s,{wrapable:"",scrollable:!1,text:M.value.msg},null,8,["text"])])):E("v-if",!0),u("div",de,[u("div",ue,[u("div",{class:v(["mt-27 mb-13 font-12 textColor form-item-title",{"is-ar":o(r)}])},n(o(l)("nationality")),3),x(q,{modelValue:A.value,"onUpdate:modelValue":a[0]||(a[0]=e=>A.value=e),disabled:M.value.disabled},null,8,["modelValue","disabled"])]),x(N,{label:o(l)("realName"),placeholderText:o(l)("entryRealName"),required:!0,modelValue:g.value,"onUpdate:modelValue":a[1]||(a[1]=e=>g.value=e),disabled:M.value.disabled,clearBtn:!M.value.disabled},null,8,["label","placeholderText","modelValue","disabled","clearBtn"]),x(N,{label:o(l)("credentPassport"),placeholderText:o(l)("entryCredentPassport"),maxLength:40,required:!0,modelValue:m.value,"onUpdate:modelValue":a[2]||(a[2]=e=>m.value=e),disabled:M.value.disabled,clearBtn:!M.value.disabled},null,8,["label","placeholderText","modelValue","disabled","clearBtn"]),M.value.showImg?(i(),d("div",ne,[E(' <div v-if="resultArr.length > 0" class="mb-13 textColor">{{ t(\'uploadCredentPassport\') }}</div>\r\n          <div v-else class="mt-55 mb-13">{{ t(\'uploadPicCredentPassport\') }}</div> '),u("div",oe,[u("div",re,[u("div",Ae,[x(f,{modelValue:D.value,"onUpdate:modelValue":a[3]||(a[3]=e=>D.value=e),multiple:"","max-count":1,disabled:M.value.disabled,deletable:!M.value.disabled,"after-read":e=>W(e,D.value,"idimg_1")},null,8,["modelValue","disabled","deletable","after-read"])]),u("div",ce,n(o(l)("credentFront")),1)]),u("div",me,[u("div",ve,[x(f,{modelValue:K.value,"onUpdate:modelValue":a[4]||(a[4]=e=>K.value=e),multiple:"","max-count":1,disabled:M.value.disabled,deletable:!M.value.disabled,"after-read":e=>W(e,K.value,"idimg_2")},null,8,["modelValue","disabled","deletable","after-read"])]),u("div",ge,n(o(l)("credentObverse")),1)]),J.value?E("v-if",!0):(i(),d("div",fe,[u("div",pe,[x(f,{modelValue:T.value,"onUpdate:modelValue":a[5]||(a[5]=e=>T.value=e),multiple:"","max-count":1,disabled:M.value.disabled,deletable:!M.value.disabled,"after-read":e=>W(e,T.value,"idimg_3")},null,8,["modelValue","disabled","deletable","after-read"])]),u("div",xe,n(o(l)("handCredent")),1)]))])])):E("v-if",!0),!M.value.disabled&&M.value.showImg?(i(),d(c,{key:1},[u("div",he,n(o(l)("photoExample")),1),u("div",ye,[we,be,J.value?E("v-if",!0):(i(),d("div",Be,[Ce,E(' <img src="../../assets/imgs/me/kyc-false.png" style="width: 1.1875rem; height: 1.1875rem; margin: 0 auto" alt=""> ')]))])],64)):E("v-if",!0),M.value.disabled?E("v-if",!0):(i(),F(h,{key:2,loading:Y.value,class:"w-full",style:{"margin-top":"30px"},type:"primary",onClick:X},{default:V((()=>[R(n(o(l)("certification")),1)])),_:1},8,["loading"]))])]))])}}},[["__scopeId","data-v-964f3e05"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/name/index.vue"]]);export{Ue as default};
