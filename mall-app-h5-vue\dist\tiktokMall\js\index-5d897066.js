import{P as l,R as i,d as t,e as r,aE as e,X as a}from"./index-3d21abf8.js";import{u as n}from"./use-id-a0619e01.js";const[d,s]=l("empty");const h=a(t({name:d,props:{image:i("default"),imageSize:[Number,String,Array],description:String},setup(l,{slots:i}){const t=()=>{const t=i.description?i.description():l.description;if(t)return r("p",{class:s("description")},[t])},a=()=>{if(i.default)return r("div",{class:s("bottom")},[i.default()])},d=n(),h=l=>`${d}-${l}`,o=l=>`url(#${h(l)})`,c=(l,i,t)=>r("stop",{"stop-color":l,offset:`${i}%`,"stop-opacity":t},null),x=(l,i)=>[c(l,0),c(i,100)],F=l=>[r("defs",null,[r("radialGradient",{id:h(l),cx:"50%",cy:"54%",fx:"50%",fy:"54%",r:"297%",gradientTransform:"matrix(-.16 0 0 -.33 .58 .72)"},[c("#EBEDF0",0),c("#F2F3F5",100,.3)])]),r("ellipse",{fill:o(l),opacity:".8",cx:"80",cy:"140",rx:"46",ry:"8"},null)],f=()=>[r("defs",null,[r("linearGradient",{id:h("a"),x1:"64%",y1:"100%",x2:"64%"},[c("#FFF",0,.5),c("#F2F3F5",100)])]),r("g",{opacity:".8"},[r("path",{d:"M36 131V53H16v20H2v58h34z",fill:o("a")},null),r("path",{d:"M123 15h22v14h9v77h-31V15z",fill:o("a")},null)])],u=()=>[r("defs",null,[r("linearGradient",{id:h("b"),x1:"64%",y1:"97%",x2:"64%",y2:"0%"},[c("#F2F3F5",0,.3),c("#F2F3F5",100)])]),r("g",{opacity:".8"},[r("path",{d:"M87 6c3 0 7 3 8 6a8 8 0 1 1-1 16H80a7 7 0 0 1-8-6c0-4 3-7 6-7 0-5 4-9 9-9Z",fill:o("b")},null),r("path",{d:"M19 23c2 0 3 1 4 3 2 0 4 2 4 4a4 4 0 0 1-4 3v1h-7v-1l-1 1c-2 0-3-2-3-4 0-1 1-3 3-3 0-2 2-4 4-4Z",fill:o("b")},null)])],y=()=>r("svg",{viewBox:"0 0 160 160"},[r("defs",null,[r("linearGradient",{id:h(1),x1:"64%",y1:"100%",x2:"64%"},[c("#FFF",0,.5),c("#F2F3F5",100)]),r("linearGradient",{id:h(2),x1:"50%",x2:"50%",y2:"84%"},[c("#EBEDF0",0),c("#DCDEE0",100,0)]),r("linearGradient",{id:h(3),x1:"100%",x2:"100%",y2:"100%"},[x("#EAEDF0","#DCDEE0")]),r("radialGradient",{id:h(4),cx:"50%",cy:"0%",fx:"50%",fy:"0%",r:"100%",gradientTransform:"matrix(0 1 -.54 0 .5 -.5)"},[c("#EBEDF0",0),c("#FFF",100,0)])]),r("g",{fill:"none"},[f(),r("path",{fill:o(4),d:"M0 139h160v21H0z"},null),r("path",{d:"M80 54a7 7 0 0 1 3 13v27l-2 2h-2a2 2 0 0 1-2-2V67a7 7 0 0 1 3-13z",fill:o(2)},null),r("g",{opacity:".6","stroke-linecap":"round","stroke-width":"7"},[r("path",{d:"M64 47a19 19 0 0 0-5 13c0 5 2 10 5 13",stroke:o(3)},null),r("path",{d:"M53 36a34 34 0 0 0 0 48",stroke:o(3)},null),r("path",{d:"M95 73a19 19 0 0 0 6-13c0-5-2-9-6-13",stroke:o(3)},null),r("path",{d:"M106 84a34 34 0 0 0 0-48",stroke:o(3)},null)]),r("g",{transform:"translate(31 105)"},[r("rect",{fill:"#EBEDF0",width:"98",height:"34",rx:"2"},null),r("rect",{fill:"#FFF",x:"9",y:"8",width:"80",height:"18",rx:"1.1"},null),r("rect",{fill:"#EBEDF0",x:"15",y:"12",width:"18",height:"6",rx:"1.1"},null)])])]),E=()=>r("svg",{viewBox:"0 0 160 160"},[r("defs",null,[r("linearGradient",{x1:"50%",x2:"50%",y2:"100%",id:h(5)},[x("#F2F3F5","#DCDEE0")]),r("linearGradient",{x1:"95%",y1:"48%",x2:"5.5%",y2:"51%",id:h(6)},[x("#EAEDF1","#DCDEE0")]),r("linearGradient",{y1:"45%",x2:"100%",y2:"54%",id:h(7)},[x("#EAEDF1","#DCDEE0")])]),f(),u(),r("g",{transform:"translate(36 50)",fill:"none"},[r("g",{transform:"translate(8)"},[r("rect",{fill:"#EBEDF0",opacity:".6",x:"38",y:"13",width:"36",height:"53",rx:"2"},null),r("rect",{fill:o(5),width:"64",height:"66",rx:"2"},null),r("rect",{fill:"#FFF",x:"6",y:"6",width:"52",height:"55",rx:"1"},null),r("g",{transform:"translate(15 17)",fill:o(6)},[r("rect",{width:"34",height:"6",rx:"1"},null),r("path",{d:"M0 14h34v6H0z"},null),r("rect",{y:"28",width:"34",height:"6",rx:"1"},null)])]),r("rect",{fill:o(7),y:"61",width:"88",height:"28",rx:"1"},null),r("rect",{fill:"#F7F8FA",x:"29",y:"72",width:"30",height:"6",rx:"1"},null)])]),p=()=>r("svg",{viewBox:"0 0 160 160"},[r("defs",null,[r("linearGradient",{x1:"50%",x2:"50%",y2:"100%",id:h(8)},[x("#EAEDF1","#DCDEE0")])]),f(),u(),F("c"),r("path",{d:"m59 60 21 21 21-21h3l9 9v3L92 93l21 21v3l-9 9h-3l-21-21-21 21h-3l-9-9v-3l21-21-21-21v-3l9-9h3Z",fill:o(8)},null)]),g=()=>r("svg",{viewBox:"0 0 160 160"},[r("defs",null,[r("linearGradient",{x1:"50%",y1:"100%",x2:"50%",id:h(9)},[x("#EEE","#D8D8D8")]),r("linearGradient",{x1:"100%",y1:"50%",y2:"50%",id:h(10)},[x("#F2F3F5","#DCDEE0")]),r("linearGradient",{x1:"50%",x2:"50%",y2:"100%",id:h(11)},[x("#F2F3F5","#DCDEE0")]),r("linearGradient",{x1:"50%",x2:"50%",y2:"100%",id:h(12)},[x("#FFF","#F7F8FA")])]),f(),u(),F("d"),r("g",{transform:"rotate(-45 113 -4)",fill:"none"},[r("rect",{fill:o(9),x:"24",y:"52.8",width:"5.8",height:"19",rx:"1"},null),r("rect",{fill:o(10),x:"22.1",y:"67.3",width:"9.9",height:"28",rx:"1"},null),r("circle",{stroke:o(11),"stroke-width":"8",cx:"27",cy:"27",r:"27"},null),r("circle",{fill:o(12),cx:"27",cy:"27",r:"16"},null),r("path",{d:"M37 7c-8 0-15 5-16 12",stroke:o(11),"stroke-width":"3",opacity:".5","stroke-linecap":"round",transform:"rotate(45 29 13)"},null)])]),D=()=>{var t;if(i.image)return i.image();const e={error:p,search:g,network:y,default:E};return(null==(t=e[l.image])?void 0:t.call(e))||r("img",{src:l.image},null)};return()=>r("div",{class:s()},[r("div",{class:s("image"),style:e(l.imageSize)},[D()]),t(),a()])}}));export{h as E};
