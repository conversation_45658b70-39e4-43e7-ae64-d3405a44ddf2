import{_ as e,d as a,i as t,u as i,r as s,b4 as l,aF as n,av as o,c as d,e as r,w as u,T as f,ak as g,o as p,f as c,t as m,F as v,y as h,n as x,a as C,L as y,x as D,A as w,D as R,E as V}from"./index-3d21abf8.js";import{P as H}from"./index-fc51b7d2.js";import{L}from"./index-40e83579.js";import{E as b}from"./index-5d897066.js";import{u as k}from"./index-54dce367.js";import{_ as T}from"./goods.api-9b070dd6.js";import{s as _,r as j}from"./config-22c2c72a.js";import"./use-id-a0619e01.js";const N=a({name:"RefundRequest",setup(){const e=t(),{toClipboard:a}=k(),{t:o}=i(),d=new URL("/www/png/name-20d65991.png",self.location),r=s([]),u=s(!1),p=s(!0),c=s(!1),m=s({pageNum:1,pageSize:10}),v=()=>{m.value,T().then((e=>{const a=e.pageList||[];a.forEach((e=>{const a=Number(e.returnStatus),t=Number(e.returnReason),i=_.find((e=>e.id===a));e.statusTxt=i.txt,e.statusColor=i.color;const s=j.find((e=>e.id===t));e.returnReason=s?o(s.txt):""})),r.value=1===m.value.pageNum?a:[...r.value,...a],m.value.pageNum++,p.value=!1,u.value=!1,a.length<m.value.pageSize&&(c.value=!0)})).catch((()=>{c.value=!0,p.value=!1,u.value=!1}))};return{t:o,listData:r,refreshing:u,loading:p,finished:c,empytImg:d,formatZoneDate:l,numberStrFormat:n,isArLang:e,getListData:v,onRefresh:()=>{c.value=!1,p.value=!0,m.value.pageNum=1,v()},copyHandle:async e=>{try{await a(e),f(o("copySuccess"))}catch(t){}},goToDetails:e=>{g({path:"/refundRequest/details",query:{id:e.id}})}}}}),S=e=>(R("data-v-4a60a013"),e=e(),V(),e),q=S((()=>C("div",{style:{height:"46px"}},null,-1))),Z={key:0,class:"list-content"},A=["onClick"],F={class:"info-item"},U={class:"info-item"},E={class:"copy"},I=["onClick"],M=[S((()=>C("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M8.00416 3.0188C6.90246 3.0188 6.00935 3.91191 6.00935 5.01361V6.00834H5.01337C3.91166 6.00834 3.01855 6.90145 3.01855 8.00315V12.9859C3.01855 14.0876 3.91166 14.9807 5.01337 14.9807H9.99608C11.0978 14.9807 11.9909 14.0876 11.9909 12.9859V11.9911H12.9869C14.0886 11.9911 14.9817 11.098 14.9817 9.99632V5.01361C14.9817 3.91191 14.0886 3.0188 12.9869 3.0188H8.00416ZM11.9909 10.9911H12.9869C13.5363 10.9911 13.9817 10.5457 13.9817 9.99632V5.01361C13.9817 4.46419 13.5363 4.0188 12.9869 4.0188H8.00416C7.45474 4.0188 7.00935 4.46419 7.00935 5.01361V6.00834H9.99608C11.0978 6.00834 11.9909 6.90145 11.9909 8.00315V10.9911ZM4.01855 8.00315C4.01855 7.45373 4.46395 7.00834 5.01337 7.00834H9.99608C10.5455 7.00834 10.9909 7.45373 10.9909 8.00315V12.9859C10.9909 13.5353 10.5455 13.9807 9.99608 13.9807H5.01337C4.46395 13.9807 4.01855 13.5353 4.01855 12.9859V8.00315Z",fill:"#333"},null,-1)))],z={class:"info-item"},P={class:"info-item"},$={class:"info-item"},B={class:"info-item"};const G=e(N,[["render",function(e,a,t,i,s,l){const n=o("fx-header"),f=b,g=L,R=H;return p(),d("div",null,[r(n,{fixed:!0},{title:u((()=>[c(m(e.$t("refundRequest")),1)])),_:1}),q,r(R,{modelValue:e.refreshing,"onUpdate:modelValue":a[1]||(a[1]=a=>e.refreshing=a),"pulling-text":e.t("pullingText"),"loosing-text":e.t("loosingText"),"loading-text":e.t("loading"),onRefresh:e.onRefresh},{default:u((()=>[r(g,{loading:e.loading,"onUpdate:loading":a[0]||(a[0]=a=>e.loading=a),finished:e.finished,"loading-text":e.t("loading"),"finished-text":e.listData.length?e.t("product.3"):"",onLoad:e.getListData},{default:u((()=>[e.listData.length?(p(),d("div",Z,[(p(!0),d(v,null,h(e.listData,(a=>(p(),d("div",{key:a.id,class:x(["item",{"is-ar":e.isArLang}]),onClick:t=>e.goToDetails(a)},[C("div",F,[C("p",null,m(e.t("申请时间")),1),C("div",null,m(a.refundTime?e.formatZoneDate(a.refundTime):"-"),1)]),C("div",U,[C("p",null,m(e.t("退款单号")),1),C("div",E,[C("p",{class:x({"is-ar":e.isArLang})},m(a.id),3),(p(),d("svg",{onClick:t=>e.copyHandle(a.id),width:"18",height:"18",viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg"},M,8,I))])]),C("div",z,[C("p",null,m(e.t("退款金额")),1),C("div",null,"$"+m(e.numberStrFormat(a.returnPrice)),1)]),C("div",P,[C("p",null,m(e.t("退款状态")),1),C("div",{style:y({color:a.statusColor})},m(e.t(a.statusTxt)),5)]),C("div",$,[C("p",null,m(e.t("退款理由")),1),C("div",null,m(a.returnReason||e.t("无")),1)]),C("div",B,[C("p",null,m(e.t("退款说明")),1),C("div",null,m(a.returnDetail||e.t("无")),1)])],10,A)))),128))])):D("v-if",!0),e.listData.length||e.loading?D("v-if",!0):(p(),w(f,{key:1,image:e.empytImg.href,description:e.t("noData")},null,8,["image","description"]))])),_:1},8,["loading","finished","loading-text","finished-text","onLoad"])])),_:1},8,["modelValue","pulling-text","loosing-text","loading-text","onRefresh"])])}],["__scopeId","data-v-4a60a013"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/refundRequest/index.vue"]]);export{G as default};
