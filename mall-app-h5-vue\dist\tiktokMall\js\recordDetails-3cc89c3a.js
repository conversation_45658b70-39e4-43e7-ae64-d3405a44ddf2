import{_ as e,d as a,i,r as o,Y as s,u as l,T as t,av as n,c as r,e as c,w as u,a as d,F as f,y,x as v,n as p,b as m,o as k,f as h,t as x,aF as g,ak as _,D as b,E as j}from"./index-3d21abf8.js";import{B as w}from"./index-2406f514.js";import{u as C}from"./index-54dce367.js";import{r as D}from"./exchange.api-23bc91cd.js";import{c as T}from"./index-3ab60a77.js";import{I as N}from"./index-7d1632e5.js";/* empty css              *//* empty css               */import"./use-route-cd41a893.js";import"./index-0d6a7179.js";import"./index-a439655d.js";const U=e=>(b("data-v-74d715a3"),e=e(),j(),e),q=U((()=>d("div",{style:{height:"46px"}},null,-1))),B={key:0,class:"content"},I=["onClick"],E=["src"],S=["onClick"],F=[U((()=>d("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"},null,-1))),U((()=>d("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"},null,-1)))],H={key:0,class:"btn-content"},R=a({name:"RechageRecordDetails"}),M=e(Object.assign(R,{setup(e){const a=i(),b=o(!0),j=[{title:"订单号",info:"",copy:!0,key:"order_no"},{title:"创建时间",info:"",key:"create_time"},{title:"充值数量",info:"",color:!0,key:"volume",isNum:!0,unit:"USDT"},{title:"实际到账",info:"",color:!0,key:"amount",isNum:!0,unit:"USDT"},{title:"订单状态",info:"",color:!0,key:"state"},{title:"币种协议",info:"",key:"coin_blockchain"},{title:"收款地址",info:"",copy:!0,key:"channel_address"},{title:"支付凭证",info:"",key:"img"}],U=o([]),R=s(),M=o(""),{t:O}=l(),{toClipboard:P}=C(),V=o(!1),Y=o(null),$=R.query.order_no,z=R.query.isThirdParty||0;if(V.value=Boolean(Number(z)),V.value){const e=T(j);for(let a=0;a<e.length;a++)["coin_blockchain","channel_address","img"].includes(e[a].key)&&e.splice(a--,1);U.value=e}else U.value=j;if(R.query.r){M.value=R.query.r;const e=U.value.findIndex((e=>"state"===e.key));U.value.splice(e+1,0,{title:"失败原因",info:"",redColor:!0,key:"failure_msg"})}$?(b.value=!0,t.loading({duration:0,forbidClick:!0}),D({order_no:$}).then((e=>{Y.value=e;U.value.find((e=>"volume"===e.key)).unit=e.coin,U.value.forEach((a=>{a.info="failure_msg"===a.key?M.value:e[a.key]}))})).finally((()=>{t.clear(),b.value=!1}))):t(O("参数错误"));return(e,i)=>{const o=n("fx-header"),s=w;return k(),r("div",null,[c(o,{fixed:""},{title:u((()=>[h(x(m(O)("充值详情")),1)])),_:1}),q,d("div",{class:p(["details-content",{"is-ar":m(a)}])},[b.value?v("v-if",!0):(k(),r("div",B,[(k(!0),r(f,null,y(U.value,(e=>{return k(),r("div",{key:e.key,class:"item"},[d("p",null,x(m(O)(e.title))+"：",1),d("div",{class:p({copy:e.copy})},["state"===e.key?(k(),r("span",{key:0,class:p(`color-${e.info}`)},x((a=e.info,null!=(i={0:O("processing"),1:O("successful"),2:O("failure")}[a])?i:"")),3)):"img"===e.key?(k(),r("span",{key:1,class:"img",onClick:a=>{return i=e.info,void N({images:[i],showIndex:!1,loop:!1});var i}},[d("img",{src:e.info,alt:""},null,8,E)],8,I)):(k(),r("span",{key:2,class:p({color:e.color,"color-2":e.redColor})},x(e.isNum?m(g)(e.info,["BTC","ETH"].includes(e.unit)?6:2):e.info),3)),e.unit?(k(),r("span",{key:3,class:p([{color:e.color},"unit"])},x(e.unit),3)):v("v-if",!0),e.copy&&e.info?(k(),r("svg",{key:4,onClick:a=>(async e=>{try{await P(e),t(O("copySuccess"))}catch(a){}})(e.info),xmlns:"http://www.w3.org/2000/svg",width:"18",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"feather feather-copy"},F,8,S)):v("v-if",!0)],2)]);var a,i})),128))]))],2),V.value&&!b.value&&Number(0===Y.value.state)&&Y.value.payUrl?(k(),r("div",H,[c(s,{type:"primary",onClick:i[0]||(i[0]=e=>m(_)(Y.value.payUrl,!0))},{default:u((()=>[h(x(m(O)("去支付")),1)])),_:1})])):v("v-if",!0)])}}}),[["__scopeId","data-v-74d715a3"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/recharge/recordDetails.vue"]]);export{M as default};
