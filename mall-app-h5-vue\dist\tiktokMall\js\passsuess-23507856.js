import{_ as s,Y as a,u as e,r as t,m as l,s as o,av as r,I as n,c,e as u,w as i,a as d,t as p,b as v,f as h,B as f,o as m,C as _}from"./index-3d21abf8.js";const k={class:"center"},C={class:"phone"},x={class:"bttn"},$=s({__name:"passsuess",setup(s){const $=a(),{t:b}=e(),w=()=>{const s=$.query.id;router.push({path:"/orderdeails",query:{id:s}})},y=()=>{router.push("/my")},q=t({});return l((()=>["metashop"].includes("tiktokMall"))),o((()=>{f().then((s=>{q.value=s||{}}))})),(s,a)=>{const e=r("fx-header"),t=n;return m(),c("div",null,[u(e,null,{title:i((()=>[h(p(v(b)("支付成功")),1)])),_:1}),d("div",k,[u(t,{name:"checked",color:"#0ECB81"}),d("p",null,p(v(b)("支付成功")),1),d("div",C,[h(p(s.$t("如有疑问，请立即")),1),d("span",{onClick:a[0]||(a[0]=(...s)=>v(_)&&v(_)(...s))},p(s.$t("联系客服")),1)])]),d("div",x,[d("div",{class:"one",onClick:y},p(s.$t("回到首页")),1),d("div",{class:"two",onClick:w},p(s.$t("查看订单")),1)])])}}},[["__scopeId","data-v-86ca28f4"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/order/passsuess.vue"]]);export{$ as default};
