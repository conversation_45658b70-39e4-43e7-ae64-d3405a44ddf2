import{_ as e,i as a,r as l,m as o,o as t,c as n,e as u,a as i,t as d,x as s,b as c,n as p,D as r,E as m}from"./index-3d21abf8.js";/* empty css              *//* empty css               */import{A as v}from"./index.vue_vue_type_style_index_0_scoped_efb302ad_lang-ae7957c5.js";import{F as f}from"./index-8c1841f6.js";const y=e=>(r("data-v-efb302ad"),e=e(),m(),e),x=y((()=>i("i",{class:"iconfont icon-xiangxiajiantou"},null,-1))),_=[y((()=>i("i",{class:"iconfont icon-guanbi2fill"},null,-1)))],k={key:2,class:"icon"},g=e({__name:"index",props:{modelValue:{type:[Number,String],default:""},type:{type:String,default:"text"},placeholder:{type:String,default:""},clear:{type:Boolean,default:!1},showPassword:{type:Boolean,default:!1},areaCode:{type:Boolean,default:!1},codeNum:{type:[Number,String],default:""},isTurn:{type:Boolean,default:!1}},emits:["update:modelValue","update:codeNum"],setup(e,{emit:r}){const m=e,y=a(),g=l(!1),C=l(!1),b=l(!1),V=o((()=>m.modelValue&&m.clear)),h=()=>{r("update:modelValue","")},j=e=>{const a=e.target.value;r("update:modelValue",a)},N=e=>{localStorage.setItem("areaCode",e.dialCode),r("update:codeNum",e.dialCode)};return(a,l)=>{const o=f;return t(),n("div",null,[u(v,{modelValue:b.value,"onUpdate:modelValue":l[0]||(l[0]=e=>b.value=e),onDone:N},null,8,["modelValue"]),i("div",{class:p([{active:C.value,"is-ar":c(y),turn:e.isTurn},"input-item"])},[e.areaCode?(t(),n("div",{key:0,class:"area-code",onClick:l[1]||(l[1]=e=>b.value=!0)},[i("p",null,"+"+d(e.codeNum),1),x])):s("v-if",!0),u(o,{type:g.value?"text":e.type,"model-value":e.modelValue,placeholder:e.placeholder,onInput:j,onFocus:l[2]||(l[2]=e=>C.value=!0),onBlur:l[3]||(l[3]=e=>C.value=!1)},null,8,["type","model-value","placeholder"]),c(V)?(t(),n("div",{key:1,class:"icon",onClick:h},_)):s("v-if",!0),e.showPassword?(t(),n("div",k,[g.value?(t(),n("i",{key:0,class:"iconfont icon-denglu-mimakejian",onClick:l[4]||(l[4]=e=>g.value=!1)})):(t(),n("i",{key:1,class:"iconfont icon-denglu-mimabukejian",onClick:l[5]||(l[5]=e=>g.value=!0)}))])):s("v-if",!0)],2)])}}},[["__scopeId","data-v-efb302ad"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/components/input-item/index.vue"]]);export{g as I};
