import{_ as e,d as a,u as s,Y as t,l as n,r as i,s as o,av as r,c as d,e as c,w as l,a as u,t as f,x as m,T as p,o as v,f as _,cL as h,cJ as x,D as S,E as g}from"./index-3d21abf8.js";import{m as I}from"./config-6f1990d6.js";const b=a({name:"MessageDetails",setup(){const{t:e}=s(),a=t(),r=n(),{id:d}=a.query,c=i({}),l=(e,a)=>{const s=(e.varInfo?JSON.parse(e.varInfo):[]).find((e=>e.code===a));return s?s.value:"0"},u=(e,a)=>{const s=(e.varInfo?JSON.parse(e.varInfo):[]).find((e=>e.code===a));return s?s.value:""};return o((()=>{d?(p.loading({duration:0,message:e("loading"),forbidClick:!0}),h({id:d}).then((a=>{const s=a,t={},n=s.varInfo?JSON.parse(s.varInfo):[];n.forEach((a=>{t[a.code]=["complaintReason"].includes(a.code)?e(a.value):a.value}));const i=I[s.bizType],o=n.find((e=>e.code===i.key));let r="";i.key?"creditScore"===i.key?r=e("systemMsgScore",{creditScore:o?o.value:"0"}):"inbox_recharge_success"===i.key?r=e("rechargeSuccessTips",{orderAmount:l(s,"orderAmount")}):"inbox_withdraw_success"===i.key?r=e("withdrawalSuccessTips",{orderAmount:l(s,"orderAmount")}):"inbox_store_audit_fail"===i.key?r=e("storeAuthenticationFailedTips",{shop_name:u(s,"shop_name"),reason:u(s,"reason")}):"inbox_store_audit_success"===i.key?r=e("storeAuthenticationPassedTips",{shop_name:u(s,"shop_name")}):(r=e(i.txt)+(o?o.value:"0"),i.txt1&&(r+=e(i.txt1))):r=e(i.txt,t),s.titleStr=e(i.title),s.contentStr=r,s.timeStr=x(a.sendTime,!0),c.value=s}))):(p(e("参数错误")),setTimeout((()=>{r.back()}),1e3))})),{t:e,detailInfo:c}}}),k={class:"message-details page-main-content has-fixed-header message-page has-nav-bar"},y=(e=>(S("data-v-dd36b875"),e=e(),g(),e))((()=>u("div",{class:"fixed-header-spacer"},null,-1))),T={key:0,class:"details-content"},w={class:"title"},A={class:"time"},J=["innerHTML"];const M=e(b,[["render",function(e,a,s,t,n,i){const o=r("fx-header");return v(),d("div",k,[c(o,{fixed:!0},{title:l((()=>[_(f(e.$t("消息详情")),1)])),_:1}),y,e.detailInfo.title?(v(),d("div",T,[u("div",w,f(e.detailInfo.titleStr),1),u("p",A,f(e.detailInfo.timeStr),1),u("div",{class:"content",innerHTML:e.detailInfo.contentStr},null,8,J)])):m("v-if",!0)])}],["__scopeId","data-v-dd36b875"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/message/details/index.vue"]]);export{M as default};
