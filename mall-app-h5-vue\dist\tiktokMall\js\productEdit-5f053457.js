import{P as e,S as a,a5 as t,d as l,bF as i,a8 as o,e as s,W as n,X as u,_ as r,j as d,m as c,i as m,u as v,Y as p,l as f,r as b,bA as x,q as g,g as h,p as T,av as V,I as y,bn as C,c as k,w as S,a as P,x as $,n as N,b as _,T as D,o as F,t as j,f as w,A as I,K as M,ak as U,D as B,E as Y}from"./index-3d21abf8.js";import{B as O}from"./index-2406f514.js";import"./index-573e22f7.js";import{C as E}from"./index-6aaac5d3.js";/* empty css              *//* empty css               */import{s as z,b as R,d as q}from"./product.api-7fdfc848.js";import{F as H}from"./index-8c1841f6.js";import{D as A}from"./index-d27cfcd1.js";import{F as G}from"./index-4ac00735.js";import{D as J}from"./function-call-78245787.js";import"./use-route-cd41a893.js";import"./index-cfaf3bc2.js";import"./stringify-d53955b2.js";import"./___vite-browser-external_commonjs-proxy-9405bfc0.js";import"./index-7dfcb82a.js";import"./use-id-a0619e01.js";import"./use-placeholder-c97cb410.js";const[K,W]=e("switch");const X=u(l({name:K,props:{size:a,loading:Boolean,disabled:Boolean,modelValue:t,activeColor:String,inactiveColor:String,activeValue:{type:t,default:!0},inactiveValue:{type:t,default:!1}},emits:["change","update:modelValue"],setup(e,{emit:a,slots:t}){const l=()=>e.modelValue===e.activeValue,u=()=>{if(!e.disabled&&!e.loading){const t=l()?e.inactiveValue:e.activeValue;a("update:modelValue",t),a("change",t)}},r=()=>{if(e.loading){const a=l()?e.activeColor:e.inactiveColor;return s(n,{class:W("loading"),color:a},null)}if(t.node)return t.node()};return i((()=>e.modelValue)),()=>{var a;const{size:i,loading:n,disabled:d,activeColor:c,inactiveColor:m}=e,v=l(),p={fontSize:o(i),backgroundColor:v?c:m};return s("div",{role:"switch",class:W({on:v,loading:n,disabled:d}),style:p,tabindex:d?void 0:0,"aria-checked":v,onClick:u},[s("div",{class:W("node")},[r()]),null==(a=t.background)?void 0:a.call(t)])}}})),L=e=>(B("data-v-47e02415"),e=e(),Y(),e),Q={class:"editProduct"},Z={class:"tip pt-2 pb-2 pl-4 pr-4"},ee={class:"profit"},ae={key:0,class:"flex pl-4 pr-4 input-item pt-3 pb-3"},te={class:"flex"},le={class:"flex pl-4 pr-4 input-item pt-3 pb-3"},ie={class:"flex"},oe={class:"flex pl-4 pr-4 input-item pt-3 pb-3"},se={class:"flex"},ne={class:"tip pt-2 pb-2 pl-4 pr-4"},ue=L((()=>P("span",null,"%",-1))),re={class:"tips pt-2 pb-2 pl-4 pr-4"},de={class:"tip pt-2 pb-2 pl-4 pr-4"},ce={class:"tip pt-2 pb-2 pl-4 pr-4"},me={class:"tip pt-2 pb-2 pl-4 pr-4"},ve=L((()=>P("span",null,"%",-1))),pe={style:{margin:"16px"},class:"pb-8"},fe=r({__name:"productEdit",setup(e){const a=d(),t=c((()=>{})),l=c((()=>{})),i=m(),{t:o}=v(),n=p(),u=f(),r=b(JSON.parse(n.query.item)),B=b(new Date),Y=b({sellingPrice:"",isShelf:0,recTime:0,isCombo:!1,startTime:x(),endTime:x(),discount:"",percent:"",profit:"",id:""}),K=()=>{J.confirm({title:o("product.21"),message:o("product.22"),confirmButtonText:o("sure"),cancelButtonText:o("cancel")}).then((()=>{q({sellerGoodsId:Y.value.id}).then((()=>{sessionStorage.setItem("currentProductId",r.value.id),sessionStorage.setItem("productDelete",!0),D(o("product.10")),u.go(-1)}))})).catch((()=>{}))},W=b(!1),L=b(!1),fe=b(!1),be=b(""),xe=b(""),ge=b(""),he=c((()=>{let e={};return a.userInfo.token?e={...a.userInfo}:u.push("/login"),e}));g((()=>{Se()})),h((()=>Y.value.percent),((e,a)=>{})),h((()=>Y.value.isCombo),((e,a)=>{e&&Ve()})),h((()=>Y.value.isShelf),((e,a)=>{e/1==1&&Ce()}));const Te=b(!1),Ve=()=>{Te.value=!0,$e()},ye=b(!1),Ce=()=>{ye.value=!0,$e()},ke=T({min:"",max:""}),Se=()=>{Y.value.id=r.value.id,Y.value.discountPrice=r.value.discountPrice?(r.value.discountPrice/1).toFixed(2):(r.value.sellingPrice/1).toFixed(2),Y.value.sellingPrice=(r.value.sellingPrice/1).toFixed(2),Y.value.isShelf=r.value.isShelf/1,Y.value.isCombo=Boolean(r.value.isCombo/1),Y.value.recTime=r.value.recTime/1,Y.value.startTime=r.value.discountStartTime?r.value.discountStartTime.split(" ")[0]:r.value.discountStartTime,Y.value.endTime=r.value.discountEndTime?r.value.discountEndTime.split(" ")[0]:r.value.discountEndTime,Y.value.profit=(100*r.value.profitRatio).toFixed(2),Y.value.discount=(100*r.value.discountRatio).toFixed(2),Y.value.systemPrice=(r.value.systemPrice/1).toFixed(2),Y.value.percent=(r.value.profitRatio/1*100).toFixed(2),ke.min=Number(r.value.sysParaMin),ke.max=Number(r.value.sysParaMax),z().then((e=>{ke.min=Number(e.sysParaMin),ke.max=Number(e.sysParaMax)}))},Pe=()=>{if([].includes("tiktokMall"))if(he.value.phoneverif&&he.value.emailverif)$e();else{const e=he.value.phoneverif?"bindEmailTips":"请绑定手机号";J.confirm({title:o("dialogTips"),message:o(e),cancelButtonText:o("cancel"),confirmButtonText:o("gotoSet"),confirmButtonColor:"#1552F0",cancelButtonColor:"#999"}).then((()=>{U("/personalInfo")})).catch((()=>{}))}else $e()},$e=()=>{const{startTime:e,endTime:a,discount:t}=Y.value;if(e||a){const l=new Date(e.replace(/-/g,"/")+" 00:00:00").getTime(),i=new Date(a.replace(/-/g,"/")+" 00:00:00").getTime();if(!t)return Te.value=!1,ye.value=!1,Y.value.isCombo=!1,Y.value.isShelf=!1,void D(o("请设置折扣比例"));if(l>i)return Te.value=!1,ye.value=!1,Y.value.isCombo=!1,Y.value.isShelf=!1,void D(o("开始时间应小于结束时间"))}if(Number(t)&&(!e||!a))return Te.value=!1,ye.value=!1,Y.value.isCombo=!1,Y.value.isShelf=!1,void D(o("请正确填写活动开启时间和结束时间"));if(!Ie()){D.loading({forbidClick:!0,loadingType:"spinner",duration:0});let e={isShelf:Y.value.isShelf?1:0,recTime:Y.value.recTime?1:0,isCombo:Y.value.isCombo?1:0,sellerGoodsId:r.value.id,startTime:Y.value.startTime?Y.value.startTime+" 00:00:00":"",endTime:Y.value.endTime?Y.value.endTime+" 00:00:00":"",discount:(Y.value.discount/100).toFixed(2),percent:(Y.value.percent/100).toFixed(2),profit:(Y.value.percent/100).toFixed(2)};return R(e).then((e=>{sessionStorage.setItem("currentProductId",r.value.id),Te.value?Te.value=!1:ye.value?ye.value=!1:(D(o("product.10")),u.push("/product"))})).catch((e=>{const a=e.msg;if(Te.value=!1,ye.value=!1,a.indexOf("未购买")>-1)D(o("您暂未购买直通车套餐，请购买再试"));else if(a.indexOf("已到期")>-1)D(o("您的直通车已到期"));else if(a.indexOf("最多推广")>-1){const e=a.split("最多推广商品数量为");D(o("最多推广商品数量为")+": "+e[1])}else if(a.indexOf("未激活")>-1)D(o("您的直通未激活"));else if(a.indexOf("未激活")>-1)D(o("您的直通未激活"));else if(a.indexOf("最小下架")>-1)D(o("少于店铺设置最小下架商品数"));else if(a.indexOf("首次上架")>-1){const t="string"==typeof e.data?JSON.parse(e.data):e.data;D(o(a,{_$1:t._$1}))}else D(o(a));Y.value.isCombo=!1}))}},Ne=e=>{switch(e){case 1:W.value=!0;break;case 2:L.value=!0;break;case 3:fe.value=!0}},_e=e=>{switch(e){case 1:W.value=!1,Y.value.startTime=x(be.value).format("YYYY-MM-DD");break;case 2:L.value=!1,Y.value.endTime=x(xe.value).format("YYYY-MM-DD");break;case 3:fe.value=!1,Y.value.recTime=Fe(ge.value)}},De=e=>{switch(e){case 1:W.value=!1;break;case 2:L.value=!1;break;case 3:fe.value=!1}},Fe=e=>e.getFullYear()+"-"+(e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-"+(e.getDate()<10?"0"+e.getDate():e.getDate())+" "+(e.getHours()<10?"0"+e.getHours():e.getHours())+":"+(e.getMinutes()<10?"0"+e.getMinutes():e.getMinutes())+":"+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds()),je=c((()=>{let e=0;if(!isNaN(Y.value.sellingPrice)&&!isNaN(Y.value.discount)){const a=Number(Y.value.systemPrice),t=Number((Number(Y.value.discount)/100).toFixed(2)),l=Number(Y.value.sellingPrice)*(1-t);e=Number(l-a)}return e.toFixed(2)})),we=()=>{const e=Number(Y.value.systemPrice),a=Number((Number(Y.value.percent)/100).toFixed(2));Y.value.sellingPrice=Number((e+e*a).toFixed(2))},Ie=()=>{const e=Number(Y.value.percent),a=Number(Y.value.discount);let t=!1;if(!isNaN(e)){const l=/^\+?[1-9][0-9]*$/;if(!l.test(String(e)))return D(o("百分比必须为正整数")),!0;if(a&&!l.test(String(a)))return D(o("折扣比例必须为正整数")),!0;e>ke.max&&(Y.value.percent=ke.max,t=!0,we()),e<ke.min&&(Y.value.percent=ke.min,t=!0,we())}return t&&ke.min&&ke.max&&D(o("百分比设置范围为")+`：${ke.min}% ~ ${ke.max}%`),t},Me=()=>{const e=Number(Y.value.sellingPrice),a=Number(Y.value.systemPrice),t=Number(((e-a)/a*100).toFixed(2));Y.value.percent=t};return(e,a)=>{const n=V("fx-header"),u=H,r=E,d=X,c=y,m=A,v=C,p=O,f=G;return F(),k("div",Q,[s(n,{fixed:""},{title:S((()=>[P("div",null,j(e.$t("编辑商品")),1)])),_:1}),P("div",{class:N(["edit-product-pop",{"is-ar":_(i)}])},[$("        <div class=\"title\">{{$t('编辑商品')}}</div>"),s(f,null,{default:S((()=>[P("div",Z,j(e.$t("当前售价")),1),s(r,{class:"input-field",inset:""},{default:S((()=>[$(' <van-field v-model="fromData.discountPrice" readonly :placeholder="$t(\'当前售价\')"> '),s(u,{modelValue:Y.value.sellingPrice,"onUpdate:modelValue":a[0]||(a[0]=e=>Y.value.sellingPrice=e),type:"number",placeholder:e.$t("当前售价"),onInput:Me},{button:S((()=>[$(" <span class=\"profit\">{{$t('利润')}} {{ (fromData.discountPrice - fromData.systemPrice).toFixed(2) }}</span> "),P("span",ee,j(e.$t("利润"))+" "+j(_(je)),1)])),_:1},8,["modelValue","placeholder"])])),_:1}),_(t)?$("v-if",!0):(F(),k("div",ae,[P("div",null,j(e.$t("是否上架")),1),P("div",te,[s(d,{modelValue:Y.value.isShelf,"onUpdate:modelValue":a[1]||(a[1]=e=>Y.value.isShelf=e),size:"25","inactive-color":"#fff","inactive-value":0,"active-value":1},null,8,["modelValue"])])])),P("div",le,[P("div",null,j(e.$t("是否推荐")),1),P("div",ie,[s(d,{modelValue:Y.value.recTime,"onUpdate:modelValue":a[2]||(a[2]=e=>Y.value.recTime=e),"inactive-value":0,"active-value":1,size:"25","inactive-color":"#fff"},null,8,["modelValue"])])]),$('          <div class="tip pt-2 pb-2 pl-4 pr-4" v-if="fromData.isRecommend">{{$t(\'推荐时间\')}}</div>'),$('          <van-cell-group v-if="fromData.isRecommend" class="input-field" inset>'),$('            <van-field @click-input="onClick(3)"  v-model="fromData.recTime" :placeholder="t(\'推荐时间\')">'),$("            </van-field>"),$("          </van-cell-group>"),$('          <van-popup v-model:show="isRecommendShow"'),$("                     round"),$('                     position="bottom"'),$("          >"),$("            <van-datetime-picker"),$("                :confirm-button-text=\"$t('确定')\""),$("                :cancel-button-text=\"$t('取消')\""),$('                v-model="recommendTime"'),$('                type="datetime"'),$("                :title=\"t('选择完整时间')\""),$('                @confirm="onConfirm(3)"'),$('                @cancel="onCancel(3)"'),$("            />"),$("          </van-popup>"),P("div",oe,[P("div",null,j(e.$t("直通车")),1),P("div",se,[s(d,{loading:Te.value,modelValue:Y.value.isCombo,"onUpdate:modelValue":a[3]||(a[3]=e=>Y.value.isCombo=e),size:"25","inactive-color":"#fff"},null,8,["loading","modelValue"])])]),P("div",ne,j(e.$t("百分比")),1),s(r,{class:"input-field",inset:""},{default:S((()=>[s(u,{modelValue:Y.value.percent,"onUpdate:modelValue":a[4]||(a[4]=e=>Y.value.percent=e),type:"number",placeholder:_(o)("百分比"),rules:[{required:!0,message:_(o)("请填写百分比"),max:ke.max,min:ke.min}],onInput:we,onBlur:Ie},{button:S((()=>[ue])),_:1},8,["modelValue","placeholder","rules"])])),_:1}),P("div",re,[w(j(e.$t("将选中的商品发布到你的店铺，并填写商品利润比例，推荐比例"))+": ",1),P("span",null,j(ke.min)+"%-"+j(ke.max)+"%",1)]),P("div",de,j(e.$t("折扣开始日期")),1),s(r,{class:"input-field",inset:"",style:{position:"relative"}},{default:S((()=>[Y.value.startTime?(F(),k("div",{key:0,class:"time-clear",onClick:a[5]||(a[5]=e=>Y.value.startTime="")},[s(c,{name:"cross"})])):$("v-if",!0),s(u,{onClickInput:a[6]||(a[6]=e=>Ne(1)),modelValue:Y.value.startTime,"onUpdate:modelValue":a[7]||(a[7]=e=>Y.value.startTime=e),placeholder:_(o)("折扣开始日期")},null,8,["modelValue","placeholder"]),s(v,{show:W.value,"onUpdate:show":a[11]||(a[11]=e=>W.value=e),round:"",position:"bottom"},{default:S((()=>[s(m,{"min-date":B.value,"confirm-button-text":e.$t("确定"),"cancel-button-text":e.$t("取消"),modelValue:be.value,"onUpdate:modelValue":a[8]||(a[8]=e=>be.value=e),type:"date",title:_(o)("选择完整时间"),onConfirm:a[9]||(a[9]=e=>_e(1)),onCancel:a[10]||(a[10]=e=>De(1))},null,8,["min-date","confirm-button-text","cancel-button-text","modelValue","title"])])),_:1},8,["show"])])),_:1}),P("div",ce,j(e.$t("折扣结束日期")),1),s(r,{class:"input-field",inset:"",style:{position:"relative"}},{default:S((()=>[Y.value.endTime?(F(),k("div",{key:0,class:"time-clear",onClick:a[12]||(a[12]=e=>Y.value.endTime="")},[s(c,{name:"cross"})])):$("v-if",!0),s(u,{onClickInput:a[13]||(a[13]=e=>Ne(2)),modelValue:Y.value.endTime,"onUpdate:modelValue":a[14]||(a[14]=e=>Y.value.endTime=e),placeholder:_(o)("折扣结束日期")},null,8,["modelValue","placeholder"])])),_:1}),P("div",me,j(e.$t("折扣比例")),1),s(r,{class:"input-field",inset:""},{default:S((()=>[s(u,{modelValue:Y.value.discount,"onUpdate:modelValue":a[15]||(a[15]=e=>Y.value.discount=e),type:"number",placeholder:_(o)("折扣比例")},{button:S((()=>[ve])),_:1},8,["modelValue","placeholder"])])),_:1}),s(v,{show:L.value,"onUpdate:show":a[19]||(a[19]=e=>L.value=e),round:"",position:"bottom"},{default:S((()=>[s(m,{"min-date":B.value,"cancel-button-text":e.$t("取消"),"confirm-button-text":e.$t("确定"),modelValue:xe.value,"onUpdate:modelValue":a[16]||(a[16]=e=>xe.value=e),type:"date",title:_(o)("选择完整时间"),onConfirm:a[17]||(a[17]=e=>_e(2)),onCancel:a[18]||(a[18]=e=>De(2))},null,8,["min-date","cancel-button-text","confirm-button-text","modelValue","title"])])),_:1},8,["show"]),P("div",pe,[s(p,{class:"btn-content",block:"",type:"primary",onClick:Pe,"native-type":"submit"},{default:S((()=>[w(j(e.$t("保存")),1)])),_:1}),_(l)?$("v-if",!0):(F(),I(p,{key:0,style:{"margin-top":"16px","background-color":"red","border-radius":"4px","border-color":"red"},block:"",type:"primary",onClick:M(K,["stop"]),"native-type":"submit"},{default:S((()=>[w(j(e.$t("删除")),1)])),_:1},8,["onClick"]))])])),_:1})],2)])}}},[["__scopeId","data-v-47e02415"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/product/components/productEdit.vue"]]);export{fe as default};
