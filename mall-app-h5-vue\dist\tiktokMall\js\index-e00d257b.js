import{_ as e,d as s,i as a,u as t,Y as l,l as i,r as o,m as r,at as n,q as d,I as c,c as u,a as m,e as p,w as v,b as f,aV as g,t as x,f as h,n as y,o as b,au as j,F as k,y as P,x as $,K as _,aF as I,D as S,E as w}from"./index-3d21abf8.js";import{P as C}from"./index-fc51b7d2.js";import{L as N}from"./index-40e83579.js";/* empty css              *//* empty css               */import{S as L}from"./index-179203f3.js";import{s as V,m as M}from"./product.api-7fdfc848.js";import{_ as U}from"./search-icon-8ae5f679.js";import{_ as D}from"./more-f6d49895.js";import"./index-8c1841f6.js";import"./index-7dfcb82a.js";import"./use-route-cd41a893.js";import"./use-id-a0619e01.js";import"./index-cfaf3bc2.js";import"./stringify-d53955b2.js";import"./___vite-browser-external_commonjs-proxy-9405bfc0.js";const E={class:"product page-main-content product-home-page"},G=["src"],q={class:"flex ml-4 mr-4 product-header"},O={key:0,class:"moeny icon"},R=["src"],F={key:1,class:"moeny"},J={class:"title"},z={class:"moeny"},K={class:"title"},Y={class:"hot-title ml-4 mr-4 mt-4 mb-4"},A=["onClick"],B={class:"flex-1 flex left"},H={class:"product-img-wrap w-20 h-20"},Q=["src"],T={key:0,class:"take_off"},W={class:"product-info flex-1"},X={class:"name-content"},Z=["onClick"],ee=["src"],se={class:"Specification"},ae={style:{"margin-left":"20px"}},te={class:"money-content"},le={key:0,class:"dis"},ie=(e=>(S("data-v-690b902b"),e=e(),w(),e))((()=>m("div",{class:"footer-padding"},null,-1))),oe=s({name:"ProductIndex"}),re=e(Object.assign(oe,{setup(e){const s=a();t(),l();const S=i();let w=o("");o(!1);let oe=o(1),re=o(0),ne=o(0),de=o(0),ce=o({});const ue=o([]),me=o(!1),pe=o(!1),ve=o(!1),fe=r((()=>["int"].includes("tiktokMall"))),ge=o(!0);n((()=>{if(!ge.value){sessionStorage.getItem("productReload")&&(sessionStorage.removeItem("productReload"),xe(!0));const e=sessionStorage.getItem("currentProductId");if(e){const s=ue.value.findIndex((s=>s.id===e));sessionStorage.getItem("productDelete")?(ue.value.splice(s,1),de.value-=1,sessionStorage.removeItem("productDelete")):(ue.value=[],xe(!0)),sessionStorage.removeItem("currentProductId")}}}));const xe=e=>{e&&(oe.value=1,ve.value=!1);let s={pageNum:oe.value,pageSize:20};M(s).then((s=>{ge.value=!1,pe.value&&(pe.value=!1),re.value=s.evaluations,ne.value=s.systemGoodsNum,de.value=s.sellerGoodsNum,ue.value=e?s.pageList||[]:1===oe.value?s.pageList:[...ue.value,...s.pageList],ue.value.length>=s.sellerGoodsNum?ve.value=!0:oe.value++,me.value=!1}))},he=()=>{xe(!0)},ye=()=>{S.push("/search?id=1")},be=()=>{S.push("/productPage/list")},je=()=>{S.push("/productPage/comment")},ke=o(""),Pe=o("");V().then((e=>{ke.value=e.sysParaMin,Pe.value=e.sysParaMax}));return d((()=>{document.addEventListener("langChange",(()=>{he()}))})),(e,a)=>{const t=L,l=c,i=N,o=C;return b(),u("div",E,[m("div",{onClick:ye},[p(t,{class:"search-wrap",disabled:"",modelValue:f(w),"onUpdate:modelValue":a[0]||(a[0]=e=>g(w)?w.value=e:w=e),placeholder:e.$t("请输入搜索商品名称"),clearable:""},{"left-icon":v((e=>[m("img",{class:"search-icon",src:f(U)},null,8,G)])),_:1},8,["modelValue","placeholder"])]),m("div",q,[m("div",{class:"flex-1 text-center after",onClick:be},[f(fe)?(b(),u("div",O,[m("img",{src:f(j)("image/order/diamond.svg"),alt:""},null,8,R)])):(b(),u("div",F,x(f(ne)),1)),m("div",J,[h(x(e.$t("商品库")),1),p(l,{name:"arrow"})])]),m("div",{class:"flex-1 text-center",onClick:je},[m("div",z,x(f(re)),1),m("div",K,[h(x(e.$t("评论")),1),p(l,{name:"arrow"})])])]),m("div",Y,x(e.$t("店铺产品"))+"("+x(f(de))+") ",1),m("div",{class:y(["list ml-4 mr-4 mt-4 mb-4 list-content",{"is-ar":f(s)}])},[p(o,{modelValue:pe.value,"onUpdate:modelValue":a[2]||(a[2]=e=>pe.value=e),onRefresh:he,"loading-text":e.$t("加载中"),"loosing-text":e.$t("释放以刷新"),"pulling-text":e.$t("下拉以刷新")},{default:v((()=>[p(i,{ref:"vanList",loading:me.value,"onUpdate:loading":a[1]||(a[1]=e=>me.value=e),"loading-text":e.$t("加载中"),finished:ve.value,"finished-text":e.$t("noMore"),onLoad:xe},{default:v((()=>[(b(!0),u(k,null,P(ue.value,((s,a)=>(b(),u("div",{class:"item pl-3 pr-3 pb-3 pt-3 flex",onClick:e=>(e=>{S.push({path:"/productPage/details",query:{item:JSON.stringify(e)}})})(s),key:a},[m("div",B,[m("div",H,[m("img",{class:"product-img",src:s.imgUrl1},null,8,Q),s.isShelf/1==0?(b(),u("div",T,x(e.$t("已下架")),1)):$("v-if",!0),$('                <div class="delete-wrap" @click.stop="deleteGood(item)">'),$("                  {{ $t('删除') }}"),$("                </div>")]),m("div",W,[m("div",X,[m("p",null,x(s.name),1),m("div",{class:"more",onClick:_((e=>(e=>{e.sysParaMin=ke.value,e.sysParaMax=Pe.value,S.push({path:"/productPage/productEdit",query:{item:JSON.stringify(e)}}),ce.value=e})(s)),["stop"])},[m("img",{class:"more-icon",src:f(D)},null,8,ee)],8,Z)]),$(' <div class="name">{{ item.name }}</div> '),m("div",se,[m("span",null,x(s.categoryName),1),m("span",ae,x(e.$t("销量"))+": "+x(f(I)(s.soldNum,0)),1)]),m("div",te,[m("p",null,"$"+x(f(I)(s.sellingPrice)),1),s.discountPrice?(b(),u("p",le,x(e.$t("折扣价"))+" $"+x(f(I)(s.discountPrice)),1)):$("v-if",!0)])])]),$(' <div>\r\n              <img\r\n                class="more-icon"\r\n                @click.stop="openEdit(item)"\r\n                src="@/assets/imgs/product/more.png"\r\n              />\r\n            </div> ')],8,A)))),128))])),_:1},8,["loading","loading-text","finished","finished-text"])])),_:1},8,["modelValue","loading-text","loosing-text","pulling-text"]),ie],2)])}}}),[["__scopeId","data-v-690b902b"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/product/index.vue"]]);export{re as default};
