System.register(["./index-legacy-46a00900.js","./index-legacy-6156faa3.js","./index-legacy-bbd15202.js"],(function(e,a){"use strict";var o,i,l,n,r,t,d,s,p,c,u,v,m,f,g,b,w,x,k,h,y,z,_,S,j,F,R,A,C=document.createElement("style");return C.textContent=":root{--van-uploader-size: 80px;--van-uploader-icon-size: 24px;--van-uploader-icon-color: var(--van-gray-4);--van-uploader-text-color: var(--van-text-color-2);--van-uploader-text-font-size: var(--van-font-size-sm);--van-uploader-upload-background-color: var(--van-gray-1);--van-uploader-upload-active-color: var(--van-active-color);--van-uploader-delete-color: var(--van-white);--van-uploader-delete-icon-size: 14px;--van-uploader-delete-background-color: rgba(0, 0, 0, .7);--van-uploader-file-background-color: var(--van-background-color);--van-uploader-file-icon-size: 20px;--van-uploader-file-icon-color: var(--van-gray-7);--van-uploader-file-name-padding: 0 var(--van-padding-base);--van-uploader-file-name-margin-top: var(--van-padding-xs);--van-uploader-file-name-font-size: var(--van-font-size-sm);--van-uploader-file-name-text-color: var(--van-gray-7);--van-uploader-mask-text-color: var(--van-white);--van-uploader-mask-background-color: rgba(50, 50, 51, .88);--van-uploader-mask-icon-size: 22px;--van-uploader-mask-message-font-size: var(--van-font-size-sm);--van-uploader-mask-message-line-height: var(--van-line-height-xs);--van-uploader-loading-icon-size: 22px;--van-uploader-loading-icon-color: var(--van-white);--van-uploader-disabled-opacity: var(--van-disabled-opacity) }.van-uploader{position:relative;display:inline-block}.van-uploader__wrapper{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-flex-wrap:wrap;flex-wrap:wrap}.van-uploader__wrapper--disabled{opacity:var(--van-uploader-disabled-opacity)}.van-uploader__input{position:absolute;top:0;left:0;width:100%;height:100%;overflow:hidden;cursor:pointer;opacity:0}.van-uploader__input-wrapper{position:relative}.van-uploader__input:disabled{cursor:not-allowed}.van-uploader__upload{position:relative;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;box-sizing:border-box;width:var(--van-uploader-size);height:var(--van-uploader-size);margin:0 var(--van-padding-xs) var(--van-padding-xs) 0;background:var(--van-uploader-upload-background-color)}.van-uploader__upload:active{background-color:var(--van-uploader-upload-active-color)}.van-uploader__upload--readonly:active{background-color:var(--van-uploader-upload-background-color)}.van-uploader__upload-icon{color:var(--van-uploader-icon-color);font-size:var(--van-uploader-icon-size)}.van-uploader__upload-text{margin-top:var(--van-padding-xs);color:var(--van-uploader-text-color);font-size:var(--van-uploader-text-font-size)}.van-uploader__preview{position:relative;margin:0 var(--van-padding-xs) var(--van-padding-xs) 0;cursor:pointer}.van-uploader__preview-image{display:block;width:var(--van-uploader-size);height:var(--van-uploader-size);overflow:hidden}.van-uploader__preview-delete{position:absolute;top:0;right:0}.van-uploader__preview-delete--shadow{width:var(--van-uploader-delete-icon-size);height:var(--van-uploader-delete-icon-size);background:var(--van-uploader-delete-background-color);border-radius:0 0 0 12px}.van-uploader__preview-delete-icon{position:absolute;top:0;right:0;color:var(--van-uploader-delete-color);font-size:var(--van-uploader-delete-icon-size);-webkit-transform:scale(.7) translate(10%,-10%);transform:scale(.7) translate(10%,-10%)}.van-uploader__preview-cover{position:absolute;top:0;right:0;bottom:0;left:0}.van-uploader__mask{position:absolute;top:0;right:0;bottom:0;left:0;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;color:var(--van-uploader-mask-text-color);background:var(--van-uploader-mask-background-color)}.van-uploader__mask-icon{font-size:var(--van-uploader-mask-icon-size)}.van-uploader__mask-message{margin-top:6px;padding:0 var(--van-padding-base);font-size:var(--van-uploader-mask-message-font-size);line-height:var(--van-uploader-mask-message-line-height)}.van-uploader__loading{width:var(--van-uploader-loading-icon-size);height:var(--van-uploader-loading-icon-size);color:var(--van-uploader-loading-icon-color)}.van-uploader__file{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;width:var(--van-uploader-size);height:var(--van-uploader-size);background:var(--van-uploader-file-background-color)}.van-uploader__file-icon{color:var(--van-uploader-file-icon-color);font-size:var(--van-uploader-file-icon-size)}.van-uploader__file-name{box-sizing:border-box;width:100%;margin-top:var(--van-uploader-file-name-margin-top);padding:var(--van-uploader-file-name-padding);color:var(--van-uploader-file-name-text-color);font-size:var(--van-uploader-file-name-font-size);text-align:center}\n",document.head.appendChild(C),{setters:[e=>{o=e.P,i=e.ae,l=e.a3,n=e.d,r=e.S,t=e.bc,d=e.e,s=e.I,p=e.W,c=e.aE,u=e.ad,v=e.bf,m=e.Q,f=e.a4,g=e.R,b=e.ai,w=e.bl,x=e.r,k=e.aN,h=e.a9,y=e.bF,z=e.a2,_=e.aY,S=e.ag,j=e.p,F=e.X},e=>{R=e.I},e=>{A=e.I}],execute:function(){const[a,C,V]=o("uploader");function I(e,a){return new Promise((o=>{if("file"===a)return void o();const i=new FileReader;i.onload=e=>{o(e.target.result)},"dataUrl"===a?i.readAsDataURL(e):"text"===a&&i.readAsText(e)}))}function L(e,a){return i(e).some((e=>!!e.file&&(l(a)?a(e.file):e.file.size>a)))}const U=/\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i;function O(e){return!!e.isImage||(e.file&&e.file.type?0===e.file.type.indexOf("image"):e.url?(a=e.url,U.test(a)):"string"==typeof e.content&&0===e.content.indexOf("data:image"));var a}var B=n({props:{name:r,item:t(Object),index:Number,imageFit:String,lazyLoad:Boolean,deletable:Boolean,previewSize:[Number,String,Array],beforeDelete:Function},emits:["delete","preview"],setup(e,{emit:a,slots:o}){const i=()=>{const{status:a,message:o}=e.item;if("uploading"===a||"failed"===a){const e="failed"===a?d(s,{name:"close",class:C("mask-icon")},null):d(p,{class:C("loading")},null),i=u(o)&&""!==o;return d("div",{class:C("mask")},[e,i&&d("div",{class:C("mask-message")},[o])])}},l=o=>{const{name:i,item:l,index:n,beforeDelete:r}=e;o.stopPropagation(),v(r,{args:[l,{name:i,index:n}],done:()=>a("delete")})},n=()=>a("preview"),r=()=>{if(e.deletable&&"uploading"!==e.item.status){const e=o["preview-delete"];return d("div",{role:"button",class:C("preview-delete",{shadow:!e}),tabindex:0,"aria-label":V("delete"),onClick:l},[e?e():d(s,{name:"cross",class:C("preview-delete-icon")},null)])}},t=()=>{if(o["preview-cover"]){const{index:a,item:i}=e;return d("div",{class:C("preview-cover")},[o["preview-cover"](m({index:a},i))])}},f=()=>{const{item:a,lazyLoad:o,imageFit:i,previewSize:l}=e;return O(a)?d(A,{fit:i,src:a.content||a.url,class:C("preview-image"),width:Array.isArray(l)?l[0]:l,height:Array.isArray(l)?l[1]:l,lazyLoad:o,onClick:n},{default:t}):d("div",{class:C("file"),style:c(e.previewSize)},[d(s,{class:C("file-icon"),name:"description"},null),d("div",{class:[C("file-name"),"van-ellipsis"]},[a.file?a.file.name:a.url]),t()])};return()=>d("div",{class:C("preview")},[f(),i(),r()])}});const P={name:f(""),accept:g("image/*"),capture:String,multiple:Boolean,disabled:Boolean,readonly:Boolean,lazyLoad:Boolean,maxCount:f(1/0),imageFit:g("cover"),resultType:g("dataUrl"),uploadIcon:g("photograph"),uploadText:String,deletable:b,afterRead:Function,showUpload:b,modelValue:w(),beforeRead:Function,beforeDelete:Function,previewSize:[Number,String,Array],previewImage:b,previewOptions:Object,previewFullImage:b,maxSize:{type:[Number,String,Function],default:1/0}};var D=n({name:a,props:P,emits:["delete","oversize","click-upload","close-preview","click-preview","update:modelValue"],setup(e,{emit:a,slots:o}){const l=x(),n=[],r=(a=e.modelValue.length)=>({name:e.name,index:a}),t=()=>{l.value&&(l.value.value="")},p=o=>{if(t(),L(o,e.maxSize)){if(!Array.isArray(o))return void a("oversize",o,r());{const i=function(e,a){const o=[],i=[];return e.forEach((e=>{L(e,a)?i.push(e):o.push(e)})),{valid:o,invalid:i}}(o,e.maxSize);if(o=i.valid,a("oversize",i.invalid,r()),!o.length)return}}o=j(o),a("update:modelValue",[...e.modelValue,...i(o)]),e.afterRead&&e.afterRead(o,r())},u=a=>{const{maxCount:o,modelValue:i,resultType:l}=e;if(Array.isArray(a)){const e=+o-i.length;a.length>e&&(a=a.slice(0,e)),Promise.all(a.map((e=>I(e,l)))).then((e=>{const o=a.map(((a,o)=>{const i={file:a,status:"",message:""};return e[o]&&(i.content=e[o]),i}));p(o)}))}else I(a,l).then((e=>{const o={file:a,status:"",message:""};e&&(o.content=e),p(o)}))},v=a=>{const{files:o}=a.target;if(e.disabled||!o||!o.length)return;const i=1===o.length?o[0]:[].slice.call(o);if(e.beforeRead){const a=e.beforeRead(i,r());if(!a)return void t();if(z(a))return void a.then((e=>{u(e||i)})).catch(t)}u(i)};let f;const g=()=>a("close-preview"),b=(i,l)=>{const t=["imageFit","deletable","previewSize","beforeDelete"],s=m(_(e,t),_(i,t,!0));return d(B,S({item:i,index:l,onClick:()=>a("click-preview",i,r(l)),onDelete:()=>((o,i)=>{const l=e.modelValue.slice(0);l.splice(i,1),a("update:modelValue",l),a("delete",o,r(i))})(i,l),onPreview:()=>(a=>{if(e.previewFullImage){const o=e.modelValue.filter(O),i=o.map((e=>(e.file&&!e.url&&"failed"!==e.status&&(e.url=URL.createObjectURL(e.file),n.push(e.url)),e.url))).filter(Boolean);f=R(m({images:i,startPosition:o.indexOf(a),onClose:g},e.previewOptions))}})(i)},_(e,["name","lazyLoad"]),s),_(o,["preview-cover","preview-delete"]))},w=()=>{if(e.previewImage)return e.modelValue.map(b)},F=e=>a("click-upload",e),A=()=>{if(e.modelValue.length>=e.maxCount||!e.showUpload)return;const a=e.readonly?null:d("input",{ref:l,type:"file",class:C("input"),accept:e.accept,capture:e.capture,multiple:e.multiple,disabled:e.disabled,onChange:v},null);return o.default?d("div",{class:C("input-wrapper"),onClick:F},[o.default(),a]):d("div",{class:C("upload",{readonly:e.readonly}),style:c(e.previewSize),onClick:F},[d(s,{name:e.uploadIcon,class:C("upload-icon")},null),e.uploadText&&d("span",{class:C("upload-text")},[e.uploadText]),a])};return k((()=>{n.forEach((e=>URL.revokeObjectURL(e)))})),h({chooseFile:()=>{l.value&&!e.disabled&&l.value.click()},closeImagePreview:()=>{f&&f.close()}}),y((()=>e.modelValue)),()=>d("div",{class:C()},[d("div",{class:C("wrapper",{disabled:e.disabled})},[w(),A()])])}});e("U",F(D))}}}));
