import{_ as a,d as t,u as e,Y as l,s,c as i,F as d,y as n,b as o,o as r,a as c,t as u,x as g}from"./index-3d21abf8.js";const m=[{title:"定义与范围",data:['本协议适用于商家（以下简称"您"）在本电子商务平台（以下简称"平台"）上进行商业活动的全部行为。',"本协议中所指的平台包括网站、移动应用或其他通过互联网提供的销售平台。"]},{title:"注册与账户",data:["您需要注册一个商家账户，并提供真实、准确、完整的信息。","您将对您的账户和密码保密，对于使用您的账户进行的一切行为和活动，您将承担全部责任。","成为商家之后三个月内不能申请关闭店铺，否则属于违约行为，你将承担全部责任。"]},{title:"商品上架与销售",data:["您可以将符合法律法规和平台规定的商品上架至平台进行销售。","您需要提供商品的真实、准确、完整的描述和信息，并确保商品的质量和安全符合相关标准。","您应当按照平台的规定设置商品价格、库存和发货等信息。（切记发货时间为你收到商品订单后24小时内完成）","店铺最低销售商品种类为10或以上。"]},{title:"订单处理与交付",data:["您应及时处理平台上产生的订单，并按照约定时间和方式交付商品。","您需确保商品的包装、配送等环节符合平台和运输相关规定。"]},{title:"客户服务与投诉处理",data:["您应提供优质的客户服务，解答客户疑问并处理客户投诉。","在发生订单纠纷或客户投诉时，您应积极与平台进行沟通，并配合进行解决。"]},{title:"价格与支付",data:["您应根据平台规定设定商品价格，并确保价格的准确性和透明度。","您应提供安全、可靠的支付方式，确保支付过程的安全性和顺利进行。"]},{title:"退换货与售后服务",data:["您应根据平台规定处理客户的退换货请求，并提供相应的售后服务。","在发生商品质量问题或其他售后纠纷时，您应与客户进行诚实、公正的解决。"]},{title:"知识产权保护",data:["您应尊重他人的知识产权，不侵犯他人的版权、商标权、专利权等。","您应确保您所销售的商品和内容不违反知识产权相关法律法规。"]},{title:"平台规则与合规",data:["您应遵守平台的相关规定和政策，包括但不限于用户协议、服务条款、商家规则等。","您应遵守适用的法律法规，并承担相应的法律责任。"]},{title:"终止与违约处理",data:["平台有权根据您的违约行为采取警告、限制、暂停或终止您的商家账户。","在发生严重违约或法律违规行为时，平台有权采取法律手段追究您的责任。"]},{title:"免责声明",data:["平台不对您的商业活动及其结果承担责任，包括但不限于交易风险、信息安全等方面的风险。","您应自行承担因使用平台而产生的风险，平台不对由此导致的损失承担责任。"]},{title:"其他约定",data:["本协议的解释、有效性、执行和争议解决应适用相关法律法规。","若本协议与平台其他规定存在冲突，以本协议为准。"]}],p={class:"agrrement-content"},v={key:0,class:"info"},f=t({name:"LoginAgreement"}),x=a(Object.assign(f,{setup(a){const{t:t,locale:f}=e(),x=l();return s((()=>{const{lang:a}=x.query;a&&(f.value=a,localStorage.setItem("lang",a))})),(a,e)=>(r(),i("div",p,[(r(!0),i(d,null,n(o(m),((a,e)=>(r(),i("div",{key:e,class:"agree-item"},[c("h3",null,u(o(t)(a.title)),1),a.data.length?(r(),i("div",v,[(r(!0),i(d,null,n(a.data,((a,l)=>(r(),i("p",{key:a},u(`${e+1}.${l+1} ${o(t)(a)}`),1)))),128))])):g("v-if",!0)])))),128))]))}}),[["__scopeId","data-v-711d8683"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/login-agreement/index.vue"]]);export{x as default};
