import{_ as e,i as a,u as l,l as s,Y as i,r as t,aX as o,q as n,cB as r,I as c,c as u,e as d,w as p,F as v,y as m,x as f,a as g,t as h,h as A,aS as x,b as k,A as j,n as y,cC as b,T as w,o as C,aV as L,aF as S,K as U,D as N,E as O,cD as J}from"./index-3d21abf8.js";import{P as E}from"./index-fc51b7d2.js";import{L as B}from"./index-40e83579.js";import{E as I}from"./index-5d897066.js";import{N as T}from"./index-cfdda867.js";/* empty css              *//* empty css               */import{S as V}from"./index-179203f3.js";import{_ as G}from"./search-icon-8ae5f679.js";import{_ as H}from"./more-f6d49895.js";import{e as Y}from"./editProfit-723f706e.js";import{f as z,g as M}from"./product.api-7fdfc848.js";import{c as P}from"./index-3ab60a77.js";import"./use-id-a0619e01.js";import"./use-placeholder-c97cb410.js";import"./index-8c1841f6.js";import"./index-7dfcb82a.js";import"./use-route-cd41a893.js";import"./index-2406f514.js";import"./index-573e22f7.js";import"./index-6aaac5d3.js";import"./index-d27cfcd1.js";import"./index-4ac00735.js";import"./function-call-78245787.js";import"./index-cfaf3bc2.js";import"./stringify-d53955b2.js";import"./___vite-browser-external_commonjs-proxy-9405bfc0.js";const R=e=>(N("data-v-77847b5f"),e=e(),O(),e),_=R((()=>g("img",{class:"search-icon",src:G},null,-1))),K=R((()=>g("div",{class:"fixed-header-spacer"},null,-1))),q={key:0,class:"result-list pl-4 pr-4"},D=["onClick"],F={key:1,class:"search-history"},Q={class:"title"},X=R((()=>g("img",{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAwCAYAAABqkJjhAAAACXBIWXMAACE4AAAhOAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAKESURBVHgB7Zk9btswFMefZHu0obGeqt7AtT14q3qC+gZtTpD4BE5u0Gzd7J6g6tipymbAH0lOEGXyGO2G7fwZUAmT6ItiqMAAf4BASnoE/6RIPurRIkU6nY6LxC1oHl0BUsCikvR6vSGSMa6OTLn9fh8iOVutVlMqQY1K0O12x5Zl/UL2A0mCcg6uYbvddtbr9T+SRLqHIfYHKpwIjyL02i1L88qi3EcShg/KjdDTP0mCMoJvULHLb/1ms3kUBEGu2Jh+v38KoWN+G223208Y1oXL2yQBm2CC2KjRaIxkxDIWi8UpPX0Np16vS80BKcH0/HNez2azkMrxN87sdjuXJJAV/O4cnOCHSYex6dm27eUZYxi4sPvOb0Pc/6YSYB58o6f122fDK68Mhg7zOb7FJlKtVruhAwAN/cqGhEMHAr6E8zAkmDMgYQVAS46JNwRG51TAKbwVqHssCDwTXoXMnSc6DjQg5F6J+MIeUkVgj7KP88vl8pU+s6zpxgjWjRGsmzopAk/J1mv2uxSkLX/8v8/LsimKcg/DrU/4dcnFv67Etv9zmz+kiLJgeKPPPOtQipsXNv1Sm/UkzKTTjRGsGyNYN0awboxg3RjBulEWjI1NkSBM9CItjbJgbB1H2LFdQPgoY697xGyYLSmivIGfz+fskMXLskF8wUfi0xtgJp1ujGDdJArGjL+L8zg0caki+N91TOISmNbDjxFxLEfHVBFCdJ/SovKJyxp6eIoCceEhwq8ThOxLHQ8UxIHYL6j3RHg2TTJMPVhEnJadUFbWuyLMySB47SW9S510WOxZa8+pevxWqzVMe5l7dDsYDNzNZnOCVisHQbLAcGBR/ylOSoMsu3tYs9QwhJVNvQAAAABJRU5ErkJggg==",alt:"",class:"delete-icon"},null,-1))),$={key:0,class:"content"},W=["onClick"],Z={key:2,class:"list ml-4 mr-4 mt-4 mb-4"},ee=["onClick"],ae={class:"flex-1 flex left"},le={class:"product-img-wrap w-20 h-20",style:{overflow:"hidden"}},se=["src"],ie={class:"product-info"},te={class:"name"},oe={class:"Specification"},ne={key:0,style:{"margin-right":"20px"}},re={key:1},ce={class:"money"},ue={class:"item-more-content"},de=["onClick"],pe=[R((()=>g("img",{class:"more-icon",src:H},null,-1)))],ve=e({__name:"index",setup(e){const N=a(),{t:O}=l(),G=s(),H=i(),R=t(H.query.id);let ve=t(""),me=t(1),fe=t(""),ge=t(!1);t({});const he=t([]),Ae=t([]),xe=t(!1),ke=t(!1),je=t(!1);let ye=t([]);const be=()=>{},we=new URL("/www/png/name-20d65991.png",self.location),Ce=t(null);o((()=>{Ce.value})),n((()=>{R.value/1==1?r("searchOne")&&(ye.value=r("searchOne")):r("searchTwo")&&(ye.value=r("searchTwo"))}));const Le=J((()=>{ve.value.trim()?(me.value=1,he.value=[],w.loading({forbidClick:!0,loadingType:"spinner"}),Te(),Se()):w({message:O("请输入搜索关键字"),duration:2e3})}),500),Se=()=>{const e=ve.value.trim(),a=R.value/1==1?"searchOne":"searchTwo";if(""!=e)if(0==ye.value.length)ye.value.push(e),b(a,ye.value);else{const l=P(ye.value);l.unshift(e);const s=[...new Set(l)];ye.value=s,b(a,s)}},Ue=()=>{R.value/1==1?b("searchOne",[]):b("searchTwo",[])},Ne=()=>{ve.value=""},Oe=e=>{ye.value=[],R.value/1==1?b("searchOne",""):b("searchTwo","")},Je=()=>{xe.value=!1,Le()},Ee=()=>{let e={pageNum:me.value,pageSize:20,keyword:ve.value};z(e).then((e=>{w.clear(),me.value++;for(let a=0;a<e.pageList.length;a++)e.pageList[a].check=!1,he.value.push(e.pageList[a]);ke.value=!1,e.pageList.length<20?(me.value>1&&w(O("没有更多数据")),je.value=!0):me.value++}))},Be=()=>{let e={name:ve.value,categoryId:"",pageNum:me.value,pageSize:20};M(e).then((e=>{w.clear();for(let a=0;a<e.pageList.length;a++)he.value.push(e.pageList[a]);ke.value=!1,e.pageList.length<20?(me.value>1&&w(O("没有更多数据")),je.value=!0):me.value++}))},Ie=t(null);R.value/1==2?Ie.value=Be:Ie.value=Ee;const Te=()=>{Ie.value()};let Ve=t([]);const Ge=(e,a)=>{e.sellingPrice=a},He=()=>{ge.value=!1};return(e,a)=>{const l=c,s=V,i=T,t=I,o=B,n=E;return C(),u("div",{class:y(["search-container page-main-content has-fixed-header",{"is-ar":k(N)}])},[d(i,{fixed:"","left-arrow":"",onClickLeft:a[2]||(a[2]=()=>e.$router.back())},{title:p((()=>[f('        <van-search v-model="keyword" shape="round" @blur="record" @update:model-value="search" :clearable="false"'),f('          placeholder="请输入商品" @input="inputHandle">'),d(s,{modelValue:k(ve),"onUpdate:modelValue":a[0]||(a[0]=e=>L(ve)?ve.value=e:ve=e),shape:"round",clearable:!1,placeholder:e.$t("请输入商品"),onInput:be},{"left-icon":p((()=>[_])),"right-icon":p((()=>[k(ve)?(C(),j(l,{key:0,name:"cross",onClick:Ne,size:"14",color:"#333333"})):f("v-if",!0)])),_:1},8,["modelValue","placeholder"])])),right:p((()=>[g("div",{onClick:a[1]||(a[1]=(...e)=>k(Le)&&k(Le)(...e))},h(e.$t("搜索")),1)])),_:1}),K,Ae.value.length>0?(C(),u("div",q,[(C(!0),u(v,null,m(Ae.value,((e,a)=>(C(),u("div",{class:"result-list-item pt-2 pb-2",onClick:a=>(e=>{ve.value="",Ae.value=[],fe.value=e.goodsId,me.value=1,he.value=[],Te()})(e),key:a},h(e.name),9,D)))),128))])):f("v-if",!0),0==he.value.length?(C(),u("div",F,[g("div",Q,[g("p",null,h(e.$t("历史搜索")),1),A(g("div",{class:"clear",onClick:Ue},[X,g("p",{onClick:Oe},h(e.$t("清空")),1)],512),[[x,k(ye).length>0]])]),k(ye).length>0?(C(),u("div",$,[(C(!0),u(v,null,m(k(ye),((e,a)=>(C(),u("div",{key:a,class:"item",onClick:a=>(e=>{ve.value=e,Le()})(e)},h(e),9,W)))),128))])):f("v-if",!0),k(ye).length?f("v-if",!0):(C(),j(t,{key:1,image:k(we).href,description:k(O)("noData")},null,8,["image","description"]))])):f("v-if",!0),he.value.length>0?(C(),u("div",Z,[d(n,{"loading-text":e.$t("加载中"),"loosing-text":e.$t("释放以刷新"),"pulling-text":e.$t("下拉以刷新"),modelValue:ke.value,"onUpdate:modelValue":a[4]||(a[4]=e=>ke.value=e),onRefresh:Je},{default:p((()=>[d(o,{ref_key:"checkEl",ref:Ce,loading:ke.value,"onUpdate:loading":a[3]||(a[3]=e=>ke.value=e),finished:je.value,"finished-text":e.$t("没有更多了"),onLoad:Te},{default:p((()=>[(C(!0),u(v,null,m(he.value,((e,a)=>(C(),u("div",{class:"item pl-3 pr-3 pb-3 pt-3 flex",onClick:a=>(e=>{R.value/1==1&&G.push({path:"/productPage/details",query:{item:JSON.stringify(e)}})})(e),key:a},[g("div",ae,[g("div",le,[g("img",{class:"product-img",src:e.imgUrl1},null,8,se),f('                <div class="delete-wrap" @click.stop="deleteGood(item)">删除</div>')]),g("div",ie,[g("div",te,h(e.name),1),g("div",oe,[e.categoryName?(C(),u("span",ne,h(e.categoryName),1)):f("v-if",!0),f(" <span>{{t('product.4')}}: {{ item.unit || '-' }}</span> "),R.value/1==1?(C(),u("span",re,h(k(O)("sales"))+": "+h(e.soldNum),1)):f("v-if",!0)]),g("div",ce,"$"+h(R.value/1==1?k(S)(e.sellingPrice):k(S)(e.systemPrice)),1)])]),g("div",ue,[g("div",{class:"more",onClick:U((a=>(e=>{R.value/1==1?G.push({path:"/productPage/productEdit",query:{item:JSON.stringify(e)}}):(Ve.value=[],Ve.value.push(e.id),ge.value=!0)})(e)),["stop"])},pe,8,de),R.value/1==1?(C(),j(l,{key:0,name:"arrow",size:"20px",style:{top:"50%",right:"0",position:"absolute","margin-top":"-10px"}})):f("v-if",!0)])],8,ee)))),128))])),_:1},8,["loading","finished","finished-text"])])),_:1},8,["loading-text","loosing-text","pulling-text","modelValue"])])):f("v-if",!0),d(Y,{isEdit:k(ge),onUpdate:Ge,productArry:k(Ve),onClose:He},null,8,["isEdit","productArry"])],2)}}},[["__scopeId","data-v-77847b5f"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/search/index.vue"]]);export{ve as default};
