import{P as e,ae as a,a3 as l,d as i,S as t,bc as s,e as r,I as n,W as o,aE as d,ad as c,bf as u,Q as p,a4 as m,R as f,ai as v,bl as g,r as w,aN as b,a9 as y,bF as x,a2 as h,aY as z,ag as S,p as k,X as F}from"./index-3d21abf8.js";import{I as R}from"./index-7d1632e5.js";import{I as A}from"./index-a439655d.js";const[V,I,j]=e("uploader");function C(e,a){return new Promise((l=>{if("file"===a)return void l();const i=new FileReader;i.onload=e=>{l(e.target.result)},"dataUrl"===a?i.readAsDataURL(e):"text"===a&&i.readAsText(e)}))}function L(e,i){return a(e).some((e=>!!e.file&&(l(i)?i(e.file):e.file.size>i)))}const U=/\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i;function O(e){return!!e.isImage||(e.file&&e.file.type?0===e.file.type.indexOf("image"):e.url?(a=e.url,U.test(a)):"string"==typeof e.content&&0===e.content.indexOf("data:image"));var a}var B=i({props:{name:t,item:s(Object),index:Number,imageFit:String,lazyLoad:Boolean,deletable:Boolean,previewSize:[Number,String,Array],beforeDelete:Function},emits:["delete","preview"],setup(e,{emit:a,slots:l}){const i=()=>{const{status:a,message:l}=e.item;if("uploading"===a||"failed"===a){const e="failed"===a?r(n,{name:"close",class:I("mask-icon")},null):r(o,{class:I("loading")},null),i=c(l)&&""!==l;return r("div",{class:I("mask")},[e,i&&r("div",{class:I("mask-message")},[l])])}},t=l=>{const{name:i,item:t,index:s,beforeDelete:r}=e;l.stopPropagation(),u(r,{args:[t,{name:i,index:s}],done:()=>a("delete")})},s=()=>a("preview"),m=()=>{if(e.deletable&&"uploading"!==e.item.status){const e=l["preview-delete"];return r("div",{role:"button",class:I("preview-delete",{shadow:!e}),tabindex:0,"aria-label":j("delete"),onClick:t},[e?e():r(n,{name:"cross",class:I("preview-delete-icon")},null)])}},f=()=>{if(l["preview-cover"]){const{index:a,item:i}=e;return r("div",{class:I("preview-cover")},[l["preview-cover"](p({index:a},i))])}},v=()=>{const{item:a,lazyLoad:l,imageFit:i,previewSize:t}=e;return O(a)?r(A,{fit:i,src:a.content||a.url,class:I("preview-image"),width:Array.isArray(t)?t[0]:t,height:Array.isArray(t)?t[1]:t,lazyLoad:l,onClick:s},{default:f}):r("div",{class:I("file"),style:d(e.previewSize)},[r(n,{class:I("file-icon"),name:"description"},null),r("div",{class:[I("file-name"),"van-ellipsis"]},[a.file?a.file.name:a.url]),f()])};return()=>r("div",{class:I("preview")},[v(),i(),m()])}});const P=F(i({name:V,props:{name:m(""),accept:f("image/*"),capture:String,multiple:Boolean,disabled:Boolean,readonly:Boolean,lazyLoad:Boolean,maxCount:m(1/0),imageFit:f("cover"),resultType:f("dataUrl"),uploadIcon:f("photograph"),uploadText:String,deletable:v,afterRead:Function,showUpload:v,modelValue:g(),beforeRead:Function,beforeDelete:Function,previewSize:[Number,String,Array],previewImage:v,previewOptions:Object,previewFullImage:v,maxSize:{type:[Number,String,Function],default:1/0}},emits:["delete","oversize","click-upload","close-preview","click-preview","update:modelValue"],setup(e,{emit:l,slots:i}){const t=w(),s=[],o=(a=e.modelValue.length)=>({name:e.name,index:a}),c=()=>{t.value&&(t.value.value="")},u=i=>{if(c(),L(i,e.maxSize)){if(!Array.isArray(i))return void l("oversize",i,o());{const a=function(e,a){const l=[],i=[];return e.forEach((e=>{L(e,a)?i.push(e):l.push(e)})),{valid:l,invalid:i}}(i,e.maxSize);if(i=a.valid,l("oversize",a.invalid,o()),!i.length)return}}i=k(i),l("update:modelValue",[...e.modelValue,...a(i)]),e.afterRead&&e.afterRead(i,o())},m=a=>{const{maxCount:l,modelValue:i,resultType:t}=e;if(Array.isArray(a)){const e=+l-i.length;a.length>e&&(a=a.slice(0,e)),Promise.all(a.map((e=>C(e,t)))).then((e=>{const l=a.map(((a,l)=>{const i={file:a,status:"",message:""};return e[l]&&(i.content=e[l]),i}));u(l)}))}else C(a,t).then((e=>{const l={file:a,status:"",message:""};e&&(l.content=e),u(l)}))},f=a=>{const{files:l}=a.target;if(e.disabled||!l||!l.length)return;const i=1===l.length?l[0]:[].slice.call(l);if(e.beforeRead){const a=e.beforeRead(i,o());if(!a)return void c();if(h(a))return void a.then((e=>{m(e||i)})).catch(c)}m(i)};let v;const g=()=>l("close-preview"),F=(a,t)=>{const n=["imageFit","deletable","previewSize","beforeDelete"],d=p(z(e,n),z(a,n,!0));return r(B,S({item:a,index:t,onClick:()=>l("click-preview",a,o(t)),onDelete:()=>((a,i)=>{const t=e.modelValue.slice(0);t.splice(i,1),l("update:modelValue",t),l("delete",a,o(i))})(a,t),onPreview:()=>(a=>{if(e.previewFullImage){const l=e.modelValue.filter(O),i=l.map((e=>(e.file&&!e.url&&"failed"!==e.status&&(e.url=URL.createObjectURL(e.file),s.push(e.url)),e.url))).filter(Boolean);v=R(p({images:i,startPosition:l.indexOf(a),onClose:g},e.previewOptions))}})(a)},z(e,["name","lazyLoad"]),d),z(i,["preview-cover","preview-delete"]))},A=()=>{if(e.previewImage)return e.modelValue.map(F)},V=e=>l("click-upload",e),j=()=>{if(e.modelValue.length>=e.maxCount||!e.showUpload)return;const a=e.readonly?null:r("input",{ref:t,type:"file",class:I("input"),accept:e.accept,capture:e.capture,multiple:e.multiple,disabled:e.disabled,onChange:f},null);return i.default?r("div",{class:I("input-wrapper"),onClick:V},[i.default(),a]):r("div",{class:I("upload",{readonly:e.readonly}),style:d(e.previewSize),onClick:V},[r(n,{name:e.uploadIcon,class:I("upload-icon")},null),e.uploadText&&r("span",{class:I("upload-text")},[e.uploadText]),a])};return b((()=>{s.forEach((e=>URL.revokeObjectURL(e)))})),y({chooseFile:()=>{t.value&&!e.disabled&&t.value.click()},closeImagePreview:()=>{v&&v.close()}}),x((()=>e.modelValue)),()=>r("div",{class:I()},[r("div",{class:I("wrapper",{disabled:e.disabled})},[A(),j()])])}}));export{P as U};
