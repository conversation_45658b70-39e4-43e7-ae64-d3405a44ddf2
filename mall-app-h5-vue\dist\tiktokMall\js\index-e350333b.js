import{_ as e,j as a,u as l,Y as s,l as t,r as i,q as n,i as u,m as o,av as r,c,e as v,w as d,a as f,t as p,b as m,x as y,f as h,n as x,T as b,ch as g,o as j,cg as _,F as I,D as k,E as C}from"./index-3d21abf8.js";import{B as V}from"./index-2406f514.js";/* empty css              *//* empty css               */import{a as q}from"./login.api-cb7fcde3.js";import{F as w}from"./index-8c1841f6.js";import"./use-route-cd41a893.js";import"./index-7dfcb82a.js";import"./use-id-a0619e01.js";const U={class:"verify-content"},D=(e=>(k("data-v-1ca2bd92"),e=e(),C(),e))((()=>f("div",{style:{height:"46px"}},null,-1))),F={class:"info-content"},B={class:"info-item"},E={class:"info-item"},N={class:"gap"},S={key:0},T={class:"submit-content"},Y=e({__name:"index",setup(e){const k=a(),{t:C}=l(),Y=s(),z=t(),A=i(1),G=i("44"),H=i(""),J=i(0),K=i(null),L=i(!1);A.value=Y.query&&Y.query.type?Number(Y.query.type):1,n((()=>{clearInterval(K.value),K.value=null}));const M=u(),O=o((()=>{if(k.userInfo.token){const{email:e,phone:a}=k.userInfo;let l="";if(1===A.value){if(a){const e=a.split(" ");2===e.length?(G.value=e[0],l=e[1]):l=e[0]}}else l=e||"";return l}z.push("/login")})),P=()=>{if(J.value>0)return!1;const{email:e,phone:a}=k.userInfo;b.loading({duration:0,forbidClick:!0}),q({target:1===A.value?a:e}).then((()=>{b(C("sendSuccess")),J.value=60,K.value=setInterval((()=>{J.value>0?J.value=J.value-1:(J.value=0,clearInterval(K.value),K.value=null)}),1e3)})).catch((()=>{b.clear()}))},Q=async()=>{if(""===H.value)return void b(C("entryVerifyCode"));L.value=!0;const{email:e,phone:a}=k.userInfo,l={target:1===A.value?a:e,verifcode:H.value};1===A.value?l.phone=a:l.email=e,g(l).then((async e=>{await k.getUserInfo(!0),L.value=!1,z.back()})).catch((()=>{L.value=!1}))};return(e,a)=>{const l=r("fx-header"),s=w,t=V;return j(),c("div",U,[v(l,{fixed:!0},{title:d((()=>[h(p(1===A.value?m(C)("手机验证"):m(C)("邮箱验证")),1)])),_:1}),D,f("div",F,[f("div",B,[f("p",null,p(m(C)("为了保障您的账号安全，请验证后进行下一步操作")),1)]),f("div",E,[f("p",N,p(1===A.value?m(C)("当前绑定手机号"):m(C)("当前绑定邮箱")),1),f("h2",null,[1===A.value?(j(),c("span",S,"(+"+p(G.value)+")",1)):y("v-if",!0),h(p(m(_)(m(O),!1)),1)])]),f("div",{class:x(["code-content",{"is-ar":m(M)}])},[v(s,{modelValue:H.value,"onUpdate:modelValue":a[0]||(a[0]=e=>H.value=e),type:"tel",label:"",placeholder:m(C)("entryVerifyCode")},null,8,["modelValue","placeholder"]),f("div",{class:x(["btn",{"is-ar":m(M)}])},[v(t,{type:"primary",onClick:P},{default:d((()=>[h(p(m(C)("sendVerifyCode")),1),J.value?(j(),c(I,{key:0},[h("("+p(J.value)+")s",1)],64)):y("v-if",!0)])),_:1})],2)],2)]),f("div",T,[v(t,{type:"primary",loading:L.value,onClick:Q},{default:d((()=>[h(p(m(C)("confirm")),1)])),_:1},8,["loading"])])])}}},[["__scopeId","data-v-1ca2bd92"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/certified/index.vue"]]);export{Y as default};
