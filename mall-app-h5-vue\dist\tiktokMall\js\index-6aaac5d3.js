import{P as t,ai as s,d as e,e as i,F as a,ag as l,bd as n,X as r}from"./index-3d21abf8.js";const[o,d]=t("cell-group");const c=r(e({name:o,inheritAttrs:!1,props:{title:String,inset:Boolean,border:s},setup(t,{slots:s,attrs:e}){const r=()=>{var a;return i("div",l({class:[d({inset:t.inset}),{[n]:t.border&&!t.inset}]},e),[null==(a=s.default)?void 0:a.call(s)])};return()=>t.title||s.title?i(a,null,[i("div",{class:d("title",{inset:t.inset})},[s.title?s.title():t.title]),r()]):r()}}));export{c as C};
