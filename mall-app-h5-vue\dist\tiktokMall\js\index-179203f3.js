import{P as a,Q as e,R as l,ai as c,d as o,r as n,a9 as t,e as s,aY as i,ag as r,V as u,X as d}from"./index-3d21abf8.js";import{f as b,F as f}from"./index-8c1841f6.js";import{u as p}from"./use-id-a0619e01.js";const[h,k,m]=a("search");const g=d(o({name:h,props:e({},b,{label:String,shape:l("square"),leftIcon:l("search"),clearable:c,actionText:String,background:String,showAction:<PERSON><PERSON>an}),emits:["blur","focus","clear","search","cancel","click-input","click-left-icon","click-right-icon","update:modelValue"],setup(a,{emit:l,slots:c,attrs:o}){const d=p(),h=n(),g=()=>{c.action||(l("update:modelValue",""),l("cancel"))},v=e=>{13===e.keyCode&&(u(e),l("search",a.modelValue))},x=()=>a.id||`${d}-input`,C=()=>{if(c.label||a.label)return s("label",{class:k("label"),for:x()},[c.label?c.label():a.label])},V=()=>{if(a.showAction){const e=a.actionText||m("cancel");return s("div",{class:k("action"),role:"button",tabindex:0,onClick:g},[c.action?c.action():e])}},y=a=>l("blur",a),j=a=>l("focus",a),w=a=>l("clear",a),S=a=>l("click-input",a),A=a=>l("click-left-icon",a),B=a=>l("click-right-icon",a),F=Object.keys(b),T=()=>{const n=e({},o,i(a,F),{id:x()});return s(f,r({ref:h,type:"search",class:k("field"),border:!1,onBlur:y,onFocus:j,onClear:w,onKeypress:v,"onClick-input":S,"onClick-left-icon":A,"onClick-right-icon":B,"onUpdate:modelValue":a=>l("update:modelValue",a)},n),i(c,["left-icon","right-icon"]))};return t({focus:()=>{var a;return null==(a=h.value)?void 0:a.focus()},blur:()=>{var a;return null==(a=h.value)?void 0:a.blur()}}),()=>{var e;return s("div",{class:k({"show-action":a.showAction}),style:{background:a.background}},[null==(e=c.left)?void 0:e.call(c),s("div",{class:k("content",a.shape)},[C(),T()]),V()])}}}));export{g as S};
