import{_ as s,d as a,i as t,u as e,Y as i,l,r as o,s as n,aF as d,av as r,c,e as u,w as f,n as v,a as m,t as p,L as g,x as C,F as I,y as h,T as x,o as y,f as b,D as H,E as w}from"./index-3d21abf8.js";import{u as V}from"./index-54dce367.js";import{s as L}from"./config-22c2c72a.js";import{d as k,o as D}from"./goods.api-9b070dd6.js";const _=a({name:"RefundRequestDetails",setup(){const s=t(),{t:a}=e(),{toClipboard:r}=V(),c=i(),u=l(),{id:f}=c.query,v=o([]),m=o(null);return n((()=>{f?(x.loading({duration:0,message:a("loading"),forbidClick:!0}),k({orderId:f}).then((s=>{const a=Number(s.returnStatus),t=L.find((s=>s.id===a));s.statusTxt=t.txt,s.statusColor=t.color,m.value=s})),D({orderId:f}).then((s=>{const a=s.pageList;v.value=a||[]}))):(x(a("参数错误")),setTimeout((()=>{u.back()}),1e3))})),{t:a,detailsInfo:m,isArLang:s,copyHandle:async s=>{try{await r(s),x(a("copySuccess"))}catch(t){}},goodsListData:v,numberStrFormat:d}}}),F=s=>(H("data-v-c27dceb0"),s=s(),w(),s),R=F((()=>m("div",{style:{height:"46px"}},null,-1))),S={class:"item"},T={class:"info-item"},j={class:"info-item"},q={class:"copy"},A=[F((()=>m("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M8.00416 3.0188C6.90246 3.0188 6.00935 3.91191 6.00935 5.01361V6.00834H5.01337C3.91166 6.00834 3.01855 6.90145 3.01855 8.00315V12.9859C3.01855 14.0876 3.91166 14.9807 5.01337 14.9807H9.99608C11.0978 14.9807 11.9909 14.0876 11.9909 12.9859V11.9911H12.9869C14.0886 11.9911 14.9817 11.098 14.9817 9.99632V5.01361C14.9817 3.91191 14.0886 3.0188 12.9869 3.0188H8.00416ZM11.9909 10.9911H12.9869C13.5363 10.9911 13.9817 10.5457 13.9817 9.99632V5.01361C13.9817 4.46419 13.5363 4.0188 12.9869 4.0188H8.00416C7.45474 4.0188 7.00935 4.46419 7.00935 5.01361V6.00834H9.99608C11.0978 6.00834 11.9909 6.90145 11.9909 8.00315V10.9911ZM4.01855 8.00315C4.01855 7.45373 4.46395 7.00834 5.01337 7.00834H9.99608C10.5455 7.00834 10.9909 7.45373 10.9909 8.00315V12.9859C10.9909 13.5353 10.5455 13.9807 9.99608 13.9807H5.01337C4.46395 13.9807 4.01855 13.5353 4.01855 12.9859V8.00315Z",fill:"#333"},null,-1)))],M={class:"info-item"},N={class:"info-item"},Z={class:"info-item"},$={class:"info-item"},B={class:"poster"},E=["src"],P={class:"info"},U={class:"name"},X={class:"num"},Y={class:"price"};const z=s(_,[["render",function(s,a,t,e,i,l){const o=r("fx-header");return y(),c("div",null,[u(o,{fixed:!0},{title:f((()=>[b(p(s.$t("退款详情")),1)])),_:1}),R,s.detailsInfo?(y(),c("div",{key:0,class:v(["list-content",{"is-ar":s.isArLang}])},[m("div",S,[m("div",T,[m("p",null,p(s.t("申请时间")),1),m("div",null,p(s.detailsInfo.refundTime),1)]),m("div",j,[m("p",null,p(s.t("退款单号")),1),m("div",q,[m("p",null,p(s.detailsInfo.id),1),(y(),c("svg",{onClick:a[0]||(a[0]=a=>s.copyHandle(s.detailsInfo.id)),width:"18",height:"18",viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg"},A))])]),m("div",M,[m("p",null,p(s.t("退款金额")),1),m("div",null,"$"+p(s.numberStrFormat(s.detailsInfo.returnPrice)),1)]),m("div",N,[m("p",null,p(s.t("退款状态")),1),m("div",{style:g({color:s.detailsInfo.statusColor})},p(s.t(s.detailsInfo.statusTxt)),5)]),m("div",Z,[m("p",null,p(s.t("退款理由")),1),m("div",null,p(s.detailsInfo.returnReason),1)]),m("div",$,[m("p",null,p(s.t("退款说明")),1),m("div",null,p(s.detailsInfo.returnDetail||s.t("无")),1)])])],2)):C("v-if",!0),s.detailsInfo&&s.goodsListData.length?(y(),c("div",{key:1,class:v(["goods-content",{"is-ar":s.isArLang}])},[(y(!0),c(I,null,h(s.goodsListData,(a=>(y(),c("div",{key:a.id,class:"item"},[m("div",B,[m("img",{src:a.goodsIcon,alt:""},null,8,E)]),m("div",P,[m("p",U,p(a.goodsName),1),m("div",X,[m("p",Y,"$"+p(s.numberStrFormat(a.goodsReal)),1),m("p",null,"X"+p(a.goodsNum),1)])])])))),128))],2)):C("v-if",!0)])}],["__scopeId","data-v-c27dceb0"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/refundRequest/details/index.vue"]]);export{z as default};
