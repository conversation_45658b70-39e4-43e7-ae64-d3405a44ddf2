System.register(["./index-legacy-46a00900.js","./index-legacy-72e00c5f.js","./index-legacy-c989a436.js","./index-legacy-e952cf7f.js","./login.api-legacy-d31fdc92.js","./nationalityList-legacy-4b6c8c35.js","./index-legacy-bfbc73de.js","./index-legacy-bbd15202.js","./use-route-legacy-be86ac1c.js","./index-legacy-f9c0699e.js","./index-legacy-f9c0699e2.js","./index-legacy-0ade4760.js","./index-legacy-df3e01aa.js","./use-id-legacy-df76950f.js","./index-legacy-322f21cd.js","./index-legacy-ff56f089.js","./countryList-legacy-94cb363f.js"],(function(e,a){"use strict";var l,t,i,o,u,n,d,s,r,v,c,p,g,x,y,f,h,b,m,A,w,k,C,V,j,E,B,S,$,T,U,I,z,F,_,q,D,H=document.createElement("style");return H.textContent=".bindVerify[data-v-59a3607d]{width:100%;min-height:100vh;box-sizing:border-box}.content[data-v-59a3607d]{font-size:12px;padding:0 16px}.iptbox[data-v-59a3607d]{height:44px;margin-top:8px;padding:0 10px 0 20px;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;align-items:center;border-radius:3px}.iptbox input[data-v-59a3607d]{-webkit-box-flex:1;-webkit-flex:1 1;flex:1 1;height:100%;border:none}.iptbox input[data-v-59a3607d]::-webkit-input-placeholder{color:#868c9a}.iptbox span[data-v-59a3607d]{color:#1d91ff}.imgbox[data-v-59a3607d]{border:1px solid #E5E7ED;padding:5px;width:182px;height:182px;box-sizing:border-box}.imgbox img[data-v-59a3607d]{width:100%;height:100%}.code[data-v-59a3607d]{font-size:15px;font-weight:300;line-height:18px;margin-top:22px;height:18px}.code img[data-v-59a3607d]{width:14px;height:14px;margin-left:5px}.tips[data-v-59a3607d]{margin-top:10px;color:#999}.copy[data-v-59a3607d]{border-radius:4px;width:132px;height:40px;margin-top:16px;border:1px solid #E5E7ED;line-height:40px}.bottom[data-v-59a3607d]{padding:20px 16px 7px}.bottom p[data-v-59a3607d]{padding-bottom:13px}.van-password-input[data-v-59a3607d]{margin:0}.van-password-input__security li[data-v-59a3607d],[data-v-59a3607d] .van-password-input__security li{background:#F5F5F5;width:50px;height:50px;color:#333}[data-v-59a3607d] .van-button--primary{background-color:var(--site-main-color);border-color:var(--site-main-color);border-radius:4px}[data-v-59a3607d] .inputBackground{background-color:#fff!important;border-radius:4px;border:1px solid #eee}\n",document.head.appendChild(H),{setters:[e=>{l=e._,t=e.Y,i=e.l,o=e.u,u=e.r,n=e.j,d=e.q,s=e.cn,r=e.g,v=e.av,c=e.W,p=e.c,g=e.e,x=e.w,y=e.a,f=e.x,h=e.t,b=e.h,m=e.v,A=e.f,w=e.F,k=e.b,C=e.T,V=e.co,j=e.cp,E=e.cq,B=e.o,S=e.D,$=e.E},e=>{T=e.B},e=>{U=e.P,I=e.N},e=>{z=e.E},e=>{F=e.a},e=>{_=e.n},e=>{q=e.u},e=>{D=e.I},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{}],execute:function(){const a={class:"bindVerify h-full bg-white"},H={class:"content"},N={key:0},R={style:{"margin-top":"22px"}},O={class:"label mt-2 textColor"},P={class:"iptbox inputBackground"},Y=["placeholder"],Z={style:{"margin-top":"22px"}},J={key:1},X={class:"pl-30 pr-30 text-center flex flex-col items-center justify-center mt40"},G={class:"imgbox"},W={class:"code flex items-center justify-center textColor"},K=["src"],M={class:"tips"},Q={class:"flex justify-between mt-6 mb-3"},L={class:"flex items-center"},ee=(e=>(S("data-v-59a3607d"),e=e(),$(),e))((()=>y("div",{class:"mt-2"},null,-1))),ae={class:"mt-5 bottom tabBackground textColor"};e("default",l({__name:"index",setup(e){const l=t(),S=i(),{t:$}=o(),{toClipboard:le}=q(),te=u(""),ie=u(""),oe=u(""),ue=u(!1),ne=u(0),de=u(""),se=u(""),re=u(""),ve=u(!1),ce=u("");u(!0);const pe=u(""),ge=u(0),xe=u(null),ye=u(0),fe=u(""),he=n(),be=u(!1);d((()=>{me(),clearInterval(xe.value),xe.value=null}));const me=()=>{ne.value=l.query.type,de.value=l.query.verifyCode||"",1==ne.value?(te.value=$("bindPhone"),ue.value=!0):2==ne.value?te.value=$("bindEmail"):3==ne.value&&(te.value=$("googleAuthenticatorEn"),je())},Ae=async()=>{if(2!=ne.value||""!=ie.value)if(2!=ne.value||/^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(ie.value))if(1!=ne.value||""!=ie.value)if(1!=ne.value||/^[0-9]+$/.test(ie.value)){if(ye.value>0)return!1;C.loading({mask:!0}),await V({target:1==ne.value?`${ge.value} ${ie.value}`:ie.value}),F({target:1==ne.value?`${ge.value} ${ie.value}`:ie.value}).then((e=>{C($("sendSuccess")),ye.value=30,xe.value=setInterval((()=>{ye.value>0?ye.value=ye.value-1:(ye.value=0,clearInterval(xe.value),xe.value=null)}),1e3)})).catch((()=>{C.clear()}))}else C($("请输入正确的手机号码"));else C($("entryPhone"));else C($("请输入正确的邮箱地址"));else C($("entryEmail"))},we=()=>{2!=ne.value||""!=ie.value?1!=ne.value||""!=ie.value?""!=oe.value?(be.value=!0,1==ne.value?Ve():2==ne.value?Ce():3==ne.value&&Ee()):C($("请输入登录密码")):C($("entryPhone")):C($("entryEmail"))},ke=async()=>{await he.getUserInfo(!0),C($("bindSuccess")),be.value=!1,setTimeout((()=>{l.query.reset?S.go(-2):S.back()}),1e3)},Ce=()=>{j({target:ie.value,email:ie.value,verifcode:se.value,password:oe.value,verifyCode:de.value}).then((e=>{ke()})).catch((e=>{be.value=!1}))},Ve=()=>{j({target:`${ge.value} ${ie.value}`,phone:`${ge.value} ${ie.value}`,verifcode:se.value,password:oe.value,verifyCode:de.value}).then((e=>{ke()})).catch((e=>{be.value=!1}))},je=()=>{s({}).then((e=>{re.value=e.google_auth_secret,pe.value=e.google_auth_url}))},Ee=()=>{E({secret:re.value,code:ce.value}).then((e=>{e.google_auth_bind?ke():C(err||$("bindFailed"))}))},Be=async()=>{try{await le(re.value),C($("copySuccess"))}catch(e){}},Se=async()=>{ce.value=await navigator.clipboard.readText()},$e=(e,a,l)=>{fe.value=a,ge.value=l},Te=u(null),Ue=()=>{Te.value.open()};return r(ce,((e,a)=>{6===ce.value.length&&(ve.value=!1)})),(e,l)=>{const t=v("fx-header"),i=c,o=U,u=I,n=T;return B(),p("div",a,[g(t,null,{title:x((()=>[A(h(te.value),1)])),_:1}),y("div",H,[f(" 手机邮箱验证 "),1==ne.value||2==ne.value?(B(),p("div",N,[y("div",R,[g(z,{label:2==ne.value?e.$t("email"):e.$t("phoneNum"),placeholderText:2==ne.value?e.$t("entryEmail"):e.$t("entryPhone"),modelValue:ie.value,"onUpdate:modelValue":l[0]||(l[0]=e=>ie.value=e),area:ue.value,onSelectArea:Ue,dialCode:ge.value,icon:fe.value},null,8,["label","placeholderText","modelValue","area","dialCode","icon"])]),y("p",O,h(e.$t("verificationCode")),1),y("div",P,[b(y("input",{class:"inputBackground textColor",type:"text",placeholder:e.$t("entryVerifyCode"),"onUpdate:modelValue":l[1]||(l[1]=e=>se.value=e)},null,8,Y),[[m,se.value]]),3!=ne.value?(B(),p("span",{key:0,onClick:Ae},[A(h(e.$t("sendVerifyCode")),1),ye.value?(B(),p(w,{key:0},[A(" ("+h(ye.value)+")s",1)],64)):f("v-if",!0)])):f("v-if",!0)]),y("div",Z,[g(z,{label:e.$t("登录密码"),placeholderText:e.$t("请输入登录密码"),modelValue:oe.value,"onUpdate:modelValue":l[2]||(l[2]=e=>oe.value=e),typeText:"password",clearBtn:!1},null,8,["label","placeholderText","modelValue"])])])):f("v-if",!0),f(" 谷歌验证 "),3==ne.value?(B(),p("div",J,[y("div",X,[y("div",G,[g(k(D),{src:pe.value},{loading:x((()=>[g(i,{type:"spinner",size:"20"})])),_:1},8,["src"])]),y("div",W,[A(h(re.value)+" ",1),y("img",{src:k("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAKzSURBVHgBvVZdbtpAEP5214G+VOUI9ATlBqEnKDlB4CVV1JeitJHSKIqjCJCaEsRDFYVUIj1BuEHICUJPUN+gPDY23unuEsjaQLDz00+yZHt29tud2Z35GFKi9vV7AYIXGHiOJHLg0pMS3t7nzX4Sf5Zk0OHRSdHhYp2AkvrM3TO0F8qwvYi80ep07yVsNE7ylHW6ICoiDQgDPwjX3J1NzyZTESnzRT714x/rlBHXC8iG6vFun1kwFDIZ8bvePP1ok41Nc9Bone2TJDdOov61QaPe7vaHgW0weWVOiXOmw563bcTYgBEV7tYSw+G305Jg7CLiRGgHYsV1q5UhlqB2dOoyzvYX2SOEOmfIiEt7lRSisru9cY4UqDVPygyiO88WzWFG7EdCIqmalsxMysXqItt0h+ZEqkRbhvOdrY0KUsI+IHMXM3khh5dsww0PD/DEZBFCVT3eTV717tzq3R16KrIooXXfJHCFFEhKpuGMHVT+pPU3DAZIgZu/JvyJUmAIRyPkhXVeiWf/IAXsErYMhsZxkNjhsTCEN3gRqSBEwWs8Ewzhbcka3v3kRTwTHOu9j3G/AxNsFU+EerNzobuHmZfQnx6VkOjndBShqJsuHglTm/UmSJVL9ejrNiXc+/S+Byusgouu2+rm8BioRmB9eQFf6UWKNyGsWp/5jPS7eCDqrbNWrBG09VmZ6Yf1486lDqm1jJ7vy2rSu+a63Vz2VdCKVR7vy9aGOfkzEsNnK2ssIh1YScmFy1qzU15GpvOefRlcx8l8Hr6dzjbPUZc6yGgjnjgrhz6BfoVSmvLHOfIMzhsViTJmFZ0hsxvBQtVm6mvIXTC2joeAWN8Xo0q86yzVpVoucMSUwP3w1NiD3a35SiGRENbQ+eFMlJRAeoOxCpuEb6h2o0joSlLYS6rA/xv+AZfLLFBKDLSAAAAAAElFTkSuQmCC"),alt:"",onClick:je},null,8,K)]),y("p",M,h(e.$t("saveKeyTips")),1),y("div",{class:"copy textColor",onClick:Be},h(e.$t("copy")),1)]),y("div",Q,[y("div",null,h(e.$t("googleVerificationCode")),1),y("div",L,[y("div",{class:"colorMain",onClick:l[3]||(l[3]=e=>ce.value="")},h(e.$t("clear")),1),y("div",{class:"colorMain ml-30",onClick:Se},h(e.$t("paste")),1)])]),g(o,{value:ce.value,gutter:16,focused:ve.value,onFocus:l[4]||(l[4]=e=>ve.value=!0),mask:!1},null,8,["value","focused"]),g(u,{modelValue:ce.value,"onUpdate:modelValue":l[5]||(l[5]=e=>ce.value=e),show:ve.value,onBlur:l[6]||(l[6]=e=>ve.value=!1)},null,8,["modelValue","show"]),ee,y("div",ae,[y("p",null,h(e.$t("precautions")),1),y("p",null,h(e.$t("precautionsTips1")),1),y("p",null,h(e.$t("precautionsTips2")),1)])])):f("v-if",!0),g(n,{class:"w-full",style:{"margin-top":"30px"},type:"primary",loading:be.value,onClick:we},{default:x((()=>[A(h(e.$t("confirm")),1)])),_:1},8,["loading"])]),g(_,{ref_key:"controlChildRef",ref:Te,title:e.$t("selectArea"),onGetName:$e},null,8,["title"])])}}},[["__scopeId","data-v-59a3607d"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/bindVerify/index.vue"]]))}}}));
