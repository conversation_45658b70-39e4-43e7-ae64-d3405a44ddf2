System.register(["./index-legacy-46a00900.js","./index-legacy-72e00c5f.js","./index-legacy-6156faa3.js","./index-legacy-20dd4294.js","./index-legacy-f9c0699e3.js","./index-legacy-1e1b2807.js","./index-legacy-e952cf7f.js","./upload.api-legacy-ce6f3ca0.js","./use-route-legacy-be86ac1c.js","./index-legacy-15165887.js","./index-legacy-bbd15202.js","./index-legacy-f9c0699e.js","./index-legacy-f9c0699e2.js","./index-legacy-0ade4760.js","./index-legacy-df3e01aa.js","./use-id-legacy-df76950f.js"],(function(e,a){"use strict";var o,l,r,i,t,n,c,d,s,v,u,b,p,h,x,m,k,f,g,y,_,w,V,z,j,C,S,U,P,F,T,$,B,R,D,E,I,L,q,G,A,N,O,Y,H,M=document.createElement("style");return M.textContent=".van-radio-group--horizontal{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-flex-wrap:wrap;flex-wrap:wrap}:root{--van-checkbox-size: 20px;--van-checkbox-border-color: var(--van-gray-5);--van-checkbox-transition-duration: var(--van-animation-duration-fast);--van-checkbox-label-margin: var(--van-padding-xs);--van-checkbox-label-color: var(--van-text-color);--van-checkbox-checked-icon-color: var(--van-primary-color);--van-checkbox-disabled-icon-color: var(--van-gray-5);--van-checkbox-disabled-label-color: var(--van-text-color-3);--van-checkbox-disabled-background-color: var(--van-border-color) }.van-checkbox{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;overflow:hidden;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;user-select:none}.van-checkbox--disabled{cursor:not-allowed}.van-checkbox--label-disabled{cursor:default}.van-checkbox--horizontal{margin-right:var(--van-padding-sm)}.van-checkbox__icon{-webkit-box-flex:0;-webkit-flex:none;flex:none;height:1em;font-size:var(--van-checkbox-size);line-height:1em;cursor:pointer}.van-checkbox__icon .van-icon{display:block;box-sizing:border-box;width:1.25em;height:1.25em;color:transparent;font-size:.8em;line-height:1.25;text-align:center;border:1px solid var(--van-checkbox-border-color);-webkit-transition-duration:var(--van-checkbox-transition-duration);transition-duration:var(--van-checkbox-transition-duration);-webkit-transition-property:color,border-color,background-color;transition-property:color,border-color,background-color}.van-checkbox__icon--round .van-icon{border-radius:100%}.van-checkbox__icon--checked .van-icon{color:var(--van-white);background-color:var(--van-checkbox-checked-icon-color);border-color:var(--van-checkbox-checked-icon-color)}.van-checkbox__icon--disabled{cursor:not-allowed}.van-checkbox__icon--disabled .van-icon{background-color:var(--van-checkbox-disabled-background-color);border-color:var(--van-checkbox-disabled-icon-color)}.van-checkbox__icon--disabled.van-checkbox__icon--checked .van-icon{color:var(--van-checkbox-disabled-icon-color)}.van-checkbox__label{margin-left:var(--van-checkbox-label-margin);color:var(--van-checkbox-label-color);line-height:var(--van-checkbox-size)}.van-checkbox__label--left{margin:0 var(--van-checkbox-label-margin) 0 0}.van-checkbox__label--disabled{color:var(--van-checkbox-disabled-label-color)}:root{--van-radio-size: 20px;--van-radio-border-color: var(--van-gray-5);--van-radio-transition-duration: var(--van-animation-duration-fast);--van-radio-label-margin: var(--van-padding-xs);--van-radio-label-color: var(--van-text-color);--van-radio-checked-icon-color: var(--van-primary-color);--van-radio-disabled-icon-color: var(--van-gray-5);--van-radio-disabled-label-color: var(--van-text-color-3);--van-radio-disabled-background-color: var(--van-border-color) }.van-radio{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;overflow:hidden;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;user-select:none}.van-radio--disabled{cursor:not-allowed}.van-radio--label-disabled{cursor:default}.van-radio--horizontal{margin-right:var(--van-padding-sm)}.van-radio__icon{-webkit-box-flex:0;-webkit-flex:none;flex:none;height:1em;font-size:var(--van-radio-size);line-height:1em;cursor:pointer}.van-radio__icon .van-icon{display:block;box-sizing:border-box;width:1.25em;height:1.25em;color:transparent;font-size:.8em;line-height:1.25;text-align:center;border:1px solid var(--van-radio-border-color);-webkit-transition-duration:var(--van-radio-transition-duration);transition-duration:var(--van-radio-transition-duration);-webkit-transition-property:color,border-color,background-color;transition-property:color,border-color,background-color}.van-radio__icon--round .van-icon{border-radius:100%}.van-radio__icon--checked .van-icon{color:var(--van-white);background-color:var(--van-radio-checked-icon-color);border-color:var(--van-radio-checked-icon-color)}.van-radio__icon--disabled{cursor:not-allowed}.van-radio__icon--disabled .van-icon{background-color:var(--van-radio-disabled-background-color);border-color:var(--van-radio-disabled-icon-color)}.van-radio__icon--disabled.van-radio__icon--checked .van-icon{color:var(--van-radio-disabled-icon-color)}.van-radio__label{margin-left:var(--van-radio-label-margin);color:var(--van-radio-label-color);line-height:var(--van-radio-size)}.van-radio__label--left{margin:0 var(--van-radio-label-margin) 0 0}.van-radio__label--disabled{color:var(--van-radio-disabled-label-color)}.checkBox[data-v-2a3835d5]{width:100%;box-sizing:border-box;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:start;-webkit-justify-content:flex-start;justify-content:flex-start;-webkit-box-align:center;-webkit-align-items:center;align-items:center;font-size:13px;color:#000}.checkBox .select[data-v-2a3835d5]{width:15px;height:15px;border-radius:50%;margin-right:8px;border:1px solid #868D9A;box-sizing:border-box;padding:1px}.checkBox .select .checked[data-v-2a3835d5]{width:100%;height:100%;border-radius:50%}.checkBox .selected .checked[data-v-2a3835d5]{background:#2C78F8}[data-v-2a3835d5] .van-radio__icon{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;height:auto!important}.van-radio[data-v-2a3835d5]{font-size:13px!important;color:#000!important;margin-top:21px}[data-v-2a3835d5] .van-radio__label{font-size:13px!important;color:#333}.resetVerify[data-v-c5914ef7]{width:100%;box-sizing:border-box}.content[data-v-c5914ef7]{font-size:13px;padding:16px;border-top:1px solid #E5E7ED}.upload-wrap[data-v-c5914ef7]{width:110px;height:110px;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-box-align:center;-webkit-align-items:center;align-items:center;position:relative}.upload-wrap img[data-v-c5914ef7]{height:100%}.opacity0[data-v-c5914ef7]{opacity:0}.opacity1[data-v-c5914ef7]{opacity:1}.imgShow[data-v-c5914ef7]{top:0;position:absolute}\n",document.head.appendChild(M),{setters:[e=>{o=e.P,l=e.d,r=e.aH,i=e.g,t=e.bF,n=e.e,c=e.S,d=e.a5,s=e.X,v=e.R,u=e.Q,b=e.bc,p=e.ai,h=e.r,x=e.m,m=e.I,k=e.a8,f=e.a6,g=e.aY,y=e.ag,_=e._,w=e.Y,V=e.q,z=e.o,j=e.c,C=e.w,S=e.F,U=e.y,P=e.A,F=e.a,T=e.n,$=e.f,B=e.t,R=e.D,D=e.E,E=e.u,I=e.l,L=e.cr,q=e.av,G=e.x,A=e.cs},e=>{N=e.B},()=>{},()=>{},()=>{},e=>{O=e.U},e=>{Y=e.E},e=>{H=e._},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{}],execute:function(){const[a,M]=o("radio-group"),Q={disabled:Boolean,iconSize:c,direction:String,modelValue:d,checkedColor:String},X=Symbol(a);var J=l({name:a,props:Q,emits:["change","update:modelValue"],setup(e,{emit:a,slots:o}){const{linkChildren:l}=r(X);return i((()=>e.modelValue),(e=>a("change",e))),l({props:e,updateValue:e=>a("update:modelValue",e)}),t((()=>e.modelValue)),()=>{var a;return n("div",{class:M([e.direction]),role:"radiogroup"},[null==(a=o.default)?void 0:a.call(o)])}}});const K=s(J),W={name:d,shape:v("round"),disabled:Boolean,iconSize:c,modelValue:d,checkedColor:String,labelPosition:String,labelDisabled:Boolean};var Z=l({props:u({},W,{bem:b(Function),role:String,parent:Object,checked:Boolean,bindGroup:p}),emits:["click","toggle"],setup(e,{emit:a,slots:o}){const l=h(),r=a=>{if(e.parent&&e.bindGroup)return e.parent.props[a]},i=x((()=>r("disabled")||e.disabled)),t=x((()=>r("direction"))),c=x((()=>{const a=e.checkedColor||r("checkedColor");if(a&&e.checked&&!i.value)return{borderColor:a,backgroundColor:a}})),d=o=>{const{target:r}=o,t=l.value,n=t===r||(null==t?void 0:t.contains(r));i.value||!n&&e.labelDisabled||a("toggle"),a("click",o)},s=()=>{const{bem:a,shape:t,checked:d}=e,s=e.iconSize||r("iconSize");return n("div",{ref:l,class:a("icon",[t,{disabled:i.value,checked:d}]),style:{fontSize:k(s)}},[o.icon?o.icon({checked:d,disabled:i.value}):n(m,{name:"success",style:c.value},null)])},v=()=>{if(o.default)return n("span",{class:e.bem("label",[e.labelPosition,{disabled:i.value}])},[o.default()])};return()=>{const a="left"===e.labelPosition?[v(),s()]:[s(),v()];return n("div",{role:e.role,class:e.bem([{disabled:i.value,"label-disabled":e.labelDisabled},t.value]),tabindex:i.value?void 0:0,"aria-checked":e.checked,onClick:d},[a])}}});const[ee,ae]=o("radio");var oe=l({name:ee,props:W,emits:["update:modelValue"],setup(e,{emit:a,slots:o}){const{parent:l}=f(X),r=()=>{l?l.updateValue(e.name):a("update:modelValue",e.name)};return()=>n(Z,y({bem:ae,role:"radio",parent:l,checked:(l?l.props.modelValue:e.modelValue)===e.name,onToggle:r},e),g(o,["default","icon"]))}});const le=s(oe),re={class:"checkBox"},ie=[(e=>(R("data-v-2a3835d5"),e=e(),D(),e))((()=>F("div",{class:"checked"},null,-1)))],te={__name:"index",props:{list:{type:Array,default:[]},initRadio:{type:Number,default:0}},emits:["checkedSelect"],setup(e,{emit:a}){const o=w(),l=h(0);V((()=>{let e=o.query.type||0;l.value=Number(e)}));const r=()=>{a("checkedSelect",l.value+1)};return(a,o)=>{const i=le,t=K;return z(),j("div",re,[n(t,{modelValue:l.value,"onUpdate:modelValue":o[0]||(o[0]=e=>l.value=e),onChange:r},{default:C((()=>[(z(!0),j(S,null,U(e.list,((e,a)=>(z(),P(i,{key:a,name:a},{icon:C((e=>[F("div",{class:T(["select",e.checked?"selected":""])},ie,2)])),default:C((()=>[$(B(e.name)+" ",1)])),_:2},1032,["name"])))),128))])),_:1},8,["modelValue"])])}}},ne=_(te,[["__scopeId","data-v-2a3835d5"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/components/ex-checked/index.vue"]]),ce={class:"resetVerify"},de={class:"content"},se={class:"textColor"},ve={class:"flex mt-4 mb-8 justify-between"},ue={class:"flex-1 flex flex-col text-center justify-center items-center"},be={class:"upload-wrap"},pe={class:"mt-4 font-14 h-5 text-grey"},he={class:"flex-1 flex flex-col text-center justify-center items-center"},xe={class:"upload-wrap"},me={class:"mt-4 font-14 h-5 text-grey"},ke={class:"flex-1 flex flex-col text-center justify-center items-center"},fe={class:"upload-wrap"},ge={class:"mt-4 font-14 h-5 text-grey"},ye={key:0},_e={__name:"index",setup(e){const{t:a}=E(),o=I(),l=w(),r=h(""),i=h(""),t=h(""),c=h(""),d=h(0),s=h([{name:a("resetFundsPassword"),type:0},{name:a("resetPhone"),type:1},{name:a("resetEmail"),type:2},{name:a("resetGoogleVerify"),type:3}]),v=h([]),u=h([]),b=h([]),p=h(""),x=h(""),m=h(""),k=h("frontFile"),f=h("");h(!1),h(!1),h(!1),V((()=>{d.value=l.query.type,g(d.value),U()}));const g=e=>{r.value=a(1==e?"artificialResetPhone":2==e?"artificialResetEmail":3==e?"artificialResetGoogleVerify":"artificialResetFundsPassword")},y=e=>{d.value=e-1,g(d.value)},_=e=>{e.status="uploading",e.message=a("uploading"),H(e).then((o=>{e.status="success",e.message=a("uploadSuccess"),e.resURL=o,"frontFile"==k.value?v.value=[e]:"reverseFile"==k.value?u.value=[e]:b.value=[e]})).catch((o=>{e.status="failed",e.message=a("uploadFailed")}))},S=e=>{k.value=e},U=()=>{L({}).then((e=>{0!=e.length&&(f.value=e[0].status,p.value=e[0].idcard_path_front_path,x.value=e[0].idcard_path_back_path,m.value=e[0].idcard_path_hold_path)}))},P=()=>{(()=>{let e;0==d.value?e=0:1==d.value?e=2:2==d.value?e=3:3==d.value&&(e=1),A({idcard_path_front:v.value.length&&v.value[0].resURL||p.value||"",idcard_path_back:u.value.length&&u.value[0].resURL||x.value||"",idcard_path_hold:b.value.length&&b.value[0].resURL||x.value||"",operate:e,safeword:t.value,safeword_confirm:c.value,remark:i.value}).then((e=>{o.push({name:"resetSuccess",query:{type:d.value}})}))})()};return(e,a)=>{const o=q("fx-header"),l=O,p=N;return z(),j("div",ce,[n(o,null,{title:C((()=>[$(B(r.value),1)])),_:1}),F("div",de,[F("div",null,[F("div",se,B(e.$t("uploadCredentPassport")),1),F("div",ve,[F("div",ue,[F("div",be,[G(' <img :src="idcard_path_front_path" alt="" class="w-full imgShow" v-if="showImg1" /> '),n(l,{modelValue:v.value,"onUpdate:modelValue":a[0]||(a[0]=e=>v.value=e),multiple:"","max-count":1,"after-read":_,onClickUpload:a[1]||(a[1]=e=>S("frontFile"))},null,8,["modelValue"])]),F("div",pe,B(e.$t("credentFront")),1)]),F("div",he,[F("div",xe,[G(' <img :src="idcard_path_back_path" alt="" class="w-full imgShow" v-if="showImg2" /> '),n(l,{modelValue:u.value,"onUpdate:modelValue":a[2]||(a[2]=e=>u.value=e),multiple:"","max-count":1,"after-read":_,onClickUpload:a[3]||(a[3]=e=>S("reverseFile"))},null,8,["modelValue"])]),F("div",me,B(e.$t("credentObverse")),1)]),F("div",ke,[F("div",fe,[G(' <img :src="idcard_path_hold_path" alt="" class="w-full imgShow" v-if="showImg3" /> '),n(l,{modelValue:b.value,"onUpdate:modelValue":a[4]||(a[4]=e=>b.value=e),multiple:"","max-count":1,"after-read":_,onClickUpload:a[5]||(a[5]=e=>S("fileList"))},null,8,["modelValue"])]),F("div",ge,B(e.$t("handCredent")),1)])])]),n(ne,{class:"mb-5",list:s.value,onCheckedSelect:y},null,8,["list"]),0==d.value?(z(),j("div",ye,[n(Y,{label:e.$t("fundsPassword"),placeholderText:e.$t("fundsPasswordContTips"),modelValue:t.value,"onUpdate:modelValue":a[6]||(a[6]=e=>t.value=e),tips:e.$t("funsPasswordTips"),typeText:"password"},null,8,["label","placeholderText","modelValue","tips"]),n(Y,{label:e.$t("confirmFundsPassword"),placeholderText:e.$t("fundsPasswordContTips"),modelValue:c.value,"onUpdate:modelValue":a[7]||(a[7]=e=>c.value=e),tips:e.$t("funsPasswordTips"),typeText:"password"},null,8,["label","placeholderText","modelValue","tips"])])):G("v-if",!0),n(Y,{label:e.$t("message"),placeholderText:e.$t("entryMessage"),modelValue:i.value,"onUpdate:modelValue":a[8]||(a[8]=e=>i.value=e)},null,8,["label","placeholderText","modelValue"]),n(p,{class:"w-full",style:{"margin-top":"10px"},onClick:P,type:"primary"},{default:C((()=>[$(B(e.$t("submit")),1)])),_:1})])])}}};e("default",_(_e,[["__scopeId","data-v-c5914ef7"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/resetVerify/index.vue"]]))}}}));
