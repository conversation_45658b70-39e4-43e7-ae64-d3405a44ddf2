import{$ as e,a0 as a,a1 as t,a2 as r,a3 as l,P as n,S as s,R as i,a4 as o,Q as u,a5 as c,d,p as g,r as f,a6 as p,a7 as m,m as v,a8 as h,a9 as b,aa as y,ab as x,g as k,s as w,q as C,ac as S,e as M,ad as V,ae as B,af as I,V as A,I as P,ag as T,f as E,ah as $,X as z}from"./index-3d21abf8.js";import{c as j,C as W}from"./index-7dfcb82a.js";import{u as q}from"./use-id-a0619e01.js";function L(e){return Array.isArray(e)?!e.length:0!==e&&!e}function F(e,a){const{message:t}=a;return l(t)?t(e,a):t||""}function H({target:e}){e.composing=!0}function D({target:e}){e.composing&&(e.composing=!1,e.dispatchEvent(new Event("input")))}function O(e){return[...e].length}const[K,Q]=n("field"),R={id:String,name:String,leftIcon:String,rightIcon:String,autofocus:Boolean,clearable:Boolean,maxlength:s,formatter:Function,clearIcon:i("clear"),modelValue:o(""),inputAlign:String,placeholder:String,autocomplete:String,errorMessage:String,enterkeyhint:String,clearTrigger:i("focus"),formatTrigger:i("onChange"),error:{type:Boolean,default:null},disabled:{type:Boolean,default:null},readonly:{type:Boolean,default:null}};const X=z(d({name:K,props:u({},j,R,{rows:s,type:i("text"),rules:Array,autosize:[Boolean,Object],labelWidth:s,labelClass:c,labelAlign:String,showWordLimit:Boolean,errorMessageAlign:String,colon:{type:Boolean,default:null}}),emits:["blur","focus","clear","keypress","click-input","end-validate","start-validate","click-left-icon","click-right-icon","update:modelValue"],setup(l,{emit:n,slots:s}){const i=q(),o=g({status:"unvalidated",focused:!1,validateMessage:""}),u=f(),c=f(),d=f(),{parent:z}=p(m),j=()=>{var e;return String(null!=(e=l.modelValue)?e:"")},K=e=>V(l[e])?l[e]:z&&V(z.props[e])?z.props[e]:void 0,R=v((()=>{const e=K("readonly");if(l.clearable&&!e){const e=""!==j(),a="always"===l.clearTrigger||"focus"===l.clearTrigger&&o.focused;return e&&a}return!1})),X=v((()=>d.value&&s.input?d.value():l.modelValue)),G=e=>e.reduce(((e,a)=>e.then((()=>{if("failed"===o.status)return;let{value:e}=X;if(a.formatter&&(e=a.formatter(e,a)),!function(e,a){if(L(e)){if(a.required)return!1;if(!1===a.validateEmpty)return!0}return!(a.pattern&&!a.pattern.test(String(e)))}(e,a))return o.status="failed",void(o.validateMessage=F(e,a));if(a.validator){if(L(e)&&!1===a.validateEmpty)return;return function(e,a){return new Promise((t=>{const l=a.validator(e,a);r(l)?l.then(t):t(l)}))}(e,a).then((t=>{t&&"string"==typeof t?(o.status="failed",o.validateMessage=t):!1===t&&(o.status="failed",o.validateMessage=F(e,a))}))}}))),Promise.resolve()),J=()=>{o.status="unvalidated",o.validateMessage=""},N=()=>n("end-validate",{status:o.status}),U=(e=l.rules)=>new Promise((a=>{J(),e?(n("start-validate"),G(e).then((()=>{"failed"===o.status?(a({name:l.name,message:o.validateMessage}),N()):(o.status="passed",a(),N())}))):a()})),Y=e=>{if(z&&l.rules){const{validateTrigger:a}=z.props,t=B(a).includes(e),r=l.rules.filter((a=>a.trigger?B(a.trigger).includes(e):t));r.length&&U(r)}},Z=(e,a="onChange")=>{if(e=(e=>{const{maxlength:a}=l;if(V(a)&&O(e)>a){const t=j();return t&&O(t)===+a?t:function(e,a){return[...e].slice(0,a).join("")}(e,+a)}return e})(e),"number"===l.type||"digit"===l.type){const a="number"===l.type;e=I(e,a,a)}l.formatter&&a===l.formatTrigger&&(e=l.formatter(e)),u.value&&u.value.value!==e&&(u.value.value=e),e!==l.modelValue&&n("update:modelValue",e)},_=e=>{e.target.composing||Z(e.target.value)},ee=()=>{var e;return null==(e=u.value)?void 0:e.blur()},ae=()=>{const r=u.value;"textarea"===l.type&&l.autosize&&r&&function(r,l){const n=e();r.style.height="auto";let s=r.scrollHeight;if(t(l)){const{maxHeight:e,minHeight:a}=l;void 0!==e&&(s=Math.min(s,e)),void 0!==a&&(s=Math.max(s,a))}s&&(r.style.height=`${s}px`,a(n))}(r,l.autosize)},te=e=>{o.focused=!0,n("focus",e),w(ae),K("readonly")&&ee()},re=e=>{K("readonly")||(o.focused=!1,Z(j(),"onBlur"),n("blur",e),Y("onBlur"),w(ae),$())},le=e=>n("click-input",e),ne=e=>n("click-left-icon",e),se=e=>n("click-right-icon",e),ie=v((()=>"boolean"==typeof l.error?l.error:!(!z||!z.props.showError||"failed"!==o.status)||void 0)),oe=v((()=>{const e=K("labelWidth");if(e)return{width:h(e)}})),ue=e=>{if(13===e.keyCode){z&&z.props.submitOnEnter||"textarea"===l.type||A(e),"search"===l.type&&ee()}n("keypress",e)},ce=()=>l.id||`${i}-input`,de=()=>{const e=Q("control",[K("inputAlign"),{error:ie.value,custom:!!s.input,"min-height":"textarea"===l.type&&!l.autosize}]);if(s.input)return M("div",{class:e,onClick:le},[s.input()]);const a={id:ce(),ref:u,name:l.name,rows:void 0!==l.rows?+l.rows:void 0,class:e,disabled:K("disabled"),readonly:K("readonly"),autofocus:l.autofocus,placeholder:l.placeholder,autocomplete:l.autocomplete,enterkeyhint:l.enterkeyhint,"aria-labelledby":l.label?`${i}-label`:void 0,onBlur:re,onFocus:te,onInput:_,onClick:le,onChange:D,onKeypress:ue,onCompositionend:D,onCompositionstart:H};return"textarea"===l.type?M("textarea",a,null):M("input",T("number"===(t=l.type)?{type:"text",inputmode:"decimal"}:"digit"===t?{type:"tel",inputmode:"numeric"}:{type:t},a),null);var t},ge=()=>{const e=s["right-icon"];if(l.rightIcon||e)return M("div",{class:Q("right-icon"),onClick:se},[e?e():M(P,{name:l.rightIcon,classPrefix:l.iconPrefix},null)])},fe=()=>{if(l.showWordLimit&&l.maxlength){const e=O(j());return M("div",{class:Q("word-limit")},[M("span",{class:Q("word-num")},[e]),E("/"),l.maxlength])}},pe=()=>{if(z&&!1===z.props.showErrorMessage)return;const e=l.errorMessage||o.validateMessage;if(e){const a=s["error-message"],t=K("errorMessageAlign");return M("div",{class:Q("error-message",t)},[a?a({message:e}):e])}},me=()=>[M("div",{class:Q("body")},[de(),R.value&&M(P,{ref:c,name:l.clearIcon,class:Q("clear")},null),ge(),s.button&&M("div",{class:Q("button")},[s.button()])]),fe(),pe()];return b({blur:ee,focus:()=>{var e;return null==(e=u.value)?void 0:e.focus()},validate:U,formValue:X,resetValidation:J,getValidationStatus:()=>o.status}),y(x,{customValue:d,resetValidation:J,validateWithTrigger:Y}),k((()=>l.modelValue),(()=>{Z(j()),J(),Y("onChange"),w(ae)})),C((()=>{Z(j(),l.formatTrigger),w(ae)})),S("touchstart",(e=>{A(e),n("update:modelValue",""),n("clear",e)}),{target:v((()=>{var e;return null==(e=c.value)?void 0:e.$el}))}),()=>{const e=K("disabled"),a=K("labelAlign"),t=(()=>{const e=K("colon")?":":"";return s.label?[s.label(),e]:l.label?M("label",{id:`${i}-label`,for:ce()},[l.label+e]):void 0})(),r=(()=>{const e=s["left-icon"];if(l.leftIcon||e)return M("div",{class:Q("left-icon"),onClick:ne},[e?e():M(P,{name:l.leftIcon,classPrefix:l.iconPrefix},null)])})();return M(W,{size:l.size,icon:l.leftIcon,class:Q({error:ie.value,disabled:e,[`label-${a}`]:a}),center:l.center,border:l.border,isLink:l.isLink,clickable:l.clickable,titleStyle:oe.value,valueClass:Q("value"),titleClass:[Q("label",[a,{required:l.required}]),l.labelClass],arrowDirection:l.arrowDirection},{icon:r?()=>r:null,title:t?()=>t:null,value:me,extra:s.extra})}}}));export{X as F,R as f};
