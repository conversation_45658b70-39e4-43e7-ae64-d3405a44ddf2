import{_ as e,d as a,u as t,r as l,p as o,i as s,T as r,B as n,av as i,I as d,c,e as m,w as u,a as p,t as h,n as f,x as g,a_ as A,o as v,f as b,D as C,E as R}from"./index-3d21abf8.js";import{B as x}from"./index-2406f514.js";import{N as w}from"./index-cfdda867.js";/* empty css              *//* empty css               */import{S as y,a as I}from"./index-94c336c5.js";import{u as V}from"./upload.api-28f256be.js";import{F as j}from"./index-8c1841f6.js";import"./use-route-cd41a893.js";import"./use-placeholder-c97cb410.js";import"./index-7dfcb82a.js";import"./use-id-a0619e01.js";const F=a({name:"ShopBasicInfo",components:{VuePictureCropper:y},setup(){const{t:e}=t(),a=l({}),i=l(localStorage.getItem("sellerId")||""),d=o({name:"",contact:"",shopPhone:"",shopAddress:"",shopRemark:"",imInitMessage:""}),c=new URL("data:image/png;base64,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",self.location),m=s();r.loading({duration:0,message:e("loading"),forbidClick:!0}),n().then((e=>{a.value=e,d.name=e.name,d.contact=e.contact,d.shopPhone=e.shopPhone,d.shopAddress=e.shopAddress,d.shopRemark=e.shopRemark,d.imInitMessage=e.imInitMessage,r.clear()})).catch((()=>{r.clear()}));const u=l(!1),p=l(!1),h=l(null),f=l(""),g=o({dataURL:"",blobURL:"",file:""});return{isArLang:m,avatarDefault:c,storeInfo:a,storeForm:d,submitLoading:u,showBtn:i,t:e,submitHandle:async()=>{const t={...d};if(!g.file&&!a.value.avatar)return void r(`${e("请设置")}${e("storeLogo")}`);if(!t.name)return void r(`${e("请设置")}${e("storeName")}`);if(t.shopPhone){if(!/^\d{3,}$/.test(t.shopPhone))return r(e("电话格式有误")),!1}u.value=!0;let l="";if(g.file)try{l=await V({file:g.file,moduleName:"shopAvatar"})}catch(o){return u.value=!1,!1}l&&(t.avatar=l),u.value=!0,A(t).then((()=>{u.value=!1,document.dispatchEvent(new CustomEvent("headerRefresh")),r.success(e("saveSuc"))})).catch((()=>{u.value=!1}))},uploadInput:h,pic:f,result:g,isShowModal:p,selectFile:function(e){f.value="",g.dataURL="",g.blobURL="",g.file="";const{files:a}=e.target;if(!a||!a.length)return;const t=a[0],l=new FileReader;l.readAsDataURL(t),l.onload=()=>{f.value=String(l.result),p.value=!0,h.value&&(h.value.value="")}},getResult:async function(){if(!I)return;const e=I.getDataURL(),a=await I.getBlob();if(!a)return;const t=await I.getFile();g.dataURL=e,g.blobURL=URL.createObjectURL(a),g.file=t,p.value=!1},croperBack:()=>{I&&(I.clear(),I.reset(),p.value=!1)}}}}),L=(e=>(C("data-v-3c35484c"),e=e(),R(),e))((()=>p("div",{style:{height:"46px"}},null,-1))),U={class:"item avatar"},k={class:"info"},S=["src"],B=["src"],P={class:"item"},Q={class:"item"},H={class:"item"},N={class:"item"},Z={class:"item"},z={key:0,class:"croper-content-modal"},J={class:"submit-btn"};const M=e(F,[["render",function(e,a,t,l,o,s){var r;const n=i("fx-header"),A=d,C=j,R=w,y=i("VuePictureCropper"),I=x;return v(),c("div",null,[m(n,{fixed:!0},{title:u((()=>[b(h(e.t("basicInfo")),1)])),_:1}),L,p("div",{class:f(["form-content",{"is-ar":e.isArLang}])},[p("div",U,[p("p",null,h(e.t("storeLogo")),1),p("div",k,[p("div",{class:f(["avatar-img",{"is-ar":e.isArLang}])},[p("input",{ref:"uploadInput",type:"file",accept:"image/jpg, image/jpeg, image/png, image/gif",onChange:a[0]||(a[0]=(...a)=>e.selectFile&&e.selectFile(...a))},null,544),e.result.dataURL?(v(),c("img",{key:0,src:e.result.dataURL,alt:""},null,8,S)):(v(),c("img",{key:1,src:(null==(r=e.storeInfo)?void 0:r.avatar)||e.avatarDefault.href,alt:""},null,8,B))],2),m(A,{name:"arrow",color:"#999",size:"12"})])]),p("div",P,[m(C,{modelValue:e.storeForm.name,"onUpdate:modelValue":a[1]||(a[1]=a=>e.storeForm.name=a),type:"text",label:e.t("storeName"),"label-width":120,placeholder:e.t("pleaseEnter")+e.t("storeName")},null,8,["modelValue","label","placeholder"])]),p("div",Q,[m(C,{modelValue:e.storeForm.contact,"onUpdate:modelValue":a[2]||(a[2]=a=>e.storeForm.contact=a),type:"text",label:e.t("contactPerson"),"label-width":120,placeholder:e.t("pleaseEnter")+e.t("contactPerson")},null,8,["modelValue","label","placeholder"])]),p("div",H,[m(C,{modelValue:e.storeForm.shopPhone,"onUpdate:modelValue":a[3]||(a[3]=a=>e.storeForm.shopPhone=a),type:"number",label:e.t("storePhone"),"label-width":120,placeholder:e.t("pleaseEnter")+e.t("storePhone")},null,8,["modelValue","label","placeholder"])]),p("div",N,[m(C,{modelValue:e.storeForm.shopAddress,"onUpdate:modelValue":a[4]||(a[4]=a=>e.storeForm.shopAddress=a),type:"text",label:e.t("storeAddress"),"label-width":120,placeholder:e.t("pleaseEnter")+e.t("storeAddress")},null,8,["modelValue","label","placeholder"])])],2),p("div",{class:f(["remark-content",{"is-ar":e.isArLang}])},[p("p",null,h(e.t("storeProfile")),1),m(C,{modelValue:e.storeForm.shopRemark,"onUpdate:modelValue":a[5]||(a[5]=a=>e.storeForm.shopRemark=a),rows:"4",autosize:"",type:"textarea","label-width":0,label:"",placeholder:e.t("pleaseEnter")+e.t("storeProfile")},null,8,["modelValue","placeholder"])],2),p("div",{class:f(["form-content",{"is-ar":e.isArLang}])},[p("div",Z,[m(C,{modelValue:e.storeForm.imInitMessage,"onUpdate:modelValue":a[6]||(a[6]=a=>e.storeForm.imInitMessage=a),type:"text",label:e.t("进店欢迎语"),"label-width":120,placeholder:e.t("pleaseEnter")+e.t("进店欢迎语")},null,8,["modelValue","label","placeholder"])])],2),e.isShowModal?(v(),c("div",z,[m(R,{title:e.t("selectPhotos"),"right-text":e.t("sure"),"left-arrow":"",fixed:"",onClickLeft:e.croperBack,onClickRight:e.getResult},null,8,["title","right-text","onClickLeft","onClickRight"]),m(y,{boxStyle:{width:"100%",height:"100%",backgroundColor:"#f8f8f8",margin:"auto"},img:e.pic,options:{viewMode:1,dragMode:"crop",aspectRatio:1}},null,8,["img"])])):g("v-if",!0),p("div",J,[m(I,{loading:e.submitLoading,type:"primary",size:"large",disabled:!e.showBtn,onClick:e.submitHandle},{default:u((()=>[b(h(e.showBtn?e.t("save"):e.t("商家入驻尚未完成")),1)])),_:1},8,["loading","disabled","onClick"])])])}],["__scopeId","data-v-3c35484c"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/shop/basicInfo/index.vue"]]);export{M as default};
