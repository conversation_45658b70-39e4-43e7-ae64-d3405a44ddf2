System.register(["./___vite-browser-external_commonjs-proxy-legacy-70ac8ee8.js"],(function(t,e){"use strict";var r;return{setters:[t=>{r=t.r}],execute:function(){var e,o="undefined"!=typeof Symbol&&Symbol,n=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var t={},e=Symbol("test"),r=Object(e);if("string"==typeof e)return!1;if("[object Symbol]"!==Object.prototype.toString.call(e))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(e in t[e]=42,t)return!1;if("function"==typeof Object.keys&&0!==Object.keys(t).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(t).length)return!1;var o=Object.getOwnPropertySymbols(t);if(1!==o.length||o[0]!==e)return!1;if(!Object.prototype.propertyIsEnumerable.call(t,e))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var n=Object.getOwnPropertyDescriptor(t,e);if(42!==n.value||!0!==n.enumerable)return!1}return!0},a=Array.prototype.slice,i=Object.prototype.toString,p=function(t){var e=this;if("function"!=typeof e||"[object Function]"!==i.call(e))throw new TypeError("Function.prototype.bind called on incompatible "+e);for(var r,o=a.call(arguments,1),n=Math.max(0,e.length-o.length),p=[],c=0;c<n;c++)p.push("$"+c);if(r=Function("binder","return function ("+p.join(",")+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof r){var n=e.apply(this,o.concat(a.call(arguments)));return Object(n)===n?n:this}return e.apply(t,o.concat(a.call(arguments)))})),e.prototype){var l=function(){};l.prototype=e.prototype,r.prototype=new l,l.prototype=null}return r},c=Function.prototype.bind||p,l=c.call(Function.call,Object.prototype.hasOwnProperty),f=SyntaxError,u=Function,y=TypeError,s=function(t){try{return u('"use strict"; return ('+t+").constructor;")()}catch(e){}},d=Object.getOwnPropertyDescriptor;if(d)try{d({},"")}catch(ve){d=null}var g=function(){throw new y},b=d?function(){try{return g}catch(t){try{return d(arguments,"callee").get}catch(e){return g}}}():g,m="function"==typeof o&&"function"==typeof Symbol&&"symbol"==typeof o("foo")&&"symbol"==typeof Symbol("bar")&&n(),h=Object.getPrototypeOf||function(t){return t.__proto__},v={},S="undefined"==typeof Uint8Array?e:h(Uint8Array),A={"%AggregateError%":"undefined"==typeof AggregateError?e:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?e:ArrayBuffer,"%ArrayIteratorPrototype%":m?h([][Symbol.iterator]()):e,"%AsyncFromSyncIteratorPrototype%":e,"%AsyncFunction%":v,"%AsyncGenerator%":v,"%AsyncGeneratorFunction%":v,"%AsyncIteratorPrototype%":v,"%Atomics%":"undefined"==typeof Atomics?e:Atomics,"%BigInt%":"undefined"==typeof BigInt?e:BigInt,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?e:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":"undefined"==typeof Float32Array?e:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?e:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?e:FinalizationRegistry,"%Function%":u,"%GeneratorFunction%":v,"%Int8Array%":"undefined"==typeof Int8Array?e:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?e:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?e:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":m?h(h([][Symbol.iterator]())):e,"%JSON%":"object"==typeof JSON?JSON:e,"%Map%":"undefined"==typeof Map?e:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&m?h((new Map)[Symbol.iterator]()):e,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?e:Promise,"%Proxy%":"undefined"==typeof Proxy?e:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":"undefined"==typeof Reflect?e:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?e:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&m?h((new Set)[Symbol.iterator]()):e,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?e:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":m?h(""[Symbol.iterator]()):e,"%Symbol%":m?Symbol:e,"%SyntaxError%":f,"%ThrowTypeError%":b,"%TypedArray%":S,"%TypeError%":y,"%Uint8Array%":"undefined"==typeof Uint8Array?e:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?e:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?e:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?e:Uint32Array,"%URIError%":URIError,"%WeakMap%":"undefined"==typeof WeakMap?e:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?e:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?e:WeakSet},j=function t(e){var r;if("%AsyncFunction%"===e)r=s("async function () {}");else if("%GeneratorFunction%"===e)r=s("function* () {}");else if("%AsyncGeneratorFunction%"===e)r=s("async function* () {}");else if("%AsyncGenerator%"===e){var o=t("%AsyncGeneratorFunction%");o&&(r=o.prototype)}else if("%AsyncIteratorPrototype%"===e){var n=t("%AsyncGenerator%");n&&(r=h(n.prototype))}return A[e]=r,r},O={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},P=c,w=l,E=P.call(Function.call,Array.prototype.concat),x=P.call(Function.apply,Array.prototype.splice),F=P.call(Function.call,String.prototype.replace),R=P.call(Function.call,String.prototype.slice),k=P.call(Function.call,RegExp.prototype.exec),I=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,M=/\\(\\)?/g,N=function(t,e){var r,o=t;if(w(O,o)&&(o="%"+(r=O[o])[0]+"%"),w(A,o)){var n=A[o];if(n===v&&(n=j(o)),void 0===n&&!e)throw new y("intrinsic "+t+" exists, but is not available. Please file an issue!");return{alias:r,name:o,value:n}}throw new f("intrinsic "+t+" does not exist!")},U=function(t,e){if("string"!=typeof t||0===t.length)throw new y("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof e)throw new y('"allowMissing" argument must be a boolean');if(null===k(/^%?[^%]*%?$/,t))throw new f("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=function(t){var e=R(t,0,1),r=R(t,-1);if("%"===e&&"%"!==r)throw new f("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==e)throw new f("invalid intrinsic syntax, expected opening `%`");var o=[];return F(t,I,(function(t,e,r,n){o[o.length]=r?F(n,M,"$1"):e||t})),o}(t),o=r.length>0?r[0]:"",n=N("%"+o+"%",e),a=n.name,i=n.value,p=!1,c=n.alias;c&&(o=c[0],x(r,E([0,1],c)));for(var l=1,u=!0;l<r.length;l+=1){var s=r[l],g=R(s,0,1),b=R(s,-1);if(('"'===g||"'"===g||"`"===g||'"'===b||"'"===b||"`"===b)&&g!==b)throw new f("property names with quotes must have matching quotes");if("constructor"!==s&&u||(p=!0),w(A,a="%"+(o+="."+s)+"%"))i=A[a];else if(null!=i){if(!(s in i)){if(!e)throw new y("base intrinsic for "+t+" exists, but the property is not available.");return}if(d&&l+1>=r.length){var m=d(i,s);i=(u=!!m)&&"get"in m&&!("originalValue"in m.get)?m.get:i[s]}else u=w(i,s),i=i[s];u&&!p&&(A[a]=i)}}return i},D={exports:{}};!function(t){var e=c,r=U,o=r("%Function.prototype.apply%"),n=r("%Function.prototype.call%"),a=r("%Reflect.apply%",!0)||e.call(n,o),i=r("%Object.getOwnPropertyDescriptor%",!0),p=r("%Object.defineProperty%",!0),l=r("%Math.max%");if(p)try{p({},"a",{value:1})}catch(ve){p=null}t.exports=function(t){var r=a(e,n,arguments);return i&&p&&i(r,"length").configurable&&p(r,"length",{value:1+l(0,t.length-(arguments.length-1))}),r};var f=function(){return a(e,o,arguments)};p?p(t.exports,"apply",{value:f}):t.exports.apply=f}(D);var T=U,W=D.exports,_=W(T("String.prototype.indexOf")),B="function"==typeof Map&&Map.prototype,C=Object.getOwnPropertyDescriptor&&B?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,G=B&&C&&"function"==typeof C.get?C.get:null,$=B&&Map.prototype.forEach,L="function"==typeof Set&&Set.prototype,V=Object.getOwnPropertyDescriptor&&L?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,q=L&&V&&"function"==typeof V.get?V.get:null,z=L&&Set.prototype.forEach,H="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,J="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,Q="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,K=Boolean.prototype.valueOf,X=Object.prototype.toString,Y=Function.prototype.toString,Z=String.prototype.match,tt=String.prototype.slice,et=String.prototype.replace,rt=String.prototype.toUpperCase,ot=String.prototype.toLowerCase,nt=RegExp.prototype.test,at=Array.prototype.concat,it=Array.prototype.join,pt=Array.prototype.slice,ct=Math.floor,lt="function"==typeof BigInt?BigInt.prototype.valueOf:null,ft=Object.getOwnPropertySymbols,ut="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,yt="function"==typeof Symbol&&"object"==typeof Symbol.iterator,st="function"==typeof Symbol&&Symbol.toStringTag&&(Symbol.toStringTag,1)?Symbol.toStringTag:null,dt=Object.prototype.propertyIsEnumerable,gt=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(t){return t.__proto__}:null);function bt(t,e){if(t===1/0||t===-1/0||t!=t||t&&t>-1e3&&t<1e3||nt.call(/e/,e))return e;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof t){var o=t<0?-ct(-t):ct(t);if(o!==t){var n=String(o),a=tt.call(e,n.length+1);return et.call(n,r,"$&_")+"."+et.call(et.call(a,/([0-9]{3})/g,"$&_"),/_$/,"")}}return et.call(e,r,"$&_")}var mt=r,ht=mt.custom,vt=Pt(ht)?ht:null;function St(t,e,r){var o="double"===(r.quoteStyle||e)?'"':"'";return o+t+o}function At(t){return et.call(String(t),/"/g,"&quot;")}function jt(t){return!("[object Array]"!==xt(t)||st&&"object"==typeof t&&st in t)}function Ot(t){return!("[object RegExp]"!==xt(t)||st&&"object"==typeof t&&st in t)}function Pt(t){if(yt)return t&&"object"==typeof t&&t instanceof Symbol;if("symbol"==typeof t)return!0;if(!t||"object"!=typeof t||!ut)return!1;try{return ut.call(t),!0}catch(ve){}return!1}var wt=Object.prototype.hasOwnProperty||function(t){return t in this};function Et(t,e){return wt.call(t,e)}function xt(t){return X.call(t)}function Ft(t,e){if(t.indexOf)return t.indexOf(e);for(var r=0,o=t.length;r<o;r++)if(t[r]===e)return r;return-1}function Rt(t,e){if(t.length>e.maxStringLength){var r=t.length-e.maxStringLength,o="... "+r+" more character"+(r>1?"s":"");return Rt(tt.call(t,0,e.maxStringLength),e)+o}return St(et.call(et.call(t,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,kt),"single",e)}function kt(t){var e=t.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[e];return r?"\\"+r:"\\x"+(e<16?"0":"")+rt.call(e.toString(16))}function It(t){return"Object("+t+")"}function Mt(t){return t+" { ? }"}function Nt(t,e,r,o){return t+" ("+e+") {"+(o?Ut(r,o):it.call(r,", "))+"}"}function Ut(t,e){if(0===t.length)return"";var r="\n"+e.prev+e.base;return r+it.call(t,","+r)+"\n"+e.prev}function Dt(t,e){var r=jt(t),o=[];if(r){o.length=t.length;for(var n=0;n<t.length;n++)o[n]=Et(t,n)?e(t[n],t):""}var a,i="function"==typeof ft?ft(t):[];if(yt){a={};for(var p=0;p<i.length;p++)a["$"+i[p]]=i[p]}for(var c in t)Et(t,c)&&(r&&String(Number(c))===c&&c<t.length||yt&&a["$"+c]instanceof Symbol||(nt.call(/[^\w$]/,c)?o.push(e(c,t)+": "+e(t[c],t)):o.push(c+": "+e(t[c],t))));if("function"==typeof ft)for(var l=0;l<i.length;l++)dt.call(t,i[l])&&o.push("["+e(i[l])+"]: "+e(t[i[l]],t));return o}var Tt=U,Wt=function(t,e){var r=T(t,!!e);return"function"==typeof r&&_(t,".prototype.")>-1?W(r):r},_t=function t(e,r,o,n){var a=r||{};if(Et(a,"quoteStyle")&&"single"!==a.quoteStyle&&"double"!==a.quoteStyle)throw new TypeError('option "quoteStyle" must be "single" or "double"');if(Et(a,"maxStringLength")&&("number"==typeof a.maxStringLength?a.maxStringLength<0&&a.maxStringLength!==1/0:null!==a.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var i=!Et(a,"customInspect")||a.customInspect;if("boolean"!=typeof i&&"symbol"!==i)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(Et(a,"indent")&&null!==a.indent&&"\t"!==a.indent&&!(parseInt(a.indent,10)===a.indent&&a.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(Et(a,"numericSeparator")&&"boolean"!=typeof a.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var p=a.numericSeparator;if(void 0===e)return"undefined";if(null===e)return"null";if("boolean"==typeof e)return e?"true":"false";if("string"==typeof e)return Rt(e,a);if("number"==typeof e){if(0===e)return 1/0/e>0?"0":"-0";var c=String(e);return p?bt(e,c):c}if("bigint"==typeof e){var l=String(e)+"n";return p?bt(e,l):l}var f=void 0===a.depth?5:a.depth;if(void 0===o&&(o=0),o>=f&&f>0&&"object"==typeof e)return jt(e)?"[Array]":"[Object]";var u,y=function(t,e){var r;if("\t"===t.indent)r="\t";else{if(!("number"==typeof t.indent&&t.indent>0))return null;r=it.call(Array(t.indent+1)," ")}return{base:r,prev:it.call(Array(e+1),r)}}(a,o);if(void 0===n)n=[];else if(Ft(n,e)>=0)return"[Circular]";function s(e,r,i){if(r&&(n=pt.call(n)).push(r),i){var p={depth:a.depth};return Et(a,"quoteStyle")&&(p.quoteStyle=a.quoteStyle),t(e,p,o+1,n)}return t(e,a,o+1,n)}if("function"==typeof e&&!Ot(e)){var d=function(t){if(t.name)return t.name;var e=Z.call(Y.call(t),/^function\s*([\w$]+)/);return e?e[1]:null}(e),g=Dt(e,s);return"[Function"+(d?": "+d:" (anonymous)")+"]"+(g.length>0?" { "+it.call(g,", ")+" }":"")}if(Pt(e)){var b=yt?et.call(String(e),/^(Symbol\(.*\))_[^)]*$/,"$1"):ut.call(e);return"object"!=typeof e||yt?b:It(b)}if((u=e)&&"object"==typeof u&&("undefined"!=typeof HTMLElement&&u instanceof HTMLElement||"string"==typeof u.nodeName&&"function"==typeof u.getAttribute)){for(var m="<"+ot.call(String(e.nodeName)),h=e.attributes||[],v=0;v<h.length;v++)m+=" "+h[v].name+"="+St(At(h[v].value),"double",a);return m+=">",e.childNodes&&e.childNodes.length&&(m+="..."),m+="</"+ot.call(String(e.nodeName))+">"}if(jt(e)){if(0===e.length)return"[]";var S=Dt(e,s);return y&&!function(t){for(var e=0;e<t.length;e++)if(Ft(t[e],"\n")>=0)return!1;return!0}(S)?"["+Ut(S,y)+"]":"[ "+it.call(S,", ")+" ]"}if(function(t){return!("[object Error]"!==xt(t)||st&&"object"==typeof t&&st in t)}(e)){var A=Dt(e,s);return"cause"in Error.prototype||!("cause"in e)||dt.call(e,"cause")?0===A.length?"["+String(e)+"]":"{ ["+String(e)+"] "+it.call(A,", ")+" }":"{ ["+String(e)+"] "+it.call(at.call("[cause]: "+s(e.cause),A),", ")+" }"}if("object"==typeof e&&i){if(vt&&"function"==typeof e[vt]&&mt)return mt(e,{depth:f-o});if("symbol"!==i&&"function"==typeof e.inspect)return e.inspect()}if(function(t){if(!G||!t||"object"!=typeof t)return!1;try{G.call(t);try{q.call(t)}catch(m){return!0}return t instanceof Map}catch(ve){}return!1}(e)){var j=[];return $&&$.call(e,(function(t,r){j.push(s(r,e,!0)+" => "+s(t,e))})),Nt("Map",G.call(e),j,y)}if(function(t){if(!q||!t||"object"!=typeof t)return!1;try{q.call(t);try{G.call(t)}catch(e){return!0}return t instanceof Set}catch(ve){}return!1}(e)){var O=[];return z&&z.call(e,(function(t){O.push(s(t,e))})),Nt("Set",q.call(e),O,y)}if(function(t){if(!H||!t||"object"!=typeof t)return!1;try{H.call(t,H);try{J.call(t,J)}catch(m){return!0}return t instanceof WeakMap}catch(ve){}return!1}(e))return Mt("WeakMap");if(function(t){if(!J||!t||"object"!=typeof t)return!1;try{J.call(t,J);try{H.call(t,H)}catch(m){return!0}return t instanceof WeakSet}catch(ve){}return!1}(e))return Mt("WeakSet");if(function(t){if(!Q||!t||"object"!=typeof t)return!1;try{return Q.call(t),!0}catch(ve){}return!1}(e))return Mt("WeakRef");if(function(t){return!("[object Number]"!==xt(t)||st&&"object"==typeof t&&st in t)}(e))return It(s(Number(e)));if(function(t){if(!t||"object"!=typeof t||!lt)return!1;try{return lt.call(t),!0}catch(ve){}return!1}(e))return It(s(lt.call(e)));if(function(t){return!("[object Boolean]"!==xt(t)||st&&"object"==typeof t&&st in t)}(e))return It(K.call(e));if(function(t){return!("[object String]"!==xt(t)||st&&"object"==typeof t&&st in t)}(e))return It(s(String(e)));if(!function(t){return!("[object Date]"!==xt(t)||st&&"object"==typeof t&&st in t)}(e)&&!Ot(e)){var P=Dt(e,s),w=gt?gt(e)===Object.prototype:e instanceof Object||e.constructor===Object,E=e instanceof Object?"":"null prototype",x=!w&&st&&Object(e)===e&&st in e?tt.call(xt(e),8,-1):E?"Object":"",F=(w||"function"!=typeof e.constructor?"":e.constructor.name?e.constructor.name+" ":"")+(x||E?"["+it.call(at.call([],x||[],E||[]),": ")+"] ":"");return 0===P.length?F+"{}":y?F+"{"+Ut(P,y)+"}":F+"{ "+it.call(P,", ")+" }"}return String(e)},Bt=Tt("%TypeError%"),Ct=Tt("%WeakMap%",!0),Gt=Tt("%Map%",!0),$t=Wt("WeakMap.prototype.get",!0),Lt=Wt("WeakMap.prototype.set",!0),Vt=Wt("WeakMap.prototype.has",!0),qt=Wt("Map.prototype.get",!0),zt=Wt("Map.prototype.set",!0),Ht=Wt("Map.prototype.has",!0),Jt=function(t,e){for(var r,o=t;null!==(r=o.next);o=r)if(r.key===e)return o.next=r.next,r.next=t.next,t.next=r,r},Qt=String.prototype.replace,Kt=/%20/g,Xt="RFC3986",Yt=t("f",{default:Xt,formatters:{RFC1738:function(t){return Qt.call(t,Kt,"+")},RFC3986:function(t){return String(t)}},RFC1738:"RFC1738",RFC3986:Xt}),Zt=Yt,te=Object.prototype.hasOwnProperty,ee=Array.isArray,re=function(){for(var t=[],e=0;e<256;++e)t.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return t}(),oe=function(t,e){for(var r=e&&e.plainObjects?Object.create(null):{},o=0;o<t.length;++o)void 0!==t[o]&&(r[o]=t[o]);return r},ne=t("u",{arrayToObject:oe,assign:function(t,e){return Object.keys(e).reduce((function(t,r){return t[r]=e[r],t}),t)},combine:function(t,e){return[].concat(t,e)},compact:function(t){for(var e=[{obj:{o:t},prop:"o"}],r=[],o=0;o<e.length;++o)for(var n=e[o],a=n.obj[n.prop],i=Object.keys(a),p=0;p<i.length;++p){var c=i[p],l=a[c];"object"==typeof l&&null!==l&&-1===r.indexOf(l)&&(e.push({obj:a,prop:c}),r.push(l))}return function(t){for(;t.length>1;){var e=t.pop(),r=e.obj[e.prop];if(ee(r)){for(var o=[],n=0;n<r.length;++n)void 0!==r[n]&&o.push(r[n]);e.obj[e.prop]=o}}}(e),t},decode:function(t,e,r){var o=t.replace(/\+/g," ");if("iso-8859-1"===r)return o.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(o)}catch(ve){return o}},encode:function(t,e,r,o,n){if(0===t.length)return t;var a=t;if("symbol"==typeof t?a=Symbol.prototype.toString.call(t):"string"!=typeof t&&(a=String(t)),"iso-8859-1"===r)return escape(a).replace(/%u[0-9a-f]{4}/gi,(function(t){return"%26%23"+parseInt(t.slice(2),16)+"%3B"}));for(var i="",p=0;p<a.length;++p){var c=a.charCodeAt(p);45===c||46===c||95===c||126===c||c>=48&&c<=57||c>=65&&c<=90||c>=97&&c<=122||n===Zt.RFC1738&&(40===c||41===c)?i+=a.charAt(p):c<128?i+=re[c]:c<2048?i+=re[192|c>>6]+re[128|63&c]:c<55296||c>=57344?i+=re[224|c>>12]+re[128|c>>6&63]+re[128|63&c]:(p+=1,c=65536+((1023&c)<<10|1023&a.charCodeAt(p)),i+=re[240|c>>18]+re[128|c>>12&63]+re[128|c>>6&63]+re[128|63&c])}return i},isBuffer:function(t){return!(!t||"object"!=typeof t||!(t.constructor&&t.constructor.isBuffer&&t.constructor.isBuffer(t)))},isRegExp:function(t){return"[object RegExp]"===Object.prototype.toString.call(t)},maybeMap:function(t,e){if(ee(t)){for(var r=[],o=0;o<t.length;o+=1)r.push(e(t[o]));return r}return e(t)},merge:function t(e,r,o){if(!r)return e;if("object"!=typeof r){if(ee(e))e.push(r);else{if(!e||"object"!=typeof e)return[e,r];(o&&(o.plainObjects||o.allowPrototypes)||!te.call(Object.prototype,r))&&(e[r]=!0)}return e}if(!e||"object"!=typeof e)return[e].concat(r);var n=e;return ee(e)&&!ee(r)&&(n=oe(e,o)),ee(e)&&ee(r)?(r.forEach((function(r,n){if(te.call(e,n)){var a=e[n];a&&"object"==typeof a&&r&&"object"==typeof r?e[n]=t(a,r,o):e.push(r)}else e[n]=r})),e):Object.keys(r).reduce((function(e,n){var a=r[n];return te.call(e,n)?e[n]=t(e[n],a,o):e[n]=a,e}),n)}}),ae=function(){var t,e,r,o={assert:function(t){if(!o.has(t))throw new Bt("Side channel does not contain "+_t(t))},get:function(o){if(Ct&&o&&("object"==typeof o||"function"==typeof o)){if(t)return $t(t,o)}else if(Gt){if(e)return qt(e,o)}else if(r)return function(t,e){var r=Jt(t,e);return r&&r.value}(r,o)},has:function(o){if(Ct&&o&&("object"==typeof o||"function"==typeof o)){if(t)return Vt(t,o)}else if(Gt){if(e)return Ht(e,o)}else if(r)return function(t,e){return!!Jt(t,e)}(r,o);return!1},set:function(o,n){Ct&&o&&("object"==typeof o||"function"==typeof o)?(t||(t=new Ct),Lt(t,o,n)):Gt?(e||(e=new Gt),zt(e,o,n)):(r||(r={key:{},next:null}),function(t,e,r){var o=Jt(t,e);o?o.value=r:t.next={key:e,next:t.next,value:r}}(r,o,n))}};return o},ie=ne,pe=Yt,ce=Object.prototype.hasOwnProperty,le={brackets:function(t){return t+"[]"},comma:"comma",indices:function(t,e){return t+"["+e+"]"},repeat:function(t){return t}},fe=Array.isArray,ue=String.prototype.split,ye=Array.prototype.push,se=function(t,e){ye.apply(t,fe(e)?e:[e])},de=Date.prototype.toISOString,ge=pe.default,be={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:ie.encode,encodeValuesOnly:!1,format:ge,formatter:pe.formatters[ge],indices:!1,serializeDate:function(t){return de.call(t)},skipNulls:!1,strictNullHandling:!1},me={},he=function t(e,r,o,n,a,i,p,c,l,f,u,y,s,d,g,b){for(var m,h=e,v=b,S=0,A=!1;void 0!==(v=v.get(me))&&!A;){var j=v.get(e);if(S+=1,void 0!==j){if(j===S)throw new RangeError("Cyclic object value");A=!0}void 0===v.get(me)&&(S=0)}if("function"==typeof c?h=c(r,h):h instanceof Date?h=u(h):"comma"===o&&fe(h)&&(h=ie.maybeMap(h,(function(t){return t instanceof Date?u(t):t}))),null===h){if(a)return p&&!d?p(r,be.encoder,g,"key",y):r;h=""}if("string"==typeof(m=h)||"number"==typeof m||"boolean"==typeof m||"symbol"==typeof m||"bigint"==typeof m||ie.isBuffer(h)){if(p){var O=d?r:p(r,be.encoder,g,"key",y);if("comma"===o&&d){for(var P=ue.call(String(h),","),w="",E=0;E<P.length;++E)w+=(0===E?"":",")+s(p(P[E],be.encoder,g,"value",y));return[s(O)+(n&&fe(h)&&1===P.length?"[]":"")+"="+w]}return[s(O)+"="+s(p(h,be.encoder,g,"value",y))]}return[s(r)+"="+s(String(h))]}var x,F=[];if(void 0===h)return F;if("comma"===o&&fe(h))x=[{value:h.length>0?h.join(",")||null:void 0}];else if(fe(c))x=c;else{var R=Object.keys(h);x=l?R.sort(l):R}for(var k=n&&fe(h)&&1===h.length?r+"[]":r,I=0;I<x.length;++I){var M=x[I],N="object"==typeof M&&void 0!==M.value?M.value:h[M];if(!i||null!==N){var U=fe(h)?"function"==typeof o?o(k,M):k:k+(f?"."+M:"["+M+"]");b.set(e,S);var D=ae();D.set(me,b),se(F,t(N,U,o,n,a,i,p,c,l,f,u,y,s,d,g,D))}}return F};t("s",(function(t,e){var r,o=t,n=function(t){if(!t)return be;if(null!==t.encoder&&void 0!==t.encoder&&"function"!=typeof t.encoder)throw new TypeError("Encoder has to be a function.");var e=t.charset||be.charset;if(void 0!==t.charset&&"utf-8"!==t.charset&&"iso-8859-1"!==t.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=pe.default;if(void 0!==t.format){if(!ce.call(pe.formatters,t.format))throw new TypeError("Unknown format option provided.");r=t.format}var o=pe.formatters[r],n=be.filter;return("function"==typeof t.filter||fe(t.filter))&&(n=t.filter),{addQueryPrefix:"boolean"==typeof t.addQueryPrefix?t.addQueryPrefix:be.addQueryPrefix,allowDots:void 0===t.allowDots?be.allowDots:!!t.allowDots,charset:e,charsetSentinel:"boolean"==typeof t.charsetSentinel?t.charsetSentinel:be.charsetSentinel,delimiter:void 0===t.delimiter?be.delimiter:t.delimiter,encode:"boolean"==typeof t.encode?t.encode:be.encode,encoder:"function"==typeof t.encoder?t.encoder:be.encoder,encodeValuesOnly:"boolean"==typeof t.encodeValuesOnly?t.encodeValuesOnly:be.encodeValuesOnly,filter:n,format:r,formatter:o,serializeDate:"function"==typeof t.serializeDate?t.serializeDate:be.serializeDate,skipNulls:"boolean"==typeof t.skipNulls?t.skipNulls:be.skipNulls,sort:"function"==typeof t.sort?t.sort:null,strictNullHandling:"boolean"==typeof t.strictNullHandling?t.strictNullHandling:be.strictNullHandling}}(e);"function"==typeof n.filter?o=(0,n.filter)("",o):fe(n.filter)&&(r=n.filter);var a,i=[];if("object"!=typeof o||null===o)return"";a=e&&e.arrayFormat in le?e.arrayFormat:e&&"indices"in e?e.indices?"indices":"repeat":"indices";var p=le[a];if(e&&"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var c="comma"===p&&e&&e.commaRoundTrip;r||(r=Object.keys(o)),n.sort&&r.sort(n.sort);for(var l=ae(),f=0;f<r.length;++f){var u=r[f];n.skipNulls&&null===o[u]||se(i,he(o[u],u,p,c,n.strictNullHandling,n.skipNulls,n.encode?n.encoder:null,n.filter,n.sort,n.allowDots,n.serializeDate,n.format,n.formatter,n.encodeValuesOnly,n.charset,l))}var y=i.join(n.delimiter),s=!0===n.addQueryPrefix?"?":"";return n.charsetSentinel&&("iso-8859-1"===n.charset?s+="utf8=%26%2310003%3B&":s+="utf8=%E2%9C%93&"),y.length>0?s+y:""}))}}}));
