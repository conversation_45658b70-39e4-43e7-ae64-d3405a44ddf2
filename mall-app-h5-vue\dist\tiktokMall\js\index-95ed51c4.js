import{_ as e,i as a,u as s,j as l,l as o,r as t,m as r,p as n,c as d,e as i,b as c,a as u,t as p,F as m,y as w,x as v,n as f,f as g,w as h,L as y,T as x,G as _,B as k,o as b,D as j,E as U}from"./index-3d21abf8.js";import{B as V}from"./index-2406f514.js";import{l as P}from"./config-6c4e82a2.js";import{I as T}from"./index-8840aba2.js";import{g as I}from"./vue3-puzzle-vcode.es-02780b60.js";import{_ as L}from"./login.api-cb7fcde3.js";import"./use-route-cd41a893.js";/* empty css              *//* empty css               */import"./index.vue_vue_type_style_index_0_scoped_efb302ad_lang-ae7957c5.js";import"./index-8c1841f6.js";import"./index-7dfcb82a.js";import"./use-id-a0619e01.js";const C={class:"header-content"},S={class:"logo-content"},z=["src"],R={class:"type-tab"},$=["onClick"],A={class:"form-content"},N={key:0,class:"item"},Z={class:"title"},E={class:"input-content"},B={key:1,class:"item"},D={class:"title"},F={class:"input-content"},G={class:"item"},M={class:"title"},q={class:"input-content"},H={class:"item"},J={class:"title"},K={class:"input-content"},O=(e=>(j("data-v-8d5a9da1"),e=e(),U(),e))((()=>u("p",null,null,-1))),Q={class:"btn"},W=e({__name:"index",setup(e){const j=a(),{t:U,locale:W}=s(),X=l(),Y=o(),ee=t(new URL("/www/png/name-3ddd62dd.png",self.location)),ae=r((()=>{const e=P.find((e=>"tiktokMall"===("mbuy"===e.key?"argos":e.key)));return e||P[0]})),se=new URL("/www/png/name-593a7e78.png",self.location),le=new URL("/www/png/name-ae6e9405.png",self.location),oe=new URL("/www/png/name-8130147b.png",self.location),te=new URL("/www/png/name-dab02cab.png",self.location),re=new URL("/www/png/name-ca360226.png",self.location),ne=[se.href,le.href,oe.href,te.href,re.href],de=t(!1),ie=["email","phoneNum"],ce=t(0),ue=t(!1),pe=localStorage.getItem("areaCode")?localStorage.getItem("areaCode"):44,me=t(pe),we=n({username:"",password:"",password1:""}),ve=()=>{const e=0===ce.value?"entryEmail":"entryPhone";if(""===we.username)return x(U(e)),!1;if(0===ce.value&&!/^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(we.username))return void x(U("请输入正确的邮箱地址"));if(1===ce.value&&!/^[0-9]+$/.test(we.username))return void x(U("请输入正确的手机号码"));if(""===we.password)return x(U("entryPassword")),!1;return!/^[A-Za-z0-9!@#$%^&*_()<>.?\/\\{}[\]|,~+:;']+$/.test(we.password)||we.password.length<6||we.password.length>20?(x(U("setPasswordTips")),!1):""===we.password1?(x(U("surePassword")),!1):we.password!==we.password1?(x(U("noSamePassword")),!1):void(de.value=!0)},fe=()=>{de.value=!1,ue.value=!0,L({username:0==ce.value?we.username:`${me.value} ${we.username}`,password:we.password,confirmPassword:we.password1,re_password:we.password1,type:0==ce.value?2:1}).then((async e=>{await X[_](e),await k().then((e=>{e.id?localStorage.setItem("sellerId",e.id):localStorage.removeItem("sellerId")})).catch((()=>{ue.value=!1})),ue.value=!1,x.success(U("registerSuccess")),Y.push("/shop")})).catch((e=>{ue.value=!1}))},ge=()=>{Y.back()};return(e,a)=>{const s=V;return b(),d("div",{style:y({"background-image":"url("+ee.value.href+")"}),class:"login-register-content"},[i(c(I),{successText:c(U)("vertifyPass"),failText:c(U)("vertifuFail"),sliderText:c(U)("vertifyTips"),imgs:ne,show:de.value,onSuccess:fe},null,8,["successText","failText","sliderText","show"]),u("div",C,[u("h2",null,p(c(U)("register")),1)]),u("div",S,[u("img",{src:c(ae).logo.href,alt:""},null,8,z),u("h2",null,p(c(ae).name),1)]),u("div",R,[(b(),d(m,null,w(ie,((e,a)=>u("div",{key:a,class:f([{active:a===ce.value},"item"]),onClick:e=>(e=>{e!==ce.value&&(ce.value=e,we.username="",we.password="",we.password1="")})(a)},p(c(U)(e)),11,$))),64))]),u("div",A,[0===ce.value?(b(),d("div",N,[u("div",Z,p(c(U)("email")),1),u("div",E,[i(T,{modelValue:we.username,"onUpdate:modelValue":a[0]||(a[0]=e=>we.username=e),type:"text",placeholder:c(U)("entryEmail"),clear:""},null,8,["modelValue","placeholder"])])])):v("v-if",!0),1===ce.value?(b(),d("div",B,[u("div",D,p(c(U)("phoneNum")),1),u("div",F,[i(T,{modelValue:we.username,"onUpdate:modelValue":a[1]||(a[1]=e=>we.username=e),codeNum:me.value,"onUpdate:codeNum":a[2]||(a[2]=e=>me.value=e),type:"tel",placeholder:c(U)("entryPhone"),clear:"","area-code":""},null,8,["modelValue","codeNum","placeholder"])])])):v("v-if",!0),u("div",G,[u("div",M,p(c(U)("password")),1),u("div",q,[i(T,{modelValue:we.password,"onUpdate:modelValue":a[3]||(a[3]=e=>we.password=e),type:"password",placeholder:c(U)("entryPassword"),clear:"","show-password":""},null,8,["modelValue","placeholder"])])]),u("div",H,[u("div",J,p(c(U)("repassword")),1),u("div",K,[i(T,{modelValue:we.password1,"onUpdate:modelValue":a[4]||(a[4]=e=>we.password1=e),type:"password",placeholder:c(U)("surePassword"),clear:"","show-password":""},null,8,["modelValue","placeholder"])])]),u("div",{class:f(["link-content",{"is-ar":c(j)}])},[O,u("p",null,[g(p(c(U)("hasAccount"))+" ",1),u("span",{class:"link",onClick:ge},p(c(U)("goLogin")),1)])],2),u("div",Q,[i(s,{type:"custom",loading:ue.value,block:"",onClick:ve},{default:h((()=>[g(p(c(U)("register")),1)])),_:1},8,["loading"])])])],4)}}},[["__scopeId","data-v-8d5a9da1"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/register/index.vue"]]);export{W as default};
