import{_ as e,ci as a,r as t,u as s,j as n,Y as o,q as r,s as i,J as l,c as d,a as c,e as u,b as p,L as h,x as m,n as f,T as v,B as g,o as y,D as x,E as w}from"./index-3d21abf8.js";import{N as _}from"./index-cfdda867.js";import{l as b}from"./index-cfaf3bc2.js";import"./use-placeholder-c97cb410.js";import"./stringify-d53955b2.js";import"./___vite-browser-external_commonjs-proxy-9405bfc0.js";const j=(e=>(x("data-v-cdd47d6d"),e=e(),w(),e))((()=>c("div",{class:"fixed-header-spacer"},null,-1))),k=["src"],I=e({__name:"index",setup(e){const{height:x}=a();t(null);const w=x.value-46,{t:I}=s(),L=n(),q=t(!1);let C="";C=location.host;const E=o();let S=null;S="partyId"in E.query?{token:L.userInfo.token,lang:localStorage.getItem("lang")||"en",height:`${w}px`,partyid:E.query.partyId,name:E.query.username}:{token:L.userInfo.token,lang:localStorage.getItem("lang")||"en",height:`${w}px`};let $=t("");$.value="name"in S?S.name:I("消息中心");const D=t("");r((()=>{const e=navigator.userAgent.toLowerCase();q.value=/iphone|ipad|ipod/.test(e)||!0===window.navigator.standalone,q.value&&document.body.classList.add("ios-device")})),i((async()=>{v.loading({duration:0});let e="";try{await g().then((a=>{e=a.avatar||""}))}catch(t){}S.nohead=!0,S.type="shop",e&&(S.selfimg=e),D.value="https://"+C+"/chat/#/h5/message/blue?"+b.stringify(S),window.addEventListener("message",A,!1);const a=document.querySelector("iframe");a&&(a.onerror=e=>{v.clear()},a.onload=()=>{v.clear();try{a.contentWindow.postMessage({type:"init",data:S},"*")}catch(t){}})}));const A=e=>{try{if(e.origin!==`https://${C}`)return;const{type:a,data:t}=e.data;"ready"===a||"error"===a&&v.clear()}catch(a){}};l((()=>{window.removeEventListener("message",A)}));const B=()=>{try{window.history.length>1?window.history.back():router.push("/")}catch(e){router.push("/")}};return(e,a)=>{const t=_;return y(),d("div",{class:f(["page-main-content has-fixed-header",{"ios-device":q.value}])},[c("div",null,[u(t,{ref:"navEl",title:p($),"left-arrow":"",onClickLeft:B,fixed:""},null,8,["title"])]),j,D.value?(y(),d("div",{key:0,style:h({height:w+"px"}),class:"iframe-content"},[c("iframe",{src:D.value,class:"flex-1"},null,8,k)],4)):m("v-if",!0)],2)}}},[["__scopeId","data-v-cdd47d6d"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/messageCenter/index.vue"]]);export{I as default};
