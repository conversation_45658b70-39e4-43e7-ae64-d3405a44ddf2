System.register(["./index-legacy-46a00900.js","./index-legacy-6b2aee05.js","./index-legacy-73ef4548.js","./product.api-legacy-82d5f74f.js","./editProfit-legacy-0c93f5c4.js","./index-legacy-a4cde014.js","./stringify-legacy-93b715ae.js","./___vite-browser-external_commonjs-proxy-legacy-70ac8ee8.js","./index-legacy-72e00c5f.js","./use-route-legacy-be86ac1c.js","./index-legacy-1fd93e33.js","./index-legacy-71866ecf.js","./index-legacy-f9c0699e.js","./index-legacy-f9c0699e2.js","./index-legacy-b65b115e.js","./index-legacy-9e9f7160.js","./function-call-legacy-3e53b389.js","./use-placeholder-legacy-f22ccc27.js","./index-legacy-0ade4760.js","./index-legacy-df3e01aa.js","./use-id-legacy-df76950f.js"],(function(e,t){"use strict";var a,i,l,c,o,n,s,d,r,u,p,v,b,f,g,m,h,x,w,k,y,j,A,I,Z,D,E,R,S,T,B,G,Q,C,J,N,U=document.createElement("style");return U.textContent='@charset "UTF-8";.product[data-v-4cde41b8]{padding-top:0;padding-bottom:50px;min-height:100vh;background:#EFF2F6;overflow:auto;-webkit-overflow-scrolling:touch}.product .list[data-v-4cde41b8]{overflow:visible}.product .list .van-pull-refresh[data-v-4cde41b8]{touch-action:pan-y;-webkit-overflow-scrolling:touch}.product .list .item[data-v-4cde41b8]{border-radius:4px;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.product .list .item .left[data-v-4cde41b8]{-webkit-box-align:center;-webkit-align-items:center;align-items:center;background:#fff;padding:12px;border-radius:4px}.product .list .item .left .product-info[data-v-4cde41b8]{padding-left:10px;-webkit-box-flex:1;-webkit-flex:1 1;flex:1 1}.product .list .item .left .product-info.is-ar[data-v-4cde41b8]{padding-left:0;padding-right:10px}.product .list .item .left .product-info .name[data-v-4cde41b8]{font-size:14px;color:#333;line-height:16px;font-weight:700;word-break:break-all;overflow:hidden;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;text-overflow:ellipsis;margin-bottom:5px}.product .list .item .left .product-info .Specification[data-v-4cde41b8]{font-size:12px;color:#999}.product .list .item .left .product-info .money[data-v-4cde41b8]{color:var(--site-main-color);font-weight:700}.product .list .product-img-wrap[data-v-4cde41b8]{position:relative}.product .fixed-wrap[data-v-4cde41b8]{height:50px;position:fixed;bottom:0;left:0;width:100%;background:#fff;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.product .fixed-wrap .submit-but[data-v-4cde41b8]{min-width:130px;padding:0 15px;background:var(--site-main-color);display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;height:100%;color:#fff}.product .fixed-wrap .submit-but.disabled[data-v-4cde41b8]{width:100%;background-color:#666}.check-icon[data-v-4cde41b8]{width:20px;height:20px;border-radius:50%;border:1px solid #ccc;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center}.check-icon>.iconfont[data-v-4cde41b8]{color:#fff;font-size:12px;opacity:0}.check-icon.check-true[data-v-4cde41b8]{border-color:var(--site-main-color);background-color:var(--site-main-color)}.check-icon.check-true>.iconfont[data-v-4cde41b8]{opacity:1}.nav_filtering_icon[data-v-4cde41b8]{width:18px;height:18px}.dropdown[data-v-4cde41b8]{padding:0 15px;margin:10px 0;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between;width:100%}.dropdown .dropdownitem[data-v-4cde41b8]{width:48%;border-radius:5px;overflow:hidden}.triangle[data-v-4cde41b8]{width:0;height:0;border:6px solid transparent;border-top-color:#000;position:relative;top:3px}.classify-pop-content[data-v-4cde41b8]{width:18rem;min-height:3rem}.classify-pop-content .title[data-v-4cde41b8]{width:100%;height:3rem;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;font-size:16px;color:#333;font-weight:700;border-bottom:1px solid #eaeaea}.classify-pop-content .content[data-v-4cde41b8]{max-height:60vh;overflow-y:scroll}.classify-pop-content .content .classify-item[data-v-4cde41b8]{width:100%;height:3rem;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;position:relative;border-bottom:1px solid #eaeaea}.classify-pop-content .content .classify-item[data-v-4cde41b8]:last-child{border-bottom:none}.classify-pop-content .content .classify-item .yes[data-v-4cde41b8]{color:var(--site-main-color);position:absolute;right:30px;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}\n',document.head.appendChild(U),{setters:[e=>{a=e._,i=e.i,l=e.u,c=e.l,o=e.r,n=e.q,s=e.J,d=e.B,r=e.an,u=e.m,p=e.av,v=e.I,b=e.bn,f=e.c,g=e.e,m=e.w,h=e.x,x=e.a,w=e.t,k=e.n,y=e.b,j=e.ak,A=e.T,I=e.o,Z=e.f,D=e.F,E=e.y,R=e.A,S=e.aF,T=e.D,B=e.E},e=>{G=e.P},e=>{Q=e.L},e=>{C=e.c,J=e.g},e=>{N=e.e},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{}],execute:function(){const t=e=>(T("data-v-4cde41b8"),e=e(),B(),e),U={class:"product page-main-content has-fixed-header"},M=["src"],z=t((()=>x("div",{class:"fixed-header-spacer"},null,-1))),F={class:"dropdown"},W={class:"p-2.5 bg-white flex justify-between items-center w-full h-full"},X={class:"text-xs"},L=t((()=>x("div",{class:"triangle"},null,-1))),Y={class:"p-2.5 bg-white flex justify-between items-center w-full h-full"},V={class:"text-xs"},H=t((()=>x("div",{class:"triangle"},null,-1))),O={class:"classify-pop-content"},P={class:"title"},_={class:"content"},q=["onClick"],K={class:"classify-pop-content"},$={class:"title"},ee={class:"content"},te=["onClick"],ae={class:"pl-3 pr-3"},ie=["onClick"],le=[t((()=>x("i",{class:"iconfont icon-duigoux"},null,-1)))],ce={class:"flex-1 flex left"},oe={class:"product-img-wrap"},ne=["src"],se={class:"name"},de={class:"Specification"},re={class:"money"},ue=[t((()=>x("i",{class:"iconfont icon-duigoux"},null,-1)))],pe={__name:"list",setup(e){const t=i(),{t:a}=l(),T=c(),B=o(1),pe=o([]),ve=o(!1),be=o(!1),fe=o(!1),ge=o(!1),me=o(null);let he=0,xe=0,we=!1;const ke=o(!1),ye=o(!1),je=o([]),Ae=o(!1),Ie=o(0),Ze=o([]),De=o(a("一级分类")),Ee=o(a("二级分类")),Re=o(!1),Se=o(!1),Te=o(""),Be=o("");let Ge=null;n((()=>{sessionStorage.setItem("productReload",!0),Ce(),Ye(),Ge=Qe()})),s((()=>{Ge&&Ge()}));const Qe=()=>{let e=null;const t=()=>{e&&clearTimeout(e),e=setTimeout((()=>{const e=document.documentElement.scrollTop||document.body.scrollTop,t=e<=3;ge.value=!t,e>3&&(we=!1)}),16)};return window.addEventListener("scroll",t,{passive:!0}),()=>{window.removeEventListener("scroll",t),e&&clearTimeout(e)}},Ce=async()=>{let e={};await d().then((t=>{e=t||{}})),await r().then((t=>{const a=Number(t.status);let i=a;2===a?i=3:3===a&&(i=2),e.authStatus=i}));const{avatar:t,name:a,authStatus:i}=e;t&&a?0!==i&&1!==i&&2!==i||(Ie.value=2):Ie.value=1,Ae.value=!0},Je=()=>{const e=1===Ie.value?"/shop/settings":"/name";j(e)},Ne=()=>{ye.value=!1},Ue=()=>{je.value=[],pe.value.map((e=>{e.check&&je.value.push(e.id)})),je.value.length?ye.value=!0:A(a("请选择商品"))},Me=e=>{const t=document.documentElement.scrollTop||document.body.scrollTop;he=e.touches[0].clientY,xe=Date.now(),we=t<=5},ze=e=>{if(!we)return;const t=e.touches[0].clientY-he;if((document.documentElement.scrollTop||document.body.scrollTop)>5)return we=!1,void(ge.value=!0);ge.value=t<30},Fe=e=>{Date.now(),setTimeout((()=>{we=!1}),100)},We=()=>{(document.documentElement.scrollTop||document.body.scrollTop)>5||!we?be.value=!1:(B.value=1,fe.value=!1,Le(!0))},Xe=()=>{be.value?ve.value=!1:Le(!1)},Le=(e=!1)=>{const t={pageNum:B.value,pageSize:20};Be.value?t.secondaryCategoryId=Be.value:Te.value&&(t.categoryId=Te.value),J(t).then((t=>{const a=t.pageList||[];a.forEach((e=>{e.check=!1})),e?pe.value=a:pe.value.push(...a),B.value++,0===a.length&&(fe.value=!0),ve.value=!1,be.value=!1,A.clear()})).catch((e=>{ve.value=!1,be.value=!1,A.clear()}))},Ye=()=>{C().then((e=>{const t=e||[];t.unshift({name:a("全部分类"),categoryId:"",subList:[]}),Ze.value=t.filter((e=>e.name))}))},Ve=u((()=>{const e=Ze.value.find((e=>e.categoryId===Te.value));let t=[{name:a("全部分类"),categoryId:""}];if(e&&e.subList&&e.subList.length){const a=e.subList.filter((e=>e.name));t=[...t,...a]}return t})),He=(e,t)=>{t?(De.value=e.name,Te.value=e.categoryId,Re.value=!1,Ee.value=a("二级分类"),Be.value=""):(Ee.value=e.name,Be.value=e.categoryId,Se.value=!1),A.loading({duration:0,forbidClick:!0}),ke.value=!1,B.value=1,pe.value=[],fe.value=!1,Le(!0)},Oe=()=>{ke.value=!ke.value,pe.value.forEach((e=>{e.check=ke.value}))},Pe=()=>{T.push("/search?id=2")},_e=()=>{Ye(),We()};return(e,i)=>{const l=p("fx-header"),c=v,o=b,n=Q,s=G;return I(),f("div",U,[g(l,{fixed:""},{title:m((()=>[Z(w(y(a)("product.2")),1)])),right:m((()=>[x("img",{onClick:Pe,class:"nav_filtering_icon",src:y("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAC0AAAAtCAYAAAA6GuKaAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyFpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNS1jMDE0IDc5LjE1MTQ4MSwgMjAxMy8wMy8xMy0xMjowOToxNSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIChXaW5kb3dzKSIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDo2RkExMEI5MkQ1NEMxMUVEQUQ2RDhGQzcyMkRFNDQ2RSIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDo2RkExMEI5M0Q1NEMxMUVEQUQ2RDhGQzcyMkRFNDQ2RSI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjZGQTEwQjkwRDU0QzExRURBRDZEOEZDNzIyREU0NDZFIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjZGQTEwQjkxRDU0QzExRURBRDZEOEZDNzIyREU0NDZFIi8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8++lp+GgAAAvdJREFUeNrUmd1t4kAQxzfWvcepIE4FZwTvcSo4UoFNBSEV4FQQUwFOBXepAHhHiq8Clg7cATcbjXXLZNZe468w0l+KIIYf49n58tXxeBSXZo64QLsaj8dNrvdAAcgHXYNcVA46gCQoA21MH7Db7Wp/6Y8zQBXkFBQioI3lCP4OSvsMDwW7Rj3VABb4v+qHrkB7UNQ1tIveWSN4U/M0eK8LaBWrHxgK3C3fgp5BD6A7dUZQN6AR6BH0hrHNwe8nk8mizYMYGuJPwS5BCf5taxHqnnkvgQP53NTTJuAX9GhcE1jg56nwmjGen4PHV02gfQZY4u0+B5aDV+H0h94J21BxmDj7zQA/YL5tyyTG+5K8HgP4tC50TE50ASw7Km5zPKi6rQDctYWOmCzRJbAOnpEUu7CFXjCHrmtgVcZzDBV6ML0q6IAJi7ivBgjAJTqJ3oFS6Ijxct9G835oim1Hy8u6l9O+iTFM3khs+yZo2k9sBmyVae6e2kK/DwidkRC5N0H7zIWDGIaIJMWOhb5mqtWQ9lePay71OdgAfRfgix1s5SVCezbQ+zoX9GC3zNDxBfrA9NNDml4Fc8woX6AzZuoexDBT+IZMcgJNK+CvAb1sVZ0dpgoFNXcabVpYUdZPssfSti3sMDQC4mkJ8ZyVQSfk9acBMsmrbXvsaGmFtoWrHr08JwdQeTm1KS4xE9vznjLGos4Q4pDy+cLcsrDj6rcmB39b5mWujCdMmkk78ngBTGfT6Jze45FpWl6rxvozUtuHaHFrmhv2HbFovlsuvJsaasHn+2Xrg7IuTwp+FeaJ08W4jadc8X8hv2eqXl4X3OaZS4J5u2yuk9gnSA1UfelPTGWmCrvFHx8wKfbTcbgTqQ0ttA9tq+DkmKmS4kEReDayBbcdAjY4ls0ajmQF7B2twpjmZjahUndySfELR9ivbC2uOeD/qjNyI0p23LbgTZ8j6oODq0kimBQVS3juOWJVqLQF3WTXYSrvJvDRt33MXBIq0T8BBgA0uu1O4qhZPgAAAABJRU5ErkJggg==")},null,8,M)])),_:1}),z,h(" 分类选择器 "),x("div",F,[x("div",{class:"dropdownitem one h-10.5",onClick:i[0]||(i[0]=e=>Re.value=!0)},[x("div",W,[x("span",X,w(De.value),1),L])]),x("div",{class:"dropdownitem one h-10.5",onClick:i[1]||(i[1]=e=>Se.value=!0)},[x("div",Y,[x("span",V,w(Ee.value),1),H])])]),h(" 一级分类弹窗 "),g(o,{show:Re.value,"onUpdate:show":i[2]||(i[2]=e=>Re.value=e),round:""},{default:m((()=>[x("div",O,[x("div",P,w(y(a)("一级分类")),1),x("div",_,[(I(!0),f(D,null,E(Ze.value,(e=>(I(),f("div",{key:e.categoryId,class:"classify-item",onClick:t=>He(e,!0)},[Z(w(e.name)+" ",1),Te.value===e.categoryId?(I(),R(c,{key:0,name:"success",class:"yes"})):h("v-if",!0)],8,q)))),128))])])])),_:1},8,["show"]),h(" 二级分类弹窗 "),g(o,{show:Se.value,"onUpdate:show":i[3]||(i[3]=e=>Se.value=e),round:""},{default:m((()=>[x("div",K,[x("div",$,w(y(a)("二级分类")),1),x("div",ee,[(I(!0),f(D,null,E(y(Ve),(e=>(I(),f("div",{key:e.categoryId,class:"classify-item",onClick:t=>He(e,!1)},[Z(w(e.name)+" ",1),Be.value===e.categoryId?(I(),R(c,{key:0,name:"success",class:"yes"})):h("v-if",!0)],8,te)))),128))])])])),_:1},8,["show"]),h(" 商品列表 "),x("div",{class:k(["list mt-2 mb-4",y(t)?"ml-4":"mr-4"])},[g(s,{ref_key:"pullRefreshRef",ref:me,"loading-text":e.$t("刷新中"),"loosing-text":e.$t("释放以刷新"),"pulling-text":e.$t("下拉以刷新"),"head-height":50,"pull-distance":150,"success-text":e.$t("刷新成功"),"success-duration":1e3,disabled:ge.value,modelValue:be.value,"onUpdate:modelValue":i[5]||(i[5]=e=>be.value=e),onRefresh:We,onTouchstart:Me,onTouchmove:ze,onTouchend:Fe},{default:m((()=>[g(n,{loading:ve.value,"onUpdate:loading":i[4]||(i[4]=e=>ve.value=e),"loading-text":e.$t("加载中"),finished:fe.value,"finished-text":y(a)("product.3"),onLoad:Xe},{default:m((()=>[(I(!0),f(D,null,E(pe.value,((e,a)=>(I(),f("div",{class:"item pb-3 pt-3 flex",key:a},[x("div",ae,[x("div",{class:k(["check-icon",[e.check?"check-true ":"check"]]),onClick:t=>e.check=!e.check},le,10,ie)]),x("div",ce,[x("div",oe,[x("img",{class:"w-20 h-20",style:{"object-fit":"contain"},src:e.imgUrl1},null,8,ne)]),x("div",{class:k(["product-info",{"is-ar":y(t)}])},[x("div",se,w(e.name),1),x("div",de,[x("span",null,w(e.categoryName),1)]),x("div",re,"$"+w(y(S)(e.systemPrice)),1)],2)])])))),128))])),_:1},8,["loading","loading-text","finished","finished-text"])])),_:1},8,["loading-text","loosing-text","pulling-text","success-text","disabled","modelValue"]),h(" 底部操作栏 "),Ae.value?(I(),f("div",{key:0,class:k(["flex fixed-wrap",{"pl-3":!y(t)&&!Ie.value,"pr-3":y(t)&&!Ie.value}])},[Ie.value?h("v-if",!0):(I(),f("div",{key:0,class:"flex-1 flex",onClick:Oe},[x("div",{class:k(["check-icon",[ke.value?"check-true ":"check"]])},ue,2),x("div",{class:k(y(t)?"pr-2":"pl-2")},w(y(a)("product.5"))+": "+w(pe.value.filter((e=>e.check)).length),3)])),Ie.value?(I(),f("div",{key:2,class:"submit-but disabled",onClick:Je},w(1===Ie.value?y(a)("product.34"):y(a)("product.35")),1)):(I(),f("div",{key:1,class:"submit-but",onClick:Ue},w(y(a)("product.6")),1))],2)):h("v-if",!0)],2),h(" 编辑利润弹窗 "),g(N,{isEdit:ye.value,onUpdate:_e,productArry:je.value,onClose:Ne},null,8,["isEdit","productArry"])])}}};e("default",a(pe,[["__scopeId","data-v-4cde41b8"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/product/list.vue"]]))}}}));
