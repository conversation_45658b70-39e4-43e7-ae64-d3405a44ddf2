import{r as t}from"./___vite-browser-external_commonjs-proxy-9405bfc0.js";var e,r="undefined"!=typeof Symbol&&Symbol,o=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var t={},e=Symbol("test"),r=Object(e);if("string"==typeof e)return!1;if("[object Symbol]"!==Object.prototype.toString.call(e))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(e in t[e]=42,t)return!1;if("function"==typeof Object.keys&&0!==Object.keys(t).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(t).length)return!1;var o=Object.getOwnPropertySymbols(t);if(1!==o.length||o[0]!==e)return!1;if(!Object.prototype.propertyIsEnumerable.call(t,e))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var n=Object.getOwnPropertyDescriptor(t,e);if(42!==n.value||!0!==n.enumerable)return!1}return!0},n=Array.prototype.slice,a=Object.prototype.toString,i=function(t){var e=this;if("function"!=typeof e||"[object Function]"!==a.call(e))throw new TypeError("Function.prototype.bind called on incompatible "+e);for(var r,o=n.call(arguments,1),i=Math.max(0,e.length-o.length),p=[],c=0;c<i;c++)p.push("$"+c);if(r=Function("binder","return function ("+p.join(",")+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof r){var a=e.apply(this,o.concat(n.call(arguments)));return Object(a)===a?a:this}return e.apply(t,o.concat(n.call(arguments)))})),e.prototype){var f=function(){};f.prototype=e.prototype,r.prototype=new f,f.prototype=null}return r},p=Function.prototype.bind||i,c=p.call(Function.call,Object.prototype.hasOwnProperty),f=SyntaxError,l=Function,u=TypeError,y=function(t){try{return l('"use strict"; return ('+t+").constructor;")()}catch(e){}},s=Object.getOwnPropertyDescriptor;if(s)try{s({},"")}catch(ve){s=null}var d=function(){throw new u},g=s?function(){try{return d}catch(t){try{return s(arguments,"callee").get}catch(e){return d}}}():d,b="function"==typeof r&&"function"==typeof Symbol&&"symbol"==typeof r("foo")&&"symbol"==typeof Symbol("bar")&&o(),m=Object.getPrototypeOf||function(t){return t.__proto__},h={},v="undefined"==typeof Uint8Array?e:m(Uint8Array),S={"%AggregateError%":"undefined"==typeof AggregateError?e:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?e:ArrayBuffer,"%ArrayIteratorPrototype%":b?m([][Symbol.iterator]()):e,"%AsyncFromSyncIteratorPrototype%":e,"%AsyncFunction%":h,"%AsyncGenerator%":h,"%AsyncGeneratorFunction%":h,"%AsyncIteratorPrototype%":h,"%Atomics%":"undefined"==typeof Atomics?e:Atomics,"%BigInt%":"undefined"==typeof BigInt?e:BigInt,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?e:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":"undefined"==typeof Float32Array?e:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?e:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?e:FinalizationRegistry,"%Function%":l,"%GeneratorFunction%":h,"%Int8Array%":"undefined"==typeof Int8Array?e:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?e:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?e:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":b?m(m([][Symbol.iterator]())):e,"%JSON%":"object"==typeof JSON?JSON:e,"%Map%":"undefined"==typeof Map?e:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&b?m((new Map)[Symbol.iterator]()):e,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?e:Promise,"%Proxy%":"undefined"==typeof Proxy?e:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":"undefined"==typeof Reflect?e:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?e:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&b?m((new Set)[Symbol.iterator]()):e,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?e:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":b?m(""[Symbol.iterator]()):e,"%Symbol%":b?Symbol:e,"%SyntaxError%":f,"%ThrowTypeError%":g,"%TypedArray%":v,"%TypeError%":u,"%Uint8Array%":"undefined"==typeof Uint8Array?e:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?e:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?e:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?e:Uint32Array,"%URIError%":URIError,"%WeakMap%":"undefined"==typeof WeakMap?e:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?e:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?e:WeakSet},A=function t(e){var r;if("%AsyncFunction%"===e)r=y("async function () {}");else if("%GeneratorFunction%"===e)r=y("function* () {}");else if("%AsyncGeneratorFunction%"===e)r=y("async function* () {}");else if("%AsyncGenerator%"===e){var o=t("%AsyncGeneratorFunction%");o&&(r=o.prototype)}else if("%AsyncIteratorPrototype%"===e){var n=t("%AsyncGenerator%");n&&(r=m(n.prototype))}return S[e]=r,r},j={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},O=p,P=c,w=O.call(Function.call,Array.prototype.concat),E=O.call(Function.apply,Array.prototype.splice),x=O.call(Function.call,String.prototype.replace),F=O.call(Function.call,String.prototype.slice),R=O.call(Function.call,RegExp.prototype.exec),k=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,I=/\\(\\)?/g,M=function(t,e){var r,o=t;if(P(j,o)&&(o="%"+(r=j[o])[0]+"%"),P(S,o)){var n=S[o];if(n===h&&(n=A(o)),void 0===n&&!e)throw new u("intrinsic "+t+" exists, but is not available. Please file an issue!");return{alias:r,name:o,value:n}}throw new f("intrinsic "+t+" does not exist!")},N=function(t,e){if("string"!=typeof t||0===t.length)throw new u("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof e)throw new u('"allowMissing" argument must be a boolean');if(null===R(/^%?[^%]*%?$/,t))throw new f("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=function(t){var e=F(t,0,1),r=F(t,-1);if("%"===e&&"%"!==r)throw new f("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==e)throw new f("invalid intrinsic syntax, expected opening `%`");var o=[];return x(t,k,(function(t,e,r,n){o[o.length]=r?x(n,I,"$1"):e||t})),o}(t),o=r.length>0?r[0]:"",n=M("%"+o+"%",e),a=n.name,i=n.value,p=!1,c=n.alias;c&&(o=c[0],E(r,w([0,1],c)));for(var l=1,y=!0;l<r.length;l+=1){var d=r[l],g=F(d,0,1),b=F(d,-1);if(('"'===g||"'"===g||"`"===g||'"'===b||"'"===b||"`"===b)&&g!==b)throw new f("property names with quotes must have matching quotes");if("constructor"!==d&&y||(p=!0),P(S,a="%"+(o+="."+d)+"%"))i=S[a];else if(null!=i){if(!(d in i)){if(!e)throw new u("base intrinsic for "+t+" exists, but the property is not available.");return}if(s&&l+1>=r.length){var m=s(i,d);i=(y=!!m)&&"get"in m&&!("originalValue"in m.get)?m.get:i[d]}else y=P(i,d),i=i[d];y&&!p&&(S[a]=i)}}return i},U={exports:{}};!function(t){var e=p,r=N,o=r("%Function.prototype.apply%"),n=r("%Function.prototype.call%"),a=r("%Reflect.apply%",!0)||e.call(n,o),i=r("%Object.getOwnPropertyDescriptor%",!0),c=r("%Object.defineProperty%",!0),f=r("%Math.max%");if(c)try{c({},"a",{value:1})}catch(ve){c=null}t.exports=function(t){var r=a(e,n,arguments);i&&c&&(i(r,"length").configurable&&c(r,"length",{value:1+f(0,t.length-(arguments.length-1))}));return r};var l=function(){return a(e,o,arguments)};c?c(t.exports,"apply",{value:l}):t.exports.apply=l}(U);var D=N,T=U.exports,W=T(D("String.prototype.indexOf")),_="function"==typeof Map&&Map.prototype,B=Object.getOwnPropertyDescriptor&&_?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,C=_&&B&&"function"==typeof B.get?B.get:null,G=_&&Map.prototype.forEach,$="function"==typeof Set&&Set.prototype,L=Object.getOwnPropertyDescriptor&&$?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,V=$&&L&&"function"==typeof L.get?L.get:null,q=$&&Set.prototype.forEach,z="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,H="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,J="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,Q=Boolean.prototype.valueOf,K=Object.prototype.toString,X=Function.prototype.toString,Y=String.prototype.match,Z=String.prototype.slice,tt=String.prototype.replace,et=String.prototype.toUpperCase,rt=String.prototype.toLowerCase,ot=RegExp.prototype.test,nt=Array.prototype.concat,at=Array.prototype.join,it=Array.prototype.slice,pt=Math.floor,ct="function"==typeof BigInt?BigInt.prototype.valueOf:null,ft=Object.getOwnPropertySymbols,lt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,ut="function"==typeof Symbol&&"object"==typeof Symbol.iterator,yt="function"==typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===ut||"symbol")?Symbol.toStringTag:null,st=Object.prototype.propertyIsEnumerable,dt=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(t){return t.__proto__}:null);function gt(t,e){if(t===1/0||t===-1/0||t!=t||t&&t>-1e3&&t<1e3||ot.call(/e/,e))return e;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof t){var o=t<0?-pt(-t):pt(t);if(o!==t){var n=String(o),a=Z.call(e,n.length+1);return tt.call(n,r,"$&_")+"."+tt.call(tt.call(a,/([0-9]{3})/g,"$&_"),/_$/,"")}}return tt.call(e,r,"$&_")}var bt=t,mt=bt.custom,ht=Ot(mt)?mt:null;function vt(t,e,r){var o="double"===(r.quoteStyle||e)?'"':"'";return o+t+o}function St(t){return tt.call(String(t),/"/g,"&quot;")}function At(t){return!("[object Array]"!==Et(t)||yt&&"object"==typeof t&&yt in t)}function jt(t){return!("[object RegExp]"!==Et(t)||yt&&"object"==typeof t&&yt in t)}function Ot(t){if(ut)return t&&"object"==typeof t&&t instanceof Symbol;if("symbol"==typeof t)return!0;if(!t||"object"!=typeof t||!lt)return!1;try{return lt.call(t),!0}catch(ve){}return!1}var Pt=Object.prototype.hasOwnProperty||function(t){return t in this};function wt(t,e){return Pt.call(t,e)}function Et(t){return K.call(t)}function xt(t,e){if(t.indexOf)return t.indexOf(e);for(var r=0,o=t.length;r<o;r++)if(t[r]===e)return r;return-1}function Ft(t,e){if(t.length>e.maxStringLength){var r=t.length-e.maxStringLength,o="... "+r+" more character"+(r>1?"s":"");return Ft(Z.call(t,0,e.maxStringLength),e)+o}return vt(tt.call(tt.call(t,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,Rt),"single",e)}function Rt(t){var e=t.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[e];return r?"\\"+r:"\\x"+(e<16?"0":"")+et.call(e.toString(16))}function kt(t){return"Object("+t+")"}function It(t){return t+" { ? }"}function Mt(t,e,r,o){return t+" ("+e+") {"+(o?Nt(r,o):at.call(r,", "))+"}"}function Nt(t,e){if(0===t.length)return"";var r="\n"+e.prev+e.base;return r+at.call(t,","+r)+"\n"+e.prev}function Ut(t,e){var r=At(t),o=[];if(r){o.length=t.length;for(var n=0;n<t.length;n++)o[n]=wt(t,n)?e(t[n],t):""}var a,i="function"==typeof ft?ft(t):[];if(ut){a={};for(var p=0;p<i.length;p++)a["$"+i[p]]=i[p]}for(var c in t)wt(t,c)&&(r&&String(Number(c))===c&&c<t.length||ut&&a["$"+c]instanceof Symbol||(ot.call(/[^\w$]/,c)?o.push(e(c,t)+": "+e(t[c],t)):o.push(c+": "+e(t[c],t))));if("function"==typeof ft)for(var f=0;f<i.length;f++)st.call(t,i[f])&&o.push("["+e(i[f])+"]: "+e(t[i[f]],t));return o}var Dt=N,Tt=function(t,e){var r=D(t,!!e);return"function"==typeof r&&W(t,".prototype.")>-1?T(r):r},Wt=function t(e,r,o,n){var a=r||{};if(wt(a,"quoteStyle")&&"single"!==a.quoteStyle&&"double"!==a.quoteStyle)throw new TypeError('option "quoteStyle" must be "single" or "double"');if(wt(a,"maxStringLength")&&("number"==typeof a.maxStringLength?a.maxStringLength<0&&a.maxStringLength!==1/0:null!==a.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var i=!wt(a,"customInspect")||a.customInspect;if("boolean"!=typeof i&&"symbol"!==i)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(wt(a,"indent")&&null!==a.indent&&"\t"!==a.indent&&!(parseInt(a.indent,10)===a.indent&&a.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(wt(a,"numericSeparator")&&"boolean"!=typeof a.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var p=a.numericSeparator;if(void 0===e)return"undefined";if(null===e)return"null";if("boolean"==typeof e)return e?"true":"false";if("string"==typeof e)return Ft(e,a);if("number"==typeof e){if(0===e)return 1/0/e>0?"0":"-0";var c=String(e);return p?gt(e,c):c}if("bigint"==typeof e){var f=String(e)+"n";return p?gt(e,f):f}var l=void 0===a.depth?5:a.depth;if(void 0===o&&(o=0),o>=l&&l>0&&"object"==typeof e)return At(e)?"[Array]":"[Object]";var u=function(t,e){var r;if("\t"===t.indent)r="\t";else{if(!("number"==typeof t.indent&&t.indent>0))return null;r=at.call(Array(t.indent+1)," ")}return{base:r,prev:at.call(Array(e+1),r)}}(a,o);if(void 0===n)n=[];else if(xt(n,e)>=0)return"[Circular]";function y(e,r,i){if(r&&(n=it.call(n)).push(r),i){var p={depth:a.depth};return wt(a,"quoteStyle")&&(p.quoteStyle=a.quoteStyle),t(e,p,o+1,n)}return t(e,a,o+1,n)}if("function"==typeof e&&!jt(e)){var s=function(t){if(t.name)return t.name;var e=Y.call(X.call(t),/^function\s*([\w$]+)/);if(e)return e[1];return null}(e),d=Ut(e,y);return"[Function"+(s?": "+s:" (anonymous)")+"]"+(d.length>0?" { "+at.call(d,", ")+" }":"")}if(Ot(e)){var g=ut?tt.call(String(e),/^(Symbol\(.*\))_[^)]*$/,"$1"):lt.call(e);return"object"!=typeof e||ut?g:kt(g)}if(function(t){if(!t||"object"!=typeof t)return!1;if("undefined"!=typeof HTMLElement&&t instanceof HTMLElement)return!0;return"string"==typeof t.nodeName&&"function"==typeof t.getAttribute}(e)){for(var b="<"+rt.call(String(e.nodeName)),m=e.attributes||[],h=0;h<m.length;h++)b+=" "+m[h].name+"="+vt(St(m[h].value),"double",a);return b+=">",e.childNodes&&e.childNodes.length&&(b+="..."),b+="</"+rt.call(String(e.nodeName))+">"}if(At(e)){if(0===e.length)return"[]";var v=Ut(e,y);return u&&!function(t){for(var e=0;e<t.length;e++)if(xt(t[e],"\n")>=0)return!1;return!0}(v)?"["+Nt(v,u)+"]":"[ "+at.call(v,", ")+" ]"}if(function(t){return!("[object Error]"!==Et(t)||yt&&"object"==typeof t&&yt in t)}(e)){var S=Ut(e,y);return"cause"in Error.prototype||!("cause"in e)||st.call(e,"cause")?0===S.length?"["+String(e)+"]":"{ ["+String(e)+"] "+at.call(S,", ")+" }":"{ ["+String(e)+"] "+at.call(nt.call("[cause]: "+y(e.cause),S),", ")+" }"}if("object"==typeof e&&i){if(ht&&"function"==typeof e[ht]&&bt)return bt(e,{depth:l-o});if("symbol"!==i&&"function"==typeof e.inspect)return e.inspect()}if(function(t){if(!C||!t||"object"!=typeof t)return!1;try{C.call(t);try{V.call(t)}catch(b){return!0}return t instanceof Map}catch(ve){}return!1}(e)){var A=[];return G&&G.call(e,(function(t,r){A.push(y(r,e,!0)+" => "+y(t,e))})),Mt("Map",C.call(e),A,u)}if(function(t){if(!V||!t||"object"!=typeof t)return!1;try{V.call(t);try{C.call(t)}catch(e){return!0}return t instanceof Set}catch(ve){}return!1}(e)){var j=[];return q&&q.call(e,(function(t){j.push(y(t,e))})),Mt("Set",V.call(e),j,u)}if(function(t){if(!z||!t||"object"!=typeof t)return!1;try{z.call(t,z);try{H.call(t,H)}catch(b){return!0}return t instanceof WeakMap}catch(ve){}return!1}(e))return It("WeakMap");if(function(t){if(!H||!t||"object"!=typeof t)return!1;try{H.call(t,H);try{z.call(t,z)}catch(b){return!0}return t instanceof WeakSet}catch(ve){}return!1}(e))return It("WeakSet");if(function(t){if(!J||!t||"object"!=typeof t)return!1;try{return J.call(t),!0}catch(ve){}return!1}(e))return It("WeakRef");if(function(t){return!("[object Number]"!==Et(t)||yt&&"object"==typeof t&&yt in t)}(e))return kt(y(Number(e)));if(function(t){if(!t||"object"!=typeof t||!ct)return!1;try{return ct.call(t),!0}catch(ve){}return!1}(e))return kt(y(ct.call(e)));if(function(t){return!("[object Boolean]"!==Et(t)||yt&&"object"==typeof t&&yt in t)}(e))return kt(Q.call(e));if(function(t){return!("[object String]"!==Et(t)||yt&&"object"==typeof t&&yt in t)}(e))return kt(y(String(e)));if(!function(t){return!("[object Date]"!==Et(t)||yt&&"object"==typeof t&&yt in t)}(e)&&!jt(e)){var O=Ut(e,y),P=dt?dt(e)===Object.prototype:e instanceof Object||e.constructor===Object,w=e instanceof Object?"":"null prototype",E=!P&&yt&&Object(e)===e&&yt in e?Z.call(Et(e),8,-1):w?"Object":"",x=(P||"function"!=typeof e.constructor?"":e.constructor.name?e.constructor.name+" ":"")+(E||w?"["+at.call(nt.call([],E||[],w||[]),": ")+"] ":"");return 0===O.length?x+"{}":u?x+"{"+Nt(O,u)+"}":x+"{ "+at.call(O,", ")+" }"}return String(e)},_t=Dt("%TypeError%"),Bt=Dt("%WeakMap%",!0),Ct=Dt("%Map%",!0),Gt=Tt("WeakMap.prototype.get",!0),$t=Tt("WeakMap.prototype.set",!0),Lt=Tt("WeakMap.prototype.has",!0),Vt=Tt("Map.prototype.get",!0),qt=Tt("Map.prototype.set",!0),zt=Tt("Map.prototype.has",!0),Ht=function(t,e){for(var r,o=t;null!==(r=o.next);o=r)if(r.key===e)return o.next=r.next,r.next=t.next,t.next=r,r},Jt=String.prototype.replace,Qt=/%20/g,Kt="RFC3986",Xt={default:Kt,formatters:{RFC1738:function(t){return Jt.call(t,Qt,"+")},RFC3986:function(t){return String(t)}},RFC1738:"RFC1738",RFC3986:Kt},Yt=Xt,Zt=Object.prototype.hasOwnProperty,te=Array.isArray,ee=function(){for(var t=[],e=0;e<256;++e)t.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return t}(),re=function(t,e){for(var r=e&&e.plainObjects?Object.create(null):{},o=0;o<t.length;++o)void 0!==t[o]&&(r[o]=t[o]);return r},oe={arrayToObject:re,assign:function(t,e){return Object.keys(e).reduce((function(t,r){return t[r]=e[r],t}),t)},combine:function(t,e){return[].concat(t,e)},compact:function(t){for(var e=[{obj:{o:t},prop:"o"}],r=[],o=0;o<e.length;++o)for(var n=e[o],a=n.obj[n.prop],i=Object.keys(a),p=0;p<i.length;++p){var c=i[p],f=a[c];"object"==typeof f&&null!==f&&-1===r.indexOf(f)&&(e.push({obj:a,prop:c}),r.push(f))}return function(t){for(;t.length>1;){var e=t.pop(),r=e.obj[e.prop];if(te(r)){for(var o=[],n=0;n<r.length;++n)void 0!==r[n]&&o.push(r[n]);e.obj[e.prop]=o}}}(e),t},decode:function(t,e,r){var o=t.replace(/\+/g," ");if("iso-8859-1"===r)return o.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(o)}catch(ve){return o}},encode:function(t,e,r,o,n){if(0===t.length)return t;var a=t;if("symbol"==typeof t?a=Symbol.prototype.toString.call(t):"string"!=typeof t&&(a=String(t)),"iso-8859-1"===r)return escape(a).replace(/%u[0-9a-f]{4}/gi,(function(t){return"%26%23"+parseInt(t.slice(2),16)+"%3B"}));for(var i="",p=0;p<a.length;++p){var c=a.charCodeAt(p);45===c||46===c||95===c||126===c||c>=48&&c<=57||c>=65&&c<=90||c>=97&&c<=122||n===Yt.RFC1738&&(40===c||41===c)?i+=a.charAt(p):c<128?i+=ee[c]:c<2048?i+=ee[192|c>>6]+ee[128|63&c]:c<55296||c>=57344?i+=ee[224|c>>12]+ee[128|c>>6&63]+ee[128|63&c]:(p+=1,c=65536+((1023&c)<<10|1023&a.charCodeAt(p)),i+=ee[240|c>>18]+ee[128|c>>12&63]+ee[128|c>>6&63]+ee[128|63&c])}return i},isBuffer:function(t){return!(!t||"object"!=typeof t)&&!!(t.constructor&&t.constructor.isBuffer&&t.constructor.isBuffer(t))},isRegExp:function(t){return"[object RegExp]"===Object.prototype.toString.call(t)},maybeMap:function(t,e){if(te(t)){for(var r=[],o=0;o<t.length;o+=1)r.push(e(t[o]));return r}return e(t)},merge:function t(e,r,o){if(!r)return e;if("object"!=typeof r){if(te(e))e.push(r);else{if(!e||"object"!=typeof e)return[e,r];(o&&(o.plainObjects||o.allowPrototypes)||!Zt.call(Object.prototype,r))&&(e[r]=!0)}return e}if(!e||"object"!=typeof e)return[e].concat(r);var n=e;return te(e)&&!te(r)&&(n=re(e,o)),te(e)&&te(r)?(r.forEach((function(r,n){if(Zt.call(e,n)){var a=e[n];a&&"object"==typeof a&&r&&"object"==typeof r?e[n]=t(a,r,o):e.push(r)}else e[n]=r})),e):Object.keys(r).reduce((function(e,n){var a=r[n];return Zt.call(e,n)?e[n]=t(e[n],a,o):e[n]=a,e}),n)}},ne=function(){var t,e,r,o={assert:function(t){if(!o.has(t))throw new _t("Side channel does not contain "+Wt(t))},get:function(o){if(Bt&&o&&("object"==typeof o||"function"==typeof o)){if(t)return Gt(t,o)}else if(Ct){if(e)return Vt(e,o)}else if(r)return function(t,e){var r=Ht(t,e);return r&&r.value}(r,o)},has:function(o){if(Bt&&o&&("object"==typeof o||"function"==typeof o)){if(t)return Lt(t,o)}else if(Ct){if(e)return zt(e,o)}else if(r)return function(t,e){return!!Ht(t,e)}(r,o);return!1},set:function(o,n){Bt&&o&&("object"==typeof o||"function"==typeof o)?(t||(t=new Bt),$t(t,o,n)):Ct?(e||(e=new Ct),qt(e,o,n)):(r||(r={key:{},next:null}),function(t,e,r){var o=Ht(t,e);o?o.value=r:t.next={key:e,next:t.next,value:r}}(r,o,n))}};return o},ae=oe,ie=Xt,pe=Object.prototype.hasOwnProperty,ce={brackets:function(t){return t+"[]"},comma:"comma",indices:function(t,e){return t+"["+e+"]"},repeat:function(t){return t}},fe=Array.isArray,le=String.prototype.split,ue=Array.prototype.push,ye=function(t,e){ue.apply(t,fe(e)?e:[e])},se=Date.prototype.toISOString,de=ie.default,ge={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:ae.encode,encodeValuesOnly:!1,format:de,formatter:ie.formatters[de],indices:!1,serializeDate:function(t){return se.call(t)},skipNulls:!1,strictNullHandling:!1},be={},me=function t(e,r,o,n,a,i,p,c,f,l,u,y,s,d,g,b){for(var m,h=e,v=b,S=0,A=!1;void 0!==(v=v.get(be))&&!A;){var j=v.get(e);if(S+=1,void 0!==j){if(j===S)throw new RangeError("Cyclic object value");A=!0}void 0===v.get(be)&&(S=0)}if("function"==typeof c?h=c(r,h):h instanceof Date?h=u(h):"comma"===o&&fe(h)&&(h=ae.maybeMap(h,(function(t){return t instanceof Date?u(t):t}))),null===h){if(a)return p&&!d?p(r,ge.encoder,g,"key",y):r;h=""}if("string"==typeof(m=h)||"number"==typeof m||"boolean"==typeof m||"symbol"==typeof m||"bigint"==typeof m||ae.isBuffer(h)){if(p){var O=d?r:p(r,ge.encoder,g,"key",y);if("comma"===o&&d){for(var P=le.call(String(h),","),w="",E=0;E<P.length;++E)w+=(0===E?"":",")+s(p(P[E],ge.encoder,g,"value",y));return[s(O)+(n&&fe(h)&&1===P.length?"[]":"")+"="+w]}return[s(O)+"="+s(p(h,ge.encoder,g,"value",y))]}return[s(r)+"="+s(String(h))]}var x,F=[];if(void 0===h)return F;if("comma"===o&&fe(h))x=[{value:h.length>0?h.join(",")||null:void 0}];else if(fe(c))x=c;else{var R=Object.keys(h);x=f?R.sort(f):R}for(var k=n&&fe(h)&&1===h.length?r+"[]":r,I=0;I<x.length;++I){var M=x[I],N="object"==typeof M&&void 0!==M.value?M.value:h[M];if(!i||null!==N){var U=fe(h)?"function"==typeof o?o(k,M):k:k+(l?"."+M:"["+M+"]");b.set(e,S);var D=ne();D.set(be,b),ye(F,t(N,U,o,n,a,i,p,c,f,l,u,y,s,d,g,D))}}return F},he=function(t,e){var r,o=t,n=function(t){if(!t)return ge;if(null!==t.encoder&&void 0!==t.encoder&&"function"!=typeof t.encoder)throw new TypeError("Encoder has to be a function.");var e=t.charset||ge.charset;if(void 0!==t.charset&&"utf-8"!==t.charset&&"iso-8859-1"!==t.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=ie.default;if(void 0!==t.format){if(!pe.call(ie.formatters,t.format))throw new TypeError("Unknown format option provided.");r=t.format}var o=ie.formatters[r],n=ge.filter;return("function"==typeof t.filter||fe(t.filter))&&(n=t.filter),{addQueryPrefix:"boolean"==typeof t.addQueryPrefix?t.addQueryPrefix:ge.addQueryPrefix,allowDots:void 0===t.allowDots?ge.allowDots:!!t.allowDots,charset:e,charsetSentinel:"boolean"==typeof t.charsetSentinel?t.charsetSentinel:ge.charsetSentinel,delimiter:void 0===t.delimiter?ge.delimiter:t.delimiter,encode:"boolean"==typeof t.encode?t.encode:ge.encode,encoder:"function"==typeof t.encoder?t.encoder:ge.encoder,encodeValuesOnly:"boolean"==typeof t.encodeValuesOnly?t.encodeValuesOnly:ge.encodeValuesOnly,filter:n,format:r,formatter:o,serializeDate:"function"==typeof t.serializeDate?t.serializeDate:ge.serializeDate,skipNulls:"boolean"==typeof t.skipNulls?t.skipNulls:ge.skipNulls,sort:"function"==typeof t.sort?t.sort:null,strictNullHandling:"boolean"==typeof t.strictNullHandling?t.strictNullHandling:ge.strictNullHandling}}(e);"function"==typeof n.filter?o=(0,n.filter)("",o):fe(n.filter)&&(r=n.filter);var a,i=[];if("object"!=typeof o||null===o)return"";a=e&&e.arrayFormat in ce?e.arrayFormat:e&&"indices"in e?e.indices?"indices":"repeat":"indices";var p=ce[a];if(e&&"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var c="comma"===p&&e&&e.commaRoundTrip;r||(r=Object.keys(o)),n.sort&&r.sort(n.sort);for(var f=ne(),l=0;l<r.length;++l){var u=r[l];n.skipNulls&&null===o[u]||ye(i,me(o[u],u,p,c,n.strictNullHandling,n.skipNulls,n.encode?n.encoder:null,n.filter,n.sort,n.allowDots,n.serializeDate,n.format,n.formatter,n.encodeValuesOnly,n.charset,f))}var y=i.join(n.delimiter),s=!0===n.addQueryPrefix?"?":"";return n.charsetSentinel&&("iso-8859-1"===n.charset?s+="utf8=%26%2310003%3B&":s+="utf8=%E2%9C%93&"),y.length>0?s+y:""};export{Xt as f,he as s,oe as u};
