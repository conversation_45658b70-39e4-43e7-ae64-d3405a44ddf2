import{P as s,a4 as a,S as t,d as e,r as n,aB as i,p as o,aC as u,g as l,ac as r,e as c,W as d,s as h,aD as g,V as m,X as p}from"./index-3d21abf8.js";const[f,v,x]=s("pull-refresh"),D=["pulling","loosing","success"];const T=p(e({name:f,props:{disabled:Boolean,modelValue:Boolean,headHeight:a(50),successText:String,pullingText:String,loosingText:String,loadingText:String,pullDistance:t,successDuration:a(500),animationDuration:a(300)},emits:["change","refresh","update:modelValue"],setup(s,{emit:a,slots:t}){let e;const p=n(),f=n(),T=i(p),H=o({status:"normal",distance:0,duration:0}),V=u(),S=()=>{if(50!==s.headHeight)return{height:`${s.headHeight}px`}},$=()=>"loading"!==H.status&&"success"!==H.status&&!s.disabled,b=(t,e)=>{const n=+(s.pullDistance||s.headHeight);H.distance=t,H.status=e?"loading":0===t?"normal":t<n?"pulling":"loosing",a("change",{status:H.status,distance:t})},B=()=>{const{status:a}=H;return"normal"===a?"":s[`${a}Text`]||x(a)},P=()=>{const{status:s,distance:a}=H;if(t[s])return t[s]({distance:a});const e=[];return D.includes(s)&&e.push(c("div",{class:v("text")},[B()])),"loading"===s&&e.push(c(d,{class:v("loading")},{default:B})),e},y=s=>{e=0===g(T.value),e&&(H.duration=0,V.start(s))},Y=s=>{$()&&y(s)},j=()=>{e&&V.deltaY.value&&$()&&(H.duration=+s.animationDuration,"loosing"===H.status?(b(+s.headHeight,!0),a("update:modelValue",!0),h((()=>a("refresh")))):b(0))};return l((()=>s.modelValue),(a=>{H.duration=+s.animationDuration,a?b(+s.headHeight,!0):t.success||s.successText?(H.status="success",setTimeout((()=>{b(0)}),+s.successDuration)):b(0,!1)})),r("touchmove",(a=>{if($()){e||y(a);const{deltaY:t}=V;V.move(a),e&&t.value>=0&&V.isVertical()&&(m(a),b((a=>{const t=+(s.pullDistance||s.headHeight);return a>t&&(a=a<2*t?t+(a-t)/2:1.5*t+(a-2*t)/4),Math.round(a)})(t.value)))}}),{target:f}),()=>{var s;const a={transitionDuration:`${H.duration}ms`,transform:H.distance?`translate3d(0,${H.distance}px, 0)`:""};return c("div",{ref:p,class:v()},[c("div",{ref:f,class:v("track"),style:a,onTouchstartPassive:Y,onTouchend:j,onTouchcancel:j},[c("div",{class:v("head"),style:S()},[P()]),null==(s=t.default)?void 0:s.call(t)])])}}}));export{T as P};
