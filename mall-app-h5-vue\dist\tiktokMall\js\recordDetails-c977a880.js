import{_ as e,d as i,i as a,r as t,m as o,Y as s,u as l,T as n,av as c,c as r,e as u,w as f,a as d,F as k,y,b as p,x as v,n as m,o as h,f as b,t as w,aF as x,D as _,E as g}from"./index-3d21abf8.js";import{u as C}from"./index-54dce367.js";import{w as N}from"./exchange.api-23bc91cd.js";import{c as D}from"./index-3ab60a77.js";const U=e=>(_("data-v-b70b1e79"),e=e(),g(),e),j=U((()=>d("div",{class:"fixed-header-spacer"},null,-1))),S={key:0,class:"content"},T=["onClick"],E=[U((()=>d("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"},null,-1))),U((()=>d("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"},null,-1)))],q=i({name:"WithdrawRecordDetails"}),A=e(Object.assign(q,{setup(e){const i="tiktokMall",_=a(),g=t(!0),U=o((()=>["argos"].includes(i))),q=o((()=>["shop2u"].includes(i))),A=t([{title:"订单号",info:"",copy:!0,key:"order_no"},{title:"创建时间",info:"",key:"create_time"},{title:"提现金额",info:"",color:!0,key:"volume",isNum:!0,unit:"USDT"},{title:"订单状态",info:"",color:!0,key:"state"},{title:"币种协议",info:"",key:"coin_blockchain"},{title:"钱包地址",info:"",copy:!0,key:"to"},{title:"手续费",info:"",key:"fee",isNum:!0,unit:"USDT"},{title:"实际到账",info:"",color:!0,key:"amount",isNum:!0,unit:"USDT"}]),B=s(),F=t(""),{t:H}=l(),{toClipboard:I}=C(),M=t(!1),O=o((()=>{const e=D(A.value);if(M.value){for(let i=0;i<e.length;i++)["coin_blockchain","to"].includes(e[i].key)&&e.splice(i--,1);q.value&&e.splice(2,0,{title:"国家",info:"",key:"countryName"}),e.splice(3,0,{title:"开户行",info:"",key:"bankName"}),e.splice(4,0,{title:"卡号",info:"",key:"bankCardNo"}),e.splice(5,0,{title:"姓名",info:"",key:"bankUserName"}),U.value&&(e.splice(6,0,{title:"国际代码",info:"",key:"swiftCode"}),e.splice(7,0,{title:"路由号码",info:"",key:"routingNum"}),e.splice(8,0,{title:"账户地址",info:"",key:"accountAddress"}),e.splice(9,0,{title:"银行地址",info:"",key:"bankAddress"}))}if(B.query.r){F.value=B.query.r;const i=e.findIndex((e=>"state"===e.key));e.splice(i+1,0,{title:"失败原因",info:"",redColor:!0,key:"failure_msg"})}return e})),R=B.query.order_no;R?(g.value=!0,n.loading({duration:0,forbidClick:!0}),N({order_no:R}).then((e=>{if(M.value="bank"===e.coin_blockchain,M.value){const i=e.to.split(",");e.bankName=i[2],e.bankUserName=i[0],e.bankCardNo=i[1]}A.value.forEach((i=>{i.unit&&!["fee"].includes(i.key)&&(i.unit=M.value?"USD":e.coin),i.unit&&"fee"===i.key&&M.value&&(i.unit="USD")})),O.value.forEach((i=>{i.info="failure_msg"===i.key?F.value:e[i.key]}))})).finally((()=>{n.clear(),g.value=!1}))):n(H("参数错误"));return(e,i)=>{const a=c("fx-header");return h(),r("div",null,[u(a,{fixed:""},{title:f((()=>[b(w(p(H)("提现详情")),1)])),_:1}),j,d("div",{class:m(["details-content",{"is-ar":p(_)}])},[g.value?v("v-if",!0):(h(),r("div",S,[(h(!0),r(k,null,y(p(O),(e=>{return h(),r("div",{key:e.key,class:"item"},[d("p",null,w(p(H)(e.title))+"：",1),d("div",{class:m({copy:e.copy})},["state"===e.key?(h(),r("span",{key:0,class:m(`color-${e.info}`)},w((i=e.info,null!=(a={0:H("processing"),1:H("successful"),2:H("failure")}[i])?a:"")),3)):(h(),r("span",{key:1,class:m({color:e.color,"color-2":e.redColor})},w(e.isNum?p(x)(e.info,["BTC","ETH"].includes(e.unit)?6:2):e.info||"--"),3)),e.unit?(h(),r("span",{key:2,class:m([{color:e.color},"unit"])},w(e.unit),3)):v("v-if",!0),e.copy&&e.info?(h(),r("svg",{key:3,onClick:i=>(async e=>{try{await I(e),n(H("copySuccess"))}catch(i){}})(e.info),xmlns:"http://www.w3.org/2000/svg",width:"18",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"feather feather-copy"},E,8,T)):v("v-if",!0)],2)]);var i,a})),128))]))],2)])}}}),[["__scopeId","data-v-b70b1e79"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/withdraw/recordDetails.vue"]]);export{A as default};
