import{_ as a,u as e,k as s,l as t,i as l,m as n,av as i,I as c,c as r,e as o,w as d,F as m,y as u,b as g,o as f,f as p,t as v,a as x,n as k,A as h,x as b,D as y,E as _}from"./index-3d21abf8.js";import{l as C}from"./config-f57d91a6.js";import{c as w}from"./index-3ab60a77.js";const E=a=>(y("data-v-e435b278"),a=a(),_(),a),I={class:"lang bg-white page-main-content language-page"},j=E((()=>x("div",{class:"header-spacer",style:{height:"46px"}},null,-1))),A=["onClick"],D={class:"lang-title flex items-center font-13 textColor"},L=["src"],F=E((()=>x("div",{class:"lang-flex"},null,-1))),N=a({__name:"index",setup(a){const{locale:y,t:_}=e(),E=s(),N=t(),S=l(),U=n((()=>{const a=w(C),e=a.map((a=>a.key));return a.filter((a=>e.includes(a.key)))}));return(a,e)=>{const s=i("fx-header"),t=c;return f(),r("div",I,[o(s,{fixed:""},{title:d((()=>[p(v(g(_)("语言设置")),1)])),_:1}),j,(f(!0),r(m,null,u(g(U),((a,e)=>(f(),r("div",{key:e,class:"lang-padding",onClick:e=>(a=>{const e="ar"===a?"rtl":"rtr";document.documentElement.setAttribute("dir",e),E.setIsArLang("ar"===a),E.setNotCnLang(!["cn","tw"].includes(a)),y.value=a,localStorage.setItem("lang",a),document.dispatchEvent(new CustomEvent("langChange")),N.back()})(a.key)},[x("div",D,[x("img",{class:k(["h-6",g(S)?"ml-3":"mr-3"]),src:a.image,alt:""},null,10,L),p(" "+v(a.title),1)]),F,a.key==g(y)?(f(),h(t,{key:0,name:"success",class:"icon"})):b("v-if",!0)],8,A)))),128))])}}},[["__scopeId","data-v-e435b278"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/language/index.vue"]]);export{N as default};
