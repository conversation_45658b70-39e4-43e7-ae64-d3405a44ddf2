System.register(["./index-legacy-46a00900.js","./index-legacy-1fd93e33.js"],(function(e,t){"use strict";var a,n,r,u,l,o,m,s,i,c,p,v,d,g,y,f,h,D,x,M;return{setters:[e=>{a=e.Q,n=e.P,r=e.d,u=e.a4,l=e.r,o=e.m,m=e.q,s=e.s,i=e.g,c=e.a9,p=e.e,v=e.ag,d=e.aY,g=e.bD,y=e.aQ,f=e.R,h=e.bE,D=e.X},e=>{x=e.p,M=e.P}],execute:function(){const t=a({},x,{filter:Function,columnsOrder:Array,formatter:{type:Function,default:(e,t)=>t}}),V=Object.keys(x);function H(e,t){if(e<0)return[];const a=Array(e);let n=-1;for(;++n<e;)a[n]=t(n);return a}const k=(e,t)=>32-new Date(e,t-1,32).getDate(),$=(e,t)=>{const a=["setValues","setIndexes","setColumnIndex","setColumnValue"];return new Proxy(e,{get:(e,n)=>a.includes(n)?(...a)=>{e[n](...a),t()}:e[n]})},[O]=n("time-picker");var Y=r({name:O,props:a({},t,{minHour:u(0),maxHour:u(23),minMinute:u(0),maxMinute:u(59),modelValue:String}),emits:["confirm","cancel","change","update:modelValue"],setup(e,{emit:t,slots:a}){const n=t=>{const{minHour:a,maxHour:n,maxMinute:r,minMinute:u}=e;t||(t=`${g(a)}:${g(u)}`);let[l,o]=t.split(":");return l=g(y(+l,+a,+n)),o=g(y(+o,+u,+r)),`${l}:${o}`},r=l(),u=l(n(e.modelValue)),f=o((()=>[{type:"hour",range:[+e.minHour,+e.maxHour]},{type:"minute",range:[+e.minMinute,+e.maxMinute]}])),h=o((()=>f.value.map((({type:t,range:a})=>{let n=H(a[1]-a[0]+1,(e=>g(a[0]+e)));return e.filter&&(n=e.filter(t,n)),{type:t,values:n}})))),D=o((()=>h.value.map((t=>({values:t.values.map((a=>e.formatter(t.type,a)))}))))),x=()=>{const t=u.value.split(":"),a=[e.formatter("hour",t[0]),e.formatter("minute",t[1])];s((()=>{var e;null==(e=r.value)||e.setValues(a)}))},k=()=>{const[e,t]=r.value.getIndexes(),[a,l]=h.value,o=a.values[e]||a.values[0],m=l.values[t]||l.values[0];u.value=n(`${o}:${m}`),x()},O=()=>t("confirm",u.value),Y=()=>t("cancel"),b=()=>{k(),s((()=>{s((()=>t("change",u.value)))}))};return m((()=>{x(),s(k)})),i(D,x),i((()=>[e.filter,e.maxHour,e.minMinute,e.maxMinute]),k),i((()=>e.minHour),(()=>{s(k)})),i(u,(e=>t("update:modelValue",e))),i((()=>e.modelValue),(e=>{(e=n(e))!==u.value&&(u.value=e,x())})),c({getPicker:()=>r.value&&$(r.value,k)}),()=>p(M,v({ref:r,columns:D.value,onChange:b,onCancel:Y,onConfirm:O},d(e,V)),a)}});const b=(new Date).getFullYear(),[w]=n("date-picker");var C=r({name:w,props:a({},t,{type:f("datetime"),modelValue:Date,minDate:{type:Date,default:()=>new Date(b-10,0,1),validator:h},maxDate:{type:Date,default:()=>new Date(b+10,11,31),validator:h}}),emits:["confirm","cancel","change","update:modelValue"],setup(e,{emit:t,slots:a}){const n=t=>{if(h(t)){const a=y(t.getTime(),e.minDate.getTime(),e.maxDate.getTime());return new Date(a)}},r=l(),u=l(n(e.modelValue)),f=(t,a)=>{const n=e[`${t}Date`],r=n.getFullYear();let u=1,l=1,o=0,m=0;return"max"===t&&(u=12,l=k(a.getFullYear(),a.getMonth()+1),o=23,m=59),a.getFullYear()===r&&(u=n.getMonth()+1,a.getMonth()+1===u&&(l=n.getDate(),a.getDate()===l&&(o=n.getHours(),a.getHours()===o&&(m=n.getMinutes())))),{[`${t}Year`]:r,[`${t}Month`]:u,[`${t}Date`]:l,[`${t}Hour`]:o,[`${t}Minute`]:m}},D=o((()=>{const{maxYear:t,maxDate:a,maxMonth:n,maxHour:r,maxMinute:l}=f("max",u.value||e.minDate),{minYear:o,minDate:m,minMonth:s,minHour:i,minMinute:c}=f("min",u.value||e.minDate);let p=[{type:"year",range:[o,t]},{type:"month",range:[s,n]},{type:"day",range:[m,a]},{type:"hour",range:[i,r]},{type:"minute",range:[c,l]}];switch(e.type){case"date":p=p.slice(0,3);break;case"year-month":p=p.slice(0,2);break;case"month-day":p=p.slice(1,3);break;case"datehour":p=p.slice(0,4)}if(e.columnsOrder){const t=e.columnsOrder.concat(p.map((e=>e.type)));p.sort(((e,a)=>t.indexOf(e.type)-t.indexOf(a.type)))}return p})),x=o((()=>D.value.map((({type:t,range:a})=>{let n=H(a[1]-a[0]+1,(e=>g(a[0]+e)));return e.filter&&(n=e.filter(t,n)),{type:t,values:n}})))),O=o((()=>x.value.map((t=>({values:t.values.map((a=>e.formatter(t.type,a)))}))))),Y=()=>{const t=u.value||e.minDate,{formatter:a}=e,n=x.value.map((e=>{switch(e.type){case"year":return a("year",`${t.getFullYear()}`);case"month":return a("month",g(t.getMonth()+1));case"day":return a("day",g(t.getDate()));case"hour":return a("hour",g(t.getHours()));case"minute":return a("minute",g(t.getMinutes()));default:return""}}));s((()=>{var e;null==(e=r.value)||e.setValues(n)}))},b=()=>{const{type:t}=e,a=r.value.getIndexes(),l=e=>{let t=0;x.value.forEach(((a,n)=>{e===a.type&&(t=n)}));const{values:n}=x.value[t];return function(e){if(!e)return 0;for(;Number.isNaN(parseInt(e,10));){if(!(e.length>1))return 0;e=e.slice(1)}return parseInt(e,10)}(n[a[t]])};let o,m,s;"month-day"===t?(o=(u.value||e.minDate).getFullYear(),m=l("month"),s=l("day")):(o=l("year"),m=l("month"),s="year-month"===t?1:l("day"));const i=k(o,m);s=s>i?i:s;let c=0,p=0;"datehour"===t&&(c=l("hour")),"datetime"===t&&(c=l("hour"),p=l("minute"));const v=new Date(o,m-1,s,c,p);u.value=n(v)},w=()=>{t("update:modelValue",u.value),t("confirm",u.value)},C=()=>t("cancel"),F=()=>{b(),s((()=>{b(),s((()=>t("change",u.value)))}))};return m((()=>{Y(),s(b)})),i(O,Y),i(u,((e,a)=>t("update:modelValue",a?e:null))),i((()=>[e.filter,e.minDate,e.maxDate]),(()=>{s(b)})),i((()=>e.modelValue),(e=>{var t;(e=n(e))&&e.valueOf()!==(null==(t=u.value)?void 0:t.valueOf())&&(u.value=e)})),c({getPicker:()=>r.value&&$(r.value,b)}),()=>p(M,v({ref:r,columns:O.value,onChange:F,onCancel:C,onConfirm:w},d(e,V)),a)}});const[F,P]=n("datetime-picker"),I=Object.keys(Y.props),j=Object.keys(C.props),N=a({},Y.props,C.props,{modelValue:[String,Date]});var S=r({name:F,props:N,setup(e,{attrs:t,slots:a}){const n=l();return c({getPicker:()=>{var e;return null==(e=n.value)?void 0:e.getPicker()}}),()=>{const r="time"===e.type,u=r?Y:C,l=d(e,r?I:j);return p(u,v({ref:n,class:P()},l,t),a)}}});e("D",D(S))}}}));
