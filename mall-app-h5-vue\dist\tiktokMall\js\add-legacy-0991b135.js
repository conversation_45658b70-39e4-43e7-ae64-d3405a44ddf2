System.register(["./index-legacy-46a00900.js","./index-legacy-72e00c5f.js","./index-legacy-71866ecf.js","./index-legacy-f9c0699e.js","./index-legacy-f9c0699e2.js","./index-legacy-0ade4760.js","./index-legacy-9e9f7160.js","./use-route-legacy-be86ac1c.js","./index-legacy-df3e01aa.js","./use-id-legacy-df76950f.js"],(function(e,a){"use strict";var t,l,s,d,n,i,c,r,p,o,u,m,g,A,b,h,x,v,F,y,k,f=document.createElement("style");return f.textContent=".addPay .select-item[data-v-827e863e]{background:#F5F5F5;padding:0 15px;-webkit-box-align:center;-webkit-align-items:center;align-items:center;height:50px;border-radius:3px}.addPay .select-item-textarea[data-v-827e863e]{background:#F5F5F5;padding:0 15px;-webkit-box-align:center;-webkit-align-items:center;align-items:center;height:120px;border-radius:3px}.addPay .ash[data-v-827e863e]{color:#868d9a}.addPay .tips[data-v-827e863e]{background:#F0F1F4;border-radius:3px}.addPay .tips .tip-title[data-v-827e863e]{font-weight:700;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.addPay .tips .tip-title img[data-v-827e863e]{width:20px;height:20px}\n",document.head.appendChild(f),{setters:[e=>{t=e._,l=e.Y,s=e.l,d=e.r,n=e.av,i=e.I,c=e.c,r=e.e,p=e.w,o=e.a,u=e.cE,m=e.o,g=e.f,A=e.b,b=e.aV,h=e.D,x=e.E},e=>{v=e.B},e=>{F=e.C},()=>{},()=>{},e=>{y=e.F},e=>{k=e.F},()=>{},()=>{},()=>{}],execute:function(){const a=e=>(h("data-v-827e863e"),e=e(),x(),e),f={class:"addPay pb-10"},V=a((()=>o("p",{class:"pt-6 pb-2 ash"},"Name",-1))),U=a((()=>o("p",{class:"pt-6 pb-2 ash"},"Phone Number",-1))),C=a((()=>o("p",{class:"pt-6 pb-2 ash"},"银行名称",-1))),E=a((()=>o("p",{class:"pt-6 pb-2 ash"},"Bank Account Number",-1))),P=a((()=>o("p",{class:"pt-6 pb-2 ash"},"开户支行（选填）",-1))),B=a((()=>o("p",{class:"pt-6 pb-2 ash"},"Note（选填）",-1))),w=u('<div class="tips mx-4 mt-8 px-4 pt-4 pb-4" data-v-827e863e><div class="flex tip-title" data-v-827e863e><img class="mr-2" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADYAAAA3CAYAAABHGbl4AAAACXBIWXMAACE4AAAhOAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAPLSURBVHgB3ZrNbtNAEMdnN0nbSJXIhRMHXNEibqRPQPoEBGgrbrQPgNo+QZMnqCIeIOGGSGnDE5BeeyFcKkRbyUj0AgfCKR/+GGbcJMqn4zp2EvsnbT7stZK/ZndndmYF+IySrin8HosZCcRIQtdBBViqqiVRBR8R4CEsIhrFNAl4KiWm6FKi1YaCKCogUEUDPxsmlNVSXAWPmFiYksZENNrcEwJ3+CtMhopo5nRdlCYV6VrYk9c1xdAhA0K8AV8QBU0zs24F3lkYWyi2oB0CmvswFdwJvJOwR68aaZo7ebCZNz6hmgZmr0/iBacPSKcd17abRyTqFKYvilFkRORXNxuHTh8Ya7HbxaFxKgSkYC4QFU1b2BjnLmyFWQuEIclKmIT5QtU03LCbdyOFsaUWFhpfEd0t4Q/uC2r2fW7+cENwh73loqMeI1F5t6KYlykJb7eitn3eFXVqBrgDk7FYg+f8xrC7QxcPnqQkKg3zT2ptu3407MaAsNXNWoqiiAwEBYR9/s/9lweESSnyEDCEEHleE7qv9QhrDUEFggcF3/WeSKgjjJf2QA3BPshqe91W6wizAtpgk+i2WkeYkOIZBJxuq1nC1rZqOwGdW/0kIrJuuSlLGO1kg+CzHEHBsrU/tIRRgPscwkOSh6Mc5twCDi8iSbZYCkIHsjAxb1uSibGyZPR+D0IG7fSTFBriCoSPhEQQDyF8KI6TOUEj1MJ8LQ7MiKoUgP8gfFSlyRWP0IE/oxQnquADJ2cmnF9otn1+uU692WOaokL5Max4XCazuPmNVpsJiBUZiUAZQoaMGBXLVGtb9b8wm2KDH6iXxaWVdqr2PbU98BD/U9yjEGV+tYQhYonzBeAh/qe4R6Hl+NWKPK6O42V6K0PwqVwWly331QmpyGpZCDhU9cy1P3eEhcBqancptycIDrLVyCkfdH/vEcZWo0ikBIFDFK4/Lfb874FtS7O5uOtXmOUTKh+X6L84IIxLn6aJuxAY9BfDatFDHQ0PydXN+gFZ7ghcws73/MIc22cSEM2Dq+PlobsT2+j38XYtQ+lvx2crpgmVvLI/PsYzI+/DGOZR3DhRVh9wAA3L/UmGpYdQGgMPLovjjx453ohxxZN8xZcZlptUXijaIdM4HGepvn+Iq+QK1uljDqZPTtMW152KYlxtnW+PIkGBHve5CopnNEIyrXDvTkyUE7DOhADseH8Y072gNp4kO1oWTAGLdGdFXhS+8b5Q1+MFLw5Ce5/FgVtLco0KUSoRaSpUH0j0/ihWDVOqQpgqH/bS9aWK16e6/wMNqJoi01Wz+gAAAABJRU5ErkJggg==" data-v-827e863e> 特别提醒 </div><div class="pl-4 ash mt-2 font-13" data-v-827e863e>请确保添加您的银行卡号进行即时付款。请勿包含其他银行或 付款方式的详细信息。您必须添加所选银行的付款/收款信息。</div></div><div class="font-13 mt-20 ash px-4" data-v-827e863e>温馨提示：当您出售数字货币时，您选择的收款方式将向买方展示，请 确认信息填写准确无误。</div>',2),I={class:"px-4 pt-6 mt-3"};e("default",t({__name:"add",setup(e){l(),s();let a=d("法币");const t=e=>{};return(e,l)=>{const s=n("fx-header"),d=i,u=y,h=F,x=k,N=v;return m(),c("div",f,[r(s,null,{title:p((()=>[g("添加 Al-Rafidain QiServices")])),_:1}),r(x,{onFailed:t},{default:p((()=>[r(h,{inset:""},{default:p((()=>[V,r(u,{class:"select-item",modelValue:A(a),"onUpdate:modelValue":l[0]||(l[0]=e=>b(a)?a.value=e:a=e),disabled:"",name:"pattern",placeholder:"正则校验",rules:[{pattern:e.pattern,message:"请输入正确内容"}]},{extra:p((()=>[r(d,{name:"arrow-down",color:"#878A96",size:"18"})])),_:1},8,["modelValue","rules"])])),_:1}),r(h,{inset:""},{default:p((()=>[U,r(u,{class:"select-item",modelValue:A(a),"onUpdate:modelValue":l[1]||(l[1]=e=>b(a)?a.value=e:a=e),clearable:"",name:"picker",placeholder:"Phone Number",rules:[{pattern:e.pattern,message:"请输入正确内容"}]},null,8,["modelValue","rules"])])),_:1}),r(h,{inset:""},{default:p((()=>[C,r(u,{class:"select-item",modelValue:A(a),"onUpdate:modelValue":l[2]||(l[2]=e=>b(a)?a.value=e:a=e),clearable:"",name:"picker",placeholder:"Phone Number",rules:[{pattern:e.pattern,message:"请输入正确内容"}]},null,8,["modelValue","rules"])])),_:1}),r(h,{inset:""},{default:p((()=>[E,r(u,{class:"select-item",modelValue:A(a),"onUpdate:modelValue":l[3]||(l[3]=e=>b(a)?a.value=e:a=e),name:"picker",clearable:"",placeholder:"Bank Account Number",rules:[{pattern:e.pattern,message:"请输入正确内容"}]},null,8,["modelValue","rules"])])),_:1}),r(h,{inset:""},{default:p((()=>[P,r(u,{class:"select-item",modelValue:A(a),"onUpdate:modelValue":l[4]||(l[4]=e=>b(a)?a.value=e:a=e),name:"picker",clearable:"",placeholder:"开户支行（选填）",rules:[{pattern:e.pattern,message:"请输入正确内容"}]},null,8,["modelValue","rules"])])),_:1}),r(h,{inset:""},{default:p((()=>[B,r(u,{class:"select-item-textarea",modelValue:A(a),"onUpdate:modelValue":l[5]||(l[5]=e=>b(a)?a.value=e:a=e),type:"textarea",name:"picker",clearable:"",placeholder:"Bank Account Number",rules:[{pattern:e.pattern,message:"请输入正确内容"}]},null,8,["modelValue","rules"])])),_:1})])),_:1}),w,o("div",I,[r(N,{class:"w-full",type:"primary",onClick:e.submit},{default:p((()=>[g("确认")])),_:1},8,["onClick"])])])}}},[["__scopeId","data-v-827e863e"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/payMentMethod/add.vue"]]))}}}));
