import{_ as e,u as a,l,j as s,r as t,av as o,c as d,e as u,w as r,a as i,T as n,cl as p,o as c,f as m,t as v,D as x,E as f}from"./index-3d21abf8.js";import{B as h}from"./index-2406f514.js";import{E as j}from"./index-9c8e9dca.js";import"./use-route-cd41a893.js";/* empty css              *//* empty css               */import"./index-8c1841f6.js";import"./index-7dfcb82a.js";import"./use-id-a0619e01.js";const w={class:"changePassword"},_=(e=>(x("data-v-40274399"),e=e(),f(),e))((()=>i("div",{class:"line"},null,-1))),T={class:"content"},b=e({__name:"index",setup(e){const{t:x}=a(),f=l(),b=s(),g=t(!1),y=t(""),V=t(""),$=()=>{""!==y.value?y.value===V.value?(g.value=!0,p({safeword:y.value}).then((async e=>{await b.getUserInfo(!0),n(x("设置成功")),g.value=!1,setTimeout((()=>{f.back()}),1e3)})).catch((e=>{g.value=!1}))):n(x("两次密码输入不一致")):n(x("请输入6位数数字密码"))};return(e,a)=>{const l=o("fx-header"),s=h;return c(),d("div",w,[u(l,null,{title:r((()=>[m(v(e.$t("设置资金密码")),1)])),_:1}),_,i("div",T,[u(j,{label:e.$t("资金密码"),placeholderText:e.$t("请输入6位数数字密码"),modelValue:y.value,"onUpdate:modelValue":a[0]||(a[0]=e=>y.value=e),typeText:"password"},null,8,["label","placeholderText","modelValue"]),u(j,{label:e.$t("再次输入资金密码"),placeholderText:e.$t("请再次输入6位数数字密码"),modelValue:V.value,"onUpdate:modelValue":a[1]||(a[1]=e=>V.value=e),typeText:"password"},null,8,["label","placeholderText","modelValue"]),u(s,{class:"w-full",style:{"margin-top":"10px"},type:"primary",loading:g.value,onClick:$},{default:r((()=>[m(v(e.$t("sure")),1)])),_:1},8,["loading"])])])}}},[["__scopeId","data-v-40274399"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/fundsPasswordSettings/index.vue"]]);export{b as default};
