import{_ as e,u as a,i as l,r as s,av as t,c as i,e as r,w as o,a as n,b as d,o as c,f as u,t as v,F as g,y as p,n as h,K as m,b4 as f,aF as x,x as _,A as w,T as k,ak as y,D as b,E as T}from"./index-3d21abf8.js";import{P as j}from"./index-fc51b7d2.js";import{L as C}from"./index-40e83579.js";import{E as O}from"./index-5d897066.js";import{k as U}from"./exchange.api-23bc91cd.js";import{u as V}from"./index-54dce367.js";import"./use-id-a0619e01.js";const D=e=>(b("data-v-9394d17d"),e=e(),T(),e),E={class:"record"},L={class:"list-wrap"},P={key:0},R=["onClick"],S={class:"item"},$={class:"label"},A=["onClick"],B=[D((()=>n("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"},null,-1))),D((()=>n("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"},null,-1)))],F={class:"item"},H={class:"label"},q={class:"value"},z={class:"item"},I={class:"label"},K={class:"value"},M={class:"item"},N={class:"label"},G={key:0,class:"item"},J={class:"label"},Q=e({__name:"record",setup(e){const{t:b}=a(),T=l(),D=new URL("/www/png/name-20d65991.png",self.location),Q=s([]),W=s(!1),X=s(!1),Y=s(!1);s(1);const{toClipboard:Z}=V(),ee=s({page_no:1}),ae=()=>{const e={...ee.value};U(e).then((e=>{const a=e.elements||[];Q.value=1===ee.value.page_no?a:[...Q.value,...a],ee.value.page_no++,W.value=!1,X.value=!1,Y.value=a.length<e.pageSize})).catch((()=>{Y.value=!0,W.value=!1,X.value=!1}))},le=()=>{Y.value=!1,W.value=!0,ee.value.page_no=1,ae()};return(e,a)=>{const l=t("fx-header"),s=O,U=C,V=j;return c(),i("div",E,[r(l,{fixed:""},{title:o((()=>[u(v(d(b)("rechargeRecord")),1)])),_:1}),n("div",L,[r(V,{modelValue:X.value,"onUpdate:modelValue":a[1]||(a[1]=e=>X.value=e),"pulling-text":d(b)("pullingText"),"loosing-text":d(b)("loosingText"),"loading-text":d(b)("loading"),onRefresh:le},{default:o((()=>[r(U,{loading:W.value,"onUpdate:loading":a[0]||(a[0]=e=>W.value=e),finished:Y.value,"loading-text":d(b)("loading"),"finished-text":Q.value.length?d(b)("product.3"):"",onLoad:ae},{default:o((()=>[Q.value.length?(c(),i("ul",P,[(c(!0),i(g,null,p(Q.value,((a,l)=>{return c(),i("li",{class:"px-2 mt-4",key:l,onClick:e=>(e=>{const a={order_no:e.order_no,isThirdParty:e.isThirdParty};2===e.state&&(a.r=e.failure_msg||"--"),y({path:"/recharge/record-details",query:a})})(a)},[n("div",S,[n("div",$,v(d(b)("rechargeOrderNumber")),1),n("div",{class:h(["value",{"is-ar":d(T)}])},[n("span",null,v(a.order_no),1),(c(),i("svg",{onClick:m((e=>(async e=>{try{await Z(e),k(b("copySuccess"))}catch(a){}})(a.order_no)),["stop"]),xmlns:"http://www.w3.org/2000/svg",width:"18",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"feather feather-copy"},B,8,A))],2)]),n("div",F,[n("div",H,v(d(b)("rechargeOrderTime")),1),n("div",q,v(d(f)(a.createTime)),1)]),n("div",z,[n("div",I,v(d(b)("rechargeOrderAmount")),1),n("div",K,v(d(x)(a.volume,["BTC","ETH"].includes(a.coin)?6:2))+" "+v(a.coin),1)]),n("div",M,[n("div",N,v(d(b)("rechargeOrderStatus")),1),n("div",{class:h(["value",`color-${a.state}`])},v((s=a.state,null!=(t={0:b("processing"),1:b("successful"),2:b("failure")}[s])?t:"")),3)]),2===a.state?(c(),i("div",G,[n("div",J,v(e.$t("失败原因")),1),n("div",{class:h(["value",`color-${a.state}`])},v(a.failure_msg||"--"),3)])):_("v-if",!0)],8,R);var s,t})),128))])):_("v-if",!0),Q.value.length||W.value?_("v-if",!0):(c(),w(s,{key:1,image:d(D).href,description:d(b)("noData")},null,8,["image","description"]))])),_:1},8,["loading","finished","loading-text","finished-text"])])),_:1},8,["modelValue","pulling-text","loosing-text","loading-text"])])])}}},[["__scopeId","data-v-9394d17d"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/recharge/record.vue"]]);export{Q as default};
