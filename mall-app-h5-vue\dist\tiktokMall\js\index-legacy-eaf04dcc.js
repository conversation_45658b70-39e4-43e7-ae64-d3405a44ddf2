System.register(["./index-legacy-46a00900.js"],(function(t,i){"use strict";var a,e,n,o,p,c;return{setters:[t=>{a=t.d,e=t.r,n=t.g,o=t.aN,p=t.e,c=t.s}],execute:function(){
/*!
       * Cropper.js v1.5.13
       * https://fengyuanchen.github.io/cropperjs
       *
       * Copyright 2015-present <PERSON>
       * Released under the MIT license
       *
       * Date: 2022-11-20T05:30:46.114Z
       */
function i(t,i){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(t);i&&(e=e.filter((function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable}))),a.push.apply(a,e)}return a}function s(t){for(var a=1;a<arguments.length;a++){var e=null!=arguments[a]?arguments[a]:{};a%2?i(Object(e),!0).forEach((function(i){d(t,i,e[i])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):i(Object(e)).forEach((function(i){Object.defineProperty(t,i,Object.getOwnPropertyDescriptor(e,i))}))}return t}function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(t,i){for(var a=0;a<i.length;a++){var e=i[a];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(t,e.key,e)}}function d(t,i,a){return i in t?Object.defineProperty(t,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[i]=a,t}function m(t){return function(t){if(Array.isArray(t))return h(t)}(t)||function(t){if(typeof Symbol<"u"&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,i){if(t){if("string"==typeof t)return h(t,i);var a=Object.prototype.toString.call(t).slice(8,-1);if("Object"===a&&t.constructor&&(a=t.constructor.name),"Map"===a||"Set"===a)return Array.from(t);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return h(t,i)}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,i){(null==i||i>t.length)&&(i=t.length);for(var a=0,e=new Array(i);a<i;a++)e[a]=t[a];return e}var v=typeof window<"u"&&typeof window.document<"u",x=v?window:{},u=!(!v||!x.document.documentElement)&&"ontouchstart"in x.document.documentElement,g=!!v&&"PointerEvent"in x,f="cropper",b="all",w="crop",y="move",k="zoom",j="e",z="w",M="s",C="n",D="ne",A="nw",B="se",q="sw",E="".concat(f,"-crop"),O="".concat(f,"-disabled"),T="".concat(f,"-hidden"),L="".concat(f,"-hide"),W="".concat(f,"-invisible"),H="".concat(f,"-modal"),N="".concat(f,"-move"),S="".concat(f,"Action"),R="".concat(f,"Preview"),X="crop",Y="move",P="none",I="crop",_="cropend",U="cropmove",$="cropstart",Q="dblclick",F=g?"pointerdown":u?"touchstart":"mousedown",Z=g?"pointermove":u?"touchmove":"mousemove",V=g?"pointerup pointercancel":u?"touchend touchcancel":"mouseup",G="ready",K="resize",J="wheel",tt="zoom",it="image/jpeg",at=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,et=/^data:/,nt=/^data:image\/jpeg;base64,/,ot=/^img|canvas$/i,pt={viewMode:0,dragMode:X,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:200,minContainerHeight:100,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},ct=Number.isNaN||x.isNaN;function st(t){return"number"==typeof t&&!ct(t)}var rt=function(t){return t>0&&t<1/0};function lt(t){return typeof t>"u"}function dt(t){return"object"===r(t)&&null!==t}var mt=Object.prototype.hasOwnProperty;function ht(t){if(!dt(t))return!1;try{var i=t.constructor,a=i.prototype;return i&&a&&mt.call(a,"isPrototypeOf")}catch{return!1}}function vt(t){return"function"==typeof t}var xt=Array.prototype.slice;function ut(t){return Array.from?Array.from(t):xt.call(t)}function gt(t,i){return t&&vt(i)&&(Array.isArray(t)||st(t.length)?ut(t).forEach((function(a,e){i.call(t,a,e,t)})):dt(t)&&Object.keys(t).forEach((function(a){i.call(t,t[a],a,t)}))),t}var ft=Object.assign||function(t){for(var i=arguments.length,a=new Array(i>1?i-1:0),e=1;e<i;e++)a[e-1]=arguments[e];return dt(t)&&a.length>0&&a.forEach((function(i){dt(i)&&Object.keys(i).forEach((function(a){t[a]=i[a]}))})),t},bt=/\.\d*(?:0|9){12}\d*$/;function wt(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e11;return bt.test(t)?Math.round(t*i)/i:t}var yt=/^width|height|left|top|marginLeft|marginTop$/;function kt(t,i){var a=t.style;gt(i,(function(t,i){yt.test(i)&&st(t)&&(t="".concat(t,"px")),a[i]=t}))}function jt(t,i){if(i){if(st(t.length))return void gt(t,(function(t){jt(t,i)}));if(t.classList)return void t.classList.add(i);var a=t.className.trim();a?a.indexOf(i)<0&&(t.className="".concat(a," ").concat(i)):t.className=i}}function zt(t,i){if(i){if(st(t.length))return void gt(t,(function(t){zt(t,i)}));if(t.classList)return void t.classList.remove(i);t.className.indexOf(i)>=0&&(t.className=t.className.replace(i,""))}}function Mt(t,i,a){if(i){if(st(t.length))return void gt(t,(function(t){Mt(t,i,a)}));a?jt(t,i):zt(t,i)}}var Ct=/([a-z\d])([A-Z])/g;function Dt(t){return t.replace(Ct,"$1-$2").toLowerCase()}function At(t,i){return dt(t[i])?t[i]:t.dataset?t.dataset[i]:t.getAttribute("data-".concat(Dt(i)))}function Bt(t,i,a){dt(a)?t[i]=a:t.dataset?t.dataset[i]=a:t.setAttribute("data-".concat(Dt(i)),a)}var qt=/\s\s*/,Et=function(){var t=!1;if(v){var i=!1,a=function(){},e=Object.defineProperty({},"once",{get:function(){return t=!0,i},set:function(t){i=t}});x.addEventListener("test",a,e),x.removeEventListener("test",a,e)}return t}();function Ot(t,i,a){var e=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=a;i.trim().split(qt).forEach((function(i){if(!Et){var o=t.listeners;o&&o[i]&&o[i][a]&&(n=o[i][a],delete o[i][a],0===Object.keys(o[i]).length&&delete o[i],0===Object.keys(o).length&&delete t.listeners)}t.removeEventListener(i,n,e)}))}function Tt(t,i,a){var e=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=a;i.trim().split(qt).forEach((function(i){if(e.once&&!Et){var o=t.listeners,p=void 0===o?{}:o;n=function(){delete p[i][a],t.removeEventListener(i,n,e);for(var o=arguments.length,c=new Array(o),s=0;s<o;s++)c[s]=arguments[s];a.apply(t,c)},p[i]||(p[i]={}),p[i][a]&&t.removeEventListener(i,p[i][a],e),p[i][a]=n,t.listeners=p}t.addEventListener(i,n,e)}))}function Lt(t,i,a){var e;return vt(Event)&&vt(CustomEvent)?e=new CustomEvent(i,{detail:a,bubbles:!0,cancelable:!0}):(e=document.createEvent("CustomEvent")).initCustomEvent(i,!0,!0,a),t.dispatchEvent(e)}function Wt(t){var i=t.getBoundingClientRect();return{left:i.left+(window.pageXOffset-document.documentElement.clientLeft),top:i.top+(window.pageYOffset-document.documentElement.clientTop)}}var Ht=x.location,Nt=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function St(t){var i=t.match(Nt);return null!==i&&(i[1]!==Ht.protocol||i[2]!==Ht.hostname||i[3]!==Ht.port)}function Rt(t){var i="timestamp=".concat((new Date).getTime());return t+(-1===t.indexOf("?")?"?":"&")+i}function Xt(t){var i=t.rotate,a=t.scaleX,e=t.scaleY,n=t.translateX,o=t.translateY,p=[];st(n)&&0!==n&&p.push("translateX(".concat(n,"px)")),st(o)&&0!==o&&p.push("translateY(".concat(o,"px)")),st(i)&&0!==i&&p.push("rotate(".concat(i,"deg)")),st(a)&&1!==a&&p.push("scaleX(".concat(a,")")),st(e)&&1!==e&&p.push("scaleY(".concat(e,")"));var c=p.length?p.join(" "):"none";return{WebkitTransform:c,msTransform:c,transform:c}}function Yt(t,i){var a=t.pageX,e=t.pageY,n={endX:a,endY:e};return i?n:s({startX:a,startY:e},n)}function Pt(t){var i=t.aspectRatio,a=t.height,e=t.width,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"contain",o=rt(e),p=rt(a);if(o&&p){var c=a*i;"contain"===n&&c>e||"cover"===n&&c<e?a=e/i:e=a*i}else o?a=e/i:p&&(e=a*i);return{width:e,height:a}}var It=String.fromCharCode,_t=/^data:.*,/;function Ut(t){var i,a=new DataView(t);try{var e,n,o;if(255===a.getUint8(0)&&216===a.getUint8(1))for(var p=a.byteLength,c=2;c+1<p;){if(255===a.getUint8(c)&&225===a.getUint8(c+1)){n=c;break}c+=1}if(n){var s=n+10;if("Exif"===function(t,i,a){var e="";a+=i;for(var n=i;n<a;n+=1)e+=It(t.getUint8(n));return e}(a,n+4,4)){var r=a.getUint16(s);if(((e=18761===r)||19789===r)&&42===a.getUint16(s+2,e)){var l=a.getUint32(s+4,e);l>=8&&(o=s+l)}}}if(o){var d,m,h=a.getUint16(o,e);for(m=0;m<h;m+=1)if(d=o+12*m+2,274===a.getUint16(d,e)){d+=8,i=a.getUint16(d,e),a.setUint16(d,1,e);break}}}catch{i=1}return i}var $t={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,i=this.options,a=this.container,e=this.cropper,n=Number(i.minContainerWidth),o=Number(i.minContainerHeight);jt(e,T),zt(t,T);var p={width:Math.max(a.offsetWidth,n>=0?n:200),height:Math.max(a.offsetHeight,o>=0?o:100)};this.containerData=p,kt(e,{width:p.width,height:p.height}),jt(t,T),zt(e,T)},initCanvas:function(){var t=this.containerData,i=this.imageData,a=this.options.viewMode,e=Math.abs(i.rotate)%180==90,n=e?i.naturalHeight:i.naturalWidth,o=e?i.naturalWidth:i.naturalHeight,p=n/o,c=t.width,s=t.height;t.height*p>t.width?3===a?c=t.height*p:s=t.width/p:3===a?s=t.width/p:c=t.height*p;var r={aspectRatio:p,naturalWidth:n,naturalHeight:o,width:c,height:s};this.canvasData=r,this.limited=1===a||2===a,this.limitCanvas(!0,!0),r.width=Math.min(Math.max(r.width,r.minWidth),r.maxWidth),r.height=Math.min(Math.max(r.height,r.minHeight),r.maxHeight),r.left=(t.width-r.width)/2,r.top=(t.height-r.height)/2,r.oldLeft=r.left,r.oldTop=r.top,this.initialCanvasData=ft({},r)},limitCanvas:function(t,i){var a=this.options,e=this.containerData,n=this.canvasData,o=this.cropBoxData,p=a.viewMode,c=n.aspectRatio,s=this.cropped&&o;if(t){var r=Number(a.minCanvasWidth)||0,l=Number(a.minCanvasHeight)||0;p>1?(r=Math.max(r,e.width),l=Math.max(l,e.height),3===p&&(l*c>r?r=l*c:l=r/c)):p>0&&(r?r=Math.max(r,s?o.width:0):l?l=Math.max(l,s?o.height:0):s&&(r=o.width,(l=o.height)*c>r?r=l*c:l=r/c));var d=Pt({aspectRatio:c,width:r,height:l});r=d.width,l=d.height,n.minWidth=r,n.minHeight=l,n.maxWidth=1/0,n.maxHeight=1/0}if(i)if(p>(s?0:1)){var m=e.width-n.width,h=e.height-n.height;n.minLeft=Math.min(0,m),n.minTop=Math.min(0,h),n.maxLeft=Math.max(0,m),n.maxTop=Math.max(0,h),s&&this.limited&&(n.minLeft=Math.min(o.left,o.left+(o.width-n.width)),n.minTop=Math.min(o.top,o.top+(o.height-n.height)),n.maxLeft=o.left,n.maxTop=o.top,2===p&&(n.width>=e.width&&(n.minLeft=Math.min(0,m),n.maxLeft=Math.max(0,m)),n.height>=e.height&&(n.minTop=Math.min(0,h),n.maxTop=Math.max(0,h))))}else n.minLeft=-n.width,n.minTop=-n.height,n.maxLeft=e.width,n.maxTop=e.height},renderCanvas:function(t,i){var a=this.canvasData,e=this.imageData;if(i){var n=function(t){var i=t.width,a=t.height,e=t.degree;if(90==(e=Math.abs(e)%180))return{width:a,height:i};var n=e%90*Math.PI/180,o=Math.sin(n),p=Math.cos(n),c=i*p+a*o,s=i*o+a*p;return e>90?{width:s,height:c}:{width:c,height:s}}({width:e.naturalWidth*Math.abs(e.scaleX||1),height:e.naturalHeight*Math.abs(e.scaleY||1),degree:e.rotate||0}),o=n.width,p=n.height,c=a.width*(o/a.naturalWidth),s=a.height*(p/a.naturalHeight);a.left-=(c-a.width)/2,a.top-=(s-a.height)/2,a.width=c,a.height=s,a.aspectRatio=o/p,a.naturalWidth=o,a.naturalHeight=p,this.limitCanvas(!0,!1)}(a.width>a.maxWidth||a.width<a.minWidth)&&(a.left=a.oldLeft),(a.height>a.maxHeight||a.height<a.minHeight)&&(a.top=a.oldTop),a.width=Math.min(Math.max(a.width,a.minWidth),a.maxWidth),a.height=Math.min(Math.max(a.height,a.minHeight),a.maxHeight),this.limitCanvas(!1,!0),a.left=Math.min(Math.max(a.left,a.minLeft),a.maxLeft),a.top=Math.min(Math.max(a.top,a.minTop),a.maxTop),a.oldLeft=a.left,a.oldTop=a.top,kt(this.canvas,ft({width:a.width,height:a.height},Xt({translateX:a.left,translateY:a.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var i=this.canvasData,a=this.imageData,e=a.naturalWidth*(i.width/i.naturalWidth),n=a.naturalHeight*(i.height/i.naturalHeight);ft(a,{width:e,height:n,left:(i.width-e)/2,top:(i.height-n)/2}),kt(this.image,ft({width:a.width,height:a.height},Xt(ft({translateX:a.left,translateY:a.top},a)))),t&&this.output()},initCropBox:function(){var t=this.options,i=this.canvasData,a=t.aspectRatio||t.initialAspectRatio,e=Number(t.autoCropArea)||.8,n={width:i.width,height:i.height};a&&(i.height*a>i.width?n.height=n.width/a:n.width=n.height*a),this.cropBoxData=n,this.limitCropBox(!0,!0),n.width=Math.min(Math.max(n.width,n.minWidth),n.maxWidth),n.height=Math.min(Math.max(n.height,n.minHeight),n.maxHeight),n.width=Math.max(n.minWidth,n.width*e),n.height=Math.max(n.minHeight,n.height*e),n.left=i.left+(i.width-n.width)/2,n.top=i.top+(i.height-n.height)/2,n.oldLeft=n.left,n.oldTop=n.top,this.initialCropBoxData=ft({},n)},limitCropBox:function(t,i){var a=this.options,e=this.containerData,n=this.canvasData,o=this.cropBoxData,p=this.limited,c=a.aspectRatio;if(t){var s=Number(a.minCropBoxWidth)||0,r=Number(a.minCropBoxHeight)||0,l=p?Math.min(e.width,n.width,n.width+n.left,e.width-n.left):e.width,d=p?Math.min(e.height,n.height,n.height+n.top,e.height-n.top):e.height;s=Math.min(s,e.width),r=Math.min(r,e.height),c&&(s&&r?r*c>s?r=s/c:s=r*c:s?r=s/c:r&&(s=r*c),d*c>l?d=l/c:l=d*c),o.minWidth=Math.min(s,l),o.minHeight=Math.min(r,d),o.maxWidth=l,o.maxHeight=d}i&&(p?(o.minLeft=Math.max(0,n.left),o.minTop=Math.max(0,n.top),o.maxLeft=Math.min(e.width,n.left+n.width)-o.width,o.maxTop=Math.min(e.height,n.top+n.height)-o.height):(o.minLeft=0,o.minTop=0,o.maxLeft=e.width-o.width,o.maxTop=e.height-o.height))},renderCropBox:function(){var t=this.options,i=this.containerData,a=this.cropBoxData;(a.width>a.maxWidth||a.width<a.minWidth)&&(a.left=a.oldLeft),(a.height>a.maxHeight||a.height<a.minHeight)&&(a.top=a.oldTop),a.width=Math.min(Math.max(a.width,a.minWidth),a.maxWidth),a.height=Math.min(Math.max(a.height,a.minHeight),a.maxHeight),this.limitCropBox(!1,!0),a.left=Math.min(Math.max(a.left,a.minLeft),a.maxLeft),a.top=Math.min(Math.max(a.top,a.minTop),a.maxTop),a.oldLeft=a.left,a.oldTop=a.top,t.movable&&t.cropBoxMovable&&Bt(this.face,S,a.width>=i.width&&a.height>=i.height?y:b),kt(this.cropBox,ft({width:a.width,height:a.height},Xt({translateX:a.left,translateY:a.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),Lt(this.element,I,this.getData())}},Qt={initPreview:function(){var t=this.element,i=this.crossOrigin,a=this.options.preview,e=i?this.crossOriginUrl:this.url,n=t.alt||"The image to preview",o=document.createElement("img");if(i&&(o.crossOrigin=i),o.src=e,o.alt=n,this.viewBox.appendChild(o),this.viewBoxImage=o,a){var p=a;"string"==typeof a?p=t.ownerDocument.querySelectorAll(a):a.querySelector&&(p=[a]),this.previews=p,gt(p,(function(t){var a=document.createElement("img");Bt(t,R,{width:t.offsetWidth,height:t.offsetHeight,html:t.innerHTML}),i&&(a.crossOrigin=i),a.src=e,a.alt=n,a.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',t.innerHTML="",t.appendChild(a)}))}},resetPreview:function(){gt(this.previews,(function(t){var i=At(t,R);kt(t,{width:i.width,height:i.height}),t.innerHTML=i.html,function(t,i){if(dt(t[i]))try{delete t[i]}catch{t[i]=void 0}else if(t.dataset)try{delete t.dataset[i]}catch{t.dataset[i]=void 0}else t.removeAttribute("data-".concat(Dt(i)))}(t,R)}))},preview:function(){var t=this.imageData,i=this.canvasData,a=this.cropBoxData,e=a.width,n=a.height,o=t.width,p=t.height,c=a.left-i.left-t.left,s=a.top-i.top-t.top;!this.cropped||this.disabled||(kt(this.viewBoxImage,ft({width:o,height:p},Xt(ft({translateX:-c,translateY:-s},t)))),gt(this.previews,(function(i){var a=At(i,R),r=a.width,l=a.height,d=r,m=l,h=1;e&&(m=n*(h=r/e)),n&&m>l&&(d=e*(h=l/n),m=l),kt(i,{width:d,height:m}),kt(i.getElementsByTagName("img")[0],ft({width:o*h,height:p*h},Xt(ft({translateX:-c*h,translateY:-s*h},t))))})))}},Ft={bind:function(){var t=this.element,i=this.options,a=this.cropper;vt(i.cropstart)&&Tt(t,$,i.cropstart),vt(i.cropmove)&&Tt(t,U,i.cropmove),vt(i.cropend)&&Tt(t,_,i.cropend),vt(i.crop)&&Tt(t,I,i.crop),vt(i.zoom)&&Tt(t,tt,i.zoom),Tt(a,F,this.onCropStart=this.cropStart.bind(this)),i.zoomable&&i.zoomOnWheel&&Tt(a,J,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),i.toggleDragModeOnDblclick&&Tt(a,Q,this.onDblclick=this.dblclick.bind(this)),Tt(t.ownerDocument,Z,this.onCropMove=this.cropMove.bind(this)),Tt(t.ownerDocument,V,this.onCropEnd=this.cropEnd.bind(this)),i.responsive&&Tt(window,K,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,i=this.options,a=this.cropper;vt(i.cropstart)&&Ot(t,$,i.cropstart),vt(i.cropmove)&&Ot(t,U,i.cropmove),vt(i.cropend)&&Ot(t,_,i.cropend),vt(i.crop)&&Ot(t,I,i.crop),vt(i.zoom)&&Ot(t,tt,i.zoom),Ot(a,F,this.onCropStart),i.zoomable&&i.zoomOnWheel&&Ot(a,J,this.onWheel,{passive:!1,capture:!0}),i.toggleDragModeOnDblclick&&Ot(a,Q,this.onDblclick),Ot(t.ownerDocument,Z,this.onCropMove),Ot(t.ownerDocument,V,this.onCropEnd),i.responsive&&Ot(window,K,this.onResize)}},Zt={resize:function(){if(!this.disabled){var t,i,a=this.options,e=this.container,n=this.containerData,o=e.offsetWidth/n.width,p=e.offsetHeight/n.height,c=Math.abs(o-1)>Math.abs(p-1)?o:p;1!==c&&(a.restore&&(t=this.getCanvasData(),i=this.getCropBoxData()),this.render(),a.restore&&(this.setCanvasData(gt(t,(function(i,a){t[a]=i*c}))),this.setCropBoxData(gt(i,(function(t,a){i[a]=t*c})))))}},dblclick:function(){var t,i;this.disabled||this.options.dragMode===P||this.setDragMode((t=this.dragBox,i=E,(t.classList?t.classList.contains(i):t.className.indexOf(i)>-1)?Y:X))},wheel:function(t){var i=this,a=Number(this.options.wheelZoomRatio)||.1,e=1;this.disabled||(t.preventDefault(),!this.wheeling&&(this.wheeling=!0,setTimeout((function(){i.wheeling=!1}),50),t.deltaY?e=t.deltaY>0?1:-1:t.wheelDelta?e=-t.wheelDelta/120:t.detail&&(e=t.detail>0?1:-1),this.zoom(-e*a,t)))},cropStart:function(t){var i=t.buttons,a=t.button;if(!(this.disabled||("mousedown"===t.type||"pointerdown"===t.type&&"mouse"===t.pointerType)&&(st(i)&&1!==i||st(a)&&0!==a||t.ctrlKey))){var e,n=this.options,o=this.pointers;t.changedTouches?gt(t.changedTouches,(function(t){o[t.identifier]=Yt(t)})):o[t.pointerId||0]=Yt(t),e=Object.keys(o).length>1&&n.zoomable&&n.zoomOnTouch?k:At(t.target,S),at.test(e)&&!1!==Lt(this.element,$,{originalEvent:t,action:e})&&(t.preventDefault(),this.action=e,this.cropping=!1,e===w&&(this.cropping=!0,jt(this.dragBox,H)))}},cropMove:function(t){var i=this.action;if(!this.disabled&&i){var a=this.pointers;t.preventDefault(),!1!==Lt(this.element,U,{originalEvent:t,action:i})&&(t.changedTouches?gt(t.changedTouches,(function(t){ft(a[t.identifier]||{},Yt(t,!0))})):ft(a[t.pointerId||0]||{},Yt(t,!0)),this.change(t))}},cropEnd:function(t){if(!this.disabled){var i=this.action,a=this.pointers;t.changedTouches?gt(t.changedTouches,(function(t){delete a[t.identifier]})):delete a[t.pointerId||0],i&&(t.preventDefault(),Object.keys(a).length||(this.action=""),this.cropping&&(this.cropping=!1,Mt(this.dragBox,H,this.cropped&&this.options.modal)),Lt(this.element,_,{originalEvent:t,action:i}))}}},Vt={change:function(t){var i,a=this.options,e=this.canvasData,n=this.containerData,o=this.cropBoxData,p=this.pointers,c=this.action,r=a.aspectRatio,l=o.left,d=o.top,m=o.width,h=o.height,v=l+m,x=d+h,u=0,g=0,f=n.width,E=n.height,O=!0;!r&&t.shiftKey&&(r=m&&h?m/h:1),this.limited&&(u=o.minLeft,g=o.minTop,f=u+Math.min(n.width,e.width,e.left+e.width),E=g+Math.min(n.height,e.height,e.top+e.height));var L=p[Object.keys(p)[0]],W={x:L.endX-L.startX,y:L.endY-L.startY},H=function(t){switch(t){case j:v+W.x>f&&(W.x=f-v);break;case z:l+W.x<u&&(W.x=u-l);break;case C:d+W.y<g&&(W.y=g-d);break;case M:x+W.y>E&&(W.y=E-x)}};switch(c){case b:l+=W.x,d+=W.y;break;case j:if(W.x>=0&&(v>=f||r&&(d<=g||x>=E))){O=!1;break}H(j),(m+=W.x)<0&&(c=z,l-=m=-m),r&&(h=m/r,d+=(o.height-h)/2);break;case C:if(W.y<=0&&(d<=g||r&&(l<=u||v>=f))){O=!1;break}H(C),h-=W.y,d+=W.y,h<0&&(c=M,d-=h=-h),r&&(m=h*r,l+=(o.width-m)/2);break;case z:if(W.x<=0&&(l<=u||r&&(d<=g||x>=E))){O=!1;break}H(z),m-=W.x,l+=W.x,m<0&&(c=j,l-=m=-m),r&&(h=m/r,d+=(o.height-h)/2);break;case M:if(W.y>=0&&(x>=E||r&&(l<=u||v>=f))){O=!1;break}H(M),(h+=W.y)<0&&(c=C,d-=h=-h),r&&(m=h*r,l+=(o.width-m)/2);break;case D:if(r){if(W.y<=0&&(d<=g||v>=f)){O=!1;break}H(C),h-=W.y,d+=W.y,m=h*r}else H(C),H(j),W.x>=0?v<f?m+=W.x:W.y<=0&&d<=g&&(O=!1):m+=W.x,W.y<=0?d>g&&(h-=W.y,d+=W.y):(h-=W.y,d+=W.y);m<0&&h<0?(c=q,d-=h=-h,l-=m=-m):m<0?(c=A,l-=m=-m):h<0&&(c=B,d-=h=-h);break;case A:if(r){if(W.y<=0&&(d<=g||l<=u)){O=!1;break}H(C),h-=W.y,d+=W.y,m=h*r,l+=o.width-m}else H(C),H(z),W.x<=0?l>u?(m-=W.x,l+=W.x):W.y<=0&&d<=g&&(O=!1):(m-=W.x,l+=W.x),W.y<=0?d>g&&(h-=W.y,d+=W.y):(h-=W.y,d+=W.y);m<0&&h<0?(c=B,d-=h=-h,l-=m=-m):m<0?(c=D,l-=m=-m):h<0&&(c=q,d-=h=-h);break;case q:if(r){if(W.x<=0&&(l<=u||x>=E)){O=!1;break}H(z),m-=W.x,l+=W.x,h=m/r}else H(M),H(z),W.x<=0?l>u?(m-=W.x,l+=W.x):W.y>=0&&x>=E&&(O=!1):(m-=W.x,l+=W.x),W.y>=0?x<E&&(h+=W.y):h+=W.y;m<0&&h<0?(c=D,d-=h=-h,l-=m=-m):m<0?(c=B,l-=m=-m):h<0&&(c=A,d-=h=-h);break;case B:if(r){if(W.x>=0&&(v>=f||x>=E)){O=!1;break}H(j),h=(m+=W.x)/r}else H(M),H(j),W.x>=0?v<f?m+=W.x:W.y>=0&&x>=E&&(O=!1):m+=W.x,W.y>=0?x<E&&(h+=W.y):h+=W.y;m<0&&h<0?(c=A,d-=h=-h,l-=m=-m):m<0?(c=q,l-=m=-m):h<0&&(c=D,d-=h=-h);break;case y:this.move(W.x,W.y),O=!1;break;case k:this.zoom(function(t){var i=s({},t),a=0;return gt(t,(function(t,e){delete i[e],gt(i,(function(i){var e=Math.abs(t.startX-i.startX),n=Math.abs(t.startY-i.startY),o=Math.abs(t.endX-i.endX),p=Math.abs(t.endY-i.endY),c=Math.sqrt(e*e+n*n),s=(Math.sqrt(o*o+p*p)-c)/c;Math.abs(s)>Math.abs(a)&&(a=s)}))})),a}(p),t),O=!1;break;case w:if(!W.x||!W.y){O=!1;break}i=Wt(this.cropper),l=L.startX-i.left,d=L.startY-i.top,m=o.minWidth,h=o.minHeight,W.x>0?c=W.y>0?B:D:W.x<0&&(l-=m,c=W.y>0?q:A),W.y<0&&(d-=h),this.cropped||(zt(this.cropBox,T),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0))}O&&(o.width=m,o.height=h,o.left=l,o.top=d,this.action=c,this.renderCropBox()),gt(p,(function(t){t.startX=t.endX,t.startY=t.endY}))}},Gt={crop:function(){return this.ready&&!this.cropped&&!this.disabled&&(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&jt(this.dragBox,H),zt(this.cropBox,T),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=ft({},this.initialImageData),this.canvasData=ft({},this.initialCanvasData),this.cropBoxData=ft({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(ft(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),zt(this.dragBox,H),jt(this.cropBox,T)),this},replace:function(t){var i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return!this.disabled&&t&&(this.isImg&&(this.element.src=t),i?(this.url=t,this.image.src=t,this.ready&&(this.viewBoxImage.src=t,gt(this.previews,(function(i){i.getElementsByTagName("img")[0].src=t})))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(t))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,zt(this.cropper,O)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,jt(this.cropper,O)),this},destroy:function(){var t=this.element;return t[f]?(t[f]=void 0,this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate(),this):this},move:function(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,a=this.canvasData,e=a.left,n=a.top;return this.moveTo(lt(t)?t:e+Number(t),lt(i)?i:n+Number(i))},moveTo:function(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,a=this.canvasData,e=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.movable&&(st(t)&&(a.left=t,e=!0),st(i)&&(a.top=i,e=!0),e&&this.renderCanvas(!0)),this},zoom:function(t,i){var a=this.canvasData;return t=(t=Number(t))<0?1/(1-t):1+t,this.zoomTo(a.width*t/a.naturalWidth,null,i)},zoomTo:function(t,i,a){var e=this.options,n=this.canvasData,o=n.width,p=n.height,c=n.naturalWidth,s=n.naturalHeight;if((t=Number(t))>=0&&this.ready&&!this.disabled&&e.zoomable){var r=c*t,l=s*t;if(!1===Lt(this.element,tt,{ratio:t,oldRatio:o/c,originalEvent:a}))return this;if(a){var d=this.pointers,m=Wt(this.cropper),h=d&&Object.keys(d).length?function(t){var i=0,a=0,e=0;return gt(t,(function(t){var n=t.startX,o=t.startY;i+=n,a+=o,e+=1})),{pageX:i/=e,pageY:a/=e}}(d):{pageX:a.pageX,pageY:a.pageY};n.left-=(r-o)*((h.pageX-m.left-n.left)/o),n.top-=(l-p)*((h.pageY-m.top-n.top)/p)}else ht(i)&&st(i.x)&&st(i.y)?(n.left-=(r-o)*((i.x-n.left)/o),n.top-=(l-p)*((i.y-n.top)/p)):(n.left-=(r-o)/2,n.top-=(l-p)/2);n.width=r,n.height=l,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return st(t=Number(t))&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var i=this.imageData.scaleY;return this.scale(t,st(i)?i:1)},scaleY:function(t){var i=this.imageData.scaleX;return this.scale(st(i)?i:1,t)},scale:function(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,a=this.imageData,e=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.scalable&&(st(t)&&(a.scaleX=t,e=!0),st(i)&&(a.scaleY=i,e=!0),e&&this.renderCanvas(!0,!0)),this},getData:function(){var t,i=arguments.length>0&&void 0!==arguments[0]&&arguments[0],a=this.options,e=this.imageData,n=this.canvasData,o=this.cropBoxData;if(this.ready&&this.cropped){t={x:o.left-n.left,y:o.top-n.top,width:o.width,height:o.height};var p=e.width/e.naturalWidth;if(gt(t,(function(i,a){t[a]=i/p})),i){var c=Math.round(t.y+t.height),s=Math.round(t.x+t.width);t.x=Math.round(t.x),t.y=Math.round(t.y),t.width=s-t.x,t.height=c-t.y}}else t={x:0,y:0,width:0,height:0};return a.rotatable&&(t.rotate=e.rotate||0),a.scalable&&(t.scaleX=e.scaleX||1,t.scaleY=e.scaleY||1),t},setData:function(t){var i=this.options,a=this.imageData,e=this.canvasData,n={};if(this.ready&&!this.disabled&&ht(t)){var o=!1;i.rotatable&&st(t.rotate)&&t.rotate!==a.rotate&&(a.rotate=t.rotate,o=!0),i.scalable&&(st(t.scaleX)&&t.scaleX!==a.scaleX&&(a.scaleX=t.scaleX,o=!0),st(t.scaleY)&&t.scaleY!==a.scaleY&&(a.scaleY=t.scaleY,o=!0)),o&&this.renderCanvas(!0,!0);var p=a.width/a.naturalWidth;st(t.x)&&(n.left=t.x*p+e.left),st(t.y)&&(n.top=t.y*p+e.top),st(t.width)&&(n.width=t.width*p),st(t.height)&&(n.height=t.height*p),this.setCropBoxData(n)}return this},getContainerData:function(){return this.ready?ft({},this.containerData):{}},getImageData:function(){return this.sized?ft({},this.imageData):{}},getCanvasData:function(){var t=this.canvasData,i={};return this.ready&&gt(["left","top","width","height","naturalWidth","naturalHeight"],(function(a){i[a]=t[a]})),i},setCanvasData:function(t){var i=this.canvasData,a=i.aspectRatio;return this.ready&&!this.disabled&&ht(t)&&(st(t.left)&&(i.left=t.left),st(t.top)&&(i.top=t.top),st(t.width)?(i.width=t.width,i.height=t.width/a):st(t.height)&&(i.height=t.height,i.width=t.height*a),this.renderCanvas(!0)),this},getCropBoxData:function(){var t,i=this.cropBoxData;return this.ready&&this.cropped&&(t={left:i.left,top:i.top,width:i.width,height:i.height}),t||{}},setCropBoxData:function(t){var i,a,e=this.cropBoxData,n=this.options.aspectRatio;return this.ready&&this.cropped&&!this.disabled&&ht(t)&&(st(t.left)&&(e.left=t.left),st(t.top)&&(e.top=t.top),st(t.width)&&t.width!==e.width&&(i=!0,e.width=t.width),st(t.height)&&t.height!==e.height&&(a=!0,e.height=t.height),n&&(i?e.height=e.width/n:a&&(e.width=e.height*n)),this.renderCropBox()),this},getCroppedCanvas:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var i=this.canvasData,a=function(t,i,a,e){var n=i.aspectRatio,o=i.naturalWidth,p=i.naturalHeight,c=i.rotate,s=void 0===c?0:c,r=i.scaleX,l=void 0===r?1:r,d=i.scaleY,h=void 0===d?1:d,v=a.aspectRatio,x=a.naturalWidth,u=a.naturalHeight,g=e.fillColor,f=void 0===g?"transparent":g,b=e.imageSmoothingEnabled,w=void 0===b||b,y=e.imageSmoothingQuality,k=void 0===y?"low":y,j=e.maxWidth,z=void 0===j?1/0:j,M=e.maxHeight,C=void 0===M?1/0:M,D=e.minWidth,A=void 0===D?0:D,B=e.minHeight,q=void 0===B?0:B,E=document.createElement("canvas"),O=E.getContext("2d"),T=Pt({aspectRatio:v,width:z,height:C}),L=Pt({aspectRatio:v,width:A,height:q},"cover"),W=Math.min(T.width,Math.max(L.width,x)),H=Math.min(T.height,Math.max(L.height,u)),N=Pt({aspectRatio:n,width:z,height:C}),S=Pt({aspectRatio:n,width:A,height:q},"cover"),R=Math.min(N.width,Math.max(S.width,o)),X=Math.min(N.height,Math.max(S.height,p)),Y=[-R/2,-X/2,R,X];return E.width=wt(W),E.height=wt(H),O.fillStyle=f,O.fillRect(0,0,W,H),O.save(),O.translate(W/2,H/2),O.rotate(s*Math.PI/180),O.scale(l,h),O.imageSmoothingEnabled=w,O.imageSmoothingQuality=k,O.drawImage.apply(O,[t].concat(m(Y.map((function(t){return Math.floor(wt(t))}))))),O.restore(),E}(this.image,this.imageData,i,t);if(!this.cropped)return a;var e=this.getData(),n=e.x,o=e.y,p=e.width,c=e.height,s=a.width/Math.floor(i.naturalWidth);1!==s&&(n*=s,o*=s,p*=s,c*=s);var r=p/c,l=Pt({aspectRatio:r,width:t.maxWidth||1/0,height:t.maxHeight||1/0}),d=Pt({aspectRatio:r,width:t.minWidth||0,height:t.minHeight||0},"cover"),h=Pt({aspectRatio:r,width:t.width||(1!==s?a.width:p),height:t.height||(1!==s?a.height:c)}),v=h.width,x=h.height;v=Math.min(l.width,Math.max(d.width,v)),x=Math.min(l.height,Math.max(d.height,x));var u=document.createElement("canvas"),g=u.getContext("2d");u.width=wt(v),u.height=wt(x),g.fillStyle=t.fillColor||"transparent",g.fillRect(0,0,v,x);var f=t.imageSmoothingEnabled,b=void 0===f||f,w=t.imageSmoothingQuality;g.imageSmoothingEnabled=b,w&&(g.imageSmoothingQuality=w);var y,k,j,z,M,C,D=a.width,A=a.height,B=n,q=o;B<=-p||B>D?(B=0,y=0,j=0,M=0):B<=0?(j=-B,B=0,M=y=Math.min(D,p+B)):B<=D&&(j=0,M=y=Math.min(p,D-B)),y<=0||q<=-c||q>A?(q=0,k=0,z=0,C=0):q<=0?(z=-q,q=0,C=k=Math.min(A,c+q)):q<=A&&(z=0,C=k=Math.min(c,A-q));var E=[B,q,y,k];if(M>0&&C>0){var O=v/p;E.push(j*O,z*O,M*O,C*O)}return g.drawImage.apply(g,[a].concat(m(E.map((function(t){return Math.floor(wt(t))}))))),u},setAspectRatio:function(t){var i=this.options;return!this.disabled&&!lt(t)&&(i.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var i=this.options,a=this.dragBox,e=this.face;if(this.ready&&!this.disabled){var n=t===X,o=i.movable&&t===Y;t=n||o?t:P,i.dragMode=t,Bt(a,S,t),Mt(a,E,n),Mt(a,N,o),i.cropBoxMovable||(Bt(e,S,t),Mt(e,E,n),Mt(e,N,o))}return this}},Kt=x.Cropper,Jt=function(){function t(i){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(function(t,i){if(!(t instanceof i))throw new TypeError("Cannot call a class as a function")}(this,t),!i||!ot.test(i.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=i,this.options=ft({},pt,ht(a)&&a),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return function(t,i,a){i&&l(t.prototype,i),a&&l(t,a),Object.defineProperty(t,"prototype",{writable:!1})}(t,[{key:"init",value:function(){var t,i=this.element,a=i.tagName.toLowerCase();if(!i[f]){if(i[f]=this,"img"===a){if(this.isImg=!0,t=i.getAttribute("src")||"",this.originalUrl=t,!t)return;t=i.src}else"canvas"===a&&window.HTMLCanvasElement&&(t=i.toDataURL());this.load(t)}}},{key:"load",value:function(t){var i=this;if(t){this.url=t,this.imageData={};var a=this.element,e=this.options;if(!e.rotatable&&!e.scalable&&(e.checkOrientation=!1),!e.checkOrientation||!window.ArrayBuffer)return void this.clone();if(et.test(t))return void(nt.test(t)?this.read(function(t){var i=t.replace(_t,""),a=atob(i),e=new ArrayBuffer(a.length),n=new Uint8Array(e);return gt(n,(function(t,i){n[i]=a.charCodeAt(i)})),e}(t)):this.clone());var n=new XMLHttpRequest,o=this.clone.bind(this);this.reloading=!0,this.xhr=n,n.onabort=o,n.onerror=o,n.ontimeout=o,n.onprogress=function(){n.getResponseHeader("content-type")!==it&&n.abort()},n.onload=function(){i.read(n.response)},n.onloadend=function(){i.reloading=!1,i.xhr=null},e.checkCrossOrigin&&St(t)&&a.crossOrigin&&(t=Rt(t)),n.open("GET",t,!0),n.responseType="arraybuffer",n.withCredentials="use-credentials"===a.crossOrigin,n.send()}}},{key:"read",value:function(t){var i=this.options,a=this.imageData,e=Ut(t),n=0,o=1,p=1;if(e>1){this.url=function(t,i){for(var a=[],e=new Uint8Array(t);e.length>0;)a.push(It.apply(null,ut(e.subarray(0,8192)))),e=e.subarray(8192);return"data:".concat(i,";base64,").concat(btoa(a.join("")))}(t,it);var c=function(t){var i=0,a=1,e=1;switch(t){case 2:a=-1;break;case 3:i=-180;break;case 4:e=-1;break;case 5:i=90,e=-1;break;case 6:i=90;break;case 7:i=90,a=-1;break;case 8:i=-90}return{rotate:i,scaleX:a,scaleY:e}}(e);n=c.rotate,o=c.scaleX,p=c.scaleY}i.rotatable&&(a.rotate=n),i.scalable&&(a.scaleX=o,a.scaleY=p),this.clone()}},{key:"clone",value:function(){var t=this.element,i=this.url,a=t.crossOrigin,e=i;this.options.checkCrossOrigin&&St(i)&&(a||(a="anonymous"),e=Rt(i)),this.crossOrigin=a,this.crossOriginUrl=e;var n=document.createElement("img");a&&(n.crossOrigin=a),n.src=e||i,n.alt=t.alt||"The image to crop",this.image=n,n.onload=this.start.bind(this),n.onerror=this.stop.bind(this),jt(n,L),t.parentNode.insertBefore(n,t.nextSibling)}},{key:"start",value:function(){var t=this,i=this.image;i.onload=null,i.onerror=null,this.sizing=!0;var a=x.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(x.navigator.userAgent),e=function(i,a){ft(t.imageData,{naturalWidth:i,naturalHeight:a,aspectRatio:i/a}),t.initialImageData=ft({},t.imageData),t.sizing=!1,t.sized=!0,t.build()};if(!i.naturalWidth||a){var n=document.createElement("img"),o=document.body||document.documentElement;this.sizingImage=n,n.onload=function(){e(n.width,n.height),a||o.removeChild(n)},n.src=i.src,a||(n.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",o.appendChild(n))}else e(i.naturalWidth,i.naturalHeight)}},{key:"stop",value:function(){var t=this.image;t.onload=null,t.onerror=null,t.parentNode.removeChild(t),this.image=null}},{key:"build",value:function(){if(this.sized&&!this.ready){var t=this.element,i=this.options,a=this.image,e=t.parentNode,n=document.createElement("div");n.innerHTML='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>';var o=n.querySelector(".".concat(f,"-container")),p=o.querySelector(".".concat(f,"-canvas")),c=o.querySelector(".".concat(f,"-drag-box")),s=o.querySelector(".".concat(f,"-crop-box")),r=s.querySelector(".".concat(f,"-face"));this.container=e,this.cropper=o,this.canvas=p,this.dragBox=c,this.cropBox=s,this.viewBox=o.querySelector(".".concat(f,"-view-box")),this.face=r,p.appendChild(a),jt(t,T),e.insertBefore(o,t.nextSibling),zt(a,L),this.initPreview(),this.bind(),i.initialAspectRatio=Math.max(0,i.initialAspectRatio)||NaN,i.aspectRatio=Math.max(0,i.aspectRatio)||NaN,i.viewMode=Math.max(0,Math.min(3,Math.round(i.viewMode)))||0,jt(s,T),i.guides||jt(s.getElementsByClassName("".concat(f,"-dashed")),T),i.center||jt(s.getElementsByClassName("".concat(f,"-center")),T),i.background&&jt(o,"".concat(f,"-bg")),i.highlight||jt(r,W),i.cropBoxMovable&&(jt(r,N),Bt(r,S,b)),i.cropBoxResizable||(jt(s.getElementsByClassName("".concat(f,"-line")),T),jt(s.getElementsByClassName("".concat(f,"-point")),T)),this.render(),this.ready=!0,this.setDragMode(i.dragMode),i.autoCrop&&this.crop(),this.setData(i.data),vt(i.ready)&&Tt(t,G,i.ready,{once:!0}),Lt(t,G)}}},{key:"unbuild",value:function(){if(this.ready){this.ready=!1,this.unbind(),this.resetPreview();var t=this.cropper.parentNode;t&&t.removeChild(this.cropper),zt(this.element,T)}}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}],[{key:"noConflict",value:function(){return window.Cropper=Kt,t}},{key:"setDefaults",value:function(t){ft(pt,ht(t)&&t)}}]),t}();function ti(){this._types=Object.create(null),this._extensions=Object.create(null);for(let t=0;t<arguments.length;t++)this.define(arguments[t]);this.define=this.define.bind(this),this.getType=this.getType.bind(this),this.getExtension=this.getExtension.bind(this)}ft(Jt.prototype,$t,Qt,Ft,Zt,Vt,Gt),ti.prototype.define=function(t,i){for(let a in t){let e=t[a].map((function(t){return t.toLowerCase()}));a=a.toLowerCase();for(let t=0;t<e.length;t++){const n=e[t];if("*"!==n[0]){if(!i&&n in this._types)throw new Error('Attempt to change mapping for "'+n+'" extension from "'+this._types[n]+'" to "'+a+'". Pass `force=true` to allow this, otherwise remove "'+n+'" from the list of extensions for "'+a+'".');this._types[n]=a}}if(i||!this._extensions[a]){const t=e[0];this._extensions[a]="*"!==t[0]?t:t.substr(1)}}},ti.prototype.getType=function(t){let i=(t=String(t)).replace(/^.*[/\\]/,"").toLowerCase(),a=i.replace(/^.*\./,"").toLowerCase(),e=i.length<t.length;return(a.length<i.length-1||!e)&&this._types[a]||null},ti.prototype.getExtension=function(t){return(t=/^\s*([^;\s]*)/.test(t)&&RegExp.$1)&&this._extensions[t.toLowerCase()]||null};var ii=new ti({"application/andrew-inset":["ez"],"application/applixware":["aw"],"application/atom+xml":["atom"],"application/atomcat+xml":["atomcat"],"application/atomdeleted+xml":["atomdeleted"],"application/atomsvc+xml":["atomsvc"],"application/atsc-dwd+xml":["dwd"],"application/atsc-held+xml":["held"],"application/atsc-rsat+xml":["rsat"],"application/bdoc":["bdoc"],"application/calendar+xml":["xcs"],"application/ccxml+xml":["ccxml"],"application/cdfx+xml":["cdfx"],"application/cdmi-capability":["cdmia"],"application/cdmi-container":["cdmic"],"application/cdmi-domain":["cdmid"],"application/cdmi-object":["cdmio"],"application/cdmi-queue":["cdmiq"],"application/cu-seeme":["cu"],"application/dash+xml":["mpd"],"application/davmount+xml":["davmount"],"application/docbook+xml":["dbk"],"application/dssc+der":["dssc"],"application/dssc+xml":["xdssc"],"application/ecmascript":["es","ecma"],"application/emma+xml":["emma"],"application/emotionml+xml":["emotionml"],"application/epub+zip":["epub"],"application/exi":["exi"],"application/express":["exp"],"application/fdt+xml":["fdt"],"application/font-tdpfr":["pfr"],"application/geo+json":["geojson"],"application/gml+xml":["gml"],"application/gpx+xml":["gpx"],"application/gxf":["gxf"],"application/gzip":["gz"],"application/hjson":["hjson"],"application/hyperstudio":["stk"],"application/inkml+xml":["ink","inkml"],"application/ipfix":["ipfix"],"application/its+xml":["its"],"application/java-archive":["jar","war","ear"],"application/java-serialized-object":["ser"],"application/java-vm":["class"],"application/javascript":["js","mjs"],"application/json":["json","map"],"application/json5":["json5"],"application/jsonml+json":["jsonml"],"application/ld+json":["jsonld"],"application/lgr+xml":["lgr"],"application/lost+xml":["lostxml"],"application/mac-binhex40":["hqx"],"application/mac-compactpro":["cpt"],"application/mads+xml":["mads"],"application/manifest+json":["webmanifest"],"application/marc":["mrc"],"application/marcxml+xml":["mrcx"],"application/mathematica":["ma","nb","mb"],"application/mathml+xml":["mathml"],"application/mbox":["mbox"],"application/mediaservercontrol+xml":["mscml"],"application/metalink+xml":["metalink"],"application/metalink4+xml":["meta4"],"application/mets+xml":["mets"],"application/mmt-aei+xml":["maei"],"application/mmt-usd+xml":["musd"],"application/mods+xml":["mods"],"application/mp21":["m21","mp21"],"application/mp4":["mp4s","m4p"],"application/msword":["doc","dot"],"application/mxf":["mxf"],"application/n-quads":["nq"],"application/n-triples":["nt"],"application/node":["cjs"],"application/octet-stream":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"],"application/oda":["oda"],"application/oebps-package+xml":["opf"],"application/ogg":["ogx"],"application/omdoc+xml":["omdoc"],"application/onenote":["onetoc","onetoc2","onetmp","onepkg"],"application/oxps":["oxps"],"application/p2p-overlay+xml":["relo"],"application/patch-ops-error+xml":["xer"],"application/pdf":["pdf"],"application/pgp-encrypted":["pgp"],"application/pgp-signature":["asc","sig"],"application/pics-rules":["prf"],"application/pkcs10":["p10"],"application/pkcs7-mime":["p7m","p7c"],"application/pkcs7-signature":["p7s"],"application/pkcs8":["p8"],"application/pkix-attr-cert":["ac"],"application/pkix-cert":["cer"],"application/pkix-crl":["crl"],"application/pkix-pkipath":["pkipath"],"application/pkixcmp":["pki"],"application/pls+xml":["pls"],"application/postscript":["ai","eps","ps"],"application/provenance+xml":["provx"],"application/pskc+xml":["pskcxml"],"application/raml+yaml":["raml"],"application/rdf+xml":["rdf","owl"],"application/reginfo+xml":["rif"],"application/relax-ng-compact-syntax":["rnc"],"application/resource-lists+xml":["rl"],"application/resource-lists-diff+xml":["rld"],"application/rls-services+xml":["rs"],"application/route-apd+xml":["rapd"],"application/route-s-tsid+xml":["sls"],"application/route-usd+xml":["rusd"],"application/rpki-ghostbusters":["gbr"],"application/rpki-manifest":["mft"],"application/rpki-roa":["roa"],"application/rsd+xml":["rsd"],"application/rss+xml":["rss"],"application/rtf":["rtf"],"application/sbml+xml":["sbml"],"application/scvp-cv-request":["scq"],"application/scvp-cv-response":["scs"],"application/scvp-vp-request":["spq"],"application/scvp-vp-response":["spp"],"application/sdp":["sdp"],"application/senml+xml":["senmlx"],"application/sensml+xml":["sensmlx"],"application/set-payment-initiation":["setpay"],"application/set-registration-initiation":["setreg"],"application/shf+xml":["shf"],"application/sieve":["siv","sieve"],"application/smil+xml":["smi","smil"],"application/sparql-query":["rq"],"application/sparql-results+xml":["srx"],"application/srgs":["gram"],"application/srgs+xml":["grxml"],"application/sru+xml":["sru"],"application/ssdl+xml":["ssdl"],"application/ssml+xml":["ssml"],"application/swid+xml":["swidtag"],"application/tei+xml":["tei","teicorpus"],"application/thraud+xml":["tfi"],"application/timestamped-data":["tsd"],"application/toml":["toml"],"application/trig":["trig"],"application/ttml+xml":["ttml"],"application/ubjson":["ubj"],"application/urc-ressheet+xml":["rsheet"],"application/urc-targetdesc+xml":["td"],"application/voicexml+xml":["vxml"],"application/wasm":["wasm"],"application/widget":["wgt"],"application/winhlp":["hlp"],"application/wsdl+xml":["wsdl"],"application/wspolicy+xml":["wspolicy"],"application/xaml+xml":["xaml"],"application/xcap-att+xml":["xav"],"application/xcap-caps+xml":["xca"],"application/xcap-diff+xml":["xdf"],"application/xcap-el+xml":["xel"],"application/xcap-ns+xml":["xns"],"application/xenc+xml":["xenc"],"application/xhtml+xml":["xhtml","xht"],"application/xliff+xml":["xlf"],"application/xml":["xml","xsl","xsd","rng"],"application/xml-dtd":["dtd"],"application/xop+xml":["xop"],"application/xproc+xml":["xpl"],"application/xslt+xml":["*xsl","xslt"],"application/xspf+xml":["xspf"],"application/xv+xml":["mxml","xhvml","xvml","xvm"],"application/yang":["yang"],"application/yin+xml":["yin"],"application/zip":["zip"],"audio/3gpp":["*3gpp"],"audio/adpcm":["adp"],"audio/amr":["amr"],"audio/basic":["au","snd"],"audio/midi":["mid","midi","kar","rmi"],"audio/mobile-xmf":["mxmf"],"audio/mp3":["*mp3"],"audio/mp4":["m4a","mp4a"],"audio/mpeg":["mpga","mp2","mp2a","mp3","m2a","m3a"],"audio/ogg":["oga","ogg","spx","opus"],"audio/s3m":["s3m"],"audio/silk":["sil"],"audio/wav":["wav"],"audio/wave":["*wav"],"audio/webm":["weba"],"audio/xm":["xm"],"font/collection":["ttc"],"font/otf":["otf"],"font/ttf":["ttf"],"font/woff":["woff"],"font/woff2":["woff2"],"image/aces":["exr"],"image/apng":["apng"],"image/avif":["avif"],"image/bmp":["bmp"],"image/cgm":["cgm"],"image/dicom-rle":["drle"],"image/emf":["emf"],"image/fits":["fits"],"image/g3fax":["g3"],"image/gif":["gif"],"image/heic":["heic"],"image/heic-sequence":["heics"],"image/heif":["heif"],"image/heif-sequence":["heifs"],"image/hej2k":["hej2"],"image/hsj2":["hsj2"],"image/ief":["ief"],"image/jls":["jls"],"image/jp2":["jp2","jpg2"],"image/jpeg":["jpeg","jpg","jpe"],"image/jph":["jph"],"image/jphc":["jhc"],"image/jpm":["jpm"],"image/jpx":["jpx","jpf"],"image/jxr":["jxr"],"image/jxra":["jxra"],"image/jxrs":["jxrs"],"image/jxs":["jxs"],"image/jxsc":["jxsc"],"image/jxsi":["jxsi"],"image/jxss":["jxss"],"image/ktx":["ktx"],"image/ktx2":["ktx2"],"image/png":["png"],"image/sgi":["sgi"],"image/svg+xml":["svg","svgz"],"image/t38":["t38"],"image/tiff":["tif","tiff"],"image/tiff-fx":["tfx"],"image/webp":["webp"],"image/wmf":["wmf"],"message/disposition-notification":["disposition-notification"],"message/global":["u8msg"],"message/global-delivery-status":["u8dsn"],"message/global-disposition-notification":["u8mdn"],"message/global-headers":["u8hdr"],"message/rfc822":["eml","mime"],"model/3mf":["3mf"],"model/gltf+json":["gltf"],"model/gltf-binary":["glb"],"model/iges":["igs","iges"],"model/mesh":["msh","mesh","silo"],"model/mtl":["mtl"],"model/obj":["obj"],"model/step+xml":["stpx"],"model/step+zip":["stpz"],"model/step-xml+zip":["stpxz"],"model/stl":["stl"],"model/vrml":["wrl","vrml"],"model/x3d+binary":["*x3db","x3dbz"],"model/x3d+fastinfoset":["x3db"],"model/x3d+vrml":["*x3dv","x3dvz"],"model/x3d+xml":["x3d","x3dz"],"model/x3d-vrml":["x3dv"],"text/cache-manifest":["appcache","manifest"],"text/calendar":["ics","ifb"],"text/coffeescript":["coffee","litcoffee"],"text/css":["css"],"text/csv":["csv"],"text/html":["html","htm","shtml"],"text/jade":["jade"],"text/jsx":["jsx"],"text/less":["less"],"text/markdown":["markdown","md"],"text/mathml":["mml"],"text/mdx":["mdx"],"text/n3":["n3"],"text/plain":["txt","text","conf","def","list","log","in","ini"],"text/richtext":["rtx"],"text/rtf":["*rtf"],"text/sgml":["sgml","sgm"],"text/shex":["shex"],"text/slim":["slim","slm"],"text/spdx":["spdx"],"text/stylus":["stylus","styl"],"text/tab-separated-values":["tsv"],"text/troff":["t","tr","roff","man","me","ms"],"text/turtle":["ttl"],"text/uri-list":["uri","uris","urls"],"text/vcard":["vcard"],"text/vtt":["vtt"],"text/xml":["*xml"],"text/yaml":["yaml","yml"],"video/3gpp":["3gp","3gpp"],"video/3gpp2":["3g2"],"video/h261":["h261"],"video/h263":["h263"],"video/h264":["h264"],"video/iso.segment":["m4s"],"video/jpeg":["jpgv"],"video/jpm":["*jpm","jpgm"],"video/mj2":["mj2","mjp2"],"video/mp2t":["ts"],"video/mp4":["mp4","mp4v","mpg4"],"video/mpeg":["mpeg","mpg","mpe","m1v","m2v"],"video/ogg":["ogv"],"video/quicktime":["qt","mov"],"video/webm":["webm"]},{"application/prs.cww":["cww"],"application/vnd.1000minds.decision-model+xml":["1km"],"application/vnd.3gpp.pic-bw-large":["plb"],"application/vnd.3gpp.pic-bw-small":["psb"],"application/vnd.3gpp.pic-bw-var":["pvb"],"application/vnd.3gpp2.tcap":["tcap"],"application/vnd.3m.post-it-notes":["pwn"],"application/vnd.accpac.simply.aso":["aso"],"application/vnd.accpac.simply.imp":["imp"],"application/vnd.acucobol":["acu"],"application/vnd.acucorp":["atc","acutc"],"application/vnd.adobe.air-application-installer-package+zip":["air"],"application/vnd.adobe.formscentral.fcdt":["fcdt"],"application/vnd.adobe.fxp":["fxp","fxpl"],"application/vnd.adobe.xdp+xml":["xdp"],"application/vnd.adobe.xfdf":["xfdf"],"application/vnd.ahead.space":["ahead"],"application/vnd.airzip.filesecure.azf":["azf"],"application/vnd.airzip.filesecure.azs":["azs"],"application/vnd.amazon.ebook":["azw"],"application/vnd.americandynamics.acc":["acc"],"application/vnd.amiga.ami":["ami"],"application/vnd.android.package-archive":["apk"],"application/vnd.anser-web-certificate-issue-initiation":["cii"],"application/vnd.anser-web-funds-transfer-initiation":["fti"],"application/vnd.antix.game-component":["atx"],"application/vnd.apple.installer+xml":["mpkg"],"application/vnd.apple.keynote":["key"],"application/vnd.apple.mpegurl":["m3u8"],"application/vnd.apple.numbers":["numbers"],"application/vnd.apple.pages":["pages"],"application/vnd.apple.pkpass":["pkpass"],"application/vnd.aristanetworks.swi":["swi"],"application/vnd.astraea-software.iota":["iota"],"application/vnd.audiograph":["aep"],"application/vnd.balsamiq.bmml+xml":["bmml"],"application/vnd.blueice.multipass":["mpm"],"application/vnd.bmi":["bmi"],"application/vnd.businessobjects":["rep"],"application/vnd.chemdraw+xml":["cdxml"],"application/vnd.chipnuts.karaoke-mmd":["mmd"],"application/vnd.cinderella":["cdy"],"application/vnd.citationstyles.style+xml":["csl"],"application/vnd.claymore":["cla"],"application/vnd.cloanto.rp9":["rp9"],"application/vnd.clonk.c4group":["c4g","c4d","c4f","c4p","c4u"],"application/vnd.cluetrust.cartomobile-config":["c11amc"],"application/vnd.cluetrust.cartomobile-config-pkg":["c11amz"],"application/vnd.commonspace":["csp"],"application/vnd.contact.cmsg":["cdbcmsg"],"application/vnd.cosmocaller":["cmc"],"application/vnd.crick.clicker":["clkx"],"application/vnd.crick.clicker.keyboard":["clkk"],"application/vnd.crick.clicker.palette":["clkp"],"application/vnd.crick.clicker.template":["clkt"],"application/vnd.crick.clicker.wordbank":["clkw"],"application/vnd.criticaltools.wbs+xml":["wbs"],"application/vnd.ctc-posml":["pml"],"application/vnd.cups-ppd":["ppd"],"application/vnd.curl.car":["car"],"application/vnd.curl.pcurl":["pcurl"],"application/vnd.dart":["dart"],"application/vnd.data-vision.rdz":["rdz"],"application/vnd.dbf":["dbf"],"application/vnd.dece.data":["uvf","uvvf","uvd","uvvd"],"application/vnd.dece.ttml+xml":["uvt","uvvt"],"application/vnd.dece.unspecified":["uvx","uvvx"],"application/vnd.dece.zip":["uvz","uvvz"],"application/vnd.denovo.fcselayout-link":["fe_launch"],"application/vnd.dna":["dna"],"application/vnd.dolby.mlp":["mlp"],"application/vnd.dpgraph":["dpg"],"application/vnd.dreamfactory":["dfac"],"application/vnd.ds-keypoint":["kpxx"],"application/vnd.dvb.ait":["ait"],"application/vnd.dvb.service":["svc"],"application/vnd.dynageo":["geo"],"application/vnd.ecowin.chart":["mag"],"application/vnd.enliven":["nml"],"application/vnd.epson.esf":["esf"],"application/vnd.epson.msf":["msf"],"application/vnd.epson.quickanime":["qam"],"application/vnd.epson.salt":["slt"],"application/vnd.epson.ssf":["ssf"],"application/vnd.eszigno3+xml":["es3","et3"],"application/vnd.ezpix-album":["ez2"],"application/vnd.ezpix-package":["ez3"],"application/vnd.fdf":["fdf"],"application/vnd.fdsn.mseed":["mseed"],"application/vnd.fdsn.seed":["seed","dataless"],"application/vnd.flographit":["gph"],"application/vnd.fluxtime.clip":["ftc"],"application/vnd.framemaker":["fm","frame","maker","book"],"application/vnd.frogans.fnc":["fnc"],"application/vnd.frogans.ltf":["ltf"],"application/vnd.fsc.weblaunch":["fsc"],"application/vnd.fujitsu.oasys":["oas"],"application/vnd.fujitsu.oasys2":["oa2"],"application/vnd.fujitsu.oasys3":["oa3"],"application/vnd.fujitsu.oasysgp":["fg5"],"application/vnd.fujitsu.oasysprs":["bh2"],"application/vnd.fujixerox.ddd":["ddd"],"application/vnd.fujixerox.docuworks":["xdw"],"application/vnd.fujixerox.docuworks.binder":["xbd"],"application/vnd.fuzzysheet":["fzs"],"application/vnd.genomatix.tuxedo":["txd"],"application/vnd.geogebra.file":["ggb"],"application/vnd.geogebra.tool":["ggt"],"application/vnd.geometry-explorer":["gex","gre"],"application/vnd.geonext":["gxt"],"application/vnd.geoplan":["g2w"],"application/vnd.geospace":["g3w"],"application/vnd.gmx":["gmx"],"application/vnd.google-apps.document":["gdoc"],"application/vnd.google-apps.presentation":["gslides"],"application/vnd.google-apps.spreadsheet":["gsheet"],"application/vnd.google-earth.kml+xml":["kml"],"application/vnd.google-earth.kmz":["kmz"],"application/vnd.grafeq":["gqf","gqs"],"application/vnd.groove-account":["gac"],"application/vnd.groove-help":["ghf"],"application/vnd.groove-identity-message":["gim"],"application/vnd.groove-injector":["grv"],"application/vnd.groove-tool-message":["gtm"],"application/vnd.groove-tool-template":["tpl"],"application/vnd.groove-vcard":["vcg"],"application/vnd.hal+xml":["hal"],"application/vnd.handheld-entertainment+xml":["zmm"],"application/vnd.hbci":["hbci"],"application/vnd.hhe.lesson-player":["les"],"application/vnd.hp-hpgl":["hpgl"],"application/vnd.hp-hpid":["hpid"],"application/vnd.hp-hps":["hps"],"application/vnd.hp-jlyt":["jlt"],"application/vnd.hp-pcl":["pcl"],"application/vnd.hp-pclxl":["pclxl"],"application/vnd.hydrostatix.sof-data":["sfd-hdstx"],"application/vnd.ibm.minipay":["mpy"],"application/vnd.ibm.modcap":["afp","listafp","list3820"],"application/vnd.ibm.rights-management":["irm"],"application/vnd.ibm.secure-container":["sc"],"application/vnd.iccprofile":["icc","icm"],"application/vnd.igloader":["igl"],"application/vnd.immervision-ivp":["ivp"],"application/vnd.immervision-ivu":["ivu"],"application/vnd.insors.igm":["igm"],"application/vnd.intercon.formnet":["xpw","xpx"],"application/vnd.intergeo":["i2g"],"application/vnd.intu.qbo":["qbo"],"application/vnd.intu.qfx":["qfx"],"application/vnd.ipunplugged.rcprofile":["rcprofile"],"application/vnd.irepository.package+xml":["irp"],"application/vnd.is-xpr":["xpr"],"application/vnd.isac.fcs":["fcs"],"application/vnd.jam":["jam"],"application/vnd.jcp.javame.midlet-rms":["rms"],"application/vnd.jisp":["jisp"],"application/vnd.joost.joda-archive":["joda"],"application/vnd.kahootz":["ktz","ktr"],"application/vnd.kde.karbon":["karbon"],"application/vnd.kde.kchart":["chrt"],"application/vnd.kde.kformula":["kfo"],"application/vnd.kde.kivio":["flw"],"application/vnd.kde.kontour":["kon"],"application/vnd.kde.kpresenter":["kpr","kpt"],"application/vnd.kde.kspread":["ksp"],"application/vnd.kde.kword":["kwd","kwt"],"application/vnd.kenameaapp":["htke"],"application/vnd.kidspiration":["kia"],"application/vnd.kinar":["kne","knp"],"application/vnd.koan":["skp","skd","skt","skm"],"application/vnd.kodak-descriptor":["sse"],"application/vnd.las.las+xml":["lasxml"],"application/vnd.llamagraphics.life-balance.desktop":["lbd"],"application/vnd.llamagraphics.life-balance.exchange+xml":["lbe"],"application/vnd.lotus-1-2-3":["123"],"application/vnd.lotus-approach":["apr"],"application/vnd.lotus-freelance":["pre"],"application/vnd.lotus-notes":["nsf"],"application/vnd.lotus-organizer":["org"],"application/vnd.lotus-screencam":["scm"],"application/vnd.lotus-wordpro":["lwp"],"application/vnd.macports.portpkg":["portpkg"],"application/vnd.mapbox-vector-tile":["mvt"],"application/vnd.mcd":["mcd"],"application/vnd.medcalcdata":["mc1"],"application/vnd.mediastation.cdkey":["cdkey"],"application/vnd.mfer":["mwf"],"application/vnd.mfmp":["mfm"],"application/vnd.micrografx.flo":["flo"],"application/vnd.micrografx.igx":["igx"],"application/vnd.mif":["mif"],"application/vnd.mobius.daf":["daf"],"application/vnd.mobius.dis":["dis"],"application/vnd.mobius.mbk":["mbk"],"application/vnd.mobius.mqy":["mqy"],"application/vnd.mobius.msl":["msl"],"application/vnd.mobius.plc":["plc"],"application/vnd.mobius.txf":["txf"],"application/vnd.mophun.application":["mpn"],"application/vnd.mophun.certificate":["mpc"],"application/vnd.mozilla.xul+xml":["xul"],"application/vnd.ms-artgalry":["cil"],"application/vnd.ms-cab-compressed":["cab"],"application/vnd.ms-excel":["xls","xlm","xla","xlc","xlt","xlw"],"application/vnd.ms-excel.addin.macroenabled.12":["xlam"],"application/vnd.ms-excel.sheet.binary.macroenabled.12":["xlsb"],"application/vnd.ms-excel.sheet.macroenabled.12":["xlsm"],"application/vnd.ms-excel.template.macroenabled.12":["xltm"],"application/vnd.ms-fontobject":["eot"],"application/vnd.ms-htmlhelp":["chm"],"application/vnd.ms-ims":["ims"],"application/vnd.ms-lrm":["lrm"],"application/vnd.ms-officetheme":["thmx"],"application/vnd.ms-outlook":["msg"],"application/vnd.ms-pki.seccat":["cat"],"application/vnd.ms-pki.stl":["*stl"],"application/vnd.ms-powerpoint":["ppt","pps","pot"],"application/vnd.ms-powerpoint.addin.macroenabled.12":["ppam"],"application/vnd.ms-powerpoint.presentation.macroenabled.12":["pptm"],"application/vnd.ms-powerpoint.slide.macroenabled.12":["sldm"],"application/vnd.ms-powerpoint.slideshow.macroenabled.12":["ppsm"],"application/vnd.ms-powerpoint.template.macroenabled.12":["potm"],"application/vnd.ms-project":["mpp","mpt"],"application/vnd.ms-word.document.macroenabled.12":["docm"],"application/vnd.ms-word.template.macroenabled.12":["dotm"],"application/vnd.ms-works":["wps","wks","wcm","wdb"],"application/vnd.ms-wpl":["wpl"],"application/vnd.ms-xpsdocument":["xps"],"application/vnd.mseq":["mseq"],"application/vnd.musician":["mus"],"application/vnd.muvee.style":["msty"],"application/vnd.mynfc":["taglet"],"application/vnd.neurolanguage.nlu":["nlu"],"application/vnd.nitf":["ntf","nitf"],"application/vnd.noblenet-directory":["nnd"],"application/vnd.noblenet-sealer":["nns"],"application/vnd.noblenet-web":["nnw"],"application/vnd.nokia.n-gage.ac+xml":["*ac"],"application/vnd.nokia.n-gage.data":["ngdat"],"application/vnd.nokia.n-gage.symbian.install":["n-gage"],"application/vnd.nokia.radio-preset":["rpst"],"application/vnd.nokia.radio-presets":["rpss"],"application/vnd.novadigm.edm":["edm"],"application/vnd.novadigm.edx":["edx"],"application/vnd.novadigm.ext":["ext"],"application/vnd.oasis.opendocument.chart":["odc"],"application/vnd.oasis.opendocument.chart-template":["otc"],"application/vnd.oasis.opendocument.database":["odb"],"application/vnd.oasis.opendocument.formula":["odf"],"application/vnd.oasis.opendocument.formula-template":["odft"],"application/vnd.oasis.opendocument.graphics":["odg"],"application/vnd.oasis.opendocument.graphics-template":["otg"],"application/vnd.oasis.opendocument.image":["odi"],"application/vnd.oasis.opendocument.image-template":["oti"],"application/vnd.oasis.opendocument.presentation":["odp"],"application/vnd.oasis.opendocument.presentation-template":["otp"],"application/vnd.oasis.opendocument.spreadsheet":["ods"],"application/vnd.oasis.opendocument.spreadsheet-template":["ots"],"application/vnd.oasis.opendocument.text":["odt"],"application/vnd.oasis.opendocument.text-master":["odm"],"application/vnd.oasis.opendocument.text-template":["ott"],"application/vnd.oasis.opendocument.text-web":["oth"],"application/vnd.olpc-sugar":["xo"],"application/vnd.oma.dd2+xml":["dd2"],"application/vnd.openblox.game+xml":["obgx"],"application/vnd.openofficeorg.extension":["oxt"],"application/vnd.openstreetmap.data+xml":["osm"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":["pptx"],"application/vnd.openxmlformats-officedocument.presentationml.slide":["sldx"],"application/vnd.openxmlformats-officedocument.presentationml.slideshow":["ppsx"],"application/vnd.openxmlformats-officedocument.presentationml.template":["potx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":["xlsx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.template":["xltx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":["docx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.template":["dotx"],"application/vnd.osgeo.mapguide.package":["mgp"],"application/vnd.osgi.dp":["dp"],"application/vnd.osgi.subsystem":["esa"],"application/vnd.palm":["pdb","pqa","oprc"],"application/vnd.pawaafile":["paw"],"application/vnd.pg.format":["str"],"application/vnd.pg.osasli":["ei6"],"application/vnd.picsel":["efif"],"application/vnd.pmi.widget":["wg"],"application/vnd.pocketlearn":["plf"],"application/vnd.powerbuilder6":["pbd"],"application/vnd.previewsystems.box":["box"],"application/vnd.proteus.magazine":["mgz"],"application/vnd.publishare-delta-tree":["qps"],"application/vnd.pvi.ptid1":["ptid"],"application/vnd.quark.quarkxpress":["qxd","qxt","qwd","qwt","qxl","qxb"],"application/vnd.rar":["rar"],"application/vnd.realvnc.bed":["bed"],"application/vnd.recordare.musicxml":["mxl"],"application/vnd.recordare.musicxml+xml":["musicxml"],"application/vnd.rig.cryptonote":["cryptonote"],"application/vnd.rim.cod":["cod"],"application/vnd.rn-realmedia":["rm"],"application/vnd.rn-realmedia-vbr":["rmvb"],"application/vnd.route66.link66+xml":["link66"],"application/vnd.sailingtracker.track":["st"],"application/vnd.seemail":["see"],"application/vnd.sema":["sema"],"application/vnd.semd":["semd"],"application/vnd.semf":["semf"],"application/vnd.shana.informed.formdata":["ifm"],"application/vnd.shana.informed.formtemplate":["itp"],"application/vnd.shana.informed.interchange":["iif"],"application/vnd.shana.informed.package":["ipk"],"application/vnd.simtech-mindmapper":["twd","twds"],"application/vnd.smaf":["mmf"],"application/vnd.smart.teacher":["teacher"],"application/vnd.software602.filler.form+xml":["fo"],"application/vnd.solent.sdkm+xml":["sdkm","sdkd"],"application/vnd.spotfire.dxp":["dxp"],"application/vnd.spotfire.sfs":["sfs"],"application/vnd.stardivision.calc":["sdc"],"application/vnd.stardivision.draw":["sda"],"application/vnd.stardivision.impress":["sdd"],"application/vnd.stardivision.math":["smf"],"application/vnd.stardivision.writer":["sdw","vor"],"application/vnd.stardivision.writer-global":["sgl"],"application/vnd.stepmania.package":["smzip"],"application/vnd.stepmania.stepchart":["sm"],"application/vnd.sun.wadl+xml":["wadl"],"application/vnd.sun.xml.calc":["sxc"],"application/vnd.sun.xml.calc.template":["stc"],"application/vnd.sun.xml.draw":["sxd"],"application/vnd.sun.xml.draw.template":["std"],"application/vnd.sun.xml.impress":["sxi"],"application/vnd.sun.xml.impress.template":["sti"],"application/vnd.sun.xml.math":["sxm"],"application/vnd.sun.xml.writer":["sxw"],"application/vnd.sun.xml.writer.global":["sxg"],"application/vnd.sun.xml.writer.template":["stw"],"application/vnd.sus-calendar":["sus","susp"],"application/vnd.svd":["svd"],"application/vnd.symbian.install":["sis","sisx"],"application/vnd.syncml+xml":["xsm"],"application/vnd.syncml.dm+wbxml":["bdm"],"application/vnd.syncml.dm+xml":["xdm"],"application/vnd.syncml.dmddf+xml":["ddf"],"application/vnd.tao.intent-module-archive":["tao"],"application/vnd.tcpdump.pcap":["pcap","cap","dmp"],"application/vnd.tmobile-livetv":["tmo"],"application/vnd.trid.tpt":["tpt"],"application/vnd.triscape.mxs":["mxs"],"application/vnd.trueapp":["tra"],"application/vnd.ufdl":["ufd","ufdl"],"application/vnd.uiq.theme":["utz"],"application/vnd.umajin":["umj"],"application/vnd.unity":["unityweb"],"application/vnd.uoml+xml":["uoml"],"application/vnd.vcx":["vcx"],"application/vnd.visio":["vsd","vst","vss","vsw"],"application/vnd.visionary":["vis"],"application/vnd.vsf":["vsf"],"application/vnd.wap.wbxml":["wbxml"],"application/vnd.wap.wmlc":["wmlc"],"application/vnd.wap.wmlscriptc":["wmlsc"],"application/vnd.webturbo":["wtb"],"application/vnd.wolfram.player":["nbp"],"application/vnd.wordperfect":["wpd"],"application/vnd.wqd":["wqd"],"application/vnd.wt.stf":["stf"],"application/vnd.xara":["xar"],"application/vnd.xfdl":["xfdl"],"application/vnd.yamaha.hv-dic":["hvd"],"application/vnd.yamaha.hv-script":["hvs"],"application/vnd.yamaha.hv-voice":["hvp"],"application/vnd.yamaha.openscoreformat":["osf"],"application/vnd.yamaha.openscoreformat.osfpvg+xml":["osfpvg"],"application/vnd.yamaha.smaf-audio":["saf"],"application/vnd.yamaha.smaf-phrase":["spf"],"application/vnd.yellowriver-custom-menu":["cmp"],"application/vnd.zul":["zir","zirz"],"application/vnd.zzazz.deck+xml":["zaz"],"application/x-7z-compressed":["7z"],"application/x-abiword":["abw"],"application/x-ace-compressed":["ace"],"application/x-apple-diskimage":["*dmg"],"application/x-arj":["arj"],"application/x-authorware-bin":["aab","x32","u32","vox"],"application/x-authorware-map":["aam"],"application/x-authorware-seg":["aas"],"application/x-bcpio":["bcpio"],"application/x-bdoc":["*bdoc"],"application/x-bittorrent":["torrent"],"application/x-blorb":["blb","blorb"],"application/x-bzip":["bz"],"application/x-bzip2":["bz2","boz"],"application/x-cbr":["cbr","cba","cbt","cbz","cb7"],"application/x-cdlink":["vcd"],"application/x-cfs-compressed":["cfs"],"application/x-chat":["chat"],"application/x-chess-pgn":["pgn"],"application/x-chrome-extension":["crx"],"application/x-cocoa":["cco"],"application/x-conference":["nsc"],"application/x-cpio":["cpio"],"application/x-csh":["csh"],"application/x-debian-package":["*deb","udeb"],"application/x-dgc-compressed":["dgc"],"application/x-director":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"],"application/x-doom":["wad"],"application/x-dtbncx+xml":["ncx"],"application/x-dtbook+xml":["dtb"],"application/x-dtbresource+xml":["res"],"application/x-dvi":["dvi"],"application/x-envoy":["evy"],"application/x-eva":["eva"],"application/x-font-bdf":["bdf"],"application/x-font-ghostscript":["gsf"],"application/x-font-linux-psf":["psf"],"application/x-font-pcf":["pcf"],"application/x-font-snf":["snf"],"application/x-font-type1":["pfa","pfb","pfm","afm"],"application/x-freearc":["arc"],"application/x-futuresplash":["spl"],"application/x-gca-compressed":["gca"],"application/x-glulx":["ulx"],"application/x-gnumeric":["gnumeric"],"application/x-gramps-xml":["gramps"],"application/x-gtar":["gtar"],"application/x-hdf":["hdf"],"application/x-httpd-php":["php"],"application/x-install-instructions":["install"],"application/x-iso9660-image":["*iso"],"application/x-iwork-keynote-sffkey":["*key"],"application/x-iwork-numbers-sffnumbers":["*numbers"],"application/x-iwork-pages-sffpages":["*pages"],"application/x-java-archive-diff":["jardiff"],"application/x-java-jnlp-file":["jnlp"],"application/x-keepass2":["kdbx"],"application/x-latex":["latex"],"application/x-lua-bytecode":["luac"],"application/x-lzh-compressed":["lzh","lha"],"application/x-makeself":["run"],"application/x-mie":["mie"],"application/x-mobipocket-ebook":["prc","mobi"],"application/x-ms-application":["application"],"application/x-ms-shortcut":["lnk"],"application/x-ms-wmd":["wmd"],"application/x-ms-wmz":["wmz"],"application/x-ms-xbap":["xbap"],"application/x-msaccess":["mdb"],"application/x-msbinder":["obd"],"application/x-mscardfile":["crd"],"application/x-msclip":["clp"],"application/x-msdos-program":["*exe"],"application/x-msdownload":["*exe","*dll","com","bat","*msi"],"application/x-msmediaview":["mvb","m13","m14"],"application/x-msmetafile":["*wmf","*wmz","*emf","emz"],"application/x-msmoney":["mny"],"application/x-mspublisher":["pub"],"application/x-msschedule":["scd"],"application/x-msterminal":["trm"],"application/x-mswrite":["wri"],"application/x-netcdf":["nc","cdf"],"application/x-ns-proxy-autoconfig":["pac"],"application/x-nzb":["nzb"],"application/x-perl":["pl","pm"],"application/x-pilot":["*prc","*pdb"],"application/x-pkcs12":["p12","pfx"],"application/x-pkcs7-certificates":["p7b","spc"],"application/x-pkcs7-certreqresp":["p7r"],"application/x-rar-compressed":["*rar"],"application/x-redhat-package-manager":["rpm"],"application/x-research-info-systems":["ris"],"application/x-sea":["sea"],"application/x-sh":["sh"],"application/x-shar":["shar"],"application/x-shockwave-flash":["swf"],"application/x-silverlight-app":["xap"],"application/x-sql":["sql"],"application/x-stuffit":["sit"],"application/x-stuffitx":["sitx"],"application/x-subrip":["srt"],"application/x-sv4cpio":["sv4cpio"],"application/x-sv4crc":["sv4crc"],"application/x-t3vm-image":["t3"],"application/x-tads":["gam"],"application/x-tar":["tar"],"application/x-tcl":["tcl","tk"],"application/x-tex":["tex"],"application/x-tex-tfm":["tfm"],"application/x-texinfo":["texinfo","texi"],"application/x-tgif":["*obj"],"application/x-ustar":["ustar"],"application/x-virtualbox-hdd":["hdd"],"application/x-virtualbox-ova":["ova"],"application/x-virtualbox-ovf":["ovf"],"application/x-virtualbox-vbox":["vbox"],"application/x-virtualbox-vbox-extpack":["vbox-extpack"],"application/x-virtualbox-vdi":["vdi"],"application/x-virtualbox-vhd":["vhd"],"application/x-virtualbox-vmdk":["vmdk"],"application/x-wais-source":["src"],"application/x-web-app-manifest+json":["webapp"],"application/x-x509-ca-cert":["der","crt","pem"],"application/x-xfig":["fig"],"application/x-xliff+xml":["*xlf"],"application/x-xpinstall":["xpi"],"application/x-xz":["xz"],"application/x-zmachine":["z1","z2","z3","z4","z5","z6","z7","z8"],"audio/vnd.dece.audio":["uva","uvva"],"audio/vnd.digital-winds":["eol"],"audio/vnd.dra":["dra"],"audio/vnd.dts":["dts"],"audio/vnd.dts.hd":["dtshd"],"audio/vnd.lucent.voice":["lvp"],"audio/vnd.ms-playready.media.pya":["pya"],"audio/vnd.nuera.ecelp4800":["ecelp4800"],"audio/vnd.nuera.ecelp7470":["ecelp7470"],"audio/vnd.nuera.ecelp9600":["ecelp9600"],"audio/vnd.rip":["rip"],"audio/x-aac":["aac"],"audio/x-aiff":["aif","aiff","aifc"],"audio/x-caf":["caf"],"audio/x-flac":["flac"],"audio/x-m4a":["*m4a"],"audio/x-matroska":["mka"],"audio/x-mpegurl":["m3u"],"audio/x-ms-wax":["wax"],"audio/x-ms-wma":["wma"],"audio/x-pn-realaudio":["ram","ra"],"audio/x-pn-realaudio-plugin":["rmp"],"audio/x-realaudio":["*ra"],"audio/x-wav":["*wav"],"chemical/x-cdx":["cdx"],"chemical/x-cif":["cif"],"chemical/x-cmdf":["cmdf"],"chemical/x-cml":["cml"],"chemical/x-csml":["csml"],"chemical/x-xyz":["xyz"],"image/prs.btif":["btif"],"image/prs.pti":["pti"],"image/vnd.adobe.photoshop":["psd"],"image/vnd.airzip.accelerator.azv":["azv"],"image/vnd.dece.graphic":["uvi","uvvi","uvg","uvvg"],"image/vnd.djvu":["djvu","djv"],"image/vnd.dvb.subtitle":["*sub"],"image/vnd.dwg":["dwg"],"image/vnd.dxf":["dxf"],"image/vnd.fastbidsheet":["fbs"],"image/vnd.fpx":["fpx"],"image/vnd.fst":["fst"],"image/vnd.fujixerox.edmics-mmr":["mmr"],"image/vnd.fujixerox.edmics-rlc":["rlc"],"image/vnd.microsoft.icon":["ico"],"image/vnd.ms-dds":["dds"],"image/vnd.ms-modi":["mdi"],"image/vnd.ms-photo":["wdp"],"image/vnd.net-fpx":["npx"],"image/vnd.pco.b16":["b16"],"image/vnd.tencent.tap":["tap"],"image/vnd.valve.source.texture":["vtf"],"image/vnd.wap.wbmp":["wbmp"],"image/vnd.xiff":["xif"],"image/vnd.zbrush.pcx":["pcx"],"image/x-3ds":["3ds"],"image/x-cmu-raster":["ras"],"image/x-cmx":["cmx"],"image/x-freehand":["fh","fhc","fh4","fh5","fh7"],"image/x-icon":["*ico"],"image/x-jng":["jng"],"image/x-mrsid-image":["sid"],"image/x-ms-bmp":["*bmp"],"image/x-pcx":["*pcx"],"image/x-pict":["pic","pct"],"image/x-portable-anymap":["pnm"],"image/x-portable-bitmap":["pbm"],"image/x-portable-graymap":["pgm"],"image/x-portable-pixmap":["ppm"],"image/x-rgb":["rgb"],"image/x-tga":["tga"],"image/x-xbitmap":["xbm"],"image/x-xpixmap":["xpm"],"image/x-xwindowdump":["xwd"],"message/vnd.wfa.wsc":["wsc"],"model/vnd.collada+xml":["dae"],"model/vnd.dwf":["dwf"],"model/vnd.gdl":["gdl"],"model/vnd.gtw":["gtw"],"model/vnd.mts":["mts"],"model/vnd.opengex":["ogex"],"model/vnd.parasolid.transmit.binary":["x_b"],"model/vnd.parasolid.transmit.text":["x_t"],"model/vnd.sap.vds":["vds"],"model/vnd.usdz+zip":["usdz"],"model/vnd.valve.source.compiled-map":["bsp"],"model/vnd.vtu":["vtu"],"text/prs.lines.tag":["dsc"],"text/vnd.curl":["curl"],"text/vnd.curl.dcurl":["dcurl"],"text/vnd.curl.mcurl":["mcurl"],"text/vnd.curl.scurl":["scurl"],"text/vnd.dvb.subtitle":["sub"],"text/vnd.fly":["fly"],"text/vnd.fmi.flexstor":["flx"],"text/vnd.graphviz":["gv"],"text/vnd.in3d.3dml":["3dml"],"text/vnd.in3d.spot":["spot"],"text/vnd.sun.j2me.app-descriptor":["jad"],"text/vnd.wap.wml":["wml"],"text/vnd.wap.wmlscript":["wmls"],"text/x-asm":["s","asm"],"text/x-c":["c","cc","cxx","cpp","h","hh","dic"],"text/x-component":["htc"],"text/x-fortran":["f","for","f77","f90"],"text/x-handlebars-template":["hbs"],"text/x-java-source":["java"],"text/x-lua":["lua"],"text/x-markdown":["mkd"],"text/x-nfo":["nfo"],"text/x-opml":["opml"],"text/x-org":["*org"],"text/x-pascal":["p","pas"],"text/x-processing":["pde"],"text/x-sass":["sass"],"text/x-scss":["scss"],"text/x-setext":["etx"],"text/x-sfv":["sfv"],"text/x-suse-ymp":["ymp"],"text/x-uuencode":["uu"],"text/x-vcalendar":["vcs"],"text/x-vcard":["vcf"],"video/vnd.dece.hd":["uvh","uvvh"],"video/vnd.dece.mobile":["uvm","uvvm"],"video/vnd.dece.pd":["uvp","uvvp"],"video/vnd.dece.sd":["uvs","uvvs"],"video/vnd.dece.video":["uvv","uvvv"],"video/vnd.dvb.file":["dvb"],"video/vnd.fvt":["fvt"],"video/vnd.mpegurl":["mxu","m4u"],"video/vnd.ms-playready.media.pyv":["pyv"],"video/vnd.uvvu.mp4":["uvu","uvvu"],"video/vnd.vivo":["viv"],"video/x-f4v":["f4v"],"video/x-fli":["fli"],"video/x-flv":["flv"],"video/x-m4v":["m4v"],"video/x-matroska":["mkv","mk3d","mks"],"video/x-mng":["mng"],"video/x-ms-asf":["asf","asx"],"video/x-ms-vob":["vob"],"video/x-ms-wm":["wm"],"video/x-ms-wmv":["wmv"],"video/x-ms-wmx":["wmx"],"video/x-ms-wvx":["wvx"],"video/x-msvideo":["avi"],"video/x-sgi-movie":["movie"],"video/x-smv":["smv"],"x-conference/x-cooltalk":["ice"]});function ai(t){return"object"===function(t){return Object.prototype.toString.call(t).slice(8,-1).toLowerCase()}(t)}const ei=typeof window<"u",ni=ei?navigator.userAgent.toLowerCase():"",oi=/iPhone|phone|android|iPod|pad|iPad/i;function pi({type:t,id:i,resource:a}){return new Promise(((e,n)=>{if(ei&&!document.querySelector(i))switch(t){case"js":{const t=document.createElement("script");t.async=!0,t.src=a,o(t),document.head.appendChild(t);break}case"css":{const t=document.createElement("link");t.id=i,t.rel="stylesheet",t.href=a,o(t),document.head.appendChild(t);break}case"style":{const t=document.createElement("style");t.id=i,o(t),document.head.appendChild(t),t.appendChild(document.createTextNode(a));break}}else n();function o(t){t.addEventListener("load",e),t.addEventListener("error",n),t.addEventListener("abort",n)}}))}function ci({mode:t,dataURI:i}){return"round"===t?"image/png":function(t){return t.startsWith("data")&&t.includes("base64")?t.split(",")[0].replace(/data:(.*);base64/,"$1"):ii.getType(t)||""}(i)}function si(t){const i=document.createElement("canvas"),a=i.getContext("2d");if(!a)return i;const{width:e,height:n}=t;return i.width=e,i.height=n,a.imageSmoothingEnabled=!0,a.drawImage(t,0,0,e,n),a.globalCompositeOperation="destination-in",a.beginPath(),a.arc(e/2,n/2,Math.min(e,n)/2,0,2*Math.PI,!0),a.fill(),i}function ri(t,i={}){if(!ai(t))return i;const{mode:a,width:e,height:n}=t;switch(a){case"fixedSize":case"round":i.width=e,i.height=n}return i}oi.test(ni),oi.test(ni),/Android/i.test(ni),/iPhone|iPod|iPad|iOS/i.test(ni),/uni-app|html5plus/.test(ni),/MicroMessenger/i.test(ni),/\sQQ|mqqbrowser|qzone|qqbrowser/i.test(ni),/mqqbrowser|qqbrowser/i.test(ni),/qzone\/.*_qz_([\d.]+)/i.test(ni),/(weibo).*weibo__([\d.]+)/i.test(ni),/(baiduboxapp)\/([\d.]+)/i.test(ni);const li={img:{type:String,required:!0,default:""},boxStyle:{type:Object,required:!1,default:()=>({})},options:{type:Object,required:!1,default:()=>({})},presetMode:{type:Object,required:!1,default:()=>({})}};let di;pi({type:"style",id:"cropperjs",resource:'/*!\n * Cropper.js v1.5.13\n * https://fengyuanchen.github.io/cropperjs\n *\n * Copyright 2015-present Chen Fengyuan\n * Released under the MIT license\n *\n * Date: 2022-11-20T05:30:43.444Z\n */.cropper-container{direction:ltr;font-size:0;line-height:0;position:relative;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.cropper-container img{-webkit-backface-visibility:hidden;backface-visibility:hidden;display:block;height:100%;image-orientation:0deg;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;width:100%}.cropper-wrap-box,.cropper-canvas,.cropper-drag-box,.cropper-crop-box,.cropper-modal{bottom:0;left:0;position:absolute;right:0;top:0}.cropper-wrap-box,.cropper-canvas{overflow:hidden}.cropper-drag-box{background-color:#fff;opacity:0}.cropper-modal{background-color:#000;opacity:.5}.cropper-view-box{display:block;height:100%;outline:1px solid #39f;outline-color:#3399ffbf;overflow:hidden;width:100%}.cropper-dashed{border:0 dashed #eee;display:block;opacity:.5;position:absolute}.cropper-dashed.dashed-h{border-bottom-width:1px;border-top-width:1px;height:calc(100% / 3);left:0;top:calc(100% / 3);width:100%}.cropper-dashed.dashed-v{border-left-width:1px;border-right-width:1px;height:100%;left:calc(100% / 3);top:0;width:calc(100% / 3)}.cropper-center{display:block;height:0;left:50%;opacity:.75;position:absolute;top:50%;width:0}.cropper-center:before,.cropper-center:after{background-color:#eee;content:" ";display:block;position:absolute}.cropper-center:before{height:1px;left:-3px;top:0;width:7px}.cropper-center:after{height:7px;left:0;top:-3px;width:1px}.cropper-face,.cropper-line,.cropper-point{display:block;height:100%;opacity:.1;position:absolute;width:100%}.cropper-face{background-color:#fff;left:0;top:0}.cropper-line{background-color:#39f}.cropper-line.line-e{cursor:ew-resize;right:-3px;top:0;width:5px}.cropper-line.line-n{cursor:ns-resize;height:5px;left:0;top:-3px}.cropper-line.line-w{cursor:ew-resize;left:-3px;top:0;width:5px}.cropper-line.line-s{bottom:-3px;cursor:ns-resize;height:5px;left:0}.cropper-point{background-color:#39f;height:5px;opacity:.75;width:5px}.cropper-point.point-e{cursor:ew-resize;margin-top:-3px;right:-3px;top:50%}.cropper-point.point-n{cursor:ns-resize;left:50%;margin-left:-3px;top:-3px}.cropper-point.point-w{cursor:ew-resize;left:-3px;margin-top:-3px;top:50%}.cropper-point.point-s{bottom:-3px;cursor:s-resize;left:50%;margin-left:-3px}.cropper-point.point-ne{cursor:nesw-resize;right:-3px;top:-3px}.cropper-point.point-nw{cursor:nwse-resize;left:-3px;top:-3px}.cropper-point.point-sw{bottom:-3px;cursor:nesw-resize;left:-3px}.cropper-point.point-se{bottom:-3px;cursor:nwse-resize;height:20px;opacity:1;right:-3px;width:20px}@media (min-width: 768px){.cropper-point.point-se{height:15px;width:15px}}@media (min-width: 992px){.cropper-point.point-se{height:10px;width:10px}}@media (min-width: 1200px){.cropper-point.point-se{height:5px;opacity:.75;width:5px}}.cropper-point.point-se:before{background-color:#39f;bottom:-50%;content:" ";display:block;height:200%;opacity:0;position:absolute;right:-50%;width:200%}.cropper-invisible{opacity:0}.cropper-bg{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC)}.cropper-hide{display:block;height:0;position:absolute;width:0}.cropper-hidden{display:none!important}.cropper-move{cursor:move}.cropper-crop{cursor:crosshair}.cropper-disabled .cropper-drag-box,.cropper-disabled .cropper-face,.cropper-disabled .cropper-line,.cropper-disabled .cropper-point{cursor:not-allowed}\n'}).catch((t=>{})),pi({type:"style",id:"vue-picture-cropper",resource:".vue--picture-cropper__wrap{width:100%;height:100%;margin:0}.vue--picture-cropper__img{display:block;width:auto;height:auto;max-width:100%;max-height:100%}.vue--picture-cropper__wrap-round .cropper-view-box,.vue--picture-cropper__wrap-round .cropper-face{border-radius:50%}\n"}).catch((t=>{})),t("a",di),t("S",a({name:"VuePictureCropper",props:li,setup(i){const a=e(""),s=e("");async function r(){await c(),s.value=function(t=10){const i="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";let a="",e=t;const n=i.length;for(;e--;)a+=i[Math.random()*n|0];return a}();const e=window.setInterval((()=>{const n=s.value?document.querySelector(`#vpc-img-${s.value}`):document.querySelector(".vue--picture-cropper__img");if(n)try{t("a",di=new Jt(n,i.options)),window.clearInterval(e),l(),a.value=ci({mode:i.presetMode.mode,dataURI:i.img}),n.addEventListener("ready",(()=>{!function(){if(!ai(i.presetMode))return;const{mode:t,width:a,height:e}=i.presetMode;switch(t){case"fixedSize":case"round":di.setCropBoxData({width:a,height:e})}}()}))}catch(o){}}),10)}function l(){di.getDataURL=d,di.getBlob=m,di.getFile=h}function d(t={}){t=ri(i.presetMode,t);try{let e=di.getCroppedCanvas(t);return"round"===i.presetMode.mode&&(e=si(e)),e.toDataURL(a.value)}catch{return""}}async function m(t={}){return t=ri(i.presetMode,t),new Promise((e=>{try{let n=di.getCroppedCanvas(t);"round"===i.presetMode.mode&&(n=si(n)),n.toBlob((t=>{e(t)}),a.value)}catch{e(null)}}))}async function h(t={}){const{fileName:i}=t,e=a.value.replace(/image\//,""),n=i?`${i}.${e}`:`cropped-${Date.now()}.${e}`,o=await m(t);return o?new File([o],n,{type:a.value}):null}return n((()=>i.img),(async()=>{if(di)try{di.replace(i.img),a.value=ci({mode:i.presetMode.mode,dataURI:i.img}),l()}catch(t){}else await r()}),{immediate:!0}),o((()=>{di&&(di.destroy(),t("a",di=null))})),()=>p("div",{id:`vpc-wrap-${s.value}`,class:"vue--picture-cropper__wrap "+("round"===i.presetMode.mode?"vue--picture-cropper__wrap-round":""),style:i.boxStyle},[p("img",{id:`vpc-img-${s.value}`,class:"vue--picture-cropper__img",src:i.img},null)])}}))}}}));
