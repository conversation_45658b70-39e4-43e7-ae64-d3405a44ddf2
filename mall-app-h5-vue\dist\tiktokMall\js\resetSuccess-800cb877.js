import{_ as s,u as e,Y as t,r as a,q as c,av as r,c as l,e as u,w as o,a as i,b as d,t as n,o as p,f as m}from"./index-3d21abf8.js";import{B as f}from"./index-2406f514.js";import{s as v}from"./success-4eb68329.js";import"./use-route-cd41a893.js";const x={class:"resetSuccess"},y={class:"content"},_={class:"imgBox"},b=["src"],$={class:"styleFont"},h={class:"text-grey mt-2"},k={class:"text-grey mt-11"},S=s({__name:"resetSuccess",setup(s){const{t:S}=e(),g=t(),j=a("");c((()=>{let s=g.query.type;q(s)}));const q=s=>{j.value=S(1==s?"重置手机号":2==s?"重置邮箱":3==s?"重置谷歌验证":"重置资金密码")};return(s,e)=>{const t=r("fx-header"),a=f;return p(),l("div",x,[u(t,{back:!1,onBack:e[0]||(e[0]=e=>s.$router.push("/my/index"))},{title:o((()=>[m(n(j.value),1)])),_:1}),i("div",y,[i("div",_,[i("img",{src:d(v),alt:""},null,8,b)]),i("div",$,n(s.$t("submitSuccess")),1),i("div",h,n(s.$t("dataUploadedSuccessfully"))+"!",1),i("div",k,n(s.$t("moderatedTips")),1),u(a,{class:"w-full btn-content",onClick:e[1]||(e[1]=e=>s.$router.push("/quotes/index")),type:"primary"},{default:o((()=>[m(n(s.$t("back")),1)])),_:1})])])}}},[["__scopeId","data-v-c1df3d83"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/resetVerify/resetSuccess.vue"]]);export{S as default};
