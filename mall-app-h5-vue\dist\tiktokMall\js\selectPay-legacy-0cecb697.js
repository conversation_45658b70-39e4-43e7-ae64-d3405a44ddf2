System.register(["./index-legacy-46a00900.js","./index-legacy-f9c0699e.js","./index-legacy-f9c0699e2.js","./index-legacy-ff56f089.js","./index-legacy-0ade4760.js","./index-legacy-df3e01aa.js","./use-route-legacy-be86ac1c.js","./use-id-legacy-df76950f.js"],(function(e,t){"use strict";var n,a,i,o,r,l,c,d,s,v,x,h,f,u,p,g,b,y,m,k,w,z,_,C,j,S,T,$,A,E,I,O,P,V,F,B,L,M,R,Y,H,D,U,X,q=document.createElement("style");return q.textContent=":root{--van-index-bar-sidebar-z-index: 2;--van-index-bar-index-font-size: var(--van-font-size-xs);--van-index-bar-index-line-height: var(--van-line-height-xs);--van-index-bar-index-active-color: var(--van-danger-color) }.van-index-bar__sidebar{position:fixed;top:50%;right:0;z-index:var(--van-index-bar-sidebar-z-index);display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;flex-direction:column;text-align:center;-webkit-transform:translateY(-50%);transform:translateY(-50%);cursor:pointer;-webkit-user-select:none;-moz-user-select:none;user-select:none}.van-index-bar__index{padding:0 var(--van-padding-xs) 0 var(--van-padding-md);font-weight:var(--van-font-weight-bold);font-size:var(--van-index-bar-index-font-size);line-height:var(--van-index-bar-index-line-height)}.van-index-bar__index--active{color:var(--van-index-bar-index-active-color)}:root{--van-index-anchor-z-index: 1;--van-index-anchor-padding: 0 var(--van-padding-md);--van-index-anchor-text-color: var(--van-text-color);--van-index-anchor-font-weight: var(--van-font-weight-bold);--van-index-anchor-font-size: var(--van-font-size-md);--van-index-anchor-line-height: 32px;--van-index-anchor-background-color: transparent;--van-index-anchor-sticky-text-color: var(--van-danger-color);--van-index-anchor-sticky-background-color: var(--van-background-color-light) }.van-index-anchor{z-index:var(--van-index-anchor-z-index);box-sizing:border-box;padding:var(--van-index-anchor-padding);color:var(--van-index-anchor-text-color);font-weight:var(--van-index-anchor-font-weight);font-size:var(--van-index-anchor-font-size);line-height:var(--van-index-anchor-line-height);background:var(--van-index-anchor-background-color)}.van-index-anchor--sticky{position:fixed;top:0;right:0;left:0;color:var(--van-index-anchor-sticky-text-color);background:var(--van-index-anchor-sticky-background-color)}.selectPay .index-anchor[data-v-4a9a5654]{background:#F5F5F5}[data-v-4a9a5654] .van-index-bar__index{color:#2555f8;font-size:14px;margin-top:6px}.item-cell[data-v-4a9a5654]{border-bottom:1px solid #EAEBEE}\n",document.head.appendChild(q),{setters:[e=>{n=e.P,a=e.ai,i=e.S,o=e.bB,r=e.d,l=e.r,c=e.aC,d=e.aB,s=e.aH,v=e.m,x=e.ad,h=e.ac,f=e.q,u=e.g,p=e.a9,g=e.e,b=e.M,y=e.s,m=e.aO,k=e.aD,w=e.aT,z=e.a0,_=e.$,C=e.V,j=e.p,S=e.a6,T=e.Q,$=e.a$,A=e.b1,E=e.X,I=e._,O=e.Y,P=e.l,V=e.av,F=e.c,B=e.w,L=e.b,M=e.aV,R=e.o,Y=e.f,H=e.F,D=e.y,U=e.a},()=>{},()=>{},e=>{X=e.S},()=>{},()=>{},()=>{},()=>{}],execute:function(){const[t,q]=n("index-bar"),Q={sticky:a,zIndex:i,teleport:[String,Object],highlightColor:String,stickyOffsetTop:o(0),indexList:{type:Array,default:function(){const e="A".charCodeAt(0);return Array(26).fill("").map(((t,n)=>String.fromCharCode(e+n)))}}},G=Symbol(t);var J=r({name:t,props:Q,emits:["select","change"],setup(e,{emit:t,slots:n}){const a=l(),i=l(),o=l(""),r=c(),j=d(a),{children:S,linkChildren:T}=s(G);let $;T({props:e});const A=v((()=>{if(x(e.zIndex))return{zIndex:+e.zIndex+1}})),E=v((()=>{if(e.highlightColor)return{color:e.highlightColor}})),I=(t,n)=>{for(let a=S.length-1;a>=0;a--){const i=a>0?n[a-1].height:0;if(t+(e.sticky?i+e.stickyOffsetTop:0)>=n[a].top)return a}return-1},O=e=>S.find((t=>String(t.index)===e)),P=()=>{if(m(a))return;const{sticky:t,indexList:n}=e,i=k(j.value),r=w(j),l=S.map((e=>e.getRect(j.value,r)));let c=-1;if($){const e=O($);if(e){const t=e.getRect(j.value,r);c=I(t.top,l)}}else c=I(i,l);o.value=n[c],t&&S.forEach(((t,n)=>{const{state:a,$el:o}=t;if(n===c||n===c-1){const e=o.getBoundingClientRect();a.left=e.left,a.width=e.width}else a.left=null,a.width=null;if(n===c)a.active=!0,a.top=Math.max(e.stickyOffsetTop,l[n].top-i)+r.top;else if(n===c-1&&""===$){const e=l[c].top-i;a.active=e>0,a.top=e+r.top-l[n].height}else a.active=!1})),$=""},V=()=>{y(P)};h("scroll",P,{target:j,passive:!0}),f(V),u((()=>e.indexList),V),u(o,(e=>{e&&t("change",e)}));const F=n=>{$=String(n);const a=O($);if(a){const n=k(j.value),i=w(j),{offsetHeight:o}=document.documentElement;if(a.$el.scrollIntoView(),n===o-i.height)return void P();e.sticky&&e.stickyOffsetTop&&z(_()-e.stickyOffsetTop),t("select",a.index)}},B=e=>{const{index:t}=e.dataset;t&&F(t)},L=e=>{B(e.target)};let M;const R=()=>g("div",{ref:i,class:q("sidebar"),style:A.value,onClick:L,onTouchstartPassive:r.start},[e.indexList.map((e=>{const t=e===o.value;return g("span",{class:q("index",{active:t}),style:t?E.value:void 0,"data-index":e},[e])}))]);return p({scrollTo:F}),h("touchmove",(e=>{if(r.move(e),r.isVertical()){C(e);const{clientX:t,clientY:n}=e.touches[0],a=document.elementFromPoint(t,n);if(a){const{index:e}=a.dataset;e&&M!==e&&(M=e,B(a))}}}),{target:i}),()=>{var t;return g("div",{ref:a,class:q()},[e.teleport?g(b,{to:e.teleport},{default:()=>[R()]}):R(),null==(t=n.default)?void 0:t.call(n)])}}});const[K,N]=n("index-anchor");var W=r({name:K,props:{index:i},setup(e,{slots:t}){const n=j({top:0,left:null,rect:{top:0,height:0},width:null,active:!1}),a=l(),{parent:i}=S(G);if(!i)return;const o=()=>n.active&&i.props.sticky,r=v((()=>{const{zIndex:e,highlightColor:t}=i.props;if(o())return T($(e),{left:n.left?`${n.left}px`:void 0,width:n.width?`${n.width}px`:void 0,transform:n.top?`translate3d(0, ${n.top}px, 0)`:void 0,color:t})}));return p({state:n,getRect:(e,t)=>{const i=w(a);return n.rect.height=i.height,e===window||e===document.body?n.rect.top=i.top+_():n.rect.top=i.top+k(e)-t.top,n.rect}}),()=>{const i=o();return g("div",{ref:a,style:{height:i?`${n.rect.height}px`:void 0}},[g("div",{style:r.value,class:[N({sticky:i}),{[A]:i}]},[t.default?t.default():e.index])])}}});const Z=E(W),ee=E(J),te={class:"selectPay pb-10"},ne={__name:"selectPay",setup(e){O();const t=P();let n=l("");return(e,a)=>{const i=V("fx-header"),o=X,r=Z,l=ee;return R(),F("div",te,[g(i,null,{title:B((()=>[Y("全部收款方式")])),_:1}),g(o,{modelValue:L(n),"onUpdate:modelValue":a[0]||(a[0]=e=>M(n)?n.value=e:n=e),placeholder:"请输入搜索关键词"},null,8,["modelValue"]),g(l,null,{default:B((()=>[g(r,{class:"index-anchor",index:"A"}),(R(),F(H,null,D(10,((e,n)=>U("div",{onClick:a[1]||(a[1]=e=>{t.push("add")}),class:"item-cell ml-4 py-4",key:n},"Asia Hawala "))),64))])),_:1})])}}};e("default",I(ne,[["__scopeId","data-v-4a9a5654"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/payMentMethod/selectPay.vue"]]))}}}));
