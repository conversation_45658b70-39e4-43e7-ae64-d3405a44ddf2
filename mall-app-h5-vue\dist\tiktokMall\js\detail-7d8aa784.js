import{_ as t,i as e,j as n,l as r,Y as o,u as a,r as i,g as s,q as u,T as l,m as c,s as d,av as f,bn as h,c as g,e as p,w as m,b as v,x as w,a as y,aV as b,t as E,f as C,c3 as A,ak as B,o as x,n as T,D as P,E as I}from"./index-3d21abf8.js";import{P as k}from"./index-573e22f7.js";/* empty css              *//* empty css               */import"./index-7d1632e5.js";/* empty css              *//* empty css               */import{U as N}from"./index-1f1846c6.js";import{B as M}from"./index-2406f514.js";import{E as R,i as _}from"./index-402f5114.js";import{a as U,t as L,g as S,h as V,i as j,j as D}from"./exchange.api-23bc91cd.js";import{u as H}from"./upload.api-28f256be.js";import{E as z}from"./index-9c8e9dca.js";import{u as F}from"./index-54dce367.js";import{F as Y}from"./index-8c1841f6.js";import"./index-0d6a7179.js";import"./index-a439655d.js";import"./use-route-cd41a893.js";import"./index-7dfcb82a.js";import"./use-id-a0619e01.js";var J={},K={},q={};let O;const Q=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];q.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return 4*t+17},q.getSymbolTotalCodewords=function(t){return Q[t]},q.getBCHDigit=function(t){let e=0;for(;0!==t;)e++,t>>>=1;return e},q.setToSJISFunction=function(t){if("function"!=typeof t)throw new Error('"toSJISFunc" is not a valid function.');O=t},q.isKanjiModeEnabled=function(){return void 0!==O},q.toSJIS=function(t){return O(t)};var $,Z={};function G(){this.buffer=[],this.length=0}($=Z).L={bit:1},$.M={bit:0},$.Q={bit:3},$.H={bit:2},$.isValid=function(t){return t&&void 0!==t.bit&&t.bit>=0&&t.bit<4},$.from=function(t,e){if($.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw new Error("Param is not a string");switch(t.toLowerCase()){case"l":case"low":return $.L;case"m":case"medium":return $.M;case"q":case"quartile":return $.Q;case"h":case"high":return $.H;default:throw new Error("Unknown EC Level: "+t)}}(t)}catch(n){return e}},G.prototype={get:function(t){const e=Math.floor(t/8);return 1==(this.buffer[e]>>>7-t%8&1)},put:function(t,e){for(let n=0;n<e;n++)this.putBit(1==(t>>>e-n-1&1))},getLengthInBits:function(){return this.length},putBit:function(t){const e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}};var X=G;function W(t){if(!t||t<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=new Uint8Array(t*t),this.reservedBit=new Uint8Array(t*t)}W.prototype.set=function(t,e,n,r){const o=t*this.size+e;this.data[o]=n,r&&(this.reservedBit[o]=!0)},W.prototype.get=function(t,e){return this.data[t*this.size+e]},W.prototype.xor=function(t,e,n){this.data[t*this.size+e]^=n},W.prototype.isReserved=function(t,e){return this.reservedBit[t*this.size+e]};var tt=W,et={};!function(t){const e=q.getSymbolSize;t.getRowColCoords=function(t){if(1===t)return[];const n=Math.floor(t/7)+2,r=e(t),o=145===r?26:2*Math.ceil((r-13)/(2*n-2)),a=[r-7];for(let e=1;e<n-1;e++)a[e]=a[e-1]-o;return a.push(6),a.reverse()},t.getPositions=function(e){const n=[],r=t.getRowColCoords(e),o=r.length;for(let t=0;t<o;t++)for(let e=0;e<o;e++)0===t&&0===e||0===t&&e===o-1||t===o-1&&0===e||n.push([r[t],r[e]]);return n}}(et);var nt={};const rt=q.getSymbolSize;nt.getPositions=function(t){const e=rt(t);return[[0,0],[e-7,0],[0,e-7]]};var ot={};!function(t){t.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const e=3,n=3,r=40,o=10;function a(e,n,r){switch(e){case t.Patterns.PATTERN000:return(n+r)%2==0;case t.Patterns.PATTERN001:return n%2==0;case t.Patterns.PATTERN010:return r%3==0;case t.Patterns.PATTERN011:return(n+r)%3==0;case t.Patterns.PATTERN100:return(Math.floor(n/2)+Math.floor(r/3))%2==0;case t.Patterns.PATTERN101:return n*r%2+n*r%3==0;case t.Patterns.PATTERN110:return(n*r%2+n*r%3)%2==0;case t.Patterns.PATTERN111:return(n*r%3+(n+r)%2)%2==0;default:throw new Error("bad maskPattern:"+e)}}t.isValid=function(t){return null!=t&&""!==t&&!isNaN(t)&&t>=0&&t<=7},t.from=function(e){return t.isValid(e)?parseInt(e,10):void 0},t.getPenaltyN1=function(t){const n=t.size;let r=0,o=0,a=0,i=null,s=null;for(let u=0;u<n;u++){o=a=0,i=s=null;for(let l=0;l<n;l++){let n=t.get(u,l);n===i?o++:(o>=5&&(r+=e+(o-5)),i=n,o=1),n=t.get(l,u),n===s?a++:(a>=5&&(r+=e+(a-5)),s=n,a=1)}o>=5&&(r+=e+(o-5)),a>=5&&(r+=e+(a-5))}return r},t.getPenaltyN2=function(t){const e=t.size;let r=0;for(let n=0;n<e-1;n++)for(let o=0;o<e-1;o++){const e=t.get(n,o)+t.get(n,o+1)+t.get(n+1,o)+t.get(n+1,o+1);4!==e&&0!==e||r++}return r*n},t.getPenaltyN3=function(t){const e=t.size;let n=0,o=0,a=0;for(let r=0;r<e;r++){o=a=0;for(let i=0;i<e;i++)o=o<<1&2047|t.get(r,i),i>=10&&(1488===o||93===o)&&n++,a=a<<1&2047|t.get(i,r),i>=10&&(1488===a||93===a)&&n++}return n*r},t.getPenaltyN4=function(t){let e=0;const n=t.data.length;for(let r=0;r<n;r++)e+=t.data[r];return Math.abs(Math.ceil(100*e/n/5)-10)*o},t.applyMask=function(t,e){const n=e.size;for(let r=0;r<n;r++)for(let o=0;o<n;o++)e.isReserved(o,r)||e.xor(o,r,a(t,o,r))},t.getBestMask=function(e,n){const r=Object.keys(t.Patterns).length;let o=0,a=1/0;for(let i=0;i<r;i++){n(i),t.applyMask(i,e);const r=t.getPenaltyN1(e)+t.getPenaltyN2(e)+t.getPenaltyN3(e)+t.getPenaltyN4(e);t.applyMask(i,e),r<a&&(a=r,o=i)}return o}}(ot);var at={};const it=Z,st=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],ut=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];at.getBlocksCount=function(t,e){switch(e){case it.L:return st[4*(t-1)+0];case it.M:return st[4*(t-1)+1];case it.Q:return st[4*(t-1)+2];case it.H:return st[4*(t-1)+3];default:return}},at.getTotalCodewordsCount=function(t,e){switch(e){case it.L:return ut[4*(t-1)+0];case it.M:return ut[4*(t-1)+1];case it.Q:return ut[4*(t-1)+2];case it.H:return ut[4*(t-1)+3];default:return}};var lt={},ct={};const dt=new Uint8Array(512),ft=new Uint8Array(256);!function(){let t=1;for(let e=0;e<255;e++)dt[e]=t,ft[t]=e,t<<=1,256&t&&(t^=285);for(let e=255;e<512;e++)dt[e]=dt[e-255]}(),ct.log=function(t){if(t<1)throw new Error("log("+t+")");return ft[t]},ct.exp=function(t){return dt[t]},ct.mul=function(t,e){return 0===t||0===e?0:dt[ft[t]+ft[e]]},function(t){const e=ct;t.mul=function(t,n){const r=new Uint8Array(t.length+n.length-1);for(let o=0;o<t.length;o++)for(let a=0;a<n.length;a++)r[o+a]^=e.mul(t[o],n[a]);return r},t.mod=function(t,n){let r=new Uint8Array(t);for(;r.length-n.length>=0;){const t=r[0];for(let a=0;a<n.length;a++)r[a]^=e.mul(n[a],t);let o=0;for(;o<r.length&&0===r[o];)o++;r=r.slice(o)}return r},t.generateECPolynomial=function(n){let r=new Uint8Array([1]);for(let o=0;o<n;o++)r=t.mul(r,new Uint8Array([1,e.exp(o)]));return r}}(lt);const ht=lt;function gt(t){this.genPoly=void 0,this.degree=t,this.degree&&this.initialize(this.degree)}gt.prototype.initialize=function(t){this.degree=t,this.genPoly=ht.generateECPolynomial(this.degree)},gt.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");const e=new Uint8Array(t.length+this.degree);e.set(t);const n=ht.mod(e,this.genPoly),r=this.degree-n.length;if(r>0){const t=new Uint8Array(this.degree);return t.set(n,r),t}return n};var pt=gt,mt={},vt={},wt={isValid:function(t){return!isNaN(t)&&t>=1&&t<=40}},yt={};const bt="[0-9]+";let Et="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";Et=Et.replace(/u/g,"\\u");const Ct="(?:(?![A-Z0-9 $%*+\\-./:]|"+Et+")(?:.|[\r\n]))+";yt.KANJI=new RegExp(Et,"g"),yt.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),yt.BYTE=new RegExp(Ct,"g"),yt.NUMERIC=new RegExp(bt,"g"),yt.ALPHANUMERIC=new RegExp("[A-Z $%*+\\-./:]+","g");const At=new RegExp("^"+Et+"$"),Bt=new RegExp("^"+bt+"$"),xt=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");yt.testKanji=function(t){return At.test(t)},yt.testNumeric=function(t){return Bt.test(t)},yt.testAlphanumeric=function(t){return xt.test(t)},function(t){const e=wt,n=yt;t.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},t.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},t.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},t.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},t.MIXED={bit:-1},t.getCharCountIndicator=function(t,n){if(!t.ccBits)throw new Error("Invalid mode: "+t);if(!e.isValid(n))throw new Error("Invalid version: "+n);return n>=1&&n<10?t.ccBits[0]:n<27?t.ccBits[1]:t.ccBits[2]},t.getBestModeForData=function(e){return n.testNumeric(e)?t.NUMERIC:n.testAlphanumeric(e)?t.ALPHANUMERIC:n.testKanji(e)?t.KANJI:t.BYTE},t.toString=function(t){if(t&&t.id)return t.id;throw new Error("Invalid mode")},t.isValid=function(t){return t&&t.bit&&t.ccBits},t.from=function(e,n){if(t.isValid(e))return e;try{return function(e){if("string"!=typeof e)throw new Error("Param is not a string");switch(e.toLowerCase()){case"numeric":return t.NUMERIC;case"alphanumeric":return t.ALPHANUMERIC;case"kanji":return t.KANJI;case"byte":return t.BYTE;default:throw new Error("Unknown mode: "+e)}}(e)}catch(r){return n}}}(vt),function(t){const e=q,n=at,r=Z,o=vt,a=wt,i=e.getBCHDigit(7973);function s(t,e){return o.getCharCountIndicator(t,e)+4}function u(t,e){let n=0;return t.forEach((function(t){const r=s(t.mode,e);n+=r+t.getBitsLength()})),n}t.from=function(t,e){return a.isValid(t)?parseInt(t,10):e},t.getCapacity=function(t,r,i){if(!a.isValid(t))throw new Error("Invalid QR Code version");void 0===i&&(i=o.BYTE);const u=8*(e.getSymbolTotalCodewords(t)-n.getTotalCodewordsCount(t,r));if(i===o.MIXED)return u;const l=u-s(i,t);switch(i){case o.NUMERIC:return Math.floor(l/10*3);case o.ALPHANUMERIC:return Math.floor(l/11*2);case o.KANJI:return Math.floor(l/13);case o.BYTE:default:return Math.floor(l/8)}},t.getBestVersionForData=function(e,n){let a;const i=r.from(n,r.M);if(Array.isArray(e)){if(e.length>1)return function(e,n){for(let r=1;r<=40;r++)if(u(e,r)<=t.getCapacity(r,n,o.MIXED))return r}(e,i);if(0===e.length)return 1;a=e[0]}else a=e;return function(e,n,r){for(let o=1;o<=40;o++)if(n<=t.getCapacity(o,r,e))return o}(a.mode,a.getLength(),i)},t.getEncodedBits=function(t){if(!a.isValid(t)||t<7)throw new Error("Invalid QR Code version");let n=t<<12;for(;e.getBCHDigit(n)-i>=0;)n^=7973<<e.getBCHDigit(n)-i;return t<<12|n}}(mt);var Tt={};const Pt=q,It=Pt.getBCHDigit(1335);Tt.getEncodedBits=function(t,e){const n=t.bit<<3|e;let r=n<<10;for(;Pt.getBCHDigit(r)-It>=0;)r^=1335<<Pt.getBCHDigit(r)-It;return 21522^(n<<10|r)};var kt={};const Nt=vt;function Mt(t){this.mode=Nt.NUMERIC,this.data=t.toString()}Mt.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},Mt.prototype.getLength=function(){return this.data.length},Mt.prototype.getBitsLength=function(){return Mt.getBitsLength(this.data.length)},Mt.prototype.write=function(t){let e,n,r;for(e=0;e+3<=this.data.length;e+=3)n=this.data.substr(e,3),r=parseInt(n,10),t.put(r,10);const o=this.data.length-e;o>0&&(n=this.data.substr(e),r=parseInt(n,10),t.put(r,3*o+1))};var Rt=Mt;const _t=vt,Ut=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function Lt(t){this.mode=_t.ALPHANUMERIC,this.data=t}Lt.getBitsLength=function(t){return 11*Math.floor(t/2)+t%2*6},Lt.prototype.getLength=function(){return this.data.length},Lt.prototype.getBitsLength=function(){return Lt.getBitsLength(this.data.length)},Lt.prototype.write=function(t){let e;for(e=0;e+2<=this.data.length;e+=2){let n=45*Ut.indexOf(this.data[e]);n+=Ut.indexOf(this.data[e+1]),t.put(n,11)}this.data.length%2&&t.put(Ut.indexOf(this.data[e]),6)};var St=Lt;const Vt=function(t){for(var e=[],n=t.length,r=0;r<n;r++){var o=t.charCodeAt(r);if(o>=55296&&o<=56319&&n>r+1){var a=t.charCodeAt(r+1);a>=56320&&a<=57343&&(o=1024*(o-55296)+a-56320+65536,r+=1)}o<128?e.push(o):o<2048?(e.push(o>>6|192),e.push(63&o|128)):o<55296||o>=57344&&o<65536?(e.push(o>>12|224),e.push(o>>6&63|128),e.push(63&o|128)):o>=65536&&o<=1114111?(e.push(o>>18|240),e.push(o>>12&63|128),e.push(o>>6&63|128),e.push(63&o|128)):e.push(239,191,189)}return new Uint8Array(e).buffer},jt=vt;function Dt(t){this.mode=jt.BYTE,"string"==typeof t&&(t=Vt(t)),this.data=new Uint8Array(t)}Dt.getBitsLength=function(t){return 8*t},Dt.prototype.getLength=function(){return this.data.length},Dt.prototype.getBitsLength=function(){return Dt.getBitsLength(this.data.length)},Dt.prototype.write=function(t){for(let e=0,n=this.data.length;e<n;e++)t.put(this.data[e],8)};var Ht=Dt;const zt=vt,Ft=q;function Yt(t){this.mode=zt.KANJI,this.data=t}Yt.getBitsLength=function(t){return 13*t},Yt.prototype.getLength=function(){return this.data.length},Yt.prototype.getBitsLength=function(){return Yt.getBitsLength(this.data.length)},Yt.prototype.write=function(t){let e;for(e=0;e<this.data.length;e++){let n=Ft.toSJIS(this.data[e]);if(n>=33088&&n<=40956)n-=33088;else{if(!(n>=57408&&n<=60351))throw new Error("Invalid SJIS character: "+this.data[e]+"\nMake sure your charset is UTF-8");n-=49472}n=192*(n>>>8&255)+(255&n),t.put(n,13)}};var Jt,Kt=Yt,qt={exports:{}};qt.exports=Jt={single_source_shortest_paths:function(t,e,n){var r={},o={};o[e]=0;var a,i,s,u,l,c,d,f=Jt.PriorityQueue.make();for(f.push(e,0);!f.empty();)for(s in i=(a=f.pop()).value,u=a.cost,l=t[i]||{})l.hasOwnProperty(s)&&(c=u+l[s],d=o[s],(void 0===o[s]||d>c)&&(o[s]=c,f.push(s,c),r[s]=i));if(void 0!==n&&void 0===o[n]){var h=["Could not find a path from ",e," to ",n,"."].join("");throw new Error(h)}return r},extract_shortest_path_from_predecessor_list:function(t,e){for(var n=[],r=e;r;)n.push(r),t[r],r=t[r];return n.reverse(),n},find_path:function(t,e,n){var r=Jt.single_source_shortest_paths(t,e,n);return Jt.extract_shortest_path_from_predecessor_list(r,n)},PriorityQueue:{make:function(t){var e,n=Jt.PriorityQueue,r={};for(e in t=t||{},n)n.hasOwnProperty(e)&&(r[e]=n[e]);return r.queue=[],r.sorter=t.sorter||n.default_sorter,r},default_sorter:function(t,e){return t.cost-e.cost},push:function(t,e){var n={value:t,cost:e};this.queue.push(n),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}},function(t){const e=vt,n=Rt,r=St,o=Ht,a=Kt,i=yt,s=q,u=qt.exports;function l(t){return unescape(encodeURIComponent(t)).length}function c(t,e,n){const r=[];let o;for(;null!==(o=t.exec(n));)r.push({data:o[0],index:o.index,mode:e,length:o[0].length});return r}function d(t){const n=c(i.NUMERIC,e.NUMERIC,t),r=c(i.ALPHANUMERIC,e.ALPHANUMERIC,t);let o,a;s.isKanjiModeEnabled()?(o=c(i.BYTE,e.BYTE,t),a=c(i.KANJI,e.KANJI,t)):(o=c(i.BYTE_KANJI,e.BYTE,t),a=[]);return n.concat(r,o,a).sort((function(t,e){return t.index-e.index})).map((function(t){return{data:t.data,mode:t.mode,length:t.length}}))}function f(t,i){switch(i){case e.NUMERIC:return n.getBitsLength(t);case e.ALPHANUMERIC:return r.getBitsLength(t);case e.KANJI:return a.getBitsLength(t);case e.BYTE:return o.getBitsLength(t)}}function h(t,i){let u;const l=e.getBestModeForData(t);if(u=e.from(i,l),u!==e.BYTE&&u.bit<l.bit)throw new Error('"'+t+'" cannot be encoded with mode '+e.toString(u)+".\n Suggested mode is: "+e.toString(l));switch(u!==e.KANJI||s.isKanjiModeEnabled()||(u=e.BYTE),u){case e.NUMERIC:return new n(t);case e.ALPHANUMERIC:return new r(t);case e.KANJI:return new a(t);case e.BYTE:return new o(t)}}t.fromArray=function(t){return t.reduce((function(t,e){return"string"==typeof e?t.push(h(e,null)):e.data&&t.push(h(e.data,e.mode)),t}),[])},t.fromString=function(n,r){const o=function(t){const n=[];for(let r=0;r<t.length;r++){const o=t[r];switch(o.mode){case e.NUMERIC:n.push([o,{data:o.data,mode:e.ALPHANUMERIC,length:o.length},{data:o.data,mode:e.BYTE,length:o.length}]);break;case e.ALPHANUMERIC:n.push([o,{data:o.data,mode:e.BYTE,length:o.length}]);break;case e.KANJI:n.push([o,{data:o.data,mode:e.BYTE,length:l(o.data)}]);break;case e.BYTE:n.push([{data:o.data,mode:e.BYTE,length:l(o.data)}])}}return n}(d(n,s.isKanjiModeEnabled())),a=function(t,n){const r={},o={start:{}};let a=["start"];for(let i=0;i<t.length;i++){const s=t[i],u=[];for(let t=0;t<s.length;t++){const l=s[t],c=""+i+t;u.push(c),r[c]={node:l,lastCount:0},o[c]={};for(let t=0;t<a.length;t++){const i=a[t];r[i]&&r[i].node.mode===l.mode?(o[i][c]=f(r[i].lastCount+l.length,l.mode)-f(r[i].lastCount,l.mode),r[i].lastCount+=l.length):(r[i]&&(r[i].lastCount=l.length),o[i][c]=f(l.length,l.mode)+4+e.getCharCountIndicator(l.mode,n))}}a=u}for(let e=0;e<a.length;e++)o[a[e]].end=0;return{map:o,table:r}}(o,r),i=u.find_path(a.map,"start","end"),c=[];for(let t=1;t<i.length-1;t++)c.push(a.table[i[t]].node);return t.fromArray(function(t){return t.reduce((function(t,e){const n=t.length-1>=0?t[t.length-1]:null;return n&&n.mode===e.mode?(t[t.length-1].data+=e.data,t):(t.push(e),t)}),[])}(c))},t.rawSplit=function(e){return t.fromArray(d(e,s.isKanjiModeEnabled()))}}(kt);const Ot=q,Qt=Z,$t=X,Zt=tt,Gt=et,Xt=nt,Wt=ot,te=at,ee=pt,ne=mt,re=Tt,oe=vt,ae=kt;function ie(t,e,n){const r=t.size,o=re.getEncodedBits(e,n);let a,i;for(a=0;a<15;a++)i=1==(o>>a&1),a<6?t.set(a,8,i,!0):a<8?t.set(a+1,8,i,!0):t.set(r-15+a,8,i,!0),a<8?t.set(8,r-a-1,i,!0):a<9?t.set(8,15-a-1+1,i,!0):t.set(8,15-a-1,i,!0);t.set(r-8,8,1,!0)}function se(t,e,n){const r=new $t;n.forEach((function(e){r.put(e.mode.bit,4),r.put(e.getLength(),oe.getCharCountIndicator(e.mode,t)),e.write(r)}));const o=8*(Ot.getSymbolTotalCodewords(t)-te.getTotalCodewordsCount(t,e));for(r.getLengthInBits()+4<=o&&r.put(0,4);r.getLengthInBits()%8!=0;)r.putBit(0);const a=(o-r.getLengthInBits())/8;for(let i=0;i<a;i++)r.put(i%2?17:236,8);return function(t,e,n){const r=Ot.getSymbolTotalCodewords(e),o=te.getTotalCodewordsCount(e,n),a=r-o,i=te.getBlocksCount(e,n),s=r%i,u=i-s,l=Math.floor(r/i),c=Math.floor(a/i),d=c+1,f=l-c,h=new ee(f);let g=0;const p=new Array(i),m=new Array(i);let v=0;const w=new Uint8Array(t.buffer);for(let A=0;A<i;A++){const t=A<u?c:d;p[A]=w.slice(g,g+t),m[A]=h.encode(p[A]),g+=t,v=Math.max(v,t)}const y=new Uint8Array(r);let b,E,C=0;for(b=0;b<v;b++)for(E=0;E<i;E++)b<p[E].length&&(y[C++]=p[E][b]);for(b=0;b<f;b++)for(E=0;E<i;E++)y[C++]=m[E][b];return y}(r,t,e)}function ue(t,e,n,r){let o;if(Array.isArray(t))o=ae.fromArray(t);else{if("string"!=typeof t)throw new Error("Invalid data");{let r=e;if(!r){const e=ae.rawSplit(t);r=ne.getBestVersionForData(e,n)}o=ae.fromString(t,r||40)}}const a=ne.getBestVersionForData(o,n);if(!a)throw new Error("The amount of data is too big to be stored in a QR Code");if(e){if(e<a)throw new Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+a+".\n")}else e=a;const i=se(e,n,o),s=Ot.getSymbolSize(e),u=new Zt(s);return function(t,e){const n=t.size,r=Xt.getPositions(e);for(let o=0;o<r.length;o++){const e=r[o][0],a=r[o][1];for(let r=-1;r<=7;r++)if(!(e+r<=-1||n<=e+r))for(let o=-1;o<=7;o++)a+o<=-1||n<=a+o||(r>=0&&r<=6&&(0===o||6===o)||o>=0&&o<=6&&(0===r||6===r)||r>=2&&r<=4&&o>=2&&o<=4?t.set(e+r,a+o,!0,!0):t.set(e+r,a+o,!1,!0))}}(u,e),function(t){const e=t.size;for(let n=8;n<e-8;n++){const e=n%2==0;t.set(n,6,e,!0),t.set(6,n,e,!0)}}(u),function(t,e){const n=Gt.getPositions(e);for(let r=0;r<n.length;r++){const e=n[r][0],o=n[r][1];for(let n=-2;n<=2;n++)for(let r=-2;r<=2;r++)-2===n||2===n||-2===r||2===r||0===n&&0===r?t.set(e+n,o+r,!0,!0):t.set(e+n,o+r,!1,!0)}}(u,e),ie(u,n,0),e>=7&&function(t,e){const n=t.size,r=ne.getEncodedBits(e);let o,a,i;for(let s=0;s<18;s++)o=Math.floor(s/3),a=s%3+n-8-3,i=1==(r>>s&1),t.set(o,a,i,!0),t.set(a,o,i,!0)}(u,e),function(t,e){const n=t.size;let r=-1,o=n-1,a=7,i=0;for(let s=n-1;s>0;s-=2)for(6===s&&s--;;){for(let n=0;n<2;n++)if(!t.isReserved(o,s-n)){let r=!1;i<e.length&&(r=1==(e[i]>>>a&1)),t.set(o,s-n,r),a--,-1===a&&(i++,a=7)}if(o+=r,o<0||n<=o){o-=r,r=-r;break}}}(u,i),isNaN(r)&&(r=Wt.getBestMask(u,ie.bind(null,u,n))),Wt.applyMask(r,u),ie(u,n,r),{modules:u,version:e,errorCorrectionLevel:n,maskPattern:r,segments:o}}K.create=function(t,e){if(void 0===t||""===t)throw new Error("No input text");let n,r,o=Qt.M;return void 0!==e&&(o=Qt.from(e.errorCorrectionLevel,Qt.M),n=ne.from(e.version),r=Wt.from(e.maskPattern),e.toSJISFunc&&Ot.setToSJISFunction(e.toSJISFunc)),ue(t,n,o,r)};var le={},ce={};!function(t){function e(t){if("number"==typeof t&&(t=t.toString()),"string"!=typeof t)throw new Error("Color should be defined as hex string");let e=t.slice().replace("#","").split("");if(e.length<3||5===e.length||e.length>8)throw new Error("Invalid hex color: "+t);3!==e.length&&4!==e.length||(e=Array.prototype.concat.apply([],e.map((function(t){return[t,t]})))),6===e.length&&e.push("F","F");const n=parseInt(e.join(""),16);return{r:n>>24&255,g:n>>16&255,b:n>>8&255,a:255&n,hex:"#"+e.slice(0,6).join("")}}t.getOptions=function(t){t||(t={}),t.color||(t.color={});const n=void 0===t.margin||null===t.margin||t.margin<0?4:t.margin,r=t.width&&t.width>=21?t.width:void 0,o=t.scale||4;return{width:r,scale:r?4:o,margin:n,color:{dark:e(t.color.dark||"#000000ff"),light:e(t.color.light||"#ffffffff")},type:t.type,rendererOpts:t.rendererOpts||{}}},t.getScale=function(t,e){return e.width&&e.width>=t+2*e.margin?e.width/(t+2*e.margin):e.scale},t.getImageWidth=function(e,n){const r=t.getScale(e,n);return Math.floor((e+2*n.margin)*r)},t.qrToImageData=function(e,n,r){const o=n.modules.size,a=n.modules.data,i=t.getScale(o,r),s=Math.floor((o+2*r.margin)*i),u=r.margin*i,l=[r.color.light,r.color.dark];for(let t=0;t<s;t++)for(let n=0;n<s;n++){let c=4*(t*s+n),d=r.color.light;if(t>=u&&n>=u&&t<s-u&&n<s-u){d=l[a[Math.floor((t-u)/i)*o+Math.floor((n-u)/i)]?1:0]}e[c++]=d.r,e[c++]=d.g,e[c++]=d.b,e[c]=d.a}}}(ce),function(t){const e=ce;t.render=function(t,n,r){let o=r,a=n;void 0!==o||n&&n.getContext||(o=n,n=void 0),n||(a=function(){try{return document.createElement("canvas")}catch(t){throw new Error("You need to specify a canvas element")}}()),o=e.getOptions(o);const i=e.getImageWidth(t.modules.size,o),s=a.getContext("2d"),u=s.createImageData(i,i);return e.qrToImageData(u.data,t,o),function(t,e,n){t.clearRect(0,0,e.width,e.height),e.style||(e.style={}),e.height=n,e.width=n,e.style.height=n+"px",e.style.width=n+"px"}(s,a,i),s.putImageData(u,0,0),a},t.renderToDataURL=function(e,n,r){let o=r;void 0!==o||n&&n.getContext||(o=n,n=void 0),o||(o={});const a=t.render(e,n,o),i=o.type||"image/png",s=o.rendererOpts||{};return a.toDataURL(i,s.quality)}}(le);var de={};const fe=ce;function he(t,e){const n=t.a/255,r=e+'="'+t.hex+'"';return n<1?r+" "+e+'-opacity="'+n.toFixed(2).slice(1)+'"':r}function ge(t,e,n){let r=t+e;return void 0!==n&&(r+=" "+n),r}de.render=function(t,e,n){const r=fe.getOptions(e),o=t.modules.size,a=t.modules.data,i=o+2*r.margin,s=r.color.light.a?"<path "+he(r.color.light,"fill")+' d="M0 0h'+i+"v"+i+'H0z"/>':"",u="<path "+he(r.color.dark,"stroke")+' d="'+function(t,e,n){let r="",o=0,a=!1,i=0;for(let s=0;s<t.length;s++){const u=Math.floor(s%e),l=Math.floor(s/e);u||a||(a=!0),t[s]?(i++,s>0&&u>0&&t[s-1]||(r+=a?ge("M",u+n,.5+l+n):ge("m",o,0),o=0,a=!1),u+1<e&&t[s+1]||(r+=ge("h",i),i=0)):o++}return r}(a,o,r.margin)+'"/>',l='viewBox="0 0 '+i+" "+i+'"',c='<svg xmlns="http://www.w3.org/2000/svg" '+(r.width?'width="'+r.width+'" height="'+r.width+'" ':"")+l+' shape-rendering="crispEdges">'+s+u+"</svg>\n";return"function"==typeof n&&n(null,c),c};const pe=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then},me=K,ve=le,we=de;function ye(t,e,n,r,o){const a=[].slice.call(arguments,1),i=a.length,s="function"==typeof a[i-1];if(!s&&!pe())throw new Error("Callback required as last argument");if(!s){if(i<1)throw new Error("Too few arguments provided");return 1===i?(n=e,e=r=void 0):2!==i||e.getContext||(r=n,n=e,e=void 0),new Promise((function(o,a){try{const a=me.create(n,r);o(t(a,e,r))}catch(i){a(i)}}))}if(i<2)throw new Error("Too few arguments provided");2===i?(o=n,n=e,e=r=void 0):3===i&&(e.getContext&&void 0===o?(o=r,r=void 0):(o=r,r=n,n=e,e=void 0));try{const a=me.create(n,r);o(null,t(a,e,r))}catch(u){o(u)}}J.create=me.create,J.toCanvas=ye.bind(null,ve.render),J.toDataURL=ye.bind(null,ve.renderToDataURL),J.toString=ye.bind(null,(function(t,e,n){return we.render(t,n)}));const be={key:0,class:"rechargeDetail"},Ee=["src"],Ce={class:"download"},Ae=(t=>(P("data-v-d7ed0442"),t=t(),I(),t))((()=>y("div",{class:"rechargeDetail-unit"},E("USDT"),-1))),Be={class:"my-uploader"},xe={key:1,class:"rechargeDetail bank-input-content"},Te={key:0,class:"icon-type-content"},Pe={class:"mb-2.5 text-xs",style:{color:"#333"}},Ie={key:1,class:"recharge-limit"},ke={key:2,class:"recharge-limit"},Ne={key:2,class:"pay-button"},Me={class:"pay-button-content"},Re={class:"pay-button-title"},_e=t({__name:"detail",setup(t){const P=e(),I=n(),K=r(),q=o(),{t:O}=a();i(1e9);const Q=i(""),$=i(""),Z=i(""),G=i(""),X=i(""),W=i(""),tt=i(""),et=i(!1),nt=i([]),rt=i([]),ot=i(""),{toClipboard:at}=F(),it=t=>[null,void 0,""].includes(t);s(Q,(t=>{var e,n;const r=nt.value.find((e=>e.blockchain_name===t)),o=null!=(e=null==r?void 0:r.address)?e:"";o&&($.value=o,X.value=null!=(n=null==r?void 0:r.fee)?n:"",(async t=>{W.value=await J.toDataURL(t)})(o))}),{deep:!0});const st=i(!1),ut=i(50),lt=i(3e4);u((async()=>{await ct(),await(()=>{const t=q.params.id;if(Et.value=t,t)if(l.loading({duration:0,forbidClick:!0}),"bank"!==t)U().then((e=>{const n=e.filter((e=>e.coin===t.toUpperCase())).map((t=>({...t,label:t.blockchain_name,value:t.blockchain_name})));n.length&&(Q.value=n[0].value),nt.value=null!=n?n:[]}));else{const{g:t,key:e}=q.query;st.value=Boolean(t),t?L().then((t=>{const n=t.find((t=>t.productType===e));n&&(ut.value=n.range[0].min_amount,lt.value=n.range[0].max_amount)})):L().then((t=>{const e=t.find((t=>"Bank"===t.productType));Ct.value=e.range||[]}))}else l(O("参数错误")),setTimeout((()=>{K.back()}),1500)})(),l.clear()}));const ct=()=>{const t=Number(I.userInfo.kyc_status);let e="";switch(t){case 0:e="未认证";break;case 1:e="审核中";break;case 3:e="审核失败"}2!==t&&(l(O(e)),setTimeout((()=>{K.back()}),1500))},dt=c((()=>{var t;const e=G.value,n=null!=(t=X.value)?t:1;return _.times(e,n)}));c((()=>{const t=Z.value;return String(t).length<6}));const ft=c((()=>`1:${X.value}`)),ht=()=>{K.push({name:"RechargeRecord"})},gt=()=>{A(W.value,"QRCode")},pt=t=>{l(O("fileMaxLimit"))},mt=t=>{t.status="uploading",t.message=O("uploading"),H({file:t.file,moduleName:"recharge"}).then((e=>{t.status="success",t.message=O("uploadSuccess"),ot.value=e})).catch((()=>{t.message=O("上传失败"),t.status="failed"}))},vt=async()=>{try{await at($.value),l(O("copySuccess"))}catch(t){}},wt=i(""),yt=()=>new Promise(((t,e)=>{var n;V({session_token:null==(n=null==I?void 0:I.userInfo)?void 0:n.token}).then((e=>{wt.value=e.session_token,t()})).catch((()=>{e()}))})),bt=async()=>{var t;if(it(Q.value))return void l(O("blockchainNetworkRequire"));if(it(G.value))return void l(O("rechargeAmountRequire"));if(it(ot.value))return void l(O("uploadPaymentImageRequire"));const e={session_token:wt.value,from:"123",blockchain_name:Q.value,channel_address:$.value,amount:G.value,img:ot.value,coin:(null!=(t=q.params.id)?t:"").toUpperCase(),tx:""};et.value=!0,yt().then((()=>{S(e).then((t=>{l(O("rechargeApplySuccess")),setTimeout((()=>{ht()}),1500)})).finally((()=>{et.value=!1}))}))},Et=i(""),Ct=i([]),At=i(!1),Bt=i(""),xt=c((()=>Ct.value.map((t=>({text:t.bank_code,value:t.bank_code}))))),Tt=c((()=>{const t=Ct.value.find((t=>t.bank_code===Bt.value));return t||null})),Pt=({value:t})=>{Bt.value=t,At.value=!1},It=()=>{if(!Bt.value&&!st.value)return void l(O("请选择充值币种"));if(!G.value)return void l(O("请输入充值金额"));const t=Number(G.value);if(isNaN(t))l(O("请输入充值金额"));else{const e=st.value?ut.value:Tt.value.min_amount,n=st.value?lt.value:Tt.value.max_amount;if(t<e)return void l(O("充值金额不得低于最小限额"));if(t>n)return void l(O("充值金额不得高于最大限额"));const{g:r}=q.query;et.value=!0,yt().then((async()=>{const{key:t}=q.query,e={session_token:wt.value,amount:G.value,pageUrl:"gcash"===t?null:window.location.href+"/www/#/my"};let n="PHP_recharge";switch(t){case"GCash pay":n="PHP_recharge5";break;case"gcash":n="PHP_recharge";break;case"GCash2.0":n="PHP_recharge2";break;case"GCash3.0":n="PHP_recharge3";break;case"Maya":n="PHP_recharge4"}const o=r?j:D;r||(e.frenchCurrency=Bt.value),await o(e,n).then((t=>{G.value="",et.value=!1,tt.value=t})).catch((()=>{et.value=!1}))}))}};return d((()=>{yt()})),(t,e)=>{const n=f("fx-header"),r=M,o=N,a=Y,i=k,s=h;return x(),g("div",null,[p(n,{fixed:""},{title:m((()=>[y("div",null,E(v(O)("recharge")),1)])),right:m((()=>[y("div",{onClick:ht},E(v(O)("rechargeRecord")),1)])),_:1}),Et.value&&"bank"!==Et.value?(x(),g("div",be,[p(R,{list:nt.value,label:v(O)("blockchainNetwork"),modelValue:Q.value,"onUpdate:modelValue":e[0]||(e[0]=t=>Q.value=t)},null,8,["list","label","modelValue"]),W.value?(x(),g("img",{key:0,class:"qrCode",src:W.value,alt:""},null,8,Ee)):w("v-if",!0),y("div",Ce,[p(r,{type:"default",onClick:gt},{default:m((()=>[C(E(v(O)("saveQrCode")),1)])),_:1})]),p(z,{label:v(O)("rechargeAddress"),modelValue:$.value,"onUpdate:modelValue":e[1]||(e[1]=t=>$.value=t),"clear-btn":!1,readonly:"",typeText:"text"},{rightBtn:m((()=>[y("div",{class:T(["rechargeDetail-all",{"is-ar":v(P)}]),onClick:vt},E(v(O)("copy")),3)])),_:1},8,["label","modelValue"]),p(z,{label:v(O)("rechargeAmount"),placeholderText:v(O)("rechargeAmountTips"),modelValue:G.value,"onUpdate:modelValue":e[2]||(e[2]=t=>G.value=t),typeText:"number"},null,8,["label","placeholderText","modelValue"]),p(z,{label:v(O)("expectedAmount",{ratio:v(ft)}),modelValue:v(dt),"onUpdate:modelValue":e[3]||(e[3]=t=>b(dt)?dt.value=t:null),maxLength:18,clearBtn:!1,readonly:"",typeText:"number"},{rightBtn:m((()=>[Ae])),_:1},8,["label","modelValue"]),y("div",Be,[y("div",null,E(v(O)("uploadPaymentImage")),1),p(o,{modelValue:rt.value,"onUpdate:modelValue":e[4]||(e[4]=t=>rt.value=t),"max-size":1024e4,onOversize:pt,"after-read":mt,"max-count":1},null,8,["modelValue"])]),p(r,{class:"w-full btn-content",type:"primary",loading:et.value,onClick:bt},{default:m((()=>[C(E(v(O)("submit")),1)])),_:1},8,["loading"])])):w("v-if",!0),"bank"===Et.value?(x(),g("div",xe,[st.value?w("v-if",!0):(x(),g("div",Te,[y("div",Pe,E(v(O)("选择币种")),1),p(a,{modelValue:Bt.value,"onUpdate:modelValue":e[5]||(e[5]=t=>Bt.value=t),readonly:"",name:"picker",placeholder:v(O)("请选择充值币种"),onClick:e[6]||(e[6]=t=>At.value=!0)},null,8,["modelValue","placeholder"]),p(s,{show:At.value,"onUpdate:show":e[8]||(e[8]=t=>At.value=t),position:"bottom"},{default:m((()=>[p(i,{columns:v(xt),"cancel-button-text":v(O)("cancel"),"confirm-button-text":v(O)("confirm"),onConfirm:Pt,onCancel:e[7]||(e[7]=t=>At.value=!1)},null,8,["columns","cancel-button-text","confirm-button-text"])])),_:1},8,["show"])])),p(z,{label:v(O)("充值金额"),placeholderText:v(O)("请输入充值金额"),modelValue:G.value,"onUpdate:modelValue":e[9]||(e[9]=t=>G.value=t),typeText:"number"},null,8,["label","placeholderText","modelValue"]),Bt.value&&v(Tt)&&!st.value?(x(),g("div",Ie,[C(E(v(O)("充值限额"))+"：",1),y("span",null,E(v(Tt).min_amount),1),C(" ~ "),y("span",null,E(v(Tt).max_amount),1)])):w("v-if",!0),st.value?(x(),g("div",ke,[C(E(v(O)("充值限额"))+"：",1),y("span",null,E(ut.value),1),C(" ~ "),y("span",null,E(lt.value),1)])):w("v-if",!0),p(r,{class:"w-full btn-content gap",type:"primary",loading:et.value,onClick:It},{default:m((()=>[C(E(v(O)("submit")),1)])),_:1},8,["loading"])])):w("v-if",!0),tt.value?(x(),g("div",Ne,[y("div",Me,[y("span",Re,E(v(O)("请前往{_$1}继续完成支付",{_$1:v(q).query.key})),1),y("div",{class:"pay-button-button",onClick:e[10]||(e[10]=t=>{return e=tt.value,tt.value="",void B(e,!0);var e})},E(v(O)("继续支付")),1)])])):w("v-if",!0)])}}},[["__scopeId","data-v-d7ed0442"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/recharge/detail.vue"]]);export{_e as default};
