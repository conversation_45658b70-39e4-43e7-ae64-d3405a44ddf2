System.register(["./index-legacy-46a00900.js"],(function(t,e){"use strict";var n,r,o,a,i,c;return{setters:[t=>{t.aG,n=t.T,r=t.j,o=t.b2,a=t.b3,i=t.N,c=t.O}],execute:function(){var e={exports:{}},u={exports:{}};!function(t,e){t.exports=function(t){var e={};function n(r){if(e[r])return e[r].exports;var o=e[r]={i:r,l:!1,exports:{}};return t[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)n.d(r,o,function(e){return t[e]}.bind(null,o));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=0)}([function(t,e,n){var r;function o(t){return["image/png","image/jpeg","image/gif"].some((e=>e===t))}n.r(e),n.d(e,"canvastoDataURL",(function(){return i})),n.d(e,"canvastoFile",(function(){return c})),n.d(e,"dataURLtoFile",(function(){return s})),n.d(e,"dataURLtoImage",(function(){return l})),n.d(e,"downloadFile",(function(){return d})),n.d(e,"filetoDataURL",(function(){return h})),n.d(e,"imagetoCanvas",(function(){return m})),n.d(e,"urltoBlob",(function(){return g})),n.d(e,"urltoImage",(function(){return p})),n.d(e,"compress",(function(){return y})),n.d(e,"compressAccurately",(function(){return b})),n.d(e,"EImageType",(function(){return r})),function(t){t.PNG="image/png",t.JPEG="image/jpeg",t.GIF="image/gif"}(r||(r={}));var a=function(t,e,n,r){return new(n||(n=Promise))((function(o,a){function i(t){try{u(r.next(t))}catch(t){a(t)}}function c(t){try{u(r.throw(t))}catch(t){a(t)}}function u(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(i,c)}u((r=r.apply(t,e||[])).next())}))};function i(t,e=.92,n=r.JPEG){return a(this,void 0,void 0,(function*(){return o(n)||(n=r.JPEG),t.toDataURL(n,e)}))}function c(t,e=.92,n=r.JPEG){return new Promise((r=>t.toBlob((t=>r(t)),n,e)))}var u=function(t,e,n,r){return new(n||(n=Promise))((function(o,a){function i(t){try{u(r.next(t))}catch(t){a(t)}}function c(t){try{u(r.throw(t))}catch(t){a(t)}}function u(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(i,c)}u((r=r.apply(t,e||[])).next())}))};function s(t,e){return u(this,void 0,void 0,(function*(){const n=t.split(",");let r=n[0].match(/:(.*?);/)[1];const a=atob(n[1]);let i=a.length;const c=new Uint8Array(i);for(;i--;)c[i]=a.charCodeAt(i);return o(e)&&(r=e),new Blob([c],{type:r})}))}function l(t){return new Promise(((e,n)=>{const r=new Image;r.onload=()=>e(r),r.onerror=()=>n(new Error("dataURLtoImage(): dataURL is illegal")),r.src=t}))}function d(t,e){const n=document.createElement("a");n.href=window.URL.createObjectURL(t),n.download=e||Date.now().toString(36),document.body.appendChild(n);const r=document.createEvent("MouseEvents");r.initEvent("click",!1,!1),n.dispatchEvent(r),document.body.removeChild(n)}function h(t){return new Promise((e=>{const n=new FileReader;n.onloadend=t=>e(t.target.result),n.readAsDataURL(t)}))}var f=function(t,e,n,r){return new(n||(n=Promise))((function(o,a){function i(t){try{u(r.next(t))}catch(t){a(t)}}function c(t){try{u(r.throw(t))}catch(t){a(t)}}function u(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(i,c)}u((r=r.apply(t,e||[])).next())}))};function m(t,e={}){return f(this,void 0,void 0,(function*(){const n=Object.assign({},e),r=document.createElement("canvas"),o=r.getContext("2d");let a,i;for(const t in n)Object.prototype.hasOwnProperty.call(n,t)&&(n[t]=Number(n[t]));if(n.scale){const e=n.scale>0&&n.scale<10?n.scale:1;i=t.width*e,a=t.height*e}else i=n.width||n.height*t.width/t.height||t.width,a=n.height||n.width*t.height/t.width||t.height;switch([5,6,7,8].some((t=>t===n.orientation))?(r.height=i,r.width=a):(r.height=a,r.width=i),n.orientation){case 3:o.rotate(180*Math.PI/180),o.drawImage(t,-r.width,-r.height,r.width,r.height);break;case 6:o.rotate(90*Math.PI/180),o.drawImage(t,0,-r.width,r.height,r.width);break;case 8:o.rotate(270*Math.PI/180),o.drawImage(t,-r.height,0,r.height,r.width);break;case 2:o.translate(r.width,0),o.scale(-1,1),o.drawImage(t,0,0,r.width,r.height);break;case 4:o.translate(r.width,0),o.scale(-1,1),o.rotate(180*Math.PI/180),o.drawImage(t,-r.width,-r.height,r.width,r.height);break;case 5:o.translate(r.width,0),o.scale(-1,1),o.rotate(90*Math.PI/180),o.drawImage(t,0,-r.width,r.height,r.width);break;case 7:o.translate(r.width,0),o.scale(-1,1),o.rotate(270*Math.PI/180),o.drawImage(t,-r.height,0,r.height,r.width);break;default:o.drawImage(t,0,0,r.width,r.height)}return r}))}function g(t){return fetch(t).then((t=>t.blob()))}function p(t){return new Promise(((e,n)=>{const r=new Image;r.onload=()=>e(r),r.onerror=()=>n(new Error("urltoImage(): Image failed to load, please check the image URL")),r.src=t}))}var w=function(t,e,n,r){return new(n||(n=Promise))((function(o,a){function i(t){try{u(r.next(t))}catch(t){a(t)}}function c(t){try{u(r.throw(t))}catch(t){a(t)}}function u(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(i,c)}u((r=r.apply(t,e||[])).next())}))};function y(t,e={}){return w(this,void 0,void 0,(function*(){if(!(t instanceof Blob))throw new Error("compress(): First arg must be a Blob object or a File object.");if("object"!=typeof e&&(e=Object.assign({quality:e})),e.quality=Number(e.quality),Number.isNaN(e.quality))return t;const n=yield h(t);let a=n.split(",")[0].match(/:(.*?);/)[1],c=r.JPEG;o(e.type)&&(c=e.type,a=e.type);const u=yield l(n),d=yield m(u,Object.assign({},e)),f=yield i(d,e.quality,c),g=yield s(f,a);return g.size>t.size?t:g}))}function b(t,e={}){return w(this,void 0,void 0,(function*(){if(!(t instanceof Blob))throw new Error("compressAccurately(): First arg must be a Blob object or a File object.");if("object"!=typeof e&&(e=Object.assign({size:e})),e.size=Number(e.size),Number.isNaN(e.size))return t;if(1024*e.size>t.size)return t;e.accuracy=Number(e.accuracy),(!e.accuracy||e.accuracy<.8||e.accuracy>.99)&&(e.accuracy=.95);const n=e.size*(2-e.accuracy)*1024,a=1024*e.size,c=e.size*e.accuracy*1024,u=yield h(t);let d=u.split(",")[0].match(/:(.*?);/)[1],f=r.JPEG;o(e.type)&&(f=e.type,d=e.type);const g=yield l(u),p=yield m(g,Object.assign({},e));let w,y=.5;const b=[null,null];for(let t=1;t<=7;t++){w=yield i(p,y,f);const e=.75*w.length;if(7===t){(n<e||c>e)&&(w=[w,...b].filter((t=>t)).sort(((t,e)=>Math.abs(.75*t.length-a)-Math.abs(.75*e.length-a)))[0]);break}if(n<e)b[1]=w,y-=Math.pow(.5,t+1);else{if(!(c>e))break;b[0]=w,y+=Math.pow(.5,t+1)}}const v=yield s(w,d);return v.size>t.size?t:v}))}}])}(u),function(t){t.exports=u.exports}(e),t("_",((t,i)=>{if(!(t.file.size/1024/1024<10))return n.fail("上传图片大小不能超过 10MB!"),!1;const c=r(),u=c?.userInfo?.token;return new Promise(((n,r)=>{e.exports.compress(t.file,.6).then((t=>{const e=new FormData;e.append("file",t),a.post(`${o}/wap/public/uploadimg!execute.action?token=${u}`,e,{onDownloadProgress:t=>{t.lengthComputeable&&i((t.loaded/t.total*100).toFixed(2))}},{headers:{"Content-Type":"multipart/form-data"},timeout:5e3}).then((t=>{const{code:e,data:r}=t.data;e/1==0&&n(r)})).catch((t=>{r(t)}))}))}))})),t("u",(t=>{const e=new FormData;return e.append("file",t.file),e.append("moduleName",t.moduleName),i({url:"/wap/api/uploadimg!execute.action",method:c.POST,data:e})}))}}}));
