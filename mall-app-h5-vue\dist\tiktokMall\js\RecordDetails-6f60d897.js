import{_ as a,Y as e,l as s,av as d,c as t,e as v,w as i,cE as l,o as c,f}from"./index-3d21abf8.js";const n={class:"RecordDetails pb-12"},u=l('<div class="quantity mt-8" data-v-a3e61e86>数量</div><div class="money mt-8" data-v-a3e61e86><span data-v-a3e61e86>123,456,78.123</span> USDT </div><div class="status flex mt-9" data-v-a3e61e86><div class="status-icon mr-2 status-bg3" data-v-a3e61e86></div> 充值成功 </div><div class="text mt-3" data-v-a3e61e86> 数字币已经充值成功。您可以在钱包账户中查看详情。 </div><ul class="px-4" data-v-a3e61e86><li class="flex mt-10 justify-between" data-v-a3e61e86><div class="ash" data-v-a3e61e86>确认数</div><div data-v-a3e61e86>12/12</div></li><li class="flex mt-10 justify-between" data-v-a3e61e86><div class="ash" data-v-a3e61e86>充值账户</div><div data-v-a3e61e86>钱包账户</div></li><li class="flex mt-10 justify-between" data-v-a3e61e86><div class="ash" data-v-a3e61e86>转账网络</div><div data-v-a3e61e86>USDT_TRC20</div></li><li class="flex mt-10 justify-between" data-v-a3e61e86><div class="ash" data-v-a3e61e86>地址</div><div class="flex" data-v-a3e61e86><div class="address" data-v-a3e61e86>TVPkt213sae3141412321ed21131e3141412</div><span class="copy" data-v-a3e61e86></span></div></li><li class="flex mt-10 justify-between" data-v-a3e61e86><div class="ash" data-v-a3e61e86>交易哈希</div><div data-v-a3e61e86><span class="copy" data-v-a3e61e86></span></div></li><li class="flex mt-10 justify-between" data-v-a3e61e86><div class="ash" data-v-a3e61e86>日期</div><div data-v-a3e61e86>2022-03-17 15:39:04</div></li><li class="flex mt-10 justify-between" data-v-a3e61e86><div class="ash" data-v-a3e61e86>备注</div><div data-v-a3e61e86>充值金额不足</div></li><li class="flex mt-10 justify-between" data-v-a3e61e86><div class="ash" data-v-a3e61e86>充值类型</div><div data-v-a3e61e86>USD</div></li><li class="flex mt-10 justify-between" data-v-a3e61e86><div class="ash" data-v-a3e61e86>银行账户</div><div data-v-a3e61e86>6688 5544 2211 8866</div></li><li class="flex mt-10 justify-between" data-v-a3e61e86><div class="ash" data-v-a3e61e86>开户行</div><div data-v-a3e61e86>北京朝阳支行</div></li></ul>',5),m=a({__name:"RecordDetails",setup:a=>(e(),s(),(a,e)=>{const s=d("fx-header");return c(),t("div",n,[v(s,null,{title:i((()=>[f("充提详情")])),_:1}),u])})},[["__scopeId","data-v-a3e61e86"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/Record/RecordDetails.vue"]]);export{m as default};
