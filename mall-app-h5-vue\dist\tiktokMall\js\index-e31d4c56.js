import{_ as e,d as a,u as t,r as i,aF as l,av as s,c as n,e as o,w as d,bx as r,o as u,f as p,t as g,F as m,y as c,a as f,x as v,A as h,D as x,E as R}from"./index-3d21abf8.js";import{P as y}from"./index-fc51b7d2.js";import{L as D}from"./index-40e83579.js";import{E as _}from"./index-5d897066.js";import"./use-id-a0619e01.js";const w=a({name:"ShopMarketingRecord",setup(){const{t:e}=t(),a=new URL("/www/png/name-20d65991.png",self.location),s=i([]),n=i(!1),o=i(!0),d=i(!1),u=i({pageNum:1,pageSize:10}),p=async()=>{const e={...u.value};await r(e).then((e=>{const{pageList:a}=e||[];s.value=1===u.value.pageNum?a:[...s.value,...a],u.value.pageNum++,o.value=!1,n.value=!1,a.length<u.value.pageSize&&(d.value=!0)})).catch((()=>{o.value=!1,d.value=!0}))};return{empytImg:a,refreshing:n,loading:o,finished:d,listData:s,t:e,getListData:p,onRefresh:()=>{d.value=!1,o.value=!0,u.value.pageNum=1,p()},numberStrFormat:l}}}),L=(e=>(x("data-v-e0e40d57"),e=e(),R(),e))((()=>f("div",{style:{height:"46px"}},null,-1))),S={key:0,class:"list-content"},T={class:"info-item"},k={class:"info-item"},N={class:"info-item"},b={class:"info-item"},j={class:"info-item"},F={class:"price"};const P=e(w,[["render",function(e,a,t,i,l,r){const x=s("fx-header"),R=_,w=D,P=y;return u(),n("div",null,[o(x,{fixed:!0},{title:d((()=>[p(g(e.t("shopBuyRecord")),1)])),_:1}),L,o(P,{modelValue:e.refreshing,"onUpdate:modelValue":a[1]||(a[1]=a=>e.refreshing=a),"pulling-text":e.t("pullingText"),"loosing-text":e.t("loosingText"),"loading-text":e.t("loading"),onRefresh:e.onRefresh},{default:d((()=>[o(w,{loading:e.loading,"onUpdate:loading":a[0]||(a[0]=a=>e.loading=a),finished:e.finished,"loading-text":e.t("loading"),"finished-text":e.listData.length?e.t("product.3"):"",onLoad:e.getListData},{default:d((()=>[e.listData.length?(u(),n("div",S,[(u(!0),n(m,null,c(e.listData,((a,t)=>(u(),n("div",{key:t,class:"item"},[f("div",T,[f("p",null,g(e.t("shopRecordName")),1),f("div",null,g(a.name),1)]),f("div",k,[f("p",null,g(e.t("shopRecordStartTime")),1),f("div",null,g(a.startTime),1)]),f("div",N,[f("p",null,g(e.t("shopRecordStopTime")),1),f("div",null,g(a.stopTime),1)]),f("div",b,[f("p",null,g(e.t("shopRecordPayType")),1),f("div",null,g(e.t("shopRecordPayName")),1)]),f("div",j,[f("p",null,g(e.t("shopRecordPrice")),1),f("div",F,g("$"+e.numberStrFormat(a.prize)),1)])])))),128))])):v("v-if",!0),e.listData.length||e.loading?v("v-if",!0):(u(),h(R,{key:1,image:e.empytImg.href,description:e.t("noData")},null,8,["image","description"]))])),_:1},8,["loading","finished","loading-text","finished-text","onLoad"])])),_:1},8,["modelValue","pulling-text","loosing-text","loading-text","onRefresh"])])}],["__scopeId","data-v-e0e40d57"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/shop/marketing/record/index.vue"]]);export{P as default};
