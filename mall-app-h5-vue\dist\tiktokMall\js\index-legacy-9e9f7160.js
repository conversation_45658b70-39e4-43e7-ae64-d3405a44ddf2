System.register(["./index-legacy-46a00900.js"],(function(e,r){"use strict";var t,n,a,l,o,i,s,u,d,c;return{setters:[e=>{t=e.P,n=e.S,a=e.ai,l=e.d,o=e.aH,i=e.a7,s=e.a9,u=e.e,d=e.V,c=e.X}],execute:function(){const[r,m]=t("form"),g={colon:<PERSON><PERSON><PERSON>,disabled:<PERSON><PERSON><PERSON>,readonly:<PERSON><PERSON><PERSON>,showError:<PERSON>olean,labelWidth:n,labelAlign:String,inputAlign:String,scrollToError:Boolean,validateFirst:Boolean,submitOnEnter:a,showErrorMessage:a,errorMessageAlign:String,validateTrigger:{type:[String,Array],default:"onBlur"}};var h=l({name:r,props:g,emits:["submit","failed"],setup(e,{emit:r,slots:t}){const{children:n,linkChildren:a}=o(i),l=e=>e?n.filter((r=>e.includes(r.name))):n,c=r=>{return"string"==typeof r?(e=>{const r=n.find((r=>r.name===e));return r?new Promise(((e,t)=>{r.validate().then((r=>{r?t(r):e()}))})):Promise.reject()})(r):e.validateFirst?(t=r,new Promise(((e,r)=>{const n=[];l(t).reduce(((e,r)=>e.then((()=>{if(!n.length)return r.validate().then((e=>{e&&n.push(e)}))}))),Promise.resolve()).then((()=>{n.length?r(n):e()}))}))):(e=>new Promise(((r,t)=>{const n=l(e);Promise.all(n.map((e=>e.validate()))).then((e=>{(e=e.filter(Boolean)).length?t(e):r()}))})))(r);var t},g=(e,r)=>{n.some((t=>t.name===e&&(t.$el.scrollIntoView(r),!0)))},h=()=>n.reduce(((e,r)=>(e[r.name]=r.formValue.value,e)),{}),f=()=>{const t=h();c().then((()=>r("submit",t))).catch((n=>{r("failed",{values:t,errors:n}),e.scrollToError&&n[0].name&&g(n[0].name)}))},v=e=>{d(e),f()};return a({props:e}),s({submit:f,validate:c,getValues:h,scrollToField:g,resetValidation:e=>{"string"==typeof e&&(e=[e]),l(e).forEach((e=>{e.resetValidation()}))},getValidationStatus:()=>n.reduce(((e,r)=>(e[r.name]=r.getValidationStatus(),e)),{})}),()=>{var e;return u("form",{class:m(),onSubmit:v},[null==(e=t.default)?void 0:e.call(t)])}}});e("F",c(h))}}}));
