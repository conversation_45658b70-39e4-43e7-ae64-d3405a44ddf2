import{P as t,ai as e,S as s,bB as a,d as i,r as o,aC as l,aB as n,aH as r,m as c,ad as d,ac as p,q as u,g as f,a9 as h,e as v,M as m,s as x,aO as g,aD as y,aT as k,a0 as w,$ as C,V as b,p as j,a6 as _,Q as S,a$ as T,b1 as $,X as I,_ as O,Y as V,l as A,av as P,c as z,w as L,b as M,aV as R,o as B,f as H,F as D,y as E,a as F}from"./index-3d21abf8.js";/* empty css              *//* empty css               */import{S as U}from"./index-179203f3.js";import"./index-8c1841f6.js";import"./index-7dfcb82a.js";import"./use-route-cd41a893.js";import"./use-id-a0619e01.js";const[X,Y]=t("index-bar"),q={sticky:e,zIndex:s,teleport:[String,Object],highlightColor:String,stickyOffsetTop:a(0),indexList:{type:Array,default:function(){const t="A".charCodeAt(0);return Array(26).fill("").map(((e,s)=>String.fromCharCode(t+s)))}}},Q=Symbol(X);var G=i({name:X,props:q,emits:["select","change"],setup(t,{emit:e,slots:s}){const a=o(),i=o(),j=o(""),_=l(),S=n(a),{children:T,linkChildren:$}=r(Q);let I;$({props:t});const O=c((()=>{if(d(t.zIndex))return{zIndex:+t.zIndex+1}})),V=c((()=>{if(t.highlightColor)return{color:t.highlightColor}})),A=(e,s)=>{for(let a=T.length-1;a>=0;a--){const i=a>0?s[a-1].height:0;if(e+(t.sticky?i+t.stickyOffsetTop:0)>=s[a].top)return a}return-1},P=t=>T.find((e=>String(e.index)===t)),z=()=>{if(g(a))return;const{sticky:e,indexList:s}=t,i=y(S.value),o=k(S),l=T.map((t=>t.getRect(S.value,o)));let n=-1;if(I){const t=P(I);if(t){const e=t.getRect(S.value,o);n=A(e.top,l)}}else n=A(i,l);j.value=s[n],e&&T.forEach(((e,s)=>{const{state:a,$el:r}=e;if(s===n||s===n-1){const t=r.getBoundingClientRect();a.left=t.left,a.width=t.width}else a.left=null,a.width=null;if(s===n)a.active=!0,a.top=Math.max(t.stickyOffsetTop,l[s].top-i)+o.top;else if(s===n-1&&""===I){const t=l[n].top-i;a.active=t>0,a.top=t+o.top-l[s].height}else a.active=!1})),I=""},L=()=>{x(z)};p("scroll",z,{target:S,passive:!0}),u(L),f((()=>t.indexList),L),f(j,(t=>{t&&e("change",t)}));const M=s=>{I=String(s);const a=P(I);if(a){const s=y(S.value),i=k(S),{offsetHeight:o}=document.documentElement;if(a.$el.scrollIntoView(),s===o-i.height)return void z();t.sticky&&t.stickyOffsetTop&&w(C()-t.stickyOffsetTop),e("select",a.index)}},R=t=>{const{index:e}=t.dataset;e&&M(e)},B=t=>{R(t.target)};let H;const D=()=>v("div",{ref:i,class:Y("sidebar"),style:O.value,onClick:B,onTouchstartPassive:_.start},[t.indexList.map((t=>{const e=t===j.value;return v("span",{class:Y("index",{active:e}),style:e?V.value:void 0,"data-index":t},[t])}))]);return h({scrollTo:M}),p("touchmove",(t=>{if(_.move(t),_.isVertical()){b(t);const{clientX:e,clientY:s}=t.touches[0],a=document.elementFromPoint(e,s);if(a){const{index:t}=a.dataset;t&&H!==t&&(H=t,R(a))}}}),{target:i}),()=>{var e;return v("div",{ref:a,class:Y()},[t.teleport?v(m,{to:t.teleport},{default:()=>[D()]}):D(),null==(e=s.default)?void 0:e.call(s)])}}});const[J,K]=t("index-anchor");const N=I(i({name:J,props:{index:s},setup(t,{slots:e}){const s=j({top:0,left:null,rect:{top:0,height:0},width:null,active:!1}),a=o(),{parent:i}=_(Q);if(!i)return;const l=()=>s.active&&i.props.sticky,n=c((()=>{const{zIndex:t,highlightColor:e}=i.props;if(l())return S(T(t),{left:s.left?`${s.left}px`:void 0,width:s.width?`${s.width}px`:void 0,transform:s.top?`translate3d(0, ${s.top}px, 0)`:void 0,color:e})}));return h({state:s,getRect:(t,e)=>{const i=k(a);return s.rect.height=i.height,t===window||t===document.body?s.rect.top=i.top+C():s.rect.top=i.top+y(t)-e.top,s.rect}}),()=>{const i=l();return v("div",{ref:a,style:{height:i?`${s.rect.height}px`:void 0}},[v("div",{style:n.value,class:[K({sticky:i}),{[$]:i}]},[e.default?e.default():t.index])])}}})),W=I(G),Z={class:"selectPay pb-10"},tt=O({__name:"selectPay",setup(t){V();const e=A();let s=o("");return(t,a)=>{const i=P("fx-header"),o=U,l=N,n=W;return B(),z("div",Z,[v(i,null,{title:L((()=>[H("全部收款方式")])),_:1}),v(o,{modelValue:M(s),"onUpdate:modelValue":a[0]||(a[0]=t=>R(s)?s.value=t:s=t),placeholder:"请输入搜索关键词"},null,8,["modelValue"]),v(n,null,{default:L((()=>[v(l,{class:"index-anchor",index:"A"}),(B(),z(D,null,E(10,((t,s)=>F("div",{onClick:a[1]||(a[1]=t=>{e.push("add")}),class:"item-cell ml-4 py-4",key:s},"Asia Hawala "))),64))])),_:1})])}}},[["__scopeId","data-v-4a9a5654"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/payMentMethod/selectPay.vue"]]);export{tt as default};
