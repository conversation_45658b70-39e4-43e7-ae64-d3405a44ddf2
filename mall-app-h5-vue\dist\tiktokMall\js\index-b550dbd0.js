import{_ as e,d as a,u as t,Y as s,r as i,av as l,c as o,e as n,w as d,o as r,f as u,t as g,F as p,y as f,A as m,x as v,D as c,E as h,a as x}from"./index-3d21abf8.js";import{P as _}from"./index-fc51b7d2.js";import{L as j}from"./index-40e83579.js";import{E as y}from"./index-5d897066.js";import{G as b}from"./GoodsItem-0eb2ed94.js";import{m as D}from"./product.api-7fdfc848.js";import"./use-id-a0619e01.js";import"./index-cfaf3bc2.js";import"./stringify-d53955b2.js";import"./___vite-browser-external_commonjs-proxy-9405bfc0.js";const I=a({name:"ShopClass",components:{GoodsItem:b},setup(){const{t:e}=t(),a=s(),l=i(""),{name:o,id:n,sellerId:d}=a.query,r=new URL("/www/png/name-20d65991.png",self.location),u=i([]),g=i(!1),p=i(!0),f=i(!1),m=i({pageNum:1,pageSize:10}),v=()=>{if(o&&n&&d){const e={...m.value,categoryId:n,sellerId:d};D(e).then((e=>{const a=e.pageList||[];u.value=1===m.value.pageNum?a:[...u.value,...a],m.value.pageNum++,p.value=!1,g.value=!1,a.length<m.value.pageSize&&(f.value=!0)})).catch((()=>{f.value=!0,p.value=!1,g.value=!1}))}else f.value=!0,p.value=!1,g.value=!1};return o&&n&&d&&(l.value=o),{t:e,pageTitle:l,listData:u,refreshing:g,loading:p,finished:f,empytImg:r,getListData:v,onRefresh:()=>{f.value=!1,p.value=!0,m.value.pageNum=1,v()}}}}),w=(e=>(c("data-v-7477504b"),e=e(),h(),e))((()=>x("div",{style:{height:"46px"}},null,-1))),L={key:0,class:"list-content"};const R=e(I,[["render",function(e,a,t,s,i,c){const h=l("fx-header"),x=l("goods-item"),b=y,D=j,I=_;return r(),o("div",null,[n(h,{fixed:!0},{title:d((()=>[u(g(e.pageTitle),1)])),_:1}),w,n(I,{modelValue:e.refreshing,"onUpdate:modelValue":a[1]||(a[1]=a=>e.refreshing=a),"pulling-text":e.t("pullingText"),"loosing-text":e.t("loosingText"),"loading-text":e.t("loading"),onRefresh:e.onRefresh},{default:d((()=>[n(D,{loading:e.loading,"onUpdate:loading":a[0]||(a[0]=a=>e.loading=a),finished:e.finished,"loading-text":e.t("loading"),"finished-text":e.t("product.3"),onLoad:e.getListData},{default:d((()=>[e.listData.length?(r(),o("div",L,[(r(!0),o(p,null,f(e.listData,(e=>(r(),m(x,{key:e.id,"goods-data":e},null,8,["goods-data"])))),128))])):v("v-if",!0),e.listData.length||e.loading?v("v-if",!0):(r(),m(b,{key:1,image:e.empytImg.href,description:e.t("noData")},null,8,["image","description"]))])),_:1},8,["loading","finished","loading-text","finished-text","onLoad"])])),_:1},8,["modelValue","pulling-text","loosing-text","loading-text","onRefresh"])])}],["__scopeId","data-v-7477504b"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/shop/class/index.vue"]]);export{R as default};
