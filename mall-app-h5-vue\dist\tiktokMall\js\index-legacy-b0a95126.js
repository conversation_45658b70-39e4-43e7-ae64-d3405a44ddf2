System.register(["./index-legacy-46a00900.js","./index-legacy-8ad4c0d7.js","./index-legacy-a4cde014.js","./use-placeholder-legacy-f22ccc27.js","./stringify-legacy-93b715ae.js","./___vite-browser-external_commonjs-proxy-legacy-70ac8ee8.js"],(function(e,t){"use strict";var a,d,o,r,n,i,s,c,l,p,h,v,f,g,u,x,m,b,y,w,k,_,j,I=document.createElement("style");return I.textContent='@charset "UTF-8";.service-box[data-v-cdd47d6d]{width:100%;box-sizing:border-box}.service-box[data-v-cdd47d6d] .van-hairline--bottom:after{border-color:#f3f3f3}.break-word[data-v-cdd47d6d]{word-wrap:break-word}.max-w-230[data-v-cdd47d6d]{max-width:115px}.responser[data-v-cdd47d6d]{position:relative}.responser[data-v-cdd47d6d]:after{content:"";width:0;height:0;border-top:5px solid transparent;border-bottom:5px solid transparent;border-right:10px solid #f3f3f3;position:absolute;left:-10px;top:10px}.borderTop[data-v-cdd47d6d]{border-top:1px solid #f3f3f3}.bottomBox[data-v-cdd47d6d]{height:65px}.black[data-v-cdd47d6d]{color:#1f2025}.chatBg[data-v-cdd47d6d]{border-radius:10.0022px 10.0022px 0;max-width:50vw}.responser-content>img.res[data-v-cdd47d6d]{width:50px;height:50px;margin:0 15px 0 0}.responser-content>.res-content[data-v-cdd47d6d]{border-radius:10.0022px 10.0022px 10.0022px 0}.iframe-content[data-v-cdd47d6d]{width:100%;height:100vh;padding-top:46px}.ios-device .iframe-content[data-v-cdd47d6d]{height:calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom));height:calc(100vh - var(--ios-safe-area-top, 44px) - var(--ios-safe-area-bottom, 34px));padding-top:calc(46px + env(safe-area-inset-top));padding-top:calc(46px + var(--ios-safe-area-top, 44px));padding-bottom:env(safe-area-inset-bottom);padding-bottom:var(--ios-safe-area-bottom, 34px)}.iframe-content>iframe[data-v-cdd47d6d]{width:100%;height:100%;border:none}.ios-device .iframe-content>iframe[data-v-cdd47d6d]{height:calc(100% - env(safe-area-inset-bottom));height:calc(100% - var(--ios-safe-area-bottom, 34px))}\n',document.head.appendChild(I),{setters:[e=>{a=e._,d=e.ci,o=e.r,r=e.u,n=e.j,i=e.Y,s=e.q,c=e.s,l=e.J,p=e.c,h=e.a,v=e.e,f=e.b,g=e.L,u=e.x,x=e.n,m=e.T,b=e.B,y=e.o,w=e.D,k=e.E},e=>{_=e.N},e=>{j=e.l},()=>{},()=>{},()=>{}],execute:function(){const t=(e=>(w("data-v-cdd47d6d"),e=e(),k(),e))((()=>h("div",{class:"fixed-header-spacer"},null,-1))),I=["src"];e("default",a({__name:"index",setup(e){const{height:a}=d();o(null);const w=a.value-46,{t:k}=r(),C=n(),L=o(!1);let q="";q=location.host;const E=i();let S=null;S="partyId"in E.query?{token:C.userInfo.token,lang:localStorage.getItem("lang")||"en",height:`${w}px`,partyid:E.query.partyId,name:E.query.username}:{token:C.userInfo.token,lang:localStorage.getItem("lang")||"en",height:`${w}px`};let B=o("");B.value="name"in S?S.name:k("消息中心");const T=o("");s((()=>{const e=navigator.userAgent.toLowerCase();L.value=/iphone|ipad|ipod/.test(e)||!0===window.navigator.standalone,L.value&&document.body.classList.add("ios-device")})),c((async()=>{m.loading({duration:0});let e="";try{await b().then((t=>{e=t.avatar||""}))}catch(a){}S.nohead=!0,S.type="shop",e&&(S.selfimg=e),T.value="https://"+q+"/chat/#/h5/message/blue?"+j.stringify(S),window.addEventListener("message",$,!1);const t=document.querySelector("iframe");t&&(t.onerror=e=>{m.clear()},t.onload=()=>{m.clear();try{t.contentWindow.postMessage({type:"init",data:S},"*")}catch(a){}})}));const $=e=>{try{if(e.origin!==`https://${q}`)return;const{type:t,data:a}=e.data;"ready"===t||"error"===t&&m.clear()}catch(t){}};l((()=>{window.removeEventListener("message",$)}));const D=()=>{try{window.history.length>1?window.history.back():router.push("/")}catch(e){router.push("/")}};return(e,a)=>{const d=_;return y(),p("div",{class:x(["page-main-content has-fixed-header",{"ios-device":L.value}])},[h("div",null,[v(d,{ref:"navEl",title:f(B),"left-arrow":"",onClickLeft:D,fixed:""},null,8,["title"])]),t,T.value?(y(),p("div",{key:0,style:g({height:w+"px"}),class:"iframe-content"},[h("iframe",{src:T.value,class:"flex-1"},null,8,I)],4)):u("v-if",!0)],2)}}},[["__scopeId","data-v-cdd47d6d"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/messageCenter/index.vue"]]))}}}));
