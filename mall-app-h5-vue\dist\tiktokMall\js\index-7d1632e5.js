import{P as o,d as e,bc as a,S as s,p as t,aC as i,r as n,m as l,g as r,ac as c,e as m,V as v,aQ as u,W as d,a9 as g,q as h,aI as p,aJ as w,s as f,ag as x,aY as b,bn as y,ai as C,bl as I,a4 as X,a5 as P,R as Y,aT as Z,b0 as z,I as W,bf as H,b9 as S,Q as R,X as T,bv as $,bw as D}from"./index-3d21abf8.js";import{S as N,a as j}from"./index-0d6a7179.js";import{I as B}from"./index-a439655d.js";const O=o=>Math.sqrt((o[0].clientX-o[1].clientX)**2+(o[0].clientY-o[1].clientY)**2),M=o("image-preview")[1];var q=e({props:{src:String,show:<PERSON><PERSON><PERSON>,active:Number,minZoom:a(s),maxZoom:a(s),rootWidth:a(Number),rootHeight:a(Number)},emits:["scale","close"],setup(o,{emit:e}){const a=t({scale:1,moveX:0,moveY:0,moving:!1,zooming:!1,imageRatio:0,displayWidth:0,displayHeight:0}),s=i(),g=n(),h=l((()=>{const{rootWidth:e,rootHeight:s}=o,t=s/e;return a.imageRatio>t})),p=l((()=>{const{scale:o,moveX:e,moveY:s,moving:t,zooming:i}=a,n={transitionDuration:i||t?"0s":".3s"};if(1!==o){const a=e/o,t=s/o;n.transform=`scale(${o}, ${o}) translate(${a}px, ${t}px)`}return n})),w=l((()=>{if(a.imageRatio){const{rootWidth:e,rootHeight:s}=o,t=h.value?s/a.imageRatio:e;return Math.max(0,(a.scale*t-e)/2)}return 0})),f=l((()=>{if(a.imageRatio){const{rootWidth:e,rootHeight:s}=o,t=h.value?s:e*a.imageRatio;return Math.max(0,(a.scale*t-s)/2)}return 0})),x=s=>{(s=u(s,+o.minZoom,+o.maxZoom+1))!==a.scale&&(a.scale=s,e("scale",{scale:s,index:o.active}))},b=()=>{x(1),a.moveX=0,a.moveY=0};let y,C,I,X,P,Y,Z;const z=o=>{const{touches:e}=o,{offsetX:t}=s;s.start(o),y=e.length,C=a.moveX,I=a.moveY,Z=Date.now(),a.moving=1===y&&1!==a.scale,a.zooming=2===y&&!t.value,a.zooming&&(X=a.scale,P=O(o.touches))},W=()=>{if(y>1)return;const{offsetX:o,offsetY:t}=s,i=Date.now()-Z;o.value<5&&t.value<5&&i<250&&(Y?(clearTimeout(Y),Y=null,(()=>{const o=a.scale>1?1:2;x(o),a.moveX=0,a.moveY=0})()):Y=setTimeout((()=>{e("close"),Y=null}),250))},H=e=>{let t=!1;(a.moving||a.zooming)&&(t=!0,a.moving&&C===a.moveX&&I===a.moveY&&(t=!1),e.touches.length||(a.zooming&&(a.moveX=u(a.moveX,-w.value,w.value),a.moveY=u(a.moveY,-f.value,f.value),a.zooming=!1),a.moving=!1,C=0,I=0,X=1,a.scale<1&&b(),a.scale>o.maxZoom&&(a.scale=+o.maxZoom))),v(e,t),W(),s.reset()},S=o=>{const{naturalWidth:e,naturalHeight:s}=o.target;a.imageRatio=s/e};return r((()=>o.active),b),r((()=>o.show),(o=>{o||b()})),c("touchmove",(o=>{const{touches:e}=o;if(s.move(o),(a.moving||a.zooming)&&v(o,!0),a.moving){const{deltaX:o,deltaY:e}=s,t=o.value+C,i=e.value+I;a.moveX=u(t,-w.value,w.value),a.moveY=u(i,-f.value,f.value)}if(a.zooming&&2===e.length){const o=O(e);x(X*o/P)}}),{target:l((()=>{var o;return null==(o=g.value)?void 0:o.$el}))}),()=>{const e={loading:()=>m(d,{type:"spinner"},null)};return m(N,{ref:g,class:M("swipe-item"),onTouchstartPassive:z,onTouchend:H,onTouchcancel:H},{default:()=>[m(B,{src:o.src,fit:"contain",class:M("image",{vertical:h.value}),style:p.value,onLoad:S},e)]})}}});const[A,Q]=o("image-preview"),U=["show","transition","overlayStyle","closeOnPopstate"];var k=e({name:A,props:{show:Boolean,loop:C,images:I(),minZoom:X(1/3),maxZoom:X(3),overlay:C,closeable:Boolean,showIndex:C,className:P,closeIcon:Y("clear"),transition:String,beforeClose:Function,overlayClass:P,overlayStyle:Object,swipeDuration:X(300),startPosition:X(0),showIndicators:Boolean,closeOnPopstate:C,closeIconPosition:Y("top-right")},emits:["scale","close","closed","change","update:show"],setup(o,{emit:e,slots:a}){const s=n(),i=t({active:0,rootWidth:0,rootHeight:0}),l=()=>{if(s.value){const o=Z(s.value.$el);i.rootWidth=o.width,i.rootHeight=o.height,s.value.resize()}},c=o=>e("scale",o),v=o=>e("update:show",o),u=()=>{H(o.beforeClose,{args:[i.active],done:()=>v(!1)})},d=o=>{o!==i.active&&(i.active=o,e("change",o))},C=()=>{if(o.showIndex)return m("div",{class:Q("index")},[a.index?a.index({index:i.active}):`${i.active+1} / ${o.images.length}`])},I=()=>{if(a.cover)return m("div",{class:Q("cover")},[a.cover()])},X=()=>{if(o.closeable)return m(W,{role:"button",name:o.closeIcon,class:[Q("close-icon",o.closeIconPosition),z],onClick:u},null)},P=()=>e("closed"),Y=(o,e)=>{var a;return null==(a=s.value)?void 0:a.swipeTo(o,e)};return g({swipeTo:Y}),h(l),r([p,w],l),r((()=>o.startPosition),(o=>d(+o))),r((()=>o.show),(a=>{const{images:s,startPosition:t}=o;a?(d(+t),f((()=>{l(),Y(+t,{immediate:!0})}))):e("close",{index:i.active,url:s[i.active]})})),()=>m(y,x({class:[Q(),o.className],overlayClass:[Q("overlay"),o.overlayClass],onClosed:P,"onUpdate:show":v},b(o,U)),{default:()=>[X(),m(j,{ref:s,lazyRender:!0,loop:o.loop,class:Q("swipe"),duration:o.swipeDuration,initialSwipe:o.startPosition,showIndicators:o.showIndicators,indicatorColor:"white",onChange:d},{default:()=>[o.images.map((e=>m(q,{src:e,show:o.show,active:i.active,maxZoom:o.maxZoom,minZoom:o.minZoom,rootWidth:i.rootWidth,rootHeight:i.rootHeight,onScale:c,onClose:u},null)))]}),C(),I()]})}});let F;const J={loop:!0,images:[],maxZoom:3,minZoom:1/3,onScale:void 0,onClose:void 0,onChange:void 0,teleport:"body",className:"",showIndex:!0,closeable:!1,closeIcon:"clear",transition:void 0,beforeClose:void 0,overlayStyle:void 0,overlayClass:void 0,startPosition:0,swipeDuration:300,showIndicators:!1,closeOnPopstate:!0,closeIconPosition:"top-right"};const L=(o,e=0)=>{if(S)return F||({instance:F}=$({setup(){const{state:o,toggle:e}=D(),a=()=>{o.images=[]};return()=>m(k,x(o,{onClosed:a,"onUpdate:show":e}),null)}})),o=Array.isArray(o)?{images:o,startPosition:e}:o,F.open(R({},J,o)),F};L.Component=T(k),L.install=o=>{o.use(L.Component)};export{L as I};
