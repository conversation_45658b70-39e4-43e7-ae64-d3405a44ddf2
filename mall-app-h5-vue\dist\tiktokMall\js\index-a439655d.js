import{P as o,S as a,ai as r,R as s,d as e,r as i,Z as l,m as n,a8 as t,ad as d,g as c,b9 as u,aN as g,e as v,h,b$ as f,ag as m,s as p,I as b,X as w}from"./index-3d21abf8.js";const[S,y]=o("image");const z=w(e({name:S,props:{src:String,alt:String,fit:String,position:String,round:Boolean,block:Boolean,width:a,height:a,radius:a,lazyLoad:Boolean,iconSize:a,showError:r,errorIcon:s("photo-fail"),iconPrefix:String,showLoading:r,loadingIcon:s("photo")},emits:["load","error"],setup(o,{emit:a,slots:r}){const s=i(!1),e=i(!0),w=i(),{$Lazyload:S}=l().proxy,z=n((()=>{const a={width:t(o.width),height:t(o.height)};return d(o.radius)&&(a.overflow="hidden",a.borderRadius=t(o.radius)),a}));c((()=>o.src),(()=>{s.value=!1,e.value=!0}));const x=o=>{e.value=!1,a("load",o)},I=o=>{s.value=!0,e.value=!1,a("error",o)},L=(a,r,s)=>s?s():v(b,{name:a,size:o.iconSize,class:r,classPrefix:o.iconPrefix},null),$=()=>{if(s.value||!o.src)return;const a={alt:o.alt,class:y("img"),style:{objectFit:o.fit,objectPosition:o.position}};return o.lazyLoad?h(v("img",m({ref:w},a),null),[[f("lazy"),o.src]]):v("img",m({src:o.src,onLoad:x,onError:I},a),null)},P=({el:o})=>{const a=()=>{o===w.value&&e.value&&x()};w.value?a():p(a)},j=({el:o})=>{o!==w.value||s.value||I()};return S&&u&&(S.$on("loaded",P),S.$on("error",j),g((()=>{S.$off("loaded",P),S.$off("error",j)}))),()=>{var a;return v("div",{class:y({round:o.round,block:o.block}),style:z.value},[$(),e.value&&o.showLoading?v("div",{class:y("loading")},[L(o.loadingIcon,y("loading-icon"),r.loading)]):s.value&&o.showError?v("div",{class:y("error")},[L(o.errorIcon,y("error-icon"),r.error)]):void 0,null==(a=r.default)?void 0:a.call(r)])}}}));export{z as I};
