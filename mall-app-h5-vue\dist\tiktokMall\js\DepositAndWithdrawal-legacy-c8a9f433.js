System.register(["./index-legacy-46a00900.js","./index-legacy-6b2aee05.js","./index-legacy-73ef4548.js","./index-legacy-2aab34ff.js","./index-legacy-20dd4294.js","./use-id-legacy-df76950f.js","./use-route-legacy-be86ac1c.js","./index-legacy-15165887.js","./use-refs-legacy-0eab8d10.js"],(function(a,e){"use strict";var t,l,i,s,d,n,c,o,u,r,v,p,f,g,x,h,b,m,y,w,k,_,j,D=document.createElement("style");return D.textContent="[data-v-287180ca] .van-tabs__line{background:#2555F8}[data-v-287180ca] .van-tab{font-size:16px}.Record[data-v-287180ca]{padding-top:var(--van-nav-bar-height)}.tab-fixed[data-v-287180ca]{position:fixed;top:var(--van-nav-bar-height);width:100%;z-index:2}.tab-wrap .tab-item[data-v-287180ca]{width:90px;height:32px;text-align:center;line-height:31px;color:#868d9a;border-radius:20px}.tab-wrap .active[data-v-287180ca]{background:#2555F8;color:#fff}ul li .type[data-v-287180ca]{font-size:18px}ul li .money[data-v-287180ca]{font-weight:700}ul li .time[data-v-287180ca]{color:#868c9a}ul li .status[data-v-287180ca]{color:#868c9a;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.list-wrap[data-v-287180ca]{padding-top:var(--van-nav-bar-height)}.status-icon[data-v-287180ca]{width:8px;height:8px;border-radius:50%}.status-icon-color1[data-v-287180ca]{background:#2EBD85}.status-icon-color2[data-v-287180ca]{background:#EA0F0F}.status-icon-color3[data-v-287180ca]{background:#F5D658}\n",document.head.appendChild(D),{setters:[a=>{t=a._,l=a.Y,i=a.l,s=a.r,d=a.av,n=a.c,c=a.e,o=a.w,u=a.a,r=a.b,v=a.aV,p=a.F,f=a.y,g=a.o,x=a.f,h=a.n,b=a.t,m=a.D,y=a.E},a=>{w=a.P},a=>{k=a.L},a=>{_=a.T,j=a.a},()=>{},()=>{},()=>{},()=>{},()=>{}],execute:function(){const e=a=>(m("data-v-287180ca"),a=a(),y(),a),D={class:"Record pb-12"},R={class:"tab-fixed"},F={class:"list-wrap"},C={class:"tab-wrap flex px-4 mt-5"},U=[e((()=>u("div",{class:"flex justify-between"},[u("div",{class:"type"},"USD"),u("div",{class:"money"},"2000")],-1))),e((()=>u("div",{class:"flex mt-1 justify-between"},[u("div",{class:"time"},"2022-03-11 17:55:12"),u("div",{class:"status flex"},[u("div",{class:"status-icon status-icon-color2 mr-2"}),x(" 成功 ")])],-1)))];a("default",t({__name:"DepositAndWithdrawal",setup(a){l();const e=i();let t=s(0),m=s(0);const y=s([]),E=s(!1),V=s(!1),z=["充值","提现"],A=["外汇货币","数字货币"],T=()=>{},$=()=>{setTimeout((()=>{for(let a=0;a<10;a++)y.value.push(y.value.length+1);E.value=!1,y.value.length>=40&&(V.value=!0)}),1e3)},L=()=>{e.push("RecordDetails")};return(a,e)=>{const l=d("fx-header"),i=_,s=j,S=k,W=w;return g(),n("div",D,[c(l,{fixed:""},{title:o((()=>[x("充提记录")])),_:1}),u("div",R,[c(s,{active:r(t),"onUpdate:active":e[0]||(e[0]=a=>v(t)?t.value=a:t=a),onClickTab:T},{default:o((()=>[(g(),n(p,null,f(z,((a,e)=>c(i,{key:e,title:a},null,8,["title"]))),64))])),_:1},8,["active"])]),u("div",F,[u("div",C,[(g(),n(p,null,f(A,((a,e)=>u("div",{class:h(["tab-item mr-4",[r(m)===e?"active":""]]),key:e},b(a),3))),64))]),c(W,{"loading-text":a.$t("加载中"),"loosing-text":a.$t("释放以刷新"),"pulling-text":a.$t("下拉以刷新"),modelValue:E.value,"onUpdate:modelValue":e[2]||(e[2]=a=>E.value=a),onRefresh:a.onRefresh},{default:o((()=>[c(S,{loading:E.value,"onUpdate:loading":e[1]||(e[1]=a=>E.value=a),finished:V.value,"finished-text":"没有更多了",onLoad:$},{default:o((()=>[u("ul",null,[(g(!0),n(p,null,f(y.value,(e=>(g(),n("li",{class:"px-4 mt-10",key:a.index,onClick:L},U)))),128))])])),_:1},8,["loading","finished"])])),_:1},8,["loading-text","loosing-text","pulling-text","modelValue","onRefresh"])])])}}},[["__scopeId","data-v-287180ca"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/Record/DepositAndWithdrawal.vue"]]))}}}));
