System.register(["./index-legacy-46a00900.js","./index-legacy-72e00c5f.js","./use-route-legacy-be86ac1c.js"],(function(e,l){"use strict";var a,t,n,d,c,i,s,u,o,r,p,m,v,f,y,g=document.createElement("style");return g.textContent=".main[data-v-6dc2c842]{padding:var(--van-cell-group-inset-padding)}\n",document.head.appendChild(g),{setters:[e=>{a=e._,t=e.r,n=e.av,d=e.c,c=e.e,i=e.w,s=e.a,u=e.b,o=e.aV,r=e.L,p=e.o,m=e.f,v=e.D,f=e.E},e=>{y=e.B},()=>{}],execute:function(){const l={class:"h-full w-full bg-white"},g={class:"main"},x=(e=>(v("data-v-6dc2c842"),e=e(),f(),e))((()=>s("div",null,"Email",-1)));e("default",a({__name:"index",setup(e){let a=t("");const v=()=>{};return(e,t)=>{const f=n("fx-header"),_=n("ExInput"),h=y;return p(),d("div",l,[c(f,null,{title:i((()=>[m(" Email ")])),_:1}),s("div",g,[x,s("div",null,[c(_,{style:{"padding-bottom":"0!important"},placeholderText:"Please enter your email",modelValue:u(a),"onUpdate:modelValue":t[0]||(t[0]=e=>o(a)?a.value=e:a=e)},null,8,["modelValue"])]),c(h,{class:"w-full",type:u(a)?"primary ":"",style:r({marginTop:"10px",backgroundColor:u(a)?"#1552F0":"#F6F6F6",color:u(a)?"#fff":"#999"}),onClick:v},{default:i((()=>[m("Save")])),_:1},8,["type","style"])])])}}},[["__scopeId","data-v-6dc2c842"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/email/index.vue"]]))}}}));
