System.register(["./index-legacy-46a00900.js","./index-legacy-72e00c5f.js","./index-legacy-1fd93e33.js","./index-legacy-71866ecf.js","./index-legacy-f9c0699e.js","./index-legacy-f9c0699e2.js","./product.api-legacy-82d5f74f.js","./index-legacy-b65b115e.js","./index-legacy-9e9f7160.js","./function-call-legacy-3e53b389.js","./index-legacy-0ade4760.js"],(function(e,t){"use strict";var a,i,l,n,o,s,d,u,c,r,p,m,v,f,b,g,x,h,y,k,w,T,_,j,C,V,D,P,$,M,U,Y,I,S,F=document.createElement("style");return F.textContent=".editProduct[data-v-9e40474f]{background:#fff;color:#333}.editProduct.is-ar[data-v-9e40474f] .van-field__error-message,.editProduct.is-ar[data-v-9e40474f] .van-field__control{text-align:right}.editProduct .title[data-v-9e40474f]{text-align:center;line-height:55px;font-size:16px;font-weight:700}.editProduct .tip[data-v-9e40474f]{font-size:14px}.editProduct .tips[data-v-9e40474f]{font-size:12px;line-height:18px;color:#000}.editProduct .tips span[data-v-9e40474f]{color:#1552f0}.editProduct .input-field[data-v-9e40474f]{border:1px solid #ddd}.editProduct .input-field .profit[data-v-9e40474f]{color:#0ecb81}.editProduct .input-item[data-v-9e40474f]{-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.time-clear[data-v-9e40474f]{width:44px;height:44px;background-color:#fff;position:absolute;z-index:9;right:0;top:0;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center}.btn-content[data-v-9e40474f]{border-color:var(--site-main-color);background-color:var(--site-main-color)}\n",document.head.appendChild(F),{setters:[e=>{a=e._,i=e.j,l=e.r,n=e.u,o=e.i,s=e.Y,d=e.l,u=e.m,c=e.p,r=e.o,p=e.c,m=e.e,v=e.w,f=e.a,b=e.t,g=e.b,x=e.f,h=e.x,y=e.n,k=e.bA,w=e.ak,T=e.T,_=e.I,j=e.bn,C=e.D,V=e.E},e=>{D=e.B},()=>{},e=>{P=e.C},()=>{},()=>{},e=>{$=e.s,M=e.a},e=>{U=e.D},e=>{Y=e.F},e=>{I=e.D},e=>{S=e.F}],execute:function(){const t=e=>(C("data-v-9e40474f"),e=e(),V(),e),F={class:"edit-product-pop"},N={class:"title"},B={class:"tip pt-2 pb-2 pl-4 pr-4"},E=t((()=>f("span",null,"%",-1))),z={class:"tips pt-2 pb-2 pl-4 pr-4"},A={class:"tip pt-2 pb-2 pl-4 pr-4"},H={class:"tip pt-2 pb-2 pl-4 pr-4"},q={class:"tip pt-2 pb-2 pl-4 pr-4"},J=t((()=>f("span",null,"%",-1))),O={style:{margin:"16px"}},G={__name:"editProfit",props:{isEdit:Boolean,productArry:Array},emits:["back","update"],setup(e,{emit:t}){const a=e,C=i();l("true");const{t:V}=n(),G=o(),K=l(!1),L=l(!1),Q=l(!1),R=l(!1),W=l(new Date),X=l(""),Z=l(""),ee=l("");s();const te=d(),ae=l({startTime:"",endTime:"",discount:"",percent:""}),ie=u((()=>{let e={};return C.userInfo.token?e={...C.userInfo}:te.push("/login"),e})),le=e=>{switch(e){case 1:L.value=!0;break;case 2:Q.value=!0;break;case 3:R.value=!0}},ne=e=>{switch(e){case 1:L.value=!1,ae.value.startTime=k(X.value).format("YYYY-MM-DD");break;case 2:Q.value=!1,ae.value.endTime=k(Z.value).format("YYYY-MM-DD");break;case 3:R.value=!1,ae.value.recTime=re(ee.value)}},oe=e=>{switch(e){case 1:L.value=!1;break;case 2:Q.value=!1;break;case 3:R.value=!1}},se=e=>{if([].includes("tiktokMall")&&(!ie.value.phoneverif||!ie.value.emailverif)){const e=ie.value.phoneverif?"bindEmailTips":"请绑定手机号";return void I.confirm({title:V("dialogTips"),message:V(e),cancelButtonText:V("cancel"),confirmButtonText:V("gotoSet"),confirmButtonColor:"#1552F0",cancelButtonColor:"#999"}).then((()=>{w("/personalInfo")})).catch((()=>{}))}const{startTime:t,endTime:i,discount:l}=ae.value;if(t||i){const e=new Date(t.replace(/-/g,"/")+" 00:00:00").getTime(),a=new Date(i.replace(/-/g,"/")+" 00:00:00").getTime();if(!l)return void T(V("请设置折扣比例"));if(e>a)return void T(V("开始时间应小于结束时间"))}if(Number(l)&&(!t||!i))return void T(V("请正确填写活动开启时间和结束时间"));let n={goodsIds:a.productArry.join(","),...ae.value};if(ae.value.percent>de.max||ae.value.percent<de.min)T(V("百分比设置范围为")+`：${de.min}% ~ ${de.max}%`);else{const e=/^\+?[1-9][0-9]*$/;e.test(String(Number(ae.value.percent)))?Number(ae.value.discount)&&!e.test(String(Number(ae.value.discount)))?T(V("折扣比例必须为正整数")):(K.value=!0,n.startTime=n.startTime+" 00:00:00",n.endTime=n.endTime+" 00:00:00",n.profit=(ae.value.percent/100).toFixed(2),n.discount=(ae.value.discount/100).toFixed(2),n.percent=(ae.value.percent/100).toFixed(2),T.loading({forbidClick:!0}),M(n).then((()=>{T(V("上架成功")),ue()})).catch((e=>{const t="string"==typeof e.data?JSON.parse(e.data):e.data;t&&t._$1?T({message:V(e.msg,{_$1:t._$1}),duration:2e3}):T({message:V(e.msg),duration:2e3}),K.value=!1}))):T(V("百分比必须为正整数"))}},de=c({min:5,max:20});$().then((e=>{de.min=Number(e.sysParaMin),de.max=Number(e.sysParaMax)}));const ue=()=>{ce(),t("update")},ce=()=>{t("close");for(const e in ae.value)ae.value[e]="";setTimeout((()=>{K.value=!1}),500)},re=e=>e.getFullYear()+"-"+(e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-"+(e.getDate()<10?"0"+e.getDate():e.getDate())+" "+(e.getHours()<10?"0"+e.getHours():e.getHours())+":"+(e.getMinutes()<10?"0"+e.getMinutes():e.getMinutes())+":"+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds());return(e,t)=>{const i=S,l=P,n=_,o=U,s=j,d=D,u=Y;return r(),p("div",{class:y(["editProduct",{"is-ar":g(G)}])},[m(s,{show:a.isEdit,"onUpdate:show":t[16]||(t[16]=e=>a.isEdit=e),round:"",closeable:"",onClickCloseIcon:ce,style:{height:"65%",width:"95%"}},{default:v((()=>[f("div",F,[f("div",N,b(g(V)("添加商品")),1),m(u,null,{default:v((()=>[f("div",B,b(g(V)("product.25")),1),m(l,{class:"input-field",inset:""},{default:v((()=>[m(i,{modelValue:ae.value.percent,"onUpdate:modelValue":t[0]||(t[0]=e=>ae.value.percent=e),type:"number",placeholder:g(V)("百分比"),rules:[{required:!0,message:g(V)("请填写百分比"),max:de.max,min:de.min}]},{button:v((()=>[E])),_:1},8,["modelValue","placeholder","rules"])])),_:1}),f("div",z,[x(b(e.$t("将选中的商品发布到你的店铺，并填写商品利润比例，推荐比例"))+": ",1),f("span",null,b(de.min)+"%-"+b(de.max)+"%",1)]),f("div",A,b(e.$t("折扣开始日期")),1),m(l,{class:"input-field",inset:"",style:{position:"relative"}},{default:v((()=>[ae.value.startTime?(r(),p("div",{key:0,class:"time-clear",onClick:t[1]||(t[1]=e=>ae.value.startTime="")},[m(n,{name:"cross"})])):h("v-if",!0),m(i,{onClickInput:t[2]||(t[2]=e=>le(1)),modelValue:ae.value.startTime,"onUpdate:modelValue":t[3]||(t[3]=e=>ae.value.startTime=e),placeholder:g(V)("折扣开始日期")},null,8,["modelValue","placeholder"]),m(s,{show:L.value,"onUpdate:show":t[7]||(t[7]=e=>L.value=e),round:"",position:"bottom"},{default:v((()=>[m(o,{"min-date":W.value,"cancel-button-text":e.$t("取消"),"confirm-button-text":e.$t("确定"),modelValue:X.value,"onUpdate:modelValue":t[4]||(t[4]=e=>X.value=e),type:"date",title:g(V)("选择完整时间"),onConfirm:t[5]||(t[5]=e=>ne(1)),onCancel:t[6]||(t[6]=e=>oe(1))},null,8,["min-date","cancel-button-text","confirm-button-text","modelValue","title"])])),_:1},8,["show"])])),_:1}),f("div",H,b(e.$t("折扣结束日期")),1),m(l,{class:"input-field",inset:"",style:{position:"relative"}},{default:v((()=>[ae.value.endTime?(r(),p("div",{key:0,class:"time-clear",onClick:t[8]||(t[8]=e=>ae.value.endTime="")},[m(n,{name:"cross"})])):h("v-if",!0),m(i,{onClickInput:t[9]||(t[9]=e=>le(2)),modelValue:ae.value.endTime,"onUpdate:modelValue":t[10]||(t[10]=e=>ae.value.endTime=e),placeholder:g(V)("折扣结束日期")},null,8,["modelValue","placeholder"])])),_:1}),f("div",q,b(e.$t("折扣比例")),1),m(l,{class:"input-field",inset:""},{default:v((()=>[m(i,{modelValue:ae.value.discount,"onUpdate:modelValue":t[11]||(t[11]=e=>ae.value.discount=e),type:"number",placeholder:g(V)("折扣比例")},{button:v((()=>[J])),_:1},8,["modelValue","placeholder"])])),_:1}),m(s,{show:Q.value,"onUpdate:show":t[15]||(t[15]=e=>Q.value=e),round:"",position:"bottom"},{default:v((()=>[m(o,{"min-date":W.value,"cancel-button-text":e.$t("取消"),"confirm-button-text":e.$t("确定"),modelValue:Z.value,"onUpdate:modelValue":t[12]||(t[12]=e=>Z.value=e),type:"date",title:g(V)("选择完整时间"),onConfirm:t[13]||(t[13]=e=>ne(2)),onCancel:t[14]||(t[14]=e=>oe(2))},null,8,["min-date","cancel-button-text","confirm-button-text","modelValue","title"])])),_:1},8,["show"]),f("div",O,[m(d,{disabled:K.value,block:"",class:"btn-content",type:"primary",onClick:se,"native-type":"submit"},{default:v((()=>[x(b(g(V)("product.26")),1)])),_:1},8,["disabled"])])])),_:1})])])),_:1},8,["show"]),h(" <fx-header fixed>\n      <template #title>编辑商品</template>\n    </fx-header> "),h(' <van-popup v-model:show="show" position="bottom">\n      <div>\n        <van-date-picker v-model="fromData.recTime" title="选择日期"   ></van-date-picker>\n\n      </div>\n    </van-popup> ')],2)}}};e("e",a(G,[["__scopeId","data-v-9e40474f"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/product/components/editProfit.vue"]]))}}}));
