import{P as e,ai as a,S as t,a4 as i,d as l,r as s,p as o,aC as n,aH as u,m as r,a9 as v,g as c,aI as h,aJ as p,aK as f,q as d,at as g,aL as w,aM as m,aN as b,ac as y,e as C,aO as x,s as M,aP as $,V as z,aQ as S,X as T,a6 as I}from"./index-3d21abf8.js";const[O,P]=e("swipe"),X={loop:a,width:t,height:t,vertical:Boolean,autoplay:i(0),duration:i(500),touchable:a,lazyRender:Boolean,initialSwipe:i(0),indicatorColor:String,showIndicators:a,stopPropagation:a},Y=Symbol(O);const k=T(l({name:O,props:X,emits:["change"],setup(e,{emit:a,slots:t}){const i=s(),l=s(),T=o({rect:null,width:0,height:0,offset:0,active:0,swiping:!1}),I=n(),{children:O,linkChildren:X}=u(Y),k=r((()=>O.length)),D=r((()=>T[e.vertical?"height":"width"])),B=r((()=>e.vertical?I.deltaY.value:I.deltaX.value)),H=r((()=>{if(T.rect){return(e.vertical?T.rect.height:T.rect.width)-D.value*k.value}return 0})),R=r((()=>Math.ceil(Math.abs(H.value)/D.value))),j=r((()=>k.value*D.value)),q=r((()=>(T.active+k.value)%k.value)),A=r((()=>{const a=e.vertical?"vertical":"horizontal";return I.direction.value===a})),E=r((()=>{const a={transitionDuration:`${T.swiping?0:e.duration}ms`,transform:`translate${e.vertical?"Y":"X"}(${T.offset}px)`};if(D.value){const t=e.vertical?"height":"width",i=e.vertical?"width":"height";a[t]=`${j.value}px`,a[i]=e[i]?`${e[i]}px`:""}return a})),J=(a,t=0)=>{let i=a*D.value;e.loop||(i=Math.min(i,-H.value));let l=t-i;return e.loop||(l=S(l,H.value,0)),l},K=({pace:t=0,offset:i=0,emitChange:l})=>{if(k.value<=1)return;const{active:s}=T,o=(a=>{const{active:t}=T;return a?e.loop?S(t+a,-1,k.value):S(t+a,0,R.value):t})(t),n=J(o,i);if(e.loop){if(O[0]&&n!==H.value){const e=n<H.value;O[0].setOffset(e?j.value:0)}if(O[k.value-1]&&0!==n){const e=n>0;O[k.value-1].setOffset(e?-j.value:0)}}T.active=o,T.offset=n,l&&o!==s&&a("change",q.value)},L=()=>{T.swiping=!0,T.active<=-1?K({pace:k.value}):T.active>=k.value&&K({pace:-k.value})},N=()=>{L(),I.reset(),$((()=>{T.swiping=!1,K({pace:1,emitChange:!0})}))};let Q;const V=()=>clearTimeout(Q),W=()=>{V(),e.autoplay>0&&k.value>1&&(Q=setTimeout((()=>{N(),W()}),+e.autoplay))},F=(a=+e.initialSwipe)=>{if(!i.value)return;const t=()=>{var t,l;if(!x(i)){const a={width:i.value.offsetWidth,height:i.value.offsetHeight};T.rect=a,T.width=+(null!=(t=e.width)?t:a.width),T.height=+(null!=(l=e.height)?l:a.height)}k.value&&(a=Math.min(k.value-1,a)),T.active=a,T.swiping=!0,T.offset=J(a),O.forEach((e=>{e.setOffset(0)})),W()};x(i)?M().then(t):t()},G=()=>F(T.active);let U;const Z=a=>{e.touchable&&(I.start(a),U=Date.now(),V(),L())},_=()=>{if(!e.touchable||!T.swiping)return;const a=Date.now()-U,t=B.value/a;if((Math.abs(t)>.25||Math.abs(B.value)>D.value/2)&&A.value){const a=e.vertical?I.offsetY.value:I.offsetX.value;let t=0;t=e.loop?a>0?B.value>0?-1:1:0:-Math[B.value>0?"ceil":"floor"](B.value/D.value),K({pace:t,emitChange:!0})}else B.value&&K({pace:0});T.swiping=!1,W()},ee=(a,t)=>{const i=t===q.value,l=i?{backgroundColor:e.indicatorColor}:void 0;return C("i",{style:l,class:P("indicator",{active:i})},null)};return v({prev:()=>{L(),I.reset(),$((()=>{T.swiping=!1,K({pace:-1,emitChange:!0})}))},next:N,state:T,resize:G,swipeTo:(a,t={})=>{L(),I.reset(),$((()=>{let i;i=e.loop&&a===k.value?0===T.active?0:a:a%k.value,t.immediate?$((()=>{T.swiping=!1})):T.swiping=!1,K({pace:i-T.active,emitChange:!0})}))}}),X({size:D,props:e,count:k,activeIndicator:q}),c((()=>e.initialSwipe),(e=>F(+e))),c(k,(()=>F(T.active))),c((()=>e.autoplay),W),c([h,p],G),c(f(),(e=>{"visible"===e?W():V()})),d(F),g((()=>F(T.active))),w((()=>F(T.active))),m(V),b(V),y("touchmove",(a=>{if(e.touchable&&T.swiping&&(I.move(a),A.value)){!e.loop&&(0===T.active&&B.value>0||T.active===k.value-1&&B.value<0)||(z(a,e.stopPropagation),K({offset:B.value}))}}),{target:l}),()=>{var a;return C("div",{ref:i,class:P()},[C("div",{ref:l,style:E.value,class:P("track",{vertical:e.vertical}),onTouchstartPassive:Z,onTouchend:_,onTouchcancel:_},[null==(a=t.default)?void 0:a.call(t)]),t.indicator?t.indicator({active:q.value,total:k.value}):e.showIndicators&&k.value>1?C("div",{class:P("indicators",{vertical:e.vertical})},[Array(k.value).fill("").map(ee)]):void 0])}}})),[D,B]=e("swipe-item");const H=T(l({name:D,setup(e,{slots:a}){let t;const i=o({offset:0,inited:!1,mounted:!1}),{parent:l,index:s}=I(Y);if(!l)return;const n=r((()=>{const e={},{vertical:a}=l.props;return l.size.value&&(e[a?"height":"width"]=`${l.size.value}px`),i.offset&&(e.transform=`translate${a?"Y":"X"}(${i.offset}px)`),e})),u=r((()=>{const{loop:e,lazyRender:a}=l.props;if(!a||t)return!0;if(!i.mounted)return!1;const o=l.activeIndicator.value,n=l.count.value-1,u=0===o&&e?n:o-1,r=o===n&&e?0:o+1;return t=s.value===o||s.value===u||s.value===r,t}));return d((()=>{M((()=>{i.mounted=!0}))})),v({setOffset:e=>{i.offset=e}}),()=>{var e;return C("div",{class:B(),style:n.value},[u.value?null==(e=a.default)?void 0:e.call(a):null])}}}));export{H as S,k as a};
