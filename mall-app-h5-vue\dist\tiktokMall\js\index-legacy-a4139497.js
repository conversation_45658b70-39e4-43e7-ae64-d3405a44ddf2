System.register(["./index-legacy-46a00900.js","./index-legacy-6b2aee05.js","./index-legacy-73ef4548.js","./index-legacy-8cf82a64.js","./index-legacy-8ad4c0d7.js","./index-legacy-f9c0699e.js","./index-legacy-f9c0699e2.js","./index-legacy-ff56f089.js","./search-icon-legacy-91a4d779.js","./more-legacy-4ed4bf86.js","./editProfit-legacy-0c93f5c4.js","./product.api-legacy-82d5f74f.js","./index-legacy-b248d96d.js","./use-id-legacy-df76950f.js","./use-placeholder-legacy-f22ccc27.js","./index-legacy-0ade4760.js","./index-legacy-df3e01aa.js","./use-route-legacy-be86ac1c.js","./index-legacy-72e00c5f.js","./index-legacy-1fd93e33.js","./index-legacy-71866ecf.js","./index-legacy-b65b115e.js","./index-legacy-9e9f7160.js","./function-call-legacy-3e53b389.js","./index-legacy-a4cde014.js","./stringify-legacy-93b715ae.js","./___vite-browser-external_commonjs-proxy-legacy-70ac8ee8.js"],(function(e,t){"use strict";var a,i,l,n,s,o,c,r,d,p,f,v,g,u,h,b,x,m,y,k,w,A,j,C,_,L,S,z,U,N,E,O,F,J,B,I,T,V,G,H,Y,M,P,R=document.createElement("style");return R.textContent="[data-v-77847b5f] .van-field__body{padding-right:10px}.search-container[data-v-77847b5f]{padding-top:50px}.search-container.is-ar[data-v-77847b5f] .van-field__left-icon{margin-right:0;margin-left:8px}.search-container.is-ar[data-v-77847b5f] .van-search__content{padding-left:0;padding-right:12px}.search-container.is-ar[data-v-77847b5f] .van-field__control{text-align:right}.search-container.is-ar .search-history>.title>.clear>img[data-v-77847b5f]{margin-right:0;margin-left:5px}.search-container.is-ar .product-info[data-v-77847b5f]{padding-left:0;padding-right:10px}.search-container[data-v-77847b5f] .van-field__left-icon{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;width:16px;margin-right:8px}.search-container[data-v-77847b5f] .van-field__left-icon>img{width:100%;height:auto}.search-container[data-v-77847b5f] .van-nav-bar__left{padding-right:10px!important}.search-container[data-v-77847b5f] .van-nav-bar__title{width:73.6%!important;max-width:73.6%!important;position:relative}.search-container[data-v-77847b5f] .van-search{padding:0!important}.search-container[data-v-77847b5f] .van-search .van-search__content{background-color:#fff}.search-container .search-btn[data-v-77847b5f]{font-size:12px;color:#333}.search-container .search-history[data-v-77847b5f]{padding:0 15px}.search-container .search-history>.title[data-v-77847b5f]{padding:25px 0;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between;font-size:14px;color:#000}.search-container .search-history>.title>.clear[data-v-77847b5f]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.search-container .search-history>.title>.clear>img[data-v-77847b5f]{width:14px;height:auto;margin-right:5px}.search-container .search-history>.title>.clear>p[data-v-77847b5f]{font-size:14px;color:#333}.search-container .search-history>.content[data-v-77847b5f]{overflow:hidden}.search-container .search-history>.content.no[data-v-77847b5f]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;font-size:14px;color:#999}.search-container .search-history>.content>.item[data-v-77847b5f]{float:left;padding:4px 15px;background-color:#fff;border-radius:5px;color:#999;font-size:12px;margin-right:14px;margin-bottom:22px}.search-container .search-tips-content>.item[data-v-77847b5f]{padding:23px 15px;border-bottom:1px solid #eee;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.search-container .search-tips-content>.item .van-icon[data-v-77847b5f]{margin-right:5px}.search-container .search-tips-content>.item>p[data-v-77847b5f]{color:#333;font-size:12px}.search-container .shop-list-content[data-v-77847b5f]{padding:20px 15px}.search-container .shop-list-content>.tips[data-v-77847b5f]{font-size:12px;color:#333}.search-container .shop-list-content>.tips>span[data-v-77847b5f]{color:#f89900}[data-v-77847b5f] .van-icon{font-size:18px;color:#1f2025}.delete-icon[data-v-77847b5f]{width:15px}.list .item[data-v-77847b5f]{background:#FFFFFF;border-radius:4px;margin-bottom:20px}.list .item .more-icon[data-v-77847b5f]{width:20px}.list .item .product-img[data-v-77847b5f]{width:100px}.list .item .left[data-v-77847b5f]{-webkit-box-align:center;-webkit-align-items:center;align-items:center}.list .item .left .product-info[data-v-77847b5f]{padding-left:10px;-webkit-box-flex:1;-webkit-flex:1 1;flex:1 1}.list .item .left .product-info .name[data-v-77847b5f]{font-size:14px;color:#333;height:50px;font-weight:700;overflow:hidden;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;-ms-text-overflow:ellipsis;text-overflow:ellipsis}.list .item .left .product-info .Specification[data-v-77847b5f]{font-size:12px;color:#999}.list .item .left .product-info .money[data-v-77847b5f]{color:var(--site-main-color);font-weight:700}.list .product-img-wrap[data-v-77847b5f]{position:relative}.list .delete-wrap[data-v-77847b5f]{padding:0 15px;background:rgba(0,0,0,.6);position:absolute;left:0;top:0;font-size:12px;color:#fff}.result-list[data-v-77847b5f]{position:fixed;top:46px;left:0;width:100%;background:#fff;z-index:2;font-size:14px}.result-list .result-list-item[data-v-77847b5f]{border-bottom:1px solid #EFF2F6}.item-more-content[data-v-77847b5f]{width:40px;position:relative}.item-more-content>.more[data-v-77847b5f]{width:40px;height:40px;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:end;-webkit-justify-content:flex-end;justify-content:flex-end;-webkit-box-align:start;-webkit-align-items:flex-start;align-items:flex-start;padding-top:5px}\n",document.head.appendChild(R),{setters:[e=>{a=e._,i=e.i,l=e.u,n=e.l,s=e.Y,o=e.r,c=e.aX,r=e.q,d=e.cB,p=e.I,f=e.c,v=e.e,g=e.w,u=e.F,h=e.y,b=e.x,x=e.a,m=e.t,y=e.h,k=e.aS,w=e.b,A=e.A,j=e.n,C=e.cC,_=e.T,L=e.o,S=e.aV,z=e.aF,U=e.K,N=e.D,E=e.E,O=e.cD},e=>{F=e.P},e=>{J=e.L},e=>{B=e.E},e=>{I=e.N},()=>{},()=>{},e=>{T=e.S},e=>{V=e._},e=>{G=e._},e=>{H=e.e},e=>{Y=e.f,M=e.g},e=>{P=e.c},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{}],execute:function(){const t=e=>(N("data-v-77847b5f"),e=e(),E(),e),R=t((()=>x("img",{class:"search-icon",src:V},null,-1))),K=t((()=>x("div",{class:"fixed-header-spacer"},null,-1))),q={key:0,class:"result-list pl-4 pr-4"},D=["onClick"],Q={key:1,class:"search-history"},X={class:"title"},$=t((()=>x("img",{src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAwCAYAAABqkJjhAAAACXBIWXMAACE4AAAhOAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAKESURBVHgB7Zk9btswFMefZHu0obGeqt7AtT14q3qC+gZtTpD4BE5u0Gzd7J6g6tipymbAH0lOEGXyGO2G7fwZUAmT6ItiqMAAf4BASnoE/6RIPurRIkU6nY6LxC1oHl0BUsCikvR6vSGSMa6OTLn9fh8iOVutVlMqQY1K0O12x5Zl/UL2A0mCcg6uYbvddtbr9T+SRLqHIfYHKpwIjyL02i1L88qi3EcShg/KjdDTP0mCMoJvULHLb/1ms3kUBEGu2Jh+v38KoWN+G223208Y1oXL2yQBm2CC2KjRaIxkxDIWi8UpPX0Np16vS80BKcH0/HNez2azkMrxN87sdjuXJJAV/O4cnOCHSYex6dm27eUZYxi4sPvOb0Pc/6YSYB58o6f122fDK68Mhg7zOb7FJlKtVruhAwAN/cqGhEMHAr6E8zAkmDMgYQVAS46JNwRG51TAKbwVqHssCDwTXoXMnSc6DjQg5F6J+MIeUkVgj7KP88vl8pU+s6zpxgjWjRGsmzopAk/J1mv2uxSkLX/8v8/LsimKcg/DrU/4dcnFv67Etv9zmz+kiLJgeKPPPOtQipsXNv1Sm/UkzKTTjRGsGyNYN0awboxg3RjBulEWjI1NkSBM9CItjbJgbB1H2LFdQPgoY697xGyYLSmivIGfz+fskMXLskF8wUfi0xtgJp1ujGDdJArGjL+L8zg0caki+N91TOISmNbDjxFxLEfHVBFCdJ/SovKJyxp6eIoCceEhwq8ThOxLHQ8UxIHYL6j3RHg2TTJMPVhEnJadUFbWuyLMySB47SW9S510WOxZa8+pevxWqzVMe5l7dDsYDNzNZnOCVisHQbLAcGBR/ylOSoMsu3tYs9QwhJVNvQAAAABJRU5ErkJggg==",alt:"",class:"delete-icon"},null,-1))),W={key:0,class:"content"},Z=["onClick"],ee={key:2,class:"list ml-4 mr-4 mt-4 mb-4"},te=["onClick"],ae={class:"flex-1 flex left"},ie={class:"product-img-wrap w-20 h-20",style:{overflow:"hidden"}},le=["src"],ne={class:"product-info"},se={class:"name"},oe={class:"Specification"},ce={key:0,style:{"margin-right":"20px"}},re={key:1},de={class:"money"},pe={class:"item-more-content"},fe=["onClick"],ve=[t((()=>x("img",{class:"more-icon",src:G},null,-1)))],ge={__name:"index",setup(e){const t=i(),{t:a}=l(),N=n(),E=s(),V=o(E.query.id);let G=o(""),ge=o(1),ue=o(""),he=o(!1);o({});const be=o([]),xe=o([]),me=o(!1),ye=o(!1),ke=o(!1);let we=o([]);const Ae=()=>{},je=new URL("/www/png/name-20d65991.png",self.location),Ce=o(null);c((()=>{Ce.value})),r((()=>{V.value/1==1?d("searchOne")&&(we.value=d("searchOne")):d("searchTwo")&&(we.value=d("searchTwo"))}));const _e=O((()=>{G.value.trim()?(ge.value=1,be.value=[],_.loading({forbidClick:!0,loadingType:"spinner"}),Je(),Le()):_({message:a("请输入搜索关键字"),duration:2e3})}),500),Le=()=>{const e=G.value.trim(),t=V.value/1==1?"searchOne":"searchTwo";if(""!=e)if(0==we.value.length)we.value.push(e),C(t,we.value);else{const a=P(we.value);a.unshift(e);const i=[...new Set(a)];we.value=i,C(t,i)}},Se=()=>{V.value/1==1?C("searchOne",[]):C("searchTwo",[])},ze=()=>{G.value=""},Ue=e=>{we.value=[],V.value/1==1?C("searchOne",""):C("searchTwo","")},Ne=()=>{me.value=!1,_e()},Ee=()=>{let e={pageNum:ge.value,pageSize:20,keyword:G.value};Y(e).then((e=>{_.clear(),ge.value++;for(let t=0;t<e.pageList.length;t++)e.pageList[t].check=!1,be.value.push(e.pageList[t]);ye.value=!1,e.pageList.length<20?(ge.value>1&&_(a("没有更多数据")),ke.value=!0):ge.value++}))},Oe=()=>{let e={name:G.value,categoryId:"",pageNum:ge.value,pageSize:20};M(e).then((e=>{_.clear();for(let t=0;t<e.pageList.length;t++)be.value.push(e.pageList[t]);ye.value=!1,e.pageList.length<20?(ge.value>1&&_(a("没有更多数据")),ke.value=!0):ge.value++}))},Fe=o(null);V.value/1==2?Fe.value=Oe:Fe.value=Ee;const Je=()=>{Fe.value()};let Be=o([]);const Ie=(e,t)=>{e.sellingPrice=t},Te=()=>{he.value=!1};return(e,i)=>{const l=p,n=T,s=I,o=B,c=J,r=F;return L(),f("div",{class:j(["search-container page-main-content has-fixed-header",{"is-ar":w(t)}])},[v(s,{fixed:"","left-arrow":"",onClickLeft:i[2]||(i[2]=()=>e.$router.back())},{title:g((()=>[b('        <van-search v-model="keyword" shape="round" @blur="record" @update:model-value="search" :clearable="false"'),b('          placeholder="请输入商品" @input="inputHandle">'),v(n,{modelValue:w(G),"onUpdate:modelValue":i[0]||(i[0]=e=>S(G)?G.value=e:G=e),shape:"round",clearable:!1,placeholder:e.$t("请输入商品"),onInput:Ae},{"left-icon":g((()=>[R])),"right-icon":g((()=>[w(G)?(L(),A(l,{key:0,name:"cross",onClick:ze,size:"14",color:"#333333"})):b("v-if",!0)])),_:1},8,["modelValue","placeholder"])])),right:g((()=>[x("div",{onClick:i[1]||(i[1]=(...e)=>w(_e)&&w(_e)(...e))},m(e.$t("搜索")),1)])),_:1}),K,xe.value.length>0?(L(),f("div",q,[(L(!0),f(u,null,h(xe.value,((e,t)=>(L(),f("div",{class:"result-list-item pt-2 pb-2",onClick:t=>(e=>{G.value="",xe.value=[],ue.value=e.goodsId,ge.value=1,be.value=[],Je()})(e),key:t},m(e.name),9,D)))),128))])):b("v-if",!0),0==be.value.length?(L(),f("div",Q,[x("div",X,[x("p",null,m(e.$t("历史搜索")),1),y(x("div",{class:"clear",onClick:Se},[$,x("p",{onClick:Ue},m(e.$t("清空")),1)],512),[[k,w(we).length>0]])]),w(we).length>0?(L(),f("div",W,[(L(!0),f(u,null,h(w(we),((e,t)=>(L(),f("div",{key:t,class:"item",onClick:t=>(e=>{G.value=e,_e()})(e)},m(e),9,Z)))),128))])):b("v-if",!0),w(we).length?b("v-if",!0):(L(),A(o,{key:1,image:w(je).href,description:w(a)("noData")},null,8,["image","description"]))])):b("v-if",!0),be.value.length>0?(L(),f("div",ee,[v(r,{"loading-text":e.$t("加载中"),"loosing-text":e.$t("释放以刷新"),"pulling-text":e.$t("下拉以刷新"),modelValue:ye.value,"onUpdate:modelValue":i[4]||(i[4]=e=>ye.value=e),onRefresh:Ne},{default:g((()=>[v(c,{ref_key:"checkEl",ref:Ce,loading:ye.value,"onUpdate:loading":i[3]||(i[3]=e=>ye.value=e),finished:ke.value,"finished-text":e.$t("没有更多了"),onLoad:Je},{default:g((()=>[(L(!0),f(u,null,h(be.value,((e,t)=>(L(),f("div",{class:"item pl-3 pr-3 pb-3 pt-3 flex",onClick:t=>(e=>{V.value/1==1&&N.push({path:"/productPage/details",query:{item:JSON.stringify(e)}})})(e),key:t},[x("div",ae,[x("div",ie,[x("img",{class:"product-img",src:e.imgUrl1},null,8,le),b('                <div class="delete-wrap" @click.stop="deleteGood(item)">删除</div>')]),x("div",ne,[x("div",se,m(e.name),1),x("div",oe,[e.categoryName?(L(),f("span",ce,m(e.categoryName),1)):b("v-if",!0),b(" <span>{{t('product.4')}}: {{ item.unit || '-' }}</span> "),V.value/1==1?(L(),f("span",re,m(w(a)("sales"))+": "+m(e.soldNum),1)):b("v-if",!0)]),x("div",de,"$"+m(V.value/1==1?w(z)(e.sellingPrice):w(z)(e.systemPrice)),1)])]),x("div",pe,[x("div",{class:"more",onClick:U((t=>(e=>{V.value/1==1?N.push({path:"/productPage/productEdit",query:{item:JSON.stringify(e)}}):(Be.value=[],Be.value.push(e.id),he.value=!0)})(e)),["stop"])},ve,8,fe),V.value/1==1?(L(),A(l,{key:0,name:"arrow",size:"20px",style:{top:"50%",right:"0",position:"absolute","margin-top":"-10px"}})):b("v-if",!0)])],8,te)))),128))])),_:1},8,["loading","finished","finished-text"])])),_:1},8,["loading-text","loosing-text","pulling-text","modelValue"])])):b("v-if",!0),v(H,{isEdit:w(he),onUpdate:Ie,productArry:w(Be),onClose:Te},null,8,["isEdit","productArry"])],2)}}};e("default",a(ge,[["__scopeId","data-v-77847b5f"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/search/index.vue"]]))}}}));
