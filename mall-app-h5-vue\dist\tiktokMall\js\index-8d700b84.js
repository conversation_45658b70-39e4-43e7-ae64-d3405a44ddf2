import{d as e,_ as a,u as l,k as t,m as i,r as s,o as n,c as o,a as u,b as c,t as r,e as v,F as d,y as m,n as p,I as f,au as g,j as h,i as y,p as b,g as k,x as w,f as x,C as N,w as C,T,G as z,A as _,H as S,Y as I,J as j,s as V,av as $,W as M,L as D,cg as L,b6 as E,an as A,aF as P,D as U,E as q}from"./index-3d21abf8.js";import{N as B}from"./index-2ef790cf.js";/* empty css              */import{u as O}from"./index-54dce367.js";import{l as H}from"./config-f57d91a6.js";import{l as W,a as F,r as G,b as J,p as R,c as Y,d as K}from"./activity.api-7fdb97d8.js";import{B as Q}from"./index-2406f514.js";import{I as X}from"./index-8840aba2.js";import{n as Z}from"./login.api-cb7fcde3.js";import{S as ee,a as ae}from"./index-0d6a7179.js";import"./use-route-cd41a893.js";/* empty css              *//* empty css               */import"./index.vue_vue_type_style_index_0_scoped_efb302ad_lang-ae7957c5.js";import"./index-8c1841f6.js";import"./index-7dfcb82a.js";import"./use-id-a0619e01.js";const le={class:"language-container"},te={class:"nation"},ie=["src"],se=["onClick"],ne=["src"],oe=e({name:"LanguageChange"}),ue=a(Object.assign(oe,{setup(e){const{locale:a}=l(),g=t(),h=i((()=>{const e=a.value||"en";return H.find((a=>a.key===e))})),y=i((()=>a.value||"en")),b=s(!1);return(e,l)=>{const t=f;return n(),o("div",le,[u("div",{class:"current",onClick:l[0]||(l[0]=e=>b.value=!b.value)},[u("div",te,[u("img",{src:c(h).image.href,alt:""},null,8,ie)]),u("p",null,r(c(h).title),1),v(t,{name:"arrow-down",class:"icon"})]),u("div",{class:p([{active:b.value},"select-content"])},[(n(!0),o(d,null,m(c(H),(e=>(n(),o("div",{key:e.key,class:p([{active:e.key===c(y)},"item"]),onClick:l=>(e=>{const l="ar"===e?"rtl":"rtr";document.documentElement.setAttribute("dir",l),g.setIsArLang("ar"===e),g.setNotCnLang(!["cn","tw"].includes(e)),a.value=e,localStorage.setItem("lang",e),document.dispatchEvent(new CustomEvent("langChange")),b.value=!1})(e.key)},[u("img",{src:e.image.href,alt:""},null,8,ne),u("p",null,r(e.title),1),v(t,{name:"success",class:"icon"})],10,se)))),128))],2),u("div",{class:p([{active:b.value},"bg"]),onClick:l[1]||(l[1]=e=>b.value=!1)},null,2)])}}}),[["__scopeId","data-v-681eb37b"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/components/language-change/index.vue"]]),ce=g("activity/turntable/block1.png"),re=g("activity/turntable/block2.png"),ve=["a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z"],de=[0,1,2,3,4,5,6,7,8,9],me=["gmail.com","yahoo.com","hotmail.com","outllok.com","qq.com","163.com","126.com","sina.com","sohu.com","139.com","189.com","360.com","21cn.com","139.com","189.cn","wo.cn","aol.com","protonmail.com","zoho.com","icloud.com","mail.ru","t-online.de","orange.fr","yahoo.co.uk","libero.it","yandex.ru","yahoo.com.sg","naver.com","saudi.net","rediffmail.com","indiatimes.com","yahoo.co.in","sify.com","naukri.com"],pe=s([{x:0,y:0,fonts:[{text:"",fontColor:"#ffffff",fontSize:"16px",top:"70%"}],imgs:[{src:ce,width:"100%",height:"100%",activeSrc:re}]},{x:1,y:0,fonts:[{text:"",fontColor:"#ffffff",fontSize:"16px",top:"70%"}],imgs:[{src:ce,width:"100%",height:"100%",activeSrc:re}]},{x:2,y:0,fonts:[{text:"",fontColor:"#ffffff",fontSize:"16px",top:"70%"}],imgs:[{src:ce,width:"100%",height:"100%",activeSrc:re}]},{x:2,y:1,fonts:[{text:"",fontColor:"#ffffff",fontSize:"16px",top:"70%"}],imgs:[{src:ce,width:"100%",height:"100%",activeSrc:re}]},{x:2,y:2,fonts:[{text:"",fontColor:"#ffffff",fontSize:"16px",top:"70%"}],imgs:[{src:ce,width:"100%",height:"100%",activeSrc:re}]},{x:1,y:2,fonts:[{text:"",fontColor:"#ffffff",fontSize:"16px",top:"70%"}],imgs:[{src:ce,width:"100%",height:"100%",activeSrc:re}]},{x:0,y:2,fonts:[{text:"",fontColor:"#ffffff",fontSize:"16px",top:"70%"}],imgs:[{src:ce,width:"100%",height:"100%",activeSrc:re}]},{x:0,y:1,fonts:[{text:"",fontColor:"#ffffff",fontSize:"16px",top:"70%"}],imgs:[{src:ce,width:"100%",height:"100%",activeSrc:re}]},{x:1,y:1,fonts:[{text:"",fontColor:"#ffffff",fontSize:"16px",top:"70%"}],imgs:[{src:ce,width:"100%",height:"100%",activeSrc:re}]}]),fe={class:"login-content"},ge={class:"dialog-title"},he={class:"type-tab"},ye=["onClick"],be={class:"form-content"},ke={key:0,class:"item"},we={key:1,class:"item"},xe={class:"item"},Ne={class:"btn"},Ce=e({name:"LoginDialog"}),Te=a(Object.assign(Ce,{props:{modelValue:{type:Boolean,default:!1}},emits:["update:modelValue","done"],setup(e,{emit:a}){const t=e,{t:i,locale:f}=l(),g=h(),_=y(),S=()=>{a("update:modelValue",!1)},I=["email","phoneNum"],j=s(0),V=s(!1),$=localStorage.getItem("areaCode")?localStorage.getItem("areaCode"):44,M=s($),D=b({username:"",password:""}),L=()=>{const{hostname:e,origin:a}=window.location,l="localhost"===e?"https://tkittkit.com/promote/#/":`${a}/promote/#/`;window.open(`${l}?lang=${f.value}`)},E=()=>{const e=0===j.value?"entryEmail":"entryPhone";return""===D.username?(T(i(e)),!1):""===D.password?(T(i("entryPassword")),!1):(V.value=!0,void Z({username:0==j.value?D.username:`${M.value} ${D.username}`,password:D.password}).then((async e=>{await g[z](e),T(i("loginSuc")),V.value=!1,setTimeout((()=>{S(),a("done")}),1500)})).catch((e=>{V.value=!1})))};return k((()=>t.modelValue),(e=>{e||(j.value=0,D.username="",D.password="")})),(a,l)=>{const t=Q;return n(),o("div",{class:p([{active:e.modelValue},"login-dialog"])},[u("div",fe,[u("div",ge,r(c(i)("卖家登录")),1),u("div",he,[(n(),o(d,null,m(I,((e,a)=>u("div",{key:a,class:p([{active:a===j.value},"item"]),onClick:e=>(e=>{e!==j.value&&(j.value=e,D.username="",D.password="")})(a)},r(c(i)(e)),11,ye))),64))]),u("div",be,[0===j.value?(n(),o("div",ke,[v(X,{modelValue:D.username,"onUpdate:modelValue":l[0]||(l[0]=e=>D.username=e),type:"text","is-turn":!0,placeholder:c(i)("entryEmail"),clear:""},null,8,["modelValue","placeholder"])])):w("v-if",!0),1===j.value?(n(),o("div",we,[v(X,{modelValue:D.username,"onUpdate:modelValue":l[1]||(l[1]=e=>D.username=e),codeNum:M.value,"onUpdate:codeNum":l[2]||(l[2]=e=>M.value=e),type:"tel","is-turn":!0,placeholder:c(i)("entryPhone"),clear:"","area-code":""},null,8,["modelValue","codeNum","placeholder"])])):w("v-if",!0),u("div",xe,[v(X,{modelValue:D.password,"onUpdate:modelValue":l[3]||(l[3]=e=>D.password=e),type:"password","is-turn":!0,placeholder:c(i)("entryPassword"),clear:"","show-password":""},null,8,["modelValue","placeholder"])]),u("div",{class:p(["link-content",{"is-ar":c(_)}])},[u("p",null,[x(r(c(i)("如果您没有账号"))+", ",1),u("span",{class:"link",onClick:L},r(c(i)("点击注册")),1)]),u("p",{onClick:l[4]||(l[4]=e=>c(N)(!0)),class:"link"},r(c(i)("forgetPassword")),1)],2),u("div",Ne,[v(t,{type:"custom",loading:V.value,block:"",onClick:E},{default:C((()=>[x(r(c(i)("login")),1)])),_:1},8,["loading"])])])]),u("div",{class:"login-bg",onClick:S})],2)}}}),[["__scopeId","data-v-88e63e2d"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/activityCenter/turntable/components/LoginDialog.vue"]]),ze={class:"tips-content"},_e={class:"dialog-title"},Se={class:"info-content"},Ie={class:"txt-content"},je={key:0},Ve={key:1},$e={class:"btn"},Me=e({name:"TipsDialog"}),De=a(Object.assign(Me,{props:{modelValue:{type:Boolean,default:!1},tipsTitle:{type:String,default:""},tipsInfo:{type:String,default:""},again:{type:Boolean,default:!1},times:{type:Number,default:0}},emits:["update:modelValue","again"],setup(e,{emit:a}){const t=e,{t:i}=l(),s=()=>{a("update:modelValue",!1)},v=()=>{t.times?m():s()},d=()=>{t.times||s()},m=()=>{s(),a("again")};return(a,l)=>{const t=Q;return n(),o("div",{class:p([{active:e.modelValue},"tips-dialog"])},[u("div",ze,[u("div",_e,r(c(i)("提示")),1),u("div",Se,[u("div",Ie,[e.tipsTitle?(n(),o("p",je,r(e.tipsTitle),1)):w("v-if",!0),e.tipsInfo?(n(),o("h2",Ve,r(e.tipsInfo),1)):w("v-if",!0)]),u("div",$e,[e.again?(n(),_(t,{key:0,type:"custom",block:"",onClick:m},{default:C((()=>[x(r(c(i)("再抽一次")),1)])),_:1})):(n(),_(t,{key:1,type:"custom",block:"",onClick:v},{default:C((()=>[x(r(e.times?c(i)("确定"):c(i)("知道了")),1)])),_:1})),e.again?(n(),_(t,{key:2,type:"cancel",block:"",onClick:s},{default:C((()=>[x(r(c(i)("取消")),1)])),_:1})):w("v-if",!0)])])]),u("div",{class:"tips-bg",onClick:d})],2)}}}),[["__scopeId","data-v-6ca20a48"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/activityCenter/turntable/components/TipsDialog.vue"]]),Le=e=>(U("data-v-2b7b83b6"),e=e(),q(),e),Ee={class:"turntable-page"},Ae={class:"audio-container"},Pe=["src"],Ue=["src"],qe=["src"],Be=["src"],Oe=["src"],He={key:0,class:"turntable-warp"},We={class:"language-content"},Fe={class:"play"},Ge=["src"],Je={class:"img-width light-left"},Re=["src"],Ye={class:"img-width light-right"},Ke=["src"],Qe={class:"img-width cart"},Xe=["src"],Ze={class:"img-height target"},ea=["src"],aa={class:"img-height trumpet1"},la=["src"],ta={class:"img-width trumpet2"},ia=["src"],sa={class:"shadow-txt"},na={class:"title-info"},oa={class:"turntable-container"},ua=["src"],ca={class:"notice-content"},ra={class:"notice-item"},va=[Le((()=>u("div",{class:"item"},null,-1))),Le((()=>u("div",{class:"item"},null,-1))),Le((()=>u("div",{class:"item"},null,-1)))],da=[Le((()=>u("div",{class:"item"},null,-1))),Le((()=>u("div",{class:"item"},null,-1))),Le((()=>u("div",{class:"item"},null,-1)))],ma=[Le((()=>u("div",{class:"item"},null,-1))),Le((()=>u("div",{class:"item"},null,-1))),Le((()=>u("div",{class:"item"},null,-1)))],pa=[Le((()=>u("div",{class:"item"},null,-1))),Le((()=>u("div",{class:"item"},null,-1))),Le((()=>u("div",{class:"item"},null,-1)))],fa={key:0,dir:"ltr",class:"lottery-content"},ga={dir:"ltr",class:"btn-content"},ha={key:0},ya={key:0,class:"color"},ba={class:"item"},ka={key:0},wa={class:"item"},xa={class:"block-content"},Na=Le((()=>u("div",{class:"block-bottom"},null,-1))),Ca=Le((()=>u("div",{class:"block-line left"},null,-1))),Ta=Le((()=>u("div",{class:"block-line right"},null,-1))),za={class:"title"},_a={class:"content my-prize"},Sa={class:"item"},Ia={class:"item"},ja={key:0,class:"block-content"},Va=Le((()=>u("div",{class:"block-bottom"},null,-1))),$a=Le((()=>u("div",{class:"block-line left"},null,-1))),Ma=Le((()=>u("div",{class:"block-line right"},null,-1))),Da={class:"title"},La={class:"content invite"},Ea={class:"link-content"},Aa={class:"txt"},Pa={key:1,dir:"ltr",class:"block-content"},Ua=Le((()=>u("div",{class:"block-bottom"},null,-1))),qa=Le((()=>u("div",{class:"block-line left"},null,-1))),Ba=Le((()=>u("div",{class:"block-line right"},null,-1))),Oa={class:"title"},Ha={class:"prize"},Wa=["src"],Fa={key:0,class:"loading"},Ga={key:1,class:"no-data"},Ja={class:"block-content bg2"},Ra=Le((()=>u("div",{class:"block-bottom"},null,-1))),Ya=Le((()=>u("div",{class:"block-line left"},null,-1))),Ka=Le((()=>u("div",{class:"block-line right"},null,-1))),Qa={class:"title"},Xa={class:"content intro"},Za=["innerHTML"],el=e({name:"ActivityTurntable"}),al=a(Object.assign(el,{setup(e){const a=S(),{toClipboard:t}=O(),f=I(),b=h(),k=s(!1),N=i((()=>({username:"",email:"",...b.userInfo}))),z=i((()=>N.value.username||N.value.email||"")),U=i((()=>["argos"].includes("tiktokMall")?g("activity/turntable/audio/bg1.mp3"):g("activity/turntable/audio/bg.mp3"))),q=y(),H=s(""),Q=g("activity/turntable/default-imgs.png"),X=g("activity/turntable/default-imgs1.png"),Z=s(!1),{t:le,locale:te}=l(),ie=s(!1),se=s(!0),ne=s(),oe=s(),ce=s(),re=s(),fe=s(),ge=s([]),he=s(""),ye=s(""),be=s(!1),ke=s(),we=s([]),xe=s({}),Ne=async()=>{T.loading({duration:0,forbidClick:!0}),await Y({activityId:H.value}).then((e=>{Al.value=!0;let a=e.prizeList||[];if(a.length>9&&(a=a.slice(0,9)),we.value=a,xe.value=e,we.value.length)for(let l=0;l<we.value.length;l++)pe.value[l].id=we.value[l].id,pe.value[l].prizeName=we.value[l].prizeName,pe.value[l].fonts[0].text=we.value[l].prizeName,pe.value[l].imgs.push({src:we.value[l].image||(3===Number(we.value[l].prizeType)?Q:X),width:"34%",top:"20%"});Ce(),V((()=>{k.value=!0,T.clear()}))})).catch((()=>{Al.value=!1}))},Ce=()=>{const e=[];for(let a=0;a<50;a++){let a="";if(Math.random()<.5){let e="",l="";const t=Math.floor(4*Math.random()),i=Math.floor(5*Math.random()),s=Math.floor(Math.random()*me.length),n="@"+me[s];for(let a=0;a<t;a++){const a=Math.floor(Math.random()*ve.length);e+=ve[a]}for(let a=0;a<i;a++){const e=Math.floor(Math.random()*de.length);l+=de[e]}a=`${e}**${l}${n}`}else{let e="";for(let a=0;a<4;a++){const a=Math.floor(Math.random()*de.length);e+=de[a]}a=`**${e}`}const l=ze();e.push(`${a} ${le("抽中了")} ${l}`)}ge.value=e},ze=()=>{const e=we.value.filter((e=>3!==Number(e.prizeType))),a=[...new Set(e.map((e=>e.odds)))].sort(((e,a)=>e-a)),l=a.reduce(((e,a)=>e+a),0),t=Math.random()*l,i=_e(t,a),s=we.value.filter((e=>e.odds===a[i]));return s[Math.floor(Math.random()*s.length)].prizeName},_e=(e,a)=>{for(let l=0;l<a.length;l++)if(e<a[l])return l;return a.length-1},Se=()=>{se.value=!se.value,se.value?ne.value.play():ne.value.pause()},Ie=s(!1),je=s(!1),Ve=s(""),$e=s(""),Me=s(!1),Le=(e=!1)=>{if(Z.value){if(!xe.value.state)return Ve.value="",$e.value=le("活动未开启"),je.value=!0,!1;const a=(new Date).getTime(),l=new Date(xe.value.startTime).getTime(),t=new Date(xe.value.endTime).getTime();if(l>a)return Ve.value=le("活动开始时间"),$e.value=xe.value.startTime,je.value=!0,!1;if(!e){if(t<a)return Ve.value=le("本次活动已结束"),$e.value=le("欢迎下次再来！"),je.value=!0,!1;if(Number(gl.value)<Number(xe.value.pointsToNumber))return Ve.value="",$e.value=le("积分不足"),je.value=!0,!1}return!0}return k.value?Ie.value=!0:T(le("请稍后重试")),!1},el=s(!1),al=s(0),ll=async e=>{if(Pl(),Le()){if(!Ll.value)return void T(le("未认证商家"));el.value=e,e?Number(gl.value)<5*Number(xe.value.pointsToNumber)?(Ve.value="",$e.value=le("积分不足"),je.value=!0):nl(!0):nl(!1)}},tl=e=>e?we.value.findIndex((a=>a.id===e)):we.value.findIndex((e=>3===Number(e.prizeType))),il=s(!1),sl=s([]),nl=async e=>{il.value||(Me.value=!1,sl.value=[],il.value=!0,ke.value.play(),oe.value.play(),be.value=!0,await W({drawTimes:e?5:1,activityId:H.value}).then((a=>{let l=null;if(a){const t=e?5*Number(xe.value.pointsToNumber):Number(xe.value.pointsToNumber);if(gl.value=Number(gl.value)-t,e){const e=we.value.find((e=>3===Number(e.prizeType))).id;sl.value=a;const t=a.find((a=>a.id!==e));l=t?t.id:e}else l=a[0].id}else T(le("系统错误，请稍后重试"));ke.value.stop(tl(l))})).catch((e=>{ke.value.stop(tl()),"login"===e&&window.location.reload()})))},ol=e=>{const a=we.value.find((a=>a.id===e.id));if(oe.value.pause(),oe.value.currentTime=0,be.value=!1,il.value=!1,wl(!0),sl.value.length){3===Number(a.prizeType)?fe.value.play():re.value.play();const e=we.value.find((e=>3===Number(e.prizeType))).id,l=sl.value.filter((a=>a.id!==e));for(let a=0;a<l.length;a++){let e=we.value.find((e=>e.id===l[a].id));ge.value.push(`${L(z.value,!1)} ${le("抽中了")} ${e.prizeName}`);const t={...e,prizeImage:e.image,id:(new Date).getTime()};Vl.value.unshift(t)}Ve.value=l.length?le("恭喜您，中奖了！"):le("很遗憾，此次未能中奖"),$e.value=sl.value.map((e=>e.prizeName)).join("\n"),je.value=!0}else if(Me.value=Number(gl.value)>Number(xe.value.pointsToNumber)&&!el.value&&!al.value,3===Number(a.prizeType))fe.value.play(),Ve.value=le("很遗憾，此次未能中奖"),$e.value=a.prizeName,je.value=!0;else{re.value.play(),Ve.value=le("恭喜您，中奖了！"),$e.value=a.prizeName,je.value=!0,ge.value.push(`${L(z.value,!1)} ${le("抽中了")} ${a.prizeName}`);const e={...a,prizeImage:a.image,id:(new Date).getTime()};Vl.value.unshift(e)}},ul=()=>{!ie.value&&Al.value&&(ne.value.play(),se.value=!0,ie.value=!0)},cl=s({gutter:20,speed:20}),rl=s({borderRadius:0,lineClamp:1}),vl=s({background:"rgba(0,0,0,0)"}),dl=s(!1),ml=s(!1),pl=e=>{e?dl.value=!0:ml.value=!0},fl=e=>{e?dl.value=!1:ml.value=!1},gl=s(0),hl=async(e=!1)=>{e&&T.loading({duration:0,forbidClick:!0}),await K({activityId:H.value}).then((e=>{Z.value=!0,gl.value=e||0})).catch((()=>{}))},yl=()=>{Z.value=!0,El(),hl(!0),wl(),Dl(!0),zl()},bl=s(0),kl=s(0),wl=async(e=!1)=>{e||T.loading({duration:0,forbidClick:!0}),await F({activityId:H.value}).then((e=>{bl.value=e.amount||0,kl.value=e.goodsNum||0}))},xl=e=>{if(Pl(),Le(!0)){if(1===Number(e)){if(!kl.value)return void T(le("暂无奖品"))}else if(bl.value<Number(xe.value.minPoints))return void T(`${le("最小领取金额")}≥${xe.value.minPoints}`);T.loading({duration:0,forbidClick:!0}),G({activityId:H.value,prizeType:e}).then((async()=>{await wl(),T(le("领取成功"))})).catch((()=>{T.clear()}))}},Nl=s(0),Cl=s(0),Tl=s(""),zl=()=>{E().then((e=>{Tl.value=e.download&&e.code?`${e.download}/#?usercode=${e.code}&lang=${te.value}`:""})),J({activityId:H.value}).then((e=>{Nl.value=e.number||0,Cl.value=e.points||0}))},_l=async()=>{Pl();try{await t(Tl.value),T(le("copySuccess"))}catch(e){}},Sl=s(),Il=s(20),jl=s(1),Vl=s([]),$l=s(!0),Ml=s(!1),Dl=(e=!1)=>{e&&(jl.value=1,$l.value=!0,Vl.value=[]),$l.value&&!Ml.value&&(Ml.value=!0,R({pageNum:jl.value,pageSize:Il.value,activityId:H.value}).then((e=>{jl.value+=1,$l.value=!e.pageInfo.lastPage,Vl.value=[...Vl.value,...e.pageList],Ml.value=!1})).catch((()=>{Ml.value=!1})))},Ll=s(!1),El=()=>{A().then((e=>{Ll.value=2===Number(e.status)}))};j((()=>{document.removeEventListener("click",ul)}));const Al=s(!1);V((async()=>{document.addEventListener("click",ul,!1),document.addEventListener("langChange",(async()=>{await Ne(),Dl(!0),V((()=>{ke.value.init()}))})),a?(he.value="60vw",ye.value="56vw",cl.value.gutter=5,cl.value.speed=50,pe.value.forEach((e=>{e.fonts&&e.fonts.forEach((e=>{e.fontSize="2.4vw"}))}))):(he.value="690px",ye.value="630px",cl.value.gutter=20,cl.value.speed=20),ne.value.volume=.25;const{lang:e,token:l,id:t}=f.query;e&&(te.value=e,localStorage.setItem("lang",e)),l&&(T.loading({duration:0}),await b.getUserInfo(!0,l)),t?(H.value=t,H.value&&(await Ne(),k.value&&(await hl(!0),Z.value&&(El(),wl(),zl(),Dl(!0))))):T(le("暂无活动"))}));const Pl=()=>{ce.value.play()};return(e,l)=>{const t=ee,i=ae,s=$("LuckyGrid"),f=B,h=M;return n(),o("div",Ee,[u("div",Ae,[w(' <audio :src="bgMp3" autoplay loop="loop" ref="bgAudio"></audio> '),u("audio",{src:c(U),loop:"loop",ref_key:"bgAudio",ref:ne},null,8,Pe),u("audio",{src:c(g)("activity/turntable/audio/rolling.mp3"),loop:"loop",ref_key:"rollingAudio",ref:oe},null,8,Ue),u("audio",{src:c(g)("activity/turntable/audio/click.mp3"),ref_key:"clickAudio",ref:ce},null,8,qe),u("audio",{src:c(g)("activity/turntable/audio/success.mp3"),ref_key:"successAudio",ref:re},null,8,Be),u("audio",{src:c(g)("activity/turntable/audio/fail.mp3"),ref_key:"failAudio",ref:fe},null,8,Oe)]),w(" 登录弹窗 "),v(Te,{modelValue:Ie.value,"onUpdate:modelValue":l[0]||(l[0]=e=>Ie.value=e),onDone:yl},null,8,["modelValue"]),w(" 提示弹窗 "),v(De,{modelValue:je.value,"onUpdate:modelValue":l[1]||(l[1]=e=>je.value=e),"tips-title":Ve.value,"tips-info":$e.value,again:Me.value,times:al.value,onAgain:l[2]||(l[2]=e=>ll(!1))},null,8,["modelValue","tips-title","tips-info","again","times"]),Al.value?(n(),o("div",He,[u("div",We,[v(ue)]),u("div",{class:"play-content",onClick:Se},[u("div",Fe,[u("img",{src:c(g)(se.value?"activity/turntable/icon1.png":"activity/turntable/icon2.png"),alt:""},null,8,Ge)])]),u("div",{style:D({"background-image":"url("+c(g)("activity/turntable/page-header.png")+")"}),class:"page-header"},[u("div",Je,[u("img",{src:c(g)("activity/turntable/light-left.png"),alt:""},null,8,Re)]),u("div",Ye,[u("img",{src:c(g)("activity/turntable/light-right.png"),alt:""},null,8,Ke)]),u("div",Qe,[u("img",{src:c(g)("activity/turntable/cart.png"),alt:""},null,8,Xe)]),u("div",Ze,[u("img",{src:c(g)("activity/turntable/target.png"),alt:""},null,8,ea)]),u("div",aa,[u("img",{src:c(g)("activity/turntable/trumpet1.png"),alt:""},null,8,la)]),u("div",ta,[u("img",{src:c(g)("activity/turntable/trumpet2.png"),alt:""},null,8,ia)]),u("h1",null,r(c(le)("开店赢大奖")),1),u("h1",sa,r(c(le)("开店赢大奖")),1),u("div",na,[u("div",null,r(c(le)("幸运转不停")),1)])],4),u("div",{style:D({"background-image":"url("+c(g)("activity/turntable/page-bg.png")+")"}),class:"page-content"},[u("div",oa,[u("div",{style:D({"background-image":"url("+c(g)("activity/turntable/machine2.png")+")"}),class:"machine-content"},[u("div",{class:p([{active:il.value},"img-width rod"])},[u("img",{src:c(g)("activity/turntable/machine1.png"),alt:""},null,8,ua)],2),u("div",ca,[v(i,{class:"swiper",autoplay:3e3,"show-indicators":!1,vertical:""},{default:C((()=>[(n(!0),o(d,null,m(ge.value,(e=>(n(),_(t,{key:e},{default:C((()=>[u("div",ra,r(e),1)])),_:2},1024)))),128))])),_:1})]),u("div",{class:p(["light-content top left",{active:be.value}])},va,2),u("div",{class:p(["light-content top right",{active:be.value}])},da,2),u("div",{class:p(["light-content bottom left",{active:be.value}])},ma,2),u("div",{class:p(["light-content bottom right",{active:be.value}])},pa,2),he.value&&ye.value?(n(),o("div",fa,[v(s,{ref_key:"lotteryCanvas",ref:ke,width:he.value,height:ye.value,"default-config":cl.value,"default-style":rl.value,"active-style":vl.value,prizes:c(pe),onEnd:ol},null,8,["width","height","default-config","default-style","active-style","prizes"])])):w("v-if",!0),u("div",ga,[u("div",{style:D({"background-image":"url("+c(g)("activity/turntable/start-btn1.png")+")"}),class:p([{active:dl.value,disabled:il.value},"btn one"]),onClick:l[3]||(l[3]=e=>ll(!1)),onTouchstart:l[4]||(l[4]=e=>pl(!0)),onTouchend:l[5]||(l[5]=e=>fl(!0))},[u("h3",null,r(c(le)("抽N次",{times:1})),1),xe.value.pointsToNumber?(n(),o("p",ha,r(c(le)("消耗N积分",{points:xe.value.pointsToNumber})),1)):w("v-if",!0)],38),u("div",{style:D({"background-image":"url("+c(g)("activity/turntable/start-btn2.png")+")"}),class:p([{active:ml.value,disabled:il.value},"btn two"]),onClick:l[6]||(l[6]=e=>ll(!0)),onTouchstart:l[7]||(l[7]=e=>pl(!1)),onTouchend:l[8]||(l[8]=e=>fl(!1))},[u("h3",null,r(c(le)("抽N次",{times:5})),1),xe.value.pointsToNumber?(n(),o("p",ya,r(c(le)("消耗N积分",{points:5*xe.value.pointsToNumber})),1)):w("v-if",!0)],38)]),Z.value?(n(),o("div",{key:1,class:p([{"is-ar":c(q)},"user-content"])},[u("div",ba,[c(a)?(n(),_(f,{key:1,color:"#ffffff",background:"rgba(0,0,0,0)",text:c(z)+" "+c(le)("欢迎您！")},null,8,["text"])):(n(),o("div",ka,[u("span",null,r(c(z)),1),x(" "+r(c(le)("欢迎您！")),1)]))]),u("div",wa,[x(r(c(le)("剩余积分"))+" ",1),u("span",null,r(gl.value),1)])],2)):(n(),o("div",{key:2,class:"user-content login",onClick:l[9]||(l[9]=e=>Ie.value=!0)},r(c(le)("请先登录")),1))],4),u("div",xa,[Na,Ca,Ta,u("div",za,r(c(le)("我的奖品")),1),u("div",_a,[u("div",Sa,[u("h2",null,r(c(P)(bl.value)),1),u("p",null,r(c(le)("累计彩金")),1),u("div",{class:p({active:bl.value>xe.value.minPoints}),onClick:l[10]||(l[10]=e=>xl(2))},r(c(le)("领取")),3)]),u("div",Ia,[u("h2",null,r(kl.value),1),u("p",null,r(c(le)("获得实物")),1),u("div",{class:p({active:kl.value}),onClick:l[11]||(l[11]=e=>xl(1))},r(c(le)("领取")),3)])])]),Tl.value?(n(),o("div",ja,[Va,$a,Ma,u("div",Da,r(c(le)("邀请好友")),1),u("div",La,[u("div",Ea,[u("div",Aa,r(Tl.value),1),u("div",{class:"btn",onClick:_l},r(c(le)("复制链接")),1)]),w(" <p v-html=\"t('成功邀请N人，获得N积分', {num: inviteNumber, points: invitePoints})\"></p>\n              <p v-html=\"t('每邀请N个好友开店可获得N积分', {num: 1, points: activityInfo.invitePoints})\"></p> ")])])):w("v-if",!0),Z.value?(n(),o("div",Pa,[Ua,qa,Ba,u("div",Oa,r(c(le)("我的中奖")),1),u("div",{class:"content record",onScroll:l[12]||(l[12]=e=>(e=>{const a=e.target;Math.ceil(a.scrollLeft+a.clientWidth)>=a.scrollWidth-50&&Dl()})(e))},[Vl.value.length?(n(),o("div",{key:0,class:"record-content",ref_key:"recordContent",ref:Sl},[(n(!0),o(d,null,m(Vl.value,(e=>(n(),o("div",{key:e.id,class:"item"},[u("div",Ha,[u("img",{src:e.prizeImage||c(X),alt:""},null,8,Wa)]),u("p",null,r(e.prizeName),1)])))),128)),Ml.value?(n(),o("div",Fa,[v(h,{color:"#ffffff",type:"spinner"})])):w("v-if",!0)],512)):(n(),o("div",Ga,r(c(le)("暂无中奖记录")),1))],32)])):w("v-if",!0),u("div",Ja,[Ra,Ya,Ka,u("div",Qa,r(c(le)("活动规则")),1),u("div",Xa,[xe.value.description?(n(),o("div",{key:0,innerHTML:xe.value.description},null,8,Za)):w("v-if",!0)])])])],4)])):w("v-if",!0)])}}}),[["__scopeId","data-v-2b7b83b6"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/activityCenter/turntable/index.vue"]]);export{al as default};
