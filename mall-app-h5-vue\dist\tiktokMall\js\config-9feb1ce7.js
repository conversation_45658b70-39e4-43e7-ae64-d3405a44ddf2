import{_ as t,o as e,c as a,t as i}from"./index-3d21abf8.js";let r=0;const s="webkit moz ms o".split(" ");let l,n;if("undefined"==typeof window)l=function(){},n=function(){};else{let t;l=window.requestAnimationFrame,n=window.cancelAnimationFrame;for(let e=0;e<s.length&&(!l||!n);e++)t=s[e],l=l||window[t+"RequestAnimationFrame"],n=n||window[t+"CancelAnimationFrame"]||window[t+"CancelRequestAnimationFrame"];l&&n||(l=function(t){const e=(new Date).getTime(),a=Math.max(0,16-(e-r)),i=window.setTimeout((()=>{t(e+a)}),a);return r=e+a,i},n=function(t){window.clearTimeout(t)})}const o=t({props:{startVal:{type:Number,required:!1,default:0},endVal:{type:Number,required:!1,default:2017},duration:{type:Number,required:!1,default:3e3},autoplay:{type:Boolean,required:!1,default:!0},decimals:{type:Number,required:!1,default:0,validator:t=>t>=0},decimal:{type:String,required:!1,default:"."},separator:{type:String,required:!1,default:","},prefix:{type:String,required:!1,default:""},suffix:{type:String,required:!1,default:""},useEasing:{type:Boolean,required:!1,default:!0},easingFn:{type:Function,default:(t,e,a,i)=>a*(1-Math.pow(2,-10*t/i))*1024/1023+e}},data(){return{localStartVal:this.startVal,displayValue:this.formatNumber(this.startVal),printVal:null,paused:!1,localDuration:this.duration,startTime:null,timestamp:null,remaining:null,rAF:null}},computed:{countDown(){return this.startVal>this.endVal}},watch:{startVal(){this.autoplay&&this.start()},endVal(){this.autoplay&&this.start()}},mounted(){this.autoplay&&this.start(),this.$emit("mountedCallback")},methods:{start(){this.localStartVal=this.startVal,this.startTime=null,this.localDuration=this.duration,this.paused=!1,this.rAF=l(this.count)},pauseResume(){this.paused?(this.resume(),this.paused=!1):(this.pause(),this.paused=!0)},pause(){n(this.rAF)},resume(){this.startTime=null,this.localDuration=+this.remaining,this.localStartVal=+this.printVal,l(this.count)},reset(){this.startTime=null,n(this.rAF),this.displayValue=this.formatNumber(this.startVal)},count(t){this.startTime||(this.startTime=t),this.timestamp=t;const e=t-this.startTime;this.remaining=this.localDuration-e,this.useEasing?this.countDown?this.printVal=this.localStartVal-this.easingFn(e,0,this.localStartVal-this.endVal,this.localDuration):this.printVal=this.easingFn(e,this.localStartVal,this.endVal-this.localStartVal,this.localDuration):this.countDown?this.printVal=this.localStartVal-(this.localStartVal-this.endVal)*(e/this.localDuration):this.printVal=this.localStartVal+(this.endVal-this.localStartVal)*(e/this.localDuration),this.countDown?this.printVal=this.printVal<this.endVal?this.endVal:this.printVal:this.printVal=this.printVal>this.endVal?this.endVal:this.printVal,this.displayValue=this.formatNumber(this.printVal),e<this.localDuration?this.rAF=l(this.count):this.$emit("callback")},isNumber:t=>!isNaN(parseFloat(t)),formatNumber(t){t=t.toFixed(this.decimals);const e=(t+="").split(".");let a=e[0];const i=e.length>1?this.decimal+e[1]:"",r=/(\d+)(\d{3})/;if(this.separator&&!this.isNumber(this.separator))for(;r.test(a);)a=a.replace(r,"$1"+this.separator+"$2");return this.prefix+a+i+this.suffix}},destroyed(){n(this.rAF)}},[["render",function(t,r,s,l,n,o){return e(),a("span",null,i(n.displayValue),1)}],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/node_modules/vue-count-to/src/vue-countTo.vue"]]);function u(t,e,a){return e in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function h(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,i)}return a}o.unmounted=o.destroyed,Reflect.deleteProperty(o,"destroyed");var c=function(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?h(Object(a),!0).forEach((function(e){u(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):h(Object(a)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}({name:"CountTo",emits:["callback","mountedCallback"]},o);const d=[{title:"待到账金额",number:0,color:"#41A3FF",prefix:"$",decimals:2,key:"willIncome"},{title:"总销售额",number:0,color:"#54C1FF",prefix:"$",decimals:2,key:"totalSales"},{title:"总利润",number:0,color:"#3CCDC4",prefix:"$",decimals:2,key:"totalProfit"},{title:"总订单",number:0,color:"#FF6F4F",prefix:"",decimals:0,key:"orderNum"},{title:"取消订单",number:0,color:"#757F8F",prefix:"",decimals:0,key:"orderCancel"},{title:"退款订单",number:0,color:"#DD4E4E",prefix:"",decimals:0,key:"orderReturns"}],m=[{name:"全部",value:0},{name:"今日",value:1},{name:"昨日",value:2},{name:"本周",value:3},{name:"本月",value:4},{name:"本年",value:5}];export{c as C,m as a,d as r};
