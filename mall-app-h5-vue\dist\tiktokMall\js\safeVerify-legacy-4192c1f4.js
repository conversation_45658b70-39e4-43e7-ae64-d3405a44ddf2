System.register(["./index-legacy-46a00900.js","./index-legacy-72e00c5f.js","./login.api-legacy-d31fdc92.js","./use-route-legacy-be86ac1c.js"],(function(e,t){"use strict";var a,l,n,i,u,r,o,s,c,p,v,d,f,y,x,g,b,h,m,k=document.createElement("style");return k.textContent=".verify[data-v-100d5362]{font-size:13px}.title[data-v-100d5362]{font-weight:700;font-size:26px;margin-top:27px;margin-bottom:17px}.label[data-v-100d5362]{margin-top:11px}.content[data-v-100d5362]{padding:0 16px}.content p[data-v-100d5362]{color:#868d9a;font-size:15px;margin-bottom:25px}.content .iptbox[data-v-100d5362]{height:44px;margin-top:8px;padding:0 10px;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;align-items:center;border-radius:3px}.content .iptbox input[data-v-100d5362]{-webkit-box-flex:1;-webkit-flex:1 1;flex:1 1;height:100%;border:none}.content .iptbox span[data-v-100d5362]{color:#1d91ff}\n",document.head.appendChild(k),{setters:[e=>{a=e._,l=e.Y,n=e.r,i=e.q,u=e.J,r=e.av,o=e.c,s=e.e,c=e.a,p=e.t,v=e.x,d=e.h,f=e.v,y=e.f,x=e.F,g=e.w,b=e.o},e=>{h=e.B},e=>{m=e.a},()=>{}],execute:function(){const t={class:"verify"},k={class:"content"},w={class:"title textColor"},$={key:0},C={class:"label textColor"},V={key:1},_={key:2},j={class:"iptbox inputBackground"},q=["placeholder"];e("default",a({__name:"safeVerify",setup(e){const a=l(),I=n(""),S=n(""),T=n(""),z=n(""),B=n(!1),L=n(null),P=n(0);i((()=>{let e=a.query.type;S.value=e,T.value=a.query.account,z.value=a.query.username,3!=S.value&&(D(),U())}));const E=()=>{6==I.value.length?B.value=!0:B.value=!1},U=()=>{if(P.value>0)return!1;m({target:T.value}).then((e=>{P.value=30,L.value=setInterval((()=>{P.value>0?P.value=P.value-1:(P.value=0,D())}),1e3)}))},D=()=>{clearInterval(L.value),L.value=null};return u((()=>{D()})),(e,a)=>{const l=r("fx-header"),n=h;return b(),o("div",t,[s(l),c("div",k,[c("div",w,p(e.$t("safeVertify")),1),3==S.value?(b(),o("p",$,p(e.$t("verifyGoogleTips")),1)):v("v-if",!0),c("span",C,p(2==S.value?e.$t("emailVerify"):1==S.value?e.$t("phoneVerify"):e.$t("googleVerify")),1),2==S.value?(b(),o("p",V,p(e.$t("verifyEmailTips",{account:T.value})),1)):v("v-if",!0),1==S.value?(b(),o("p",_,p(e.$t("verifyPhoneTips",{account:T.value})),1)):v("v-if",!0),c("div",j,[d(c("input",{type:"text",class:"inputBackground textColor",placeholder:e.$t("entryVerifyCode"),"onUpdate:modelValue":a[0]||(a[0]=e=>I.value=e),onInput:E},null,40,q),[[f,I.value]]),3!=S.value?(b(),o("span",{key:0,onClick:U},[y(p(e.$t("reSendVerifyCode")),1),P.value?(b(),o(x,{key:0},[y(" ("+p(P.value)+")s",1)],64)):v("v-if",!0)])):v("v-if",!0)]),s(n,{class:"w-full",disabled:!B.value,style:{"margin-top":"90px"},type:"primary",onClick:a[1]||(a[1]=t=>e.$router.push({name:"resetPassword",query:{type:S.value,account:T.value,verifycode:I.value,username:z.value}}))},{default:g((()=>[y(p(e.$t("nextStep")),1)])),_:1},8,["disabled"]),v(" <button :disabled=\"!hightLight\" class=\"btn\"\r\n                @click=\"$router.push({ name: 'resetPassword', query: { type: currentType, account, verifycode, username } })\"\r\n                :class=\"hightLight ? 'hightLight' : ''\">{{ $t('nextStep')\r\n                }}</button> ")])])}}},[["__scopeId","data-v-100d5362"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/forget/safeVerify.vue"]]))}}}));
