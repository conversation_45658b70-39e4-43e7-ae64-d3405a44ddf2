import{P as t,d as o,r as e,aH as n,ai as l,e as a,X as s,Q as c,a6 as i,m as r,a9 as u,bk as d,S as f,a5 as m,R as g,bm as p,p as B,ag as b,aY as C,a8 as h,bn as v,bs as w,bt as y,a3 as x,br as k,bu as O,bf as S,b9 as D,bv as T,bw as P}from"./index-3d21abf8.js";import{B as H}from"./index-2406f514.js";import{u as A}from"./use-placeholder-c97cb410.js";import{r as j,u as z}from"./use-route-cd41a893.js";const[N,E]=t("action-bar"),F=Symbol(N);const I=s(o({name:N,props:{placeholder:Boolean,safeAreaInsetBottom:l},setup(t,{slots:o}){const l=e(),s=A(l,E),{linkChildren:c}=n(F);c();const i=()=>{var e;return a("div",{ref:l,class:[E(),{"van-safe-area-bottom":t.safeAreaInsetBottom}]},[null==(e=o.default)?void 0:e.call(o)])};return()=>t.placeholder?s(i):i()}})),[R,U]=t("action-bar-button");const K=s(o({name:R,props:c({},j,{type:String,text:String,icon:String,color:String,loading:Boolean,disabled:Boolean}),setup(t,{slots:o}){const e=z(),{parent:n,index:l}=i(F),s=r((()=>{if(n){const t=n.children[l.value-1];return!(t&&"isButton"in t)}})),c=r((()=>{if(n){const t=n.children[l.value+1];return!(t&&"isButton"in t)}}));return u({isButton:!0}),()=>{const{type:n,icon:l,text:i,color:r,loading:u,disabled:d}=t;return a(H,{class:U([n,{last:c.value,first:s.value}]),size:"large",type:n,icon:l,color:r,loading:u,disabled:d,onClick:e},{default:()=>[o.default?o.default():i]})}}})),[L,M,Q]=t("dialog"),X=c({},d,{title:String,theme:String,width:f,message:[String,Function],callback:Function,allowHtml:Boolean,className:m,transition:g("van-dialog-bounce"),messageAlign:String,closeOnPopstate:l,showCancelButton:Boolean,cancelButtonText:String,cancelButtonColor:String,cancelButtonDisabled:Boolean,confirmButtonText:String,confirmButtonColor:String,confirmButtonDisabled:Boolean,showConfirmButton:l,closeOnClickOverlay:Boolean}),Y=[...p,"transition","closeOnPopstate"];var $=o({name:L,props:X,emits:["confirm","cancel","keydown","update:show"],setup(t,{emit:o,slots:n}){const l=e(),s=B({confirm:!1,cancel:!1}),c=t=>o("update:show",t),i=o=>{var e;c(!1),null==(e=t.callback)||e.call(t,o)},r=e=>()=>{t.show&&(o(e),t.beforeClose?(s[e]=!0,S(t.beforeClose,{args:[e],done(){i(e),s[e]=!1},canceled(){s[e]=!1}})):i(e))},u=r("cancel"),d=r("confirm"),f=w((e=>{var n,a;if(e.target!==(null==(a=null==(n=l.value)?void 0:n.popupRef)?void 0:a.value))return;({Enter:t.showConfirmButton?d:y,Escape:t.showCancelButton?u:y})[e.key](),o("keydown",e)}),["enter","esc"]),m=()=>{const o=n.title?n.title():t.title;if(o)return a("div",{class:M("header",{isolated:!t.message&&!n.default})},[o])},g=o=>{const{message:e,allowHtml:n,messageAlign:l}=t,s=M("message",{"has-title":o,[l]:l}),c=x(e)?e():e;return n&&"string"==typeof c?a("div",{class:s,innerHTML:c},null):a("div",{class:s},[c])},p=()=>{if(n.default)return a("div",{class:M("content")},[n.default()]);const{title:o,message:e,allowHtml:l}=t;if(e){const t=!(!o&&!n.title);return a("div",{key:l?1:0,class:M("content",{isolated:!t})},[g(t)])}},D=()=>n.footer?n.footer():"round-button"===t.theme?a(I,{class:M("footer")},{default:()=>[t.showCancelButton&&a(K,{type:"warning",text:t.cancelButtonText||Q("cancel"),class:M("cancel"),color:t.cancelButtonColor,loading:s.cancel,disabled:t.cancelButtonDisabled,onClick:u},null),t.showConfirmButton&&a(K,{type:"danger",text:t.confirmButtonText||Q("confirm"),class:M("confirm"),color:t.confirmButtonColor,loading:s.confirm,disabled:t.confirmButtonDisabled,onClick:d},null)]}):a("div",{class:[O,M("footer")]},[t.showCancelButton&&a(H,{size:"large",text:t.cancelButtonText||Q("cancel"),class:M("cancel"),style:{color:t.cancelButtonColor},loading:s.cancel,disabled:t.cancelButtonDisabled,onClick:u},null),t.showConfirmButton&&a(H,{size:"large",text:t.confirmButtonText||Q("confirm"),class:[M("confirm"),{[k]:t.showCancelButton}],style:{color:t.confirmButtonColor},loading:s.confirm,disabled:t.confirmButtonDisabled,onClick:d},null)]);return()=>{const{width:o,title:e,theme:n,message:s,className:i}=t;return a(v,b({ref:l,role:"dialog",class:[M([n]),i],style:{width:h(o)},tabindex:0,"aria-labelledby":e||s,onKeydown:f,"onUpdate:show":c},C(t,Y)),{default:()=>[m(),p(),D()]})}}});let q;function G(t){return D?new Promise(((o,e)=>{q||function(){const t={setup(){const{state:t,toggle:o}=P();return()=>a($,b(t,{"onUpdate:show":o}),null)}};({instance:q}=T(t))}(),q.open(c({},G.currentOptions,t,{callback:t=>{("confirm"===t?o:e)(t)}}))})):Promise.resolve()}G.defaultOptions={title:"",width:"",theme:null,message:"",overlay:!0,callback:null,teleport:"body",className:"",allowHtml:!1,lockScroll:!0,transition:void 0,beforeClose:null,overlayClass:"",overlayStyle:void 0,messageAlign:"",cancelButtonText:"",cancelButtonColor:null,cancelButtonDisabled:!1,confirmButtonText:"",confirmButtonColor:null,confirmButtonDisabled:!1,showConfirmButton:!0,showCancelButton:!1,closeOnPopstate:!0,closeOnClickOverlay:!1},G.currentOptions=c({},G.defaultOptions),G.alert=G,G.confirm=t=>G(c({showCancelButton:!0},t)),G.close=()=>{q&&q.toggle(!1)},G.setDefaultOptions=t=>{c(G.currentOptions,t)},G.resetDefaultOptions=()=>{G.currentOptions=c({},G.defaultOptions)},G.Component=s($),G.install=t=>{t.use(G.Component),t.config.globalProperties.$dialog=G};export{G as D};
