System.register(["./index-legacy-46a00900.js"],(function(e,n){"use strict";var t,a,l,r,i,d,o,v,g=document.createElement("style");return g.textContent=":root{--van-cell-group-background-color: var(--van-background-color-light);--van-cell-group-title-color: var(--van-text-color-2);--van-cell-group-title-padding: var(--van-padding-md) var(--van-padding-md) var(--van-padding-xs);--van-cell-group-title-font-size: var(--van-font-size-md);--van-cell-group-title-line-height: 16px;--van-cell-group-inset-padding: 0 var(--van-padding-md);--van-cell-group-inset-border-radius: var(--van-border-radius-lg);--van-cell-group-inset-title-padding: var(--van-padding-md) var(--van-padding-md) var(--van-padding-xs) var(--van-padding-xl) }.van-cell-group{background:var(--van-cell-group-background-color)}.van-cell-group--inset{margin:var(--van-cell-group-inset-padding);border-radius:var(--van-cell-group-inset-border-radius);overflow:hidden}.van-cell-group__title{padding:var(--van-cell-group-title-padding);color:var(--van-cell-group-title-color);font-size:var(--van-cell-group-title-font-size);line-height:var(--van-cell-group-title-line-height)}.van-cell-group__title--inset{padding:var(--van-cell-group-inset-title-padding)}\n",document.head.appendChild(g),{setters:[e=>{t=e.P,a=e.ai,l=e.d,r=e.e,i=e.F,d=e.ag,o=e.bd,v=e.X}],execute:function(){const[n,g]=t("cell-group"),c={title:String,inset:Boolean,border:a};var s=l({name:n,inheritAttrs:!1,props:c,setup(e,{slots:n,attrs:t}){const a=()=>{var a;return r("div",d({class:[g({inset:e.inset}),{[o]:e.border&&!e.inset}]},t),[null==(a=n.default)?void 0:a.call(n)])};return()=>e.title||n.title?r(i,null,[r("div",{class:g("title",{inset:e.inset})},[n.title?n.title():e.title]),a()]):a()}});e("C",v(s))}}}));
