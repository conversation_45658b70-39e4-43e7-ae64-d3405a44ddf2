import{_ as e,i as a,u as l,r as o,I as s,c as i,e as t,w as r,x as n,A as u,b as d,n as c,T as v,o as m,aV as p,a as f,t as g,F as h,y as x}from"./index-3d21abf8.js";import{E as j}from"./index-5d897066.js";import{P as _}from"./index-fc51b7d2.js";import{L as b}from"./index-40e83579.js";import{N as k}from"./index-cfdda867.js";/* empty css              *//* empty css               */import{S as w}from"./index-179203f3.js";import{a as V,o as y}from"./orderItem-7f0b3534.js";import{_ as C}from"./search-icon-8ae5f679.js";import"./use-id-a0619e01.js";import"./use-placeholder-c97cb410.js";import"./index-8c1841f6.js";import"./index-7dfcb82a.js";import"./use-route-cd41a893.js";import"./config-22c2c72a.js";import"./index-3ab60a77.js";const I=["src"],L={key:0,class:"list ml-4 mr-4 mt-4 mb-4"},S=e({__name:"orderSearch",setup(e){const S=a(),{t:U}=l();let $=o(""),z=o(1);const D=o([]),N=o(!1),P=o(!1),R=o(!1),T=o(!1),A=new URL("/www/png/name-20d65991.png",self.location),E=()=>{z.value=1,$.value="",P.value=!1,N.value=!0,z.value=1,T.value=!1,D.value=[]},F=e=>{$.value?(T.value=!1,P.value=!1,N.value=!0,z.value=1,e&&v.loading({forbidClick:!0,loadingType:"spinner",duration:0}),B()):(v(U("请输入订单号")),N.value=!1,R.value=!1,P.value=!0,D.value=[])},q=()=>{F(!0)},B=()=>{const e={pageNum:z.value,pageSize:20,orderId:$.value};V(e).then((e=>{const{pageInfo:a,pageList:l}=e;D.value=1===z.value?l:[...D.value,...l],z.value++,N.value=!1,R.value=!1,T.value=!0,P.value=a.lastPage,v.clear()})).catch((()=>{v.clear()}))};return(e,a)=>{const l=s,o=w,v=k,V=b,z=_,G=j;return m(),i("div",{class:c(["search-container",{"is-ar":d(S)}])},[t(v,{fixed:"","left-arrow":"",onClickLeft:a[1]||(a[1]=()=>e.$router.back())},{title:r((()=>[t(o,{modelValue:d($),"onUpdate:modelValue":a[0]||(a[0]=e=>p($)?$.value=e:$=e),shape:"round",onSearch:q,clearable:!1,placeholder:e.$t("请输入订单号")},{"left-icon":r((()=>[f("img",{class:"search-icon",src:d(C)},null,8,I)])),"right-icon":r((()=>[d($)?(m(),u(l,{key:0,name:"cross",onClick:E,size:"14",color:"#333333"})):n("v-if",!0)])),_:1},8,["modelValue","placeholder"])])),right:r((()=>[f("div",{onClick:q},g(d(U)("搜索")),1)])),_:1}),D.value.length>0?(m(),i("div",L,[t(z,{modelValue:R.value,"onUpdate:modelValue":a[3]||(a[3]=e=>R.value=e),"loading-text":e.$t("加载中"),"loosing-text":e.$t("释放以刷新"),"pulling-text":e.$t("下拉以刷新"),onRefresh:a[4]||(a[4]=e=>F(!1))},{default:r((()=>[t(V,{loading:N.value,"onUpdate:loading":a[2]||(a[2]=e=>N.value=e),finished:P.value,"finished-text":d(U)("没有更多了"),onLoad:B},{default:r((()=>[(m(!0),i(h,null,x(D.value,(e=>(m(),u(y,{info:e},null,8,["info"])))),256))])),_:1},8,["loading","finished","finished-text"])])),_:1},8,["modelValue","loading-text","loosing-text","pulling-text"])])):n("v-if",!0),!D.value.length&&T.value?(m(),u(G,{key:1,image:d(A).href,description:d(U)("noData")},null,8,["image","description"])):n("v-if",!0)],2)}}},[["__scopeId","data-v-ae0d1c7a"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/order/orderSearch.vue"]]);export{S as default};
