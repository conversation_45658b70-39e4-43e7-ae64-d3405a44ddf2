import{_ as a,d as s,u as e,Y as t,r as l,T as i,c8 as r,av as d,c as n,e as o,w as u,a as c,A as v,b as p,x as f,n as g,F as m,y as h,o as x,f as y,t as _,D as k,E as b}from"./index-3d21abf8.js";import{E as j}from"./index-5d897066.js";import"./use-id-a0619e01.js";const I=(a=>(k("data-v-d1280af0"),a=a(),b(),a))((()=>c("div",{style:{height:"46px"}},null,-1))),T={class:"logistics-container"},D={class:"info"},q={class:"time"},w=s({name:"OrderLogistics"}),C=a(Object.assign(w,{setup(a){const{t:s}=e(),k=t(),b=l(!0),w=l([]),C=l(!1);return k.query.id?(i.loading({forbidClick:!0,duration:0}),r({orderId:k.query.id}).then((a=>{if(a.length){const s=a.map((a=>{const s=a.log.split(a.orderId);return{...a,tipsTxt:s[1]}})).reverse();w.value=s}C.value=1===a.length,b.value=!1,i.clear()})).catch((()=>{b.value=!1,i.clear()}))):b.value=!1,(a,e)=>{const t=d("fx-header"),l=j;return x(),n("div",null,[o(t,{fixed:!0},{title:u((()=>[y(_(p(s)("查看物流")),1)])),_:1}),I,c("div",T,[b.value||w.value.length?f("v-if",!0):(x(),v(l,{key:0,description:p(s)("noData")},null,8,["description"])),!b.value&&w.value.length?(x(),n("div",{key:1,class:g(["content",{single:C.value}])},[(x(!0),n(m,null,h(w.value,(a=>(x(),n("div",{key:a.id,class:"item"},[c("p",D,[y(_(p(s)("订单")),1),c("span",null,"#"+_(a.orderId)+"#",1),y(_(p(s)(a.tipsTxt)),1)]),c("p",q,_(a.updateTime),1)])))),128))],2)):f("v-if",!0)])])}}}),[["__scopeId","data-v-d1280af0"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/order/logistics.vue"]]);export{C as default};
