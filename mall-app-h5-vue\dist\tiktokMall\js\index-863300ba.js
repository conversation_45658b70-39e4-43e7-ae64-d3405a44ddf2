import{_ as e,j as a,i as l,u as s,Y as t,l as i,r as n,q as u,m as r,av as o,I as v,c as d,e as c,w as p,a as f,t as m,b as y,x as h,f as x,n as b,T as g,cf as j,o as C,cg as I,F as _,D as k,E as q}from"./index-3d21abf8.js";import{B as V}from"./index-2406f514.js";/* empty css              *//* empty css               */import{a as T}from"./login.api-cb7fcde3.js";import{F as w}from"./index-8c1841f6.js";import"./use-route-cd41a893.js";import"./index-7dfcb82a.js";import"./use-id-a0619e01.js";const D={class:"verify-content"},F=(e=>(k("data-v-815bbfd7"),e=e(),q(),e))((()=>f("div",{style:{height:"46px"}},null,-1))),N={class:"info-content"},S={class:"info-item"},U={class:"gap"},$={class:"info-item"},B={class:"gap"},E={key:0},P={class:"change-ver-type"},Y={class:"submit-content"},z=e({__name:"index",setup(e){const k=a(),q=l(),{t:z}=s(),A=t(),G=i(),H=n(1),J=n(1),K=n("44"),L=n(""),M=n(0),O=n(null),Q=n(!1);H.value=A.query&&A.query.type?Number(A.query.type):1,J.value=A.query&&A.query.verType?Number(A.query.verType):1,u((()=>{clearInterval(O.value),O.value=null}));const R=r((()=>{if(k.userInfo.token){const{email:e,phone:a}=k.userInfo;let l="";if(1===H.value){if(a){const e=a.split(" ");2===e.length?(K.value=e[0],l=e[1]):l=e[0]}}else l=e||"";return l}G.push("/login")})),W=()=>{if(M.value>0)return!1;const{email:e,phone:a}=k.userInfo;g.loading({duration:0,forbidClick:!0}),T({target:1===H.value?a:e}).then((()=>{g(z("sendSuccess")),M.value=60,O.value=setInterval((()=>{M.value>0?M.value=M.value-1:(M.value=0,clearInterval(O.value),O.value=null)}),1e3)})).catch((()=>{g.clear()}))},X=()=>{const{email:e,phone:a}=k.userInfo,l=1===H.value?2:1;return 2!==l||e?1!==l||a?void(H.value=l):(g(z("暂未绑定手机号，无法进行手机验证")),!1):(g(z("暂未绑定邮箱，无法进行邮箱验证")),!1)},Z=()=>{if(""===L.value)return void g(z("entryVerifyCode"));Q.value=!0;const{email:e,phone:a}=k.userInfo,l={target:1===H.value?a:e,verifcode:L.value};1===H.value?l.phone=a:l.email=e,j(l).then((e=>{Q.value=!1,G.push(`/bindVerify?type=${J.value}&reset=1&verifyCode=${e.verifyCode}`)})).catch((()=>{Q.value=!1}))};return(e,a)=>{const l=o("fx-header"),s=w,t=V,i=v;return C(),d("div",D,[c(l,{fixed:!0},{title:p((()=>[x(m(y(z)("身份验证")),1)])),_:1}),F,f("div",N,[f("div",S,[f("h2",U,m(1===H.value?y(z)("手机验证"):y(z)("邮箱验证")),1),f("p",null,m(y(z)("为了保障您的账号安全，请验证后进行下一步操作")),1)]),f("div",$,[f("p",B,m(1===H.value?y(z)("当前绑定手机号"):y(z)("当前绑定邮箱")),1),f("h2",null,[1===H.value?(C(),d("span",E,"(+"+m(K.value)+")",1)):h("v-if",!0),x(m(y(I)(y(R),!1)),1)])]),f("div",{class:b(["code-content",{"is-ar":y(q)}])},[c(s,{modelValue:L.value,"onUpdate:modelValue":a[0]||(a[0]=e=>L.value=e),type:"tel",label:"",placeholder:y(z)("entryVerifyCode")},null,8,["modelValue","placeholder"]),f("div",{class:b(["btn",{"is-ar":y(q)}])},[c(t,{type:"primary",onClick:W},{default:p((()=>[x(m(y(z)("sendVerifyCode")),1),M.value?(C(),d(_,{key:0},[x("("+m(M.value)+")s",1)],64)):h("v-if",!0)])),_:1})],2)],2),f("div",P,[c(i,{name:"exchange"}),f("p",{onClick:X},m(1===H.value?y(z)("切换为邮箱验证"):y(z)("切换为手机验证")),1)])]),f("div",Y,[c(t,{type:"primary",loading:Q.value,onClick:Z},{default:p((()=>[x(m(y(z)("nextStep")),1)])),_:1},8,["loading"])])])}}},[["__scopeId","data-v-815bbfd7"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/verifyPage/index.vue"]]);export{z as default};
