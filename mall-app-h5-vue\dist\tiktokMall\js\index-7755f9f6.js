import{_ as e,u as a,l as s,r as l,s as t,av as d,c as o,e as r,w as u,a as n,T as i,ck as p,o as c,f as v,t as m,D as w,E as f}from"./index-3d21abf8.js";import{B as x}from"./index-2406f514.js";import{E as h}from"./index-9c8e9dca.js";import"./use-route-cd41a893.js";/* empty css              *//* empty css               */import"./index-8c1841f6.js";import"./index-7dfcb82a.js";import"./use-id-a0619e01.js";const T={class:"changePassword"},P=(e=>(w("data-v-98dc327f"),e=e(),f(),e))((()=>n("div",{class:"line"},null,-1))),$={class:"content"},_=e({__name:"index",setup(e){const{t:w}=a(),f=s(),_=l(!1),b=l(""),g=l(""),j=l(""),y=()=>{if(""===b.value)return void i(w("请输入原密码"));if(""===g.value)return void i(w("请设置新密码"));if(!/^[A-Za-z0-9!@#$%^&*_()<>.?\/\\{}[\]|,~+:;']+$/.test(g.value)||g.value.length<6||g.value.length>20)return i(w("setPasswordTips")),!1;g.value===j.value?(_.value=!0,p({old_password:encodeURI(b.value),password:encodeURI(g.value),re_password:encodeURI(j.value)}).then((e=>{i(w("changeSuccess")),_.value=!1,setTimeout((()=>{f.back()}),1e3)})).catch((e=>{_.value=!1}))):i(w("两次密码输入不一致"))};return t((()=>{["familyMart"].includes("tiktokMall")&&f.back()})),(e,a)=>{const s=d("fx-header"),l=x;return c(),o("div",T,[r(s,null,{title:u((()=>[v(m(e.$t("changeLoginPassword")),1)])),_:1}),P,n("div",$,[r(h,{label:e.$t("oldPassword"),placeholderText:e.$t("entryPassword"),modelValue:b.value,"onUpdate:modelValue":a[0]||(a[0]=e=>b.value=e),typeText:"password"},null,8,["label","placeholderText","modelValue"]),r(h,{label:e.$t("newPassword"),placeholderText:e.$t("entryPassword"),tips:e.$t("setPasswordTips"),modelValue:g.value,"onUpdate:modelValue":a[1]||(a[1]=e=>g.value=e),typeText:"password"},null,8,["label","placeholderText","tips","modelValue"]),r(h,{label:e.$t("sureNewPassword"),placeholderText:e.$t("entryPassword"),tips:e.$t("setPasswordTips"),modelValue:j.value,"onUpdate:modelValue":a[2]||(a[2]=e=>j.value=e),typeText:"password"},null,8,["label","placeholderText","tips","modelValue"]),r(l,{class:"w-full btn-content",type:"primary",loading:_.value,onClick:y},{default:u((()=>[v(m(e.$t("sure")),1)])),_:1},8,["loading"])])])}}},[["__scopeId","data-v-98dc327f"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/changePassword/index.vue"]]);export{_ as default};
