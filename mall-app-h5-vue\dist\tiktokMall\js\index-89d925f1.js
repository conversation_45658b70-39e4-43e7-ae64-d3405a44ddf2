import{_ as e,k as s,m as a,c as r,a as t,b as c,o as l}from"./index-3d21abf8.js";const i={class:"iframe-content"},o=["src"],d=e({__name:"index",setup(e){const d=s(),f=a((()=>d.customer_service_url||""));return(e,s)=>(l(),r("div",i,[t("iframe",{src:c(f),class:"flex-1"},null,8,o)]))}},[["__scopeId","data-v-fdb38d29"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/customerServiceOther/index.vue"]]);export{d as default};
