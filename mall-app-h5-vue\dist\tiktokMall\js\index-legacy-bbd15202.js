System.register(["./index-legacy-46a00900.js"],(function(o,r){"use strict";var e,a,i,l,n,t,s,c,d,u,g,v,h,f,m,y,S,b,p,w;return{setters:[o=>{e=o.P,a=o.S,i=o.ai,l=o.R,n=o.d,t=o.r,s=o.Z,c=o.m,d=o.a8,u=o.ad,g=o.g,v=o.b9,h=o.aN,f=o.e,m=o.h,y=o.b$,S=o.ag,b=o.s,p=o.I,w=o.X}],execute:function(){const[r,z]=e("image"),x={src:String,alt:String,fit:String,position:String,round:Boolean,block:Boolean,width:a,height:a,radius:a,lazyLoad:Boolean,iconSize:a,showError:i,errorIcon:l("photo-fail"),iconPrefix:String,showLoading:i,loadingIcon:l("photo")};var I=n({name:r,props:x,emits:["load","error"],setup(o,{emit:r,slots:e}){const a=t(!1),i=t(!0),l=t(),{$Lazyload:n}=s().proxy,w=c((()=>{const r={width:d(o.width),height:d(o.height)};return u(o.radius)&&(r.overflow="hidden",r.borderRadius=d(o.radius)),r}));g((()=>o.src),(()=>{a.value=!1,i.value=!0}));const x=o=>{i.value=!1,r("load",o)},I=o=>{a.value=!0,i.value=!1,r("error",o)},L=(r,e,a)=>a?a():f(p,{name:r,size:o.iconSize,class:e,classPrefix:o.iconPrefix},null),$=()=>{if(a.value||!o.src)return;const r={alt:o.alt,class:z("img"),style:{objectFit:o.fit,objectPosition:o.position}};return o.lazyLoad?m(f("img",S({ref:l},r),null),[[y("lazy"),o.src]]):f("img",S({src:o.src,onLoad:x,onError:I},r),null)},P=({el:o})=>{const r=()=>{o===l.value&&i.value&&x()};l.value?r():b(r)},j=({el:o})=>{o!==l.value||a.value||I()};return n&&v&&(n.$on("loaded",P),n.$on("error",j),h((()=>{n.$off("loaded",P),n.$off("error",j)}))),()=>{var r;return f("div",{class:z({round:o.round,block:o.block}),style:w.value},[$(),i.value&&o.showLoading?f("div",{class:z("loading")},[L(o.loadingIcon,z("loading-icon"),e.loading)]):a.value&&o.showError?f("div",{class:z("error")},[L(o.errorIcon,z("error-icon"),e.error)]):void 0,null==(r=e.default)?void 0:r.call(e)])}}});o("I",w(I))}}}));
