import{_ as e,u as a,l,j as s,m as t,r as u,g as o,av as n,c as i,e as r,w as c,a as d,b as v,aV as m,t as f,T as p,cx as h,cy as x,o as j,f as g,D as w,E as V}from"./index-3d21abf8.js";import{A as _}from"./index-3d6106f5.js";import{P as b,N as y}from"./index-2c2a5a87.js";import{B}from"./index-2406f514.js";/* empty css              *//* empty css               */import{E as T}from"./index-9c8e9dca.js";import{F as U}from"./index-8c1841f6.js";import{D as k}from"./function-call-78245787.js";import"./use-route-cd41a893.js";import"./index-7dfcb82a.js";import"./use-id-a0619e01.js";import"./use-placeholder-c97cb410.js";const $=(e=>(w("data-v-d91fc208"),e=e(),V(),e))((()=>d("div",{class:"fixed-header-spacer"},null,-1))),C={class:"cancellation-content"},D={class:"item"},E={class:"item"},I={class:"btn"},F={style:{height:"22rem"}},P=e({__name:"index",setup(e){const{t:w}=a(),V=l(),P=s(),z=t((()=>{let e={username:"***",safeword:0};return P.userInfo.token?e={...e,...P.userInfo}:V.push("/login"),e})),A=t((()=>{const e=z.value.username;if(z.value.username.indexOf("@")>-1)return e;if(z.value.phone){const e=z.value.phone.split(" ");return`(+${e[0]}) ${e[1]}`}return e})),N=u(!1),O=u(""),q=u(""),G=u(!1),H=async()=>{if(!O.value)return p(w("请输入您要注销账号的原因")),!1;G.value=!0;const e=await h();if(G.value=!1,e.money>0)p(w("该账号存在可用余额，不可注销！"));else{Boolean(z.value.safeword)?(N.value=!0,L.value=!0):J()}},J=async()=>{G.value=!0,N.value=!1,L.value=!1,k.confirm({title:w("您确认要注销吗？"),message:w("警告！请谨慎操作注销账户，如果您不再使用该账号，可点击【同意注销】，注销后可能几天内无法注册。"),confirmButtonColor:"#ee0a24",cancelButtonText:w("cancel"),confirmButtonText:w("同意注销")}).then((()=>{K()})).catch((()=>{G.value=!1}))},K=()=>{const e={account:z.value.username,reason:O.value,cashPassword:q.value||null};x(e).then((()=>{G.value=!1,p.success(w("注销成功")),setTimeout((()=>{P.logout()}),1500)})).catch((()=>{q.value="",G.value=!1}))},L=u(!1);return o((()=>q.value),(e=>{6===e.length&&(p.loading(""),K(),N.value=!1,q.value="")})),(e,a)=>{const l=n("fx-header"),s=U,t=B,u=b,o=y,p=_;return j(),i("div",null,[r(l,{fixed:""},{title:c((()=>[g(f(v(w)("账号注销")),1)])),_:1}),$,d("div",C,[d("div",D,[r(T,{label:e.$t("您的账号"),modelValue:v(A),"onUpdate:modelValue":a[0]||(a[0]=e=>m(A)?A.value=e:null),disabled:!0,clearBtn:!1,typeText:"text"},null,8,["label","modelValue"])]),d("div",E,[d("p",null,f(v(w)("为什么要离开？")),1),r(s,{modelValue:O.value,"onUpdate:modelValue":a[1]||(a[1]=e=>O.value=e),rows:"4",autosize:"",type:"textarea",placeholder:v(w)("pleaseEnter")},null,8,["modelValue","placeholder"])]),d("div",I,[r(t,{type:"danger",loading:G.value,block:"",onClick:H},{default:c((()=>[g(f(e.$t("注销账号")),1)])),_:1},8,["loading"])])]),r(p,{show:N.value,"onUpdate:show":a[5]||(a[5]=e=>N.value=e),title:v(w)("请输入交易密码")},{default:c((()=>[d("div",F,[r(u,{length:6,value:q.value,focused:L.value,onFocus:a[2]||(a[2]=e=>L.value=!0)},null,8,["value","focused"]),r(o,{modelValue:q.value,"onUpdate:modelValue":a[3]||(a[3]=e=>q.value=e),show:L.value,onBlur:a[4]||(a[4]=e=>L.value=!1)},null,8,["modelValue","show"])])])),_:1},8,["show","title"])])}}},[["__scopeId","data-v-d91fc208"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/setting/cancellation/index.vue"]]);export{P as default};
