System.register(["./index-legacy-46a00900.js"],(function(e,a){"use strict";var n,t,i,o,r,l,c,s,d,b,v,p,g,f,u,h,w,m,x,k=document.createElement("style");return k.textContent=":root{--van-notice-bar-height: 40px;--van-notice-bar-padding: 0 var(--van-padding-md);--van-notice-bar-wrapable-padding: var(--van-padding-xs) var(--van-padding-md);--van-notice-bar-text-color: var(--van-orange-dark);--van-notice-bar-font-size: var(--van-font-size-md);--van-notice-bar-line-height: 24px;--van-notice-bar-background-color: var(--van-orange-light);--van-notice-bar-icon-size: 16px;--van-notice-bar-icon-min-width: 24px }.van-notice-bar{position:relative;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;height:var(--van-notice-bar-height);padding:var(--van-notice-bar-padding);color:var(--van-notice-bar-text-color);font-size:var(--van-notice-bar-font-size);line-height:var(--van-notice-bar-line-height);background:var(--van-notice-bar-background-color)}.van-notice-bar__left-icon,.van-notice-bar__right-icon{min-width:var(--van-notice-bar-icon-min-width);font-size:var(--van-notice-bar-icon-size)}.van-notice-bar__right-icon{text-align:right;cursor:pointer}.van-notice-bar__wrap{position:relative;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-flex:1;-webkit-flex:1 1;flex:1 1;-webkit-box-align:center;-webkit-align-items:center;align-items:center;height:100%;overflow:hidden}.van-notice-bar__content{position:absolute;white-space:nowrap;-webkit-transition-timing-function:linear;transition-timing-function:linear}.van-notice-bar__content.van-ellipsis{max-width:100%}.van-notice-bar--wrapable{height:auto;padding:var(--van-notice-bar-wrapable-padding)}.van-notice-bar--wrapable .van-notice-bar__wrap{height:auto}.van-notice-bar--wrapable .van-notice-bar__content{position:relative;white-space:normal;word-wrap:break-word}\n",document.head.appendChild(k),{setters:[e=>{n=e.P,t=e.a4,i=e.d,o=e.r,r=e.p,l=e.aL,c=e.aR,s=e.ac,d=e.a9,b=e.g,v=e.h,p=e.aS,g=e.e,f=e.I,u=e.aT,h=e.aP,w=e.aU,m=e.ad,x=e.X}],execute:function(){const[a,k]=n("notice-bar"),y={text:String,mode:String,color:String,delay:t(1),speed:t(60),leftIcon:String,wrapable:Boolean,background:String,scrollable:{type:Boolean,default:null}};var _=i({name:a,props:y,emits:["close","replay"],setup(e,{emit:a,slots:n}){let t,i=0,x=0;const y=o(),_=o(),z=r({show:!0,offset:0,duration:0}),S=n=>{"closeable"===e.mode&&(z.show=!1,a("close",n))},I=()=>{if(n["right-icon"])return n["right-icon"]();const a="closeable"===e.mode?"cross":"link"===e.mode?"arrow":void 0;return a?g(f,{name:a,class:k("right-icon"),onClick:S},null):void 0},T=()=>{z.offset=i,z.duration=0,w((()=>{h((()=>{z.offset=-x,z.duration=(x+i)/+e.speed,a("replay")}))}))},C=()=>{const a=!1===e.scrollable&&!e.wrapable,t={transform:z.offset?`translateX(${z.offset}px)`:"",transitionDuration:`${z.duration}s`};return g("div",{ref:y,role:"marquee",class:k("wrap")},[g("div",{ref:_,style:t,class:[k("content"),{"van-ellipsis":a}],onTransitionend:T},[n.default?n.default():e.text])])},B=()=>{const{delay:a,speed:n,scrollable:o}=e,r=m(a)?1e3*+a:0;i=0,x=0,z.offset=0,z.duration=0,clearTimeout(t),t=setTimeout((()=>{if(!y.value||!_.value||!1===o)return;const e=u(y).width,a=u(_).width;(o||a>e)&&h((()=>{i=e,x=a,z.offset=-x,z.duration=x/+n}))}),r)};return l(B),c(B),s("pageshow",B),d({reset:B}),b((()=>[e.text,e.scrollable]),B),()=>{const{color:a,wrapable:t,background:i}=e;return v(g("div",{role:"alert",class:k({wrapable:t}),style:{color:a,background:i}},[n["left-icon"]?n["left-icon"]():e.leftIcon?g(f,{class:k("left-icon"),name:e.leftIcon},null):void 0,C(),I()]),[[p,z.show]])}}});e("N",x(_))}}}));
