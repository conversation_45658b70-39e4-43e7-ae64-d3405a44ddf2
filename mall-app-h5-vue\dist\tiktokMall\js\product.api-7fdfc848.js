import{N as a,O as o}from"./index-3d21abf8.js";import{l as s}from"./index-cfaf3bc2.js";const t=s=>a({url:"/wap/seller/systemGoods!list.action",method:o.POST,data:s}),e=s=>a({url:"/wap/seller/goods!delete.action",method:o.POST,data:s}),d=()=>a({url:"/wap/api/sysParaProduct!info.action",method:o.POST}),l=s=>a({url:"/wap/seller/goods!list.action",method:o.POST,data:s}),r=()=>a({url:"/wap/api/category!tree.action",method:o.GET}),i=s=>a({url:"/wap/seller/evaluation!list.action",method:o.POST,data:s}),c=s=>a({url:"/wap/seller/goods!update.action",method:o.POST,data:s}),n=s=>a({loadingPass:!0,url:"/wap/seller/goods!addOrUpdate.action",method:o.POST,data:s}),p=t=>a({url:"/wap/seller/goods!search-goods.action?"+s.stringify(t),method:o.POST});export{n as a,c as b,r as c,e as d,i as e,p as f,t as g,l as m,d as s};
