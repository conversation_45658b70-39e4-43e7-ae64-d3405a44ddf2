System.register(["./index-legacy-46a00900.js","./index-legacy-72e00c5f.js","./index-legacy-f9c0699e.js","./index-legacy-f9c0699e2.js","./login.api-legacy-d31fdc92.js","./index-legacy-0ade4760.js","./use-route-legacy-be86ac1c.js","./index-legacy-df3e01aa.js","./use-id-legacy-df76950f.js"],(function(e,t){"use strict";var n,a,i,o,l,d,c,r,f,s,u,v,b,p,g,x,y,h,m,k,w,j,C,_,I,q,V,z,S,T,D=document.createElement("style");return D.textContent=".verify-content[data-v-815bbfd7]{min-height:100vh;background-color:#fff}.info-content[data-v-815bbfd7]{padding:0 20px;color:#333}.info-content>.info-item[data-v-815bbfd7]{margin-top:30px}.info-content>.info-item[data-v-815bbfd7]:last-child{margin-top:20px}.info-content>.info-item>.gap[data-v-815bbfd7]{margin-bottom:10px}.info-content>.info-item h2[data-v-815bbfd7]{font-size:20px;font-weight:700}.info-content>.info-item p[data-v-815bbfd7]{font-size:14px}.info-content .code-content[data-v-815bbfd7]{width:100%;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between;margin-top:35px}.info-content .code-content.is-ar[data-v-815bbfd7] .van-field__control{text-align:right}.info-content .code-content .van-cell[data-v-815bbfd7]{width:185px;height:44px;border:1px solid #ddd;border-radius:4px}.info-content .code-content .van-cell[data-v-815bbfd7]:after{border:none}.info-content .code-content>.btn[data-v-815bbfd7]{-webkit-box-flex:1;-webkit-flex:1 1;flex:1 1;padding-left:15px}.info-content .code-content>.btn.is-ar[data-v-815bbfd7]{padding-right:15px;padding-left:0}.info-content .code-content>.btn .van-button[data-v-815bbfd7]{width:100%;height:44px;border-radius:4px;background-color:var(--site-main-color);border-color:var(--site-main-color)}.info-content>.change-ver-type[data-v-815bbfd7]{width:100%;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:end;-webkit-justify-content:flex-end;justify-content:flex-end;color:var(--site-main-color);margin-top:10px}.info-content>.change-ver-type>p[data-v-815bbfd7]{font-size:14px;padding-left:5px;font-weight:700}.submit-content[data-v-815bbfd7]{width:100%;position:fixed;left:0;bottom:60px;padding:0 20px}.submit-content .van-button[data-v-815bbfd7]{width:100%;height:44px;border-radius:4px;background-color:var(--site-main-color);border-color:var(--site-main-color)}\n",document.head.appendChild(D),{setters:[e=>{n=e._,a=e.j,i=e.i,o=e.u,l=e.Y,d=e.l,c=e.r,r=e.q,f=e.m,s=e.av,u=e.I,v=e.c,b=e.e,p=e.w,g=e.a,x=e.t,y=e.b,h=e.x,m=e.f,k=e.n,w=e.T,j=e.cf,C=e.o,_=e.cg,I=e.F,q=e.D,V=e.E},e=>{z=e.B},()=>{},()=>{},e=>{S=e.a},e=>{T=e.F},()=>{},()=>{},()=>{}],execute:function(){const t={class:"verify-content"},D=(e=>(q("data-v-815bbfd7"),e=e(),V(),e))((()=>g("div",{style:{height:"46px"}},null,-1))),E={class:"info-content"},F={class:"info-item"},N={class:"gap"},U={class:"info-item"},$={class:"gap"},B={key:0},P={class:"change-ver-type"},Y={class:"submit-content"};e("default",n({__name:"index",setup(e){const n=a(),q=i(),{t:V}=o(),A=l(),G=d(),H=c(1),J=c(1),K=c("44"),L=c(""),M=c(0),O=c(null),Q=c(!1);H.value=A.query&&A.query.type?Number(A.query.type):1,J.value=A.query&&A.query.verType?Number(A.query.verType):1,r((()=>{clearInterval(O.value),O.value=null}));const R=f((()=>{if(n.userInfo.token){const{email:e,phone:t}=n.userInfo;let a="";if(1===H.value){if(t){const e=t.split(" ");2===e.length?(K.value=e[0],a=e[1]):a=e[0]}}else a=e||"";return a}G.push("/login")})),W=()=>{if(M.value>0)return!1;const{email:e,phone:t}=n.userInfo;w.loading({duration:0,forbidClick:!0}),S({target:1===H.value?t:e}).then((()=>{w(V("sendSuccess")),M.value=60,O.value=setInterval((()=>{M.value>0?M.value=M.value-1:(M.value=0,clearInterval(O.value),O.value=null)}),1e3)})).catch((()=>{w.clear()}))},X=()=>{const{email:e,phone:t}=n.userInfo,a=1===H.value?2:1;return 2!==a||e?1!==a||t?void(H.value=a):(w(V("暂未绑定手机号，无法进行手机验证")),!1):(w(V("暂未绑定邮箱，无法进行邮箱验证")),!1)},Z=()=>{if(""===L.value)return void w(V("entryVerifyCode"));Q.value=!0;const{email:e,phone:t}=n.userInfo,a={target:1===H.value?t:e,verifcode:L.value};1===H.value?a.phone=t:a.email=e,j(a).then((e=>{Q.value=!1,G.push(`/bindVerify?type=${J.value}&reset=1&verifyCode=${e.verifyCode}`)})).catch((()=>{Q.value=!1}))};return(e,n)=>{const a=s("fx-header"),i=T,o=z,l=u;return C(),v("div",t,[b(a,{fixed:!0},{title:p((()=>[m(x(y(V)("身份验证")),1)])),_:1}),D,g("div",E,[g("div",F,[g("h2",N,x(1===H.value?y(V)("手机验证"):y(V)("邮箱验证")),1),g("p",null,x(y(V)("为了保障您的账号安全，请验证后进行下一步操作")),1)]),g("div",U,[g("p",$,x(1===H.value?y(V)("当前绑定手机号"):y(V)("当前绑定邮箱")),1),g("h2",null,[1===H.value?(C(),v("span",B,"(+"+x(K.value)+")",1)):h("v-if",!0),m(x(y(_)(y(R),!1)),1)])]),g("div",{class:k(["code-content",{"is-ar":y(q)}])},[b(i,{modelValue:L.value,"onUpdate:modelValue":n[0]||(n[0]=e=>L.value=e),type:"tel",label:"",placeholder:y(V)("entryVerifyCode")},null,8,["modelValue","placeholder"]),g("div",{class:k(["btn",{"is-ar":y(q)}])},[b(o,{type:"primary",onClick:W},{default:p((()=>[m(x(y(V)("sendVerifyCode")),1),M.value?(C(),v(I,{key:0},[m("("+x(M.value)+")s",1)],64)):h("v-if",!0)])),_:1})],2)],2),g("div",P,[b(l,{name:"exchange"}),g("p",{onClick:X},x(1===H.value?y(V)("切换为邮箱验证"):y(V)("切换为手机验证")),1)])]),g("div",Y,[b(o,{type:"primary",loading:Q.value,onClick:Z},{default:p((()=>[m(x(y(V)("nextStep")),1)])),_:1},8,["loading"])])])}}},[["__scopeId","data-v-815bbfd7"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/verifyPage/index.vue"]]))}}}));
