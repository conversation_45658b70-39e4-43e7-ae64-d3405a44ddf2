import{aG as t,T as e,j as n,b2 as r,b3 as o,N as a,O as i}from"./index-3d21abf8.js";var c={exports:{}},u={exports:{}};u.exports=function(t){var e={};function n(r){if(e[r])return e[r].exports;var o=e[r]={i:r,l:!1,exports:{}};return t[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)n.d(r,o,function(e){return t[e]}.bind(null,o));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=0)}([function(t,e,n){var r,o;function a(t){return["image/png","image/jpeg","image/gif"].some((e=>e===t))}n.r(e),n.d(e,"canvastoDataURL",(function(){return c})),n.d(e,"canvastoFile",(function(){return u})),n.d(e,"dataURLtoFile",(function(){return l})),n.d(e,"dataURLtoImage",(function(){return d})),n.d(e,"downloadFile",(function(){return h})),n.d(e,"filetoDataURL",(function(){return f})),n.d(e,"imagetoCanvas",(function(){return p})),n.d(e,"urltoBlob",(function(){return g})),n.d(e,"urltoImage",(function(){return w})),n.d(e,"compress",(function(){return b})),n.d(e,"compressAccurately",(function(){return v})),n.d(e,"EImageType",(function(){return r})),(o=r||(r={})).PNG="image/png",o.JPEG="image/jpeg",o.GIF="image/gif";var i=function(t,e,n,r){return new(n||(n=Promise))((function(o,a){function i(t){try{u(r.next(t))}catch(e){a(e)}}function c(t){try{u(r.throw(t))}catch(e){a(e)}}function u(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(i,c)}u((r=r.apply(t,e||[])).next())}))};function c(t,e=.92,n=r.JPEG){return i(this,void 0,void 0,(function*(){return a(n)||(n=r.JPEG),t.toDataURL(n,e)}))}function u(t,e=.92,n=r.JPEG){return new Promise((r=>t.toBlob((t=>r(t)),n,e)))}var s=function(t,e,n,r){return new(n||(n=Promise))((function(o,a){function i(t){try{u(r.next(t))}catch(e){a(e)}}function c(t){try{u(r.throw(t))}catch(e){a(e)}}function u(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(i,c)}u((r=r.apply(t,e||[])).next())}))};function l(t,e){return s(this,void 0,void 0,(function*(){const n=t.split(",");let r=n[0].match(/:(.*?);/)[1];const o=atob(n[1]);let i=o.length;const c=new Uint8Array(i);for(;i--;)c[i]=o.charCodeAt(i);return a(e)&&(r=e),new Blob([c],{type:r})}))}function d(t){return new Promise(((e,n)=>{const r=new Image;r.onload=()=>e(r),r.onerror=()=>n(new Error("dataURLtoImage(): dataURL is illegal")),r.src=t}))}function h(t,e){const n=document.createElement("a");n.href=window.URL.createObjectURL(t),n.download=e||Date.now().toString(36),document.body.appendChild(n);const r=document.createEvent("MouseEvents");r.initEvent("click",!1,!1),n.dispatchEvent(r),document.body.removeChild(n)}function f(t){return new Promise((e=>{const n=new FileReader;n.onloadend=t=>e(t.target.result),n.readAsDataURL(t)}))}var m=function(t,e,n,r){return new(n||(n=Promise))((function(o,a){function i(t){try{u(r.next(t))}catch(e){a(e)}}function c(t){try{u(r.throw(t))}catch(e){a(e)}}function u(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(i,c)}u((r=r.apply(t,e||[])).next())}))};function p(t,e={}){return m(this,void 0,void 0,(function*(){const n=Object.assign({},e),r=document.createElement("canvas"),o=r.getContext("2d");let a,i;for(const t in n)Object.prototype.hasOwnProperty.call(n,t)&&(n[t]=Number(n[t]));if(n.scale){const e=n.scale>0&&n.scale<10?n.scale:1;i=t.width*e,a=t.height*e}else i=n.width||n.height*t.width/t.height||t.width,a=n.height||n.width*t.height/t.width||t.height;switch([5,6,7,8].some((t=>t===n.orientation))?(r.height=i,r.width=a):(r.height=a,r.width=i),n.orientation){case 3:o.rotate(180*Math.PI/180),o.drawImage(t,-r.width,-r.height,r.width,r.height);break;case 6:o.rotate(90*Math.PI/180),o.drawImage(t,0,-r.width,r.height,r.width);break;case 8:o.rotate(270*Math.PI/180),o.drawImage(t,-r.height,0,r.height,r.width);break;case 2:o.translate(r.width,0),o.scale(-1,1),o.drawImage(t,0,0,r.width,r.height);break;case 4:o.translate(r.width,0),o.scale(-1,1),o.rotate(180*Math.PI/180),o.drawImage(t,-r.width,-r.height,r.width,r.height);break;case 5:o.translate(r.width,0),o.scale(-1,1),o.rotate(90*Math.PI/180),o.drawImage(t,0,-r.width,r.height,r.width);break;case 7:o.translate(r.width,0),o.scale(-1,1),o.rotate(270*Math.PI/180),o.drawImage(t,-r.height,0,r.height,r.width);break;default:o.drawImage(t,0,0,r.width,r.height)}return r}))}function g(t){return fetch(t).then((t=>t.blob()))}function w(t){return new Promise(((e,n)=>{const r=new Image;r.onload=()=>e(r),r.onerror=()=>n(new Error("urltoImage(): Image failed to load, please check the image URL")),r.src=t}))}var y=function(t,e,n,r){return new(n||(n=Promise))((function(o,a){function i(t){try{u(r.next(t))}catch(e){a(e)}}function c(t){try{u(r.throw(t))}catch(e){a(e)}}function u(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(i,c)}u((r=r.apply(t,e||[])).next())}))};function b(t,e={}){return y(this,void 0,void 0,(function*(){if(!(t instanceof Blob))throw new Error("compress(): First arg must be a Blob object or a File object.");if("object"!=typeof e&&(e=Object.assign({quality:e})),e.quality=Number(e.quality),Number.isNaN(e.quality))return t;const n=yield f(t);let o=n.split(",")[0].match(/:(.*?);/)[1],i=r.JPEG;a(e.type)&&(i=e.type,o=e.type);const u=yield d(n),s=yield p(u,Object.assign({},e)),h=yield c(s,e.quality,i),m=yield l(h,o);return m.size>t.size?t:m}))}function v(t,e={}){return y(this,void 0,void 0,(function*(){if(!(t instanceof Blob))throw new Error("compressAccurately(): First arg must be a Blob object or a File object.");if("object"!=typeof e&&(e=Object.assign({size:e})),e.size=Number(e.size),Number.isNaN(e.size))return t;if(1024*e.size>t.size)return t;e.accuracy=Number(e.accuracy),(!e.accuracy||e.accuracy<.8||e.accuracy>.99)&&(e.accuracy=.95);const n=e.size*(2-e.accuracy)*1024,o=1024*e.size,i=e.size*e.accuracy*1024,u=yield f(t);let s=u.split(",")[0].match(/:(.*?);/)[1],h=r.JPEG;a(e.type)&&(h=e.type,s=e.type);const m=yield d(u),g=yield p(m,Object.assign({},e));let w,y=.5;const b=[null,null];for(let t=1;t<=7;t++){w=yield c(g,y,h);const e=.75*w.length;if(7===t){(n<e||i>e)&&(w=[w,...b].filter((t=>t)).sort(((t,e)=>Math.abs(.75*t.length-o)-Math.abs(.75*e.length-o)))[0]);break}if(n<e)b[1]=w,y-=Math.pow(.5,t+1);else{if(!(i>e))break;b[0]=w,y+=Math.pow(.5,t+1)}}const v=yield l(w,s);return v.size>t.size?t:v}))}}]),c.exports=u.exports;const s=(t,a)=>{var i;if(!(t.file.size/1024/1024<10))return e.fail("上传图片大小不能超过 10MB!"),!1;const u=n(),s=null==(i=null==u?void 0:u.userInfo)?void 0:i.token;return new Promise(((e,n)=>{c.exports.compress(t.file,.6).then((t=>{const i=new FormData;i.append("file",t),o.post(`${r}/wap/public/uploadimg!execute.action?token=${s}`,i,{onDownloadProgress:t=>{t.lengthComputeable&&a((t.loaded/t.total*100).toFixed(2))}},{headers:{"Content-Type":"multipart/form-data"},timeout:5e3}).then((t=>{const{code:n,data:r}=t.data;n/1==0&&e(r)})).catch((t=>{n(t)}))}))}))},l=t=>{const e=new FormData;return e.append("file",t.file),e.append("moduleName",t.moduleName),a({url:"/wap/api/uploadimg!execute.action",method:i.POST,data:e})};export{s as _,l as u};
