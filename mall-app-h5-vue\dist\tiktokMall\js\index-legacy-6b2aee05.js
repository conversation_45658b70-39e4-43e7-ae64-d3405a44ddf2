System.register(["./index-legacy-46a00900.js"],(function(e,t){"use strict";var a,s,n,r,i,o,l,u,d,h,c,p,g,v,f,m,x=document.createElement("style");return x.textContent=":root{--van-pull-refresh-head-height: 50px;--van-pull-refresh-head-font-size: var(--van-font-size-md);--van-pull-refresh-head-text-color: var(--van-text-color-2);--van-pull-refresh-loading-icon-size: 16px }.van-pull-refresh{overflow:hidden}.van-pull-refresh__track{position:relative;height:100%;-webkit-transition-property:-webkit-transform;transition-property:-webkit-transform;transition-property:transform;transition-property:transform,-webkit-transform}.van-pull-refresh__head{position:absolute;left:0;width:100%;height:var(--van-pull-refresh-head-height);overflow:hidden;color:var(--van-pull-refresh-head-text-color);font-size:var(--van-pull-refresh-head-font-size);line-height:var(--van-pull-refresh-head-height);text-align:center;-webkit-transform:translateY(-100%);transform:translateY(-100%)}.van-pull-refresh__loading .van-loading__spinner{width:var(--van-pull-refresh-loading-icon-size);height:var(--van-pull-refresh-loading-icon-size)}\n",document.head.appendChild(x),{setters:[e=>{a=e.P,s=e.a4,n=e.S,r=e.d,i=e.r,o=e.aB,l=e.p,u=e.aC,d=e.g,h=e.ac,c=e.e,p=e.W,g=e.s,v=e.aD,f=e.V,m=e.X}],execute:function(){const[t,x,D]=a("pull-refresh"),T=["pulling","loosing","success"],w={disabled:Boolean,modelValue:Boolean,headHeight:s(50),successText:String,pullingText:String,loosingText:String,loadingText:String,pullDistance:n,successDuration:s(500),animationDuration:s(300)};var y=r({name:t,props:w,emits:["change","refresh","update:modelValue"],setup(e,{emit:t,slots:a}){let s;const n=i(),r=i(),m=o(n),w=l({status:"normal",distance:0,duration:0}),y=u(),b=()=>{if(50!==e.headHeight)return{height:`${e.headHeight}px`}},_=()=>"loading"!==w.status&&"success"!==w.status&&!e.disabled,k=(a,s)=>{const n=+(e.pullDistance||e.headHeight);w.distance=a,w.status=s?"loading":0===a?"normal":a<n?"pulling":"loosing",t("change",{status:w.status,distance:a})},z=()=>{const{status:t}=w;return"normal"===t?"":e[`${t}Text`]||D(t)},H=()=>{const{status:e,distance:t}=w;if(a[e])return a[e]({distance:t});const s=[];return T.includes(e)&&s.push(c("div",{class:x("text")},[z()])),"loading"===e&&s.push(c(p,{class:x("loading")},{default:z})),s},S=e=>{s=0===v(m.value),s&&(w.duration=0,y.start(e))},V=e=>{_()&&S(e)},Y=()=>{s&&y.deltaY.value&&_()&&(w.duration=+e.animationDuration,"loosing"===w.status?(k(+e.headHeight,!0),t("update:modelValue",!0),g((()=>t("refresh")))):k(0))};return d((()=>e.modelValue),(t=>{w.duration=+e.animationDuration,t?k(+e.headHeight,!0):a.success||e.successText?(w.status="success",setTimeout((()=>{k(0)}),+e.successDuration)):k(0,!1)})),h("touchmove",(t=>{if(_()){s||S(t);const{deltaY:a}=y;y.move(t),s&&a.value>=0&&y.isVertical()&&(f(t),k((t=>{const a=+(e.pullDistance||e.headHeight);return t>a&&(t=t<2*a?a+(t-a)/2:1.5*a+(t-2*a)/4),Math.round(t)})(a.value)))}}),{target:r}),()=>{var e;const t={transitionDuration:`${w.duration}ms`,transform:w.distance?`translate3d(0,${w.distance}px, 0)`:""};return c("div",{ref:n,class:x()},[c("div",{ref:r,class:x("track"),style:t,onTouchstartPassive:V,onTouchend:Y,onTouchcancel:Y},[c("div",{class:x("head"),style:b()},[H()]),null==(e=a.default)?void 0:e.call(a)])])}}});e("P",m(y))}}}));
