import{d as e,_ as a,u as l,o as s,c as o,a as t,t as n,b as i,e as u,w as r,f as c,n as d,r as p,g as v,h as m,v as g,T as f,i as w,j as h,k,l as A,m as b,p as y,q as C,s as U,I as x,x as S,F as V,y as I,z as T,A as _,G as j,B,C as M,D as N,E as R}from"./index-3d21abf8.js";import{l as P}from"./config-f57d91a6.js";import{l as D}from"./config-6c4e82a2.js";/* empty css              *//* empty css               */import{A as E}from"./index.vue_vue_type_style_index_0_scoped_efb302ad_lang-ae7957c5.js";import{g as Q}from"./vue3-puzzle-vcode.es-02780b60.js";import{B as L}from"./index-2406f514.js";import X from"./index-eeb70c21.js";import{n as Y}from"./login.api-cb7fcde3.js";import"./use-route-cd41a893.js";const J={class:"code-content"},$={class:"title"},F={class:"scroll-content"},G={class:"btn"},O=e({name:"AgreeDialog"}),Z=a(Object.assign(O,{props:{modelValue:{type:Boolean,default:!1}},emits:["update:modelValue","done"],setup(e,{emit:a}){const{t:p}=l(),v=()=>{a("update:modelValue",!1)},m=()=>{a("done"),a("update:modelValue",!1)};return(a,l)=>{const g=L;return s(),o("div",{class:d([{active:e.modelValue},"area-code-dialog"])},[t("div",J,[t("div",$,n(i(p)("用户协议")),1),t("div",F,[u(X)]),t("div",G,[u(g,{type:"custom",size:"small",onClick:m},{default:r((()=>[c(n(i(p)("我已阅读")),1)])),_:1})])]),t("div",{class:"code-bg",onClick:v})],2)}}}),[["__scopeId","data-v-4bfed0a1"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/login/components/AgreeDialog.vue"]]),z={class:"code-content"},H={class:"title"},K={class:"input-content"},W={class:"input"},q=["placeholder"],ee=["width","height"],ae=e({name:"NumberCodeDialog"}),le=a(Object.assign(ae,{props:{modelValue:{type:Boolean,default:!1}},emits:["update:modelValue","done"],setup(e,{emit:a}){const w=e,{t:h}=l(),k=p(""),A=p(""),b=p(180),y=p(240),C=p(100),U=p(35),x=p(50),S=p(160),V=p(32),I=p(40),T=p(40),_=p(100),j=(e,a)=>Math.floor(Math.random()*(a-e)+e),B=(e,a)=>"rgb("+j(e,a)+","+j(e,a)+","+j(e,a)+")",M=(e,a,l)=>{e.fillStyle=B(x.value,S.value),e.font=j(V.value,I.value)+"px SimHei";let s=(l+1)*(C.value/(A.value.length+1)),o=j(I.value,U.value-5);var t=j(-15,15);e.translate(s,o),e.rotate(t*Math.PI/180),e.fillText(a,0,0),e.rotate(-t*Math.PI/180),e.translate(-s,-o)},N=e=>{for(let a=0;a<4;a++)e.strokeStyle=B(T.value,_.value),e.beginPath(),e.moveTo(j(0,C.value),j(0,U.value)),e.lineTo(j(0,C.value),j(0,U.value)),e.stroke()},R=e=>{for(let a=0;a<10;a++)e.fillStyle=B(0,100),e.beginPath(),e.arc(j(0,C.value),j(0,U.value),1,0,2*Math.PI),e.fill()},P=()=>{const e=[0,1,2,3,4,5,6,7,8,9];let a="";for(let l=0;l<4;l++){a+=e[Math.floor(Math.random()*e.length)]}A.value=a,(()=>{let e=document.getElementById("s-canvas").getContext("2d");e.textBaseline="bottom",e.fillStyle=B(b.value,y.value),e.fillRect(0,0,C.value,U.value);for(let a=0;a<A.value.length;a++)M(e,A.value[a],a);N(e),R(e)})()};v((()=>w.modelValue),(e=>{e?(A.value="",P()):k.value=""}));const D=()=>{a("update:modelValue",!1)},E=()=>{k.value?k.value===A.value?(a("done"),D()):f(h("验证码不正确")):f(h("验证码不能为空"))};return(a,l)=>{const p=L;return s(),o("div",{class:d([{active:e.modelValue},"number-code-dialog"])},[t("div",z,[t("div",H,n(i(h)("entryVerifyCode")),1),t("div",K,[t("div",W,[m(t("input",{type:"text","onUpdate:modelValue":l[0]||(l[0]=e=>k.value=e),placeholder:i(h)("verificationCode")},null,8,q),[[g,k.value]])]),t("div",{onClick:P},[t("canvas",{id:"s-canvas",width:C.value,height:U.value},null,8,ee)])]),u(p,{type:"custom",block:"",onClick:E},{default:r((()=>[c(n(i(h)("确定")),1)])),_:1})]),t("div",{class:"code-bg",onClick:D})],2)}}}),[["__scopeId","data-v-64e71d2a"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/components/number-code-dialog/index.vue"]]),se=e=>(N("data-v-2bf2fc29"),e=e(),R(),e),oe={class:"tiktok-shop-login page-main-content login-page"},te={class:"tiktok-header"},ne=se((()=>t("div",{class:"header-logo"},[t("img",{src:"/www/png/name-93a01558.png",class:"logo-icon",alt:"TikTok"}),t("span",{class:"logo-text"},"TikTok Shop")],-1))),ie={class:"language-text"},ue=se((()=>t("span",{class:"arrow-down"},null,-1))),re=se((()=>t("div",{class:"login-illustration"},[S(' <img src="@/assets/image/login/temp/tiktok-phone.svg" alt="TikTok" /> '),t("img",{src:"/www/svg/name-89e8fe48.svg",alt:"TikTok"})],-1))),ce={class:"login-title"},de={class:"type-tab"},pe=["onClick"],ve={class:"login-form"},me={key:0,class:"form-group"},ge={class:"form-label"},fe={class:"input-wrapper"},we=["placeholder"],he={key:1,class:"form-group"},ke={class:"form-label"},Ae={class:"input-wrapper phone-input"},be=se((()=>t("span",{class:"arrow-down small"},null,-1))),ye=["placeholder"],Ce={class:"form-group"},Ue={class:"password-header"},xe={class:"form-label"},Se={class:"input-wrapper"},Ve=["type","placeholder"],Ie={class:"register-link"},Te={key:2,class:"agree-content"},_e=["disabled"],je={key:0},Be={key:1},Me=a({__name:"index",setup(e){w();const{t:a,locale:r}=l(),c=h(),v=k(),N=A(),R="tiktokMall",L=p(!1),X=p(!1);p(new URL("/www/png/name-3ddd62dd.png",self.location));const J=b((()=>["familyMart"].includes(R)));new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAACVSURBVHgB7ZPJDYAgEEU/iQVYAiXYkhVoB8QKLMkStAO1Au1g/N5ckeXigZcMSxgehAlA4ncoEcnZG0aOSJRSZcZmpXTmvEYc5/2UthKOeTyCC53407/emYuaMXrI9lwNG0woGIuj0C47SGsHWQUfxF4kgxDkuUg9QpF7kUbnd7NIj0XSX/nZVwJ/0kBRs7s5npC4sgFBUms+k2q2aQAAAABJRU5ErkJggg==",self.location),b((()=>{const e=r.value||"en";return P.find((a=>a.key===e)).image})),b((()=>{const e=D.find((e=>("mbuy"===e.key?"argos":e.key)===R));return e||D[0]}));const $=new URL("/www/png/name-593a7e78.png",self.location),F=new URL("/www/png/name-ae6e9405.png",self.location),G=new URL("/www/png/name-8130147b.png",self.location),O=new URL("/www/png/name-dab02cab.png",self.location),z=new URL("/www/png/name-ca360226.png",self.location),H=[$.href,F.href,G.href,O.href,z.href],K=p(!1),W=["email","phoneNum"],q=p(0),ee=p(!1),ae=localStorage.getItem("areaCode")?localStorage.getItem("areaCode"):44,se=p(ae),Me=y({username:"",password:""}),Ne=p(!1),Re=e=>{localStorage.setItem("areaCode",e.dialCode),se.value=e.dialCode},Pe=()=>{const e=0===q.value?"entryEmail":"entryPhone";return""===Me.username?(f(a(e)),!1):""===Me.password?(f(a("entryPassword")),!1):Me.password.length<6||Me.password.length>20?(f(a("setPasswordTips")),!1):J.value&&!Qe.value?(f(`${a("我已阅读并同意")}《${a("用户协议")}》`),!1):void(["hive","argos"].includes(R)?De():["tiktok-wholesale"].includes(R)?Ne.value=!0:K.value=!0)},De=()=>{K.value=!1,ee.value=!0,Y({username:0==q.value?Me.username:`${se.value} ${Me.username}`,password:Me.password}).then((async e=>{localStorage.removeItem("storeUserName"),localStorage.removeItem("storeMobile");const l=0==q.value?"storeUserName":"storeMobile";localStorage.setItem(l,Me.username),localStorage.setItem("loginType",q.value),await c[j](e),await B().then((e=>{e.id?localStorage.setItem("sellerId",e.id):localStorage.removeItem("sellerId")})).catch((()=>{ee.value=!1})),ee.value=!1,v.setShowNotic(!0),f.success(a("loginSuc")),N.push("/shop")})).catch((e=>{ee.value=!1}))},Ee=()=>{const{hostname:e,origin:a}=window.location,l="localhost"===e?"https://thsjbvh.site/promote/#/":`${a}/promote/#/`;window.open(`${l}?lang=${r.value}`)},Qe=p(!1),Le=p(!1);return C((()=>{c.logout()})),U((()=>{const e=localStorage.getItem("loginType");q.value=e?Number(e):0;const a=0==q.value?"storeUserName":"storeMobile",l=localStorage.getItem(a);Me.username=l||""})),(e,l)=>{const c=x;return s(),o("div",oe,[S(" 维持原有功能组件 "),u(le,{modelValue:Ne.value,"onUpdate:modelValue":l[0]||(l[0]=e=>Ne.value=e),onDone:De},null,8,["modelValue"]),u(Z,{modelValue:Le.value,"onUpdate:modelValue":l[1]||(l[1]=e=>Le.value=e),onDone:l[2]||(l[2]=e=>Qe.value=!0)},null,8,["modelValue"]),u(E,{modelValue:X.value,"onUpdate:modelValue":l[3]||(l[3]=e=>X.value=e),onDone:Re},null,8,["modelValue"]),u(i(Q),{successText:i(a)("vertifyPass"),failText:i(a)("vertifuFail"),sliderText:i(a)("vertifyTips"),imgs:H,show:K.value,onSuccess:De},null,8,["successText","failText","sliderText","show"]),S(" 头部 "),t("div",te,[ne,t("div",{class:"language-selector",onClick:l[4]||(l[4]=e=>i(N).push("/language"))},[t("span",ie,n("en"===i(r)?"US English":i(a)("language")),1),ue])]),S(" 插图 "),re,S(" 登录标题 "),t("h2",ce,n(i(a)("login")),1),S(" 登录方式切换 "),t("div",de,[(s(),o(V,null,I(W,((e,l)=>t("div",{key:l,class:d([{active:l===q.value},"item"]),onClick:e=>(e=>{if(e!==q.value){q.value=e;const a=0==q.value?"storeUserName":"storeMobile",l=localStorage.getItem(a);Me.username=l||"",Me.password=""}})(l)},n(i(a)(e)),11,pe))),64))]),S(" 登录表单 "),t("div",ve,[S(" 邮箱输入 "),0===q.value?(s(),o("div",me,[t("label",ge,n(i(a)("email")),1),t("div",fe,[m(t("input",{"onUpdate:modelValue":l[5]||(l[5]=e=>Me.username=e),type:"text",class:"form-input",placeholder:i(a)("entryEmail")},null,8,we),[[g,Me.username]])])])):S("v-if",!0),S(" 手机号输入 "),1===q.value?(s(),o("div",he,[t("label",ke,n(i(a)("phoneNum")),1),t("div",Ae,[t("div",{class:"area-code",onClick:l[6]||(l[6]=e=>X.value=!0)},[t("span",null,"+"+n(se.value),1),be]),m(t("input",{"onUpdate:modelValue":l[7]||(l[7]=e=>Me.username=e),type:"tel",class:"form-input",placeholder:i(a)("entryPhone")},null,8,ye),[[g,Me.username]])])])):S("v-if",!0),S(" 密码输入 "),t("div",Ce,[t("div",Ue,[t("label",xe,n(i(a)("password")),1),t("a",{class:"forgot-link",onClick:l[8]||(l[8]=(...e)=>i(M)&&i(M)(...e))},n(i(a)("forgetPassword")),1)]),t("div",Se,[m(t("input",{"onUpdate:modelValue":l[9]||(l[9]=e=>Me.password=e),type:L.value?"text":"password",class:"form-input",placeholder:i(a)("entryPassword")},null,8,Ve),[[T,Me.password]]),t("button",{class:"password-toggle",onClick:l[10]||(l[10]=e=>L.value=!L.value)},[t("i",{class:d(["eye-icon",{"eye-open":L.value}])},null,2)])])]),S(" 注册链接 "),t("div",Ie,[t("span",null,n(i(a)("如果您没有账号"))+", ",1),t("a",{onClick:Ee,class:"link"},n(i(a)("点击注册")),1)]),S(" 协议同意 "),i(J)?(s(),o("div",Te,[t("div",{class:"check",onClick:l[11]||(l[11]=e=>Qe.value=!Qe.value)},[Qe.value?(s(),_(c,{key:0,name:"success",size:12})):S("v-if",!0)]),t("p",null,[t("span",{onClick:l[12]||(l[12]=e=>Qe.value=!Qe.value)},n(i(a)("我已阅读并同意")),1),t("span",{class:"link",onClick:l[13]||(l[13]=e=>Le.value=!0)},"《"+n(i(a)("用户协议"))+"》",1)])])):S("v-if",!0),S(" 登录按钮 "),t("button",{class:"login-button",disabled:ee.value,onClick:Pe},[ee.value?(s(),o("span",Be,"Loading...")):(s(),o("span",je,n(i(a)("login")),1))],8,_e)])])}}},[["__scopeId","data-v-2bf2fc29"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/login/index.vue"]]);export{Me as default};
