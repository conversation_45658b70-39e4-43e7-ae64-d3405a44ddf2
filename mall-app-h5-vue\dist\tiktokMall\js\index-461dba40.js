import{_ as e,d as a,u as t,r as l,T as n,B as i,av as s,c as r,e as o,w as c,a as d,F as u,y as g,x as v,a_ as m,o as p,f as A,t as h,D as f,E as b}from"./index-3d21abf8.js";import{N as C}from"./index-cfdda867.js";import{B as L}from"./index-2406f514.js";import{S as B,a as R}from"./index-94c336c5.js";import{u as U}from"./upload.api-28f256be.js";import"./use-placeholder-c97cb410.js";import"./use-route-cd41a893.js";const w=a({name:"ShopBanner",components:{VuePictureCropper:B},setup(){const{t:e}=t(),a=l(localStorage.getItem("sellerId")||""),s=new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFgAAABYCAYAAABxlTA0AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAThSURBVHgB7ZxdTttaEMeHDyFEQMoOrnfQsIJrVgCR+Hy6zgpu7wpIV9DeFeA+QQhS6QpqVkC6gywhL8lLCPQ/xa4QTY5PSjxnWuYnRU7k48T5e3zOnJk5JjIMwzAMwzAUskRKODs7q29sbLxdWVn5i15GNh6Pb05OTvqkABUCn5+fRxD2C95GtBj6k8lkR4PIwQWuQNwCFSIHFbhCcQuCixxMYAFxC4KKHERgQXELgoksLnAAcQuCiCwqcEBxC8RFFhNYgbgFoiKLCKxI3AIxkSsXWKG4BSIizyUwT2drtdrew8PDG4hW9znm/v4+Jn3iFvSXl5czn4a4GANsbo6Ojq5pDrwF7na7b7E5hbhewv7B8EVp7+/vf/Rp7CVwp9NpLy0tnZLxAxjaO1hzu6xdqcCXl5cJNmdk/AS6v/+Oj48/uNosUzlmuTNAV3HK45KzjWvn1dXVHukdoDRQ39ra2nU1cAqMfuZvMpzAu2i49q+6dmrxGHAefQyyfbzlV/3Jq0GBwXk5NXIKHJABRE1x8tfD4fBrq9UazGqIQZjvsgTtY7SPSBnaBM7wah8eHt74HpC3/d4es8aEBx5NQmsReACXpw2X5396AZj2ptikFxcXnDxVMSnycdMqBSL0MFBsv1Tcp7Bvend3t819NwUmqMCw2nQ0GlUScOHvXFtb28bbuWIHiyZkF3ENS2tRhTSbTR4cm5jq84D5DwUgiAXzrQvvoFJxnwJL5kBVjwIQwoJ5QNtxuV5PgRvGcZBkxu4UXkTphWJLhofRXF1dvZUe+MQtmL2FENld/k0Mpu9IGFGBuWtYpLcwL3nkKyNBpC24TYGRtmIxgdl6EaD2ygJUCbqKDB6FV/+/CMQExp/KSA8pCSHZRaSkBMzyPpMQYm6aK4CDwP7MlFSelZ5F7DoWicmpLtz6+npvPB6TBCICc7zBtR8iJvRrRCXHThWY/WL4130SyNaIdBGSg4oveQC/ckQEhgWrE1gq0vZqLZgeU06VI+VFRKSMslzaoni1ApOQBUu5aREXaDgiaOmsA13JzDzbnNEvgGNFMtJifjB8T/5D2bR9rpCjK1zJ4vqEK5/T7XZjCEwSiM3kkO3dJSVAXLHshmQsIiE9xCSEZCyijqxCTIHJq0UjEkI0Hsy1ChQe0XOQDrjHXBRCgcir9CMSRDwnx6VNvDCGhMl/U/wOCpFVrkPkT/CLvTLLuRv2ohQ/++C80ilEKVWQugh4FI1arfaehMBvsS8dUQBCVvYknU6nPhqNWr41EvOSP0WFM8l7FIigtWmw5D1Y120VfTJ/J777S6iSqYLg1ZWAV4LeLtK7YG+Bq3hIQQW8lvpgHvjeYxLw7zyL/J7DMQZ6XCwZkxK0Vbhzji2F0G285/qFjwcHB9msxtzHbm5uNnDMLi5MonEVqtY1GhE9rrtIIDZ/7nFWhFNPEJK3Eb+4HUfFsI+komPzolXg5zQKAWGt9DuhYZD7ozGBK8YErhinwErT7aooK2BxCixZJPe7gsE3c+33eV4EP28nJmManHTdcTUo7YMnk0nLuoqpDFibskalAvPiEdwGOxpWTSqCDc5rAaWXF4HboMdLr0hREXUgeBb5gZf+siY+B8z93LT80V5vuLYLor+aJ1Dh/34dDof9qmLXhmEYhmEYhmEYhmEYhmF8AwdhLA50cpBqAAAAAElFTkSuQmCC",self.location),r=l([{name:`${e("storeBanner")} 1(1920X300)`,id:"banner1",dataURL:"",blobURL:"",file:"",imgUrl:""},{name:`${e("storeBanner")} 2(1920X300)`,id:"banner2",dataURL:"",blobURL:"",file:"",imgUrl:""},{name:`${e("storeBanner")} 3(1920X300)`,id:"banner3",dataURL:"",blobURL:"",file:"",imgUrl:""}]);n.loading({duration:0,message:e("loading"),forbidClick:!0}),i().then((e=>{r.value[0].dataURL=e.banner1||"",r.value[1].dataURL=e.banner2||"",r.value[2].dataURL=e.banner3||"",n.clear()})).catch((()=>{n.clear()}));const o=l(!1),c=l(""),d=l(null),u=l(!1);return{bannerImg:s,bannerData:r,submitLoading:u,isShowModal:o,cropperImage:c,showBtn:a,t:e,selectFile:(e,a)=>{c.value="",r.value[a].dataURL="",r.value[a].blobURL="",r.value[a].file="",d.value=a;const{files:t}=e.target;if(!t||!t.length)return;const l=t[0],n=new FileReader;n.readAsDataURL(l),n.onload=()=>{c.value=String(n.result),o.value=!0}},getResult:async()=>{if(!R)return;const e=R.getDataURL(),a=await R.getBlob();if(!a)return;const t=await R.getFile();if(null===d.value)return o.value=!1,!1;r.value[d.value].dataURL=e,r.value[d.value].blobURL=URL.createObjectURL(a),r.value[d.value].file=t,o.value=!1},croperBack:()=>{R&&(R.clear(),R.reset(),o.value=!1)},submitHandle:async()=>{const a=r.value.filter((e=>e.file));if(a.length){u.value=!0;try{await(async e=>{for(let a=0;a<e.length;a++)await U({file:e[a].file,moduleName:e[a].id}).then((t=>{r.value.find((t=>t.id===e[a].id)).imgUrl=t}))})(a)}catch(t){return u.value=!1,!1}const l={};a.forEach((e=>{l[e.id]=e.imgUrl})),m(l).then((()=>{u.value=!1,r.value.map((e=>{e.file="",e.imgUrl=""})),n.success(e("saveSuc"))})).catch((()=>{u.value=!1}))}else n(e("noNeedSub"))}}}}),x=(e=>(f("data-v-4732ee21"),e=e(),b(),e))((()=>d("div",{style:{height:"46px"}},null,-1))),k={class:"banner-content"},S={class:"title"},F={class:"img-content"},E=["onChange"],Q=["src"],D={key:1,class:"default"},M=["src"],I={class:"submit-btn"},j={key:0,class:"croper-content-modal"};const X=e(w,[["render",function(e,a,t,l,n,i){const m=s("fx-header"),f=L,b=C,B=s("VuePictureCropper");return p(),r("div",null,[o(m,{fixed:!0},{title:c((()=>[A(h(e.t("bannerSet")),1)])),_:1}),x,d("div",k,[(p(!0),r(u,null,g(e.bannerData,((a,t)=>(p(),r("div",{key:a.id,class:"item"},[d("div",S,h(a.name),1),d("div",F,[d("input",{type:"file",accept:"image/jpg, image/jpeg, image/png, image/gif",onChange:a=>e.selectFile(a,t)},null,40,E),a.dataURL?(p(),r("img",{key:0,src:a.dataURL,alt:""},null,8,Q)):(p(),r("div",D,[d("img",{src:e.bannerImg,alt:""},null,8,M),d("p",null,h(e.t("addBanner")),1)]))])])))),128)),d("div",I,[o(f,{loading:e.submitLoading,type:"primary",size:"large",disabled:!e.showBtn,onClick:e.submitHandle},{default:c((()=>[A(h(e.showBtn?e.t("save"):e.t("商家入驻尚未完成")),1)])),_:1},8,["loading","disabled","onClick"])])]),e.isShowModal?(p(),r("div",j,[o(b,{title:e.t("selectPhotos"),"right-text":e.t("sure"),"left-arrow":"",fixed:"",onClickLeft:e.croperBack,onClickRight:e.getResult},null,8,["title","right-text","onClickLeft","onClickRight"]),o(B,{boxStyle:{width:"100%",height:"100%",backgroundColor:"#f8f8f8",margin:"auto"},img:e.cropperImage,options:{viewMode:1,dragMode:"crop",aspectRatio:6.4}},null,8,["img"])])):v("v-if",!0)])}],["__scopeId","data-v-4732ee21"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/shop/banner/index.vue"]]);export{X as default};
