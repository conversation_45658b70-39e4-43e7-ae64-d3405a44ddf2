import{_ as s,d as a,u as e,j as t,m as i,r as n,s as l,av as c,I as r,c as o,e as A,w as v,a as d,F as f,y as g,T as h,by as p,o as u,f as S,t as m,b as w,D as I,E as L,ak as x}from"./index-3d21abf8.js";import{c as b}from"./index-3ab60a77.js";import{d as C}from"./config-0489190f.js";const W={class:"page-main-content"},R=(s=>(I("data-v-4b627e45"),s=s(),L(),s))((()=>d("div",{style:{height:"46px"}},null,-1))),H={class:"settings-nav-content"},U=["onClick"],y={class:"icon"},O=["src"],E={class:"title"},k=a({name:"ShopSettings"}),B=s(Object.assign(k,{setup(s){const{t:a}=e(),I=t(),L=i((()=>{var s;return(null==(s=I.userInfo)?void 0:s.signPdfUrl)||""})),k=n([]),B=b(C);if(["argos","argos2","int"].includes("tiktokMall")){const s=B.findIndex((s=>"soical"===s.title));B.splice(s,1)}k.value=B;return l((async()=>{h.loading({duration:0}),await I.getUserInfo(!0),await p().then((s=>{("string"==typeof s.sellerSign?JSON.parse(s.sellerSign):s.sellerSign)&&k.value.push({title:"电子合同",icon:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAALvSURBVHgB7ZtNaBNBFMf/O2laWmu7otha/MhVlGIFDxYFezOgWLy0oAc9KKKHXvTSi8aDIvRQDxWsVz14i+DBi6gIvSg0+HERxWhBU62w0VLbptnxvU0qMeql2ZlsZ/dXttlNQsgvb+bNvEnGQhXPOlsPiKI8AoF+CSSwGpHI0L9MQbipvbn5bOVD1vLJpG3bxaalS/TEIRgECY6KhXiqx3Gc8nVZtrHwiK52wUQo4rHFeB9LC772ImuqLENublPhonc62WknirLwHiFAWlafcN2SeRjgZCykyU25CilkP/fh0AhTD04IhIxI2HQiYdMJnXADfKBpyzZsvnwNsfZ2rJRvd+/QcRuq8UW4tXcf7OQh1MLa3v30wW3Fp5ErUEmgmvSm88PookMlgRB+d2IQxe9571y1dCCE516/xJujSS3SgWnSc69eaJEOVB/WIR24cVi1dCAnHv+S5mHLD3wZh2tl46mzv+Uq+THxFPbB0vjeSGO0HwRCuOP0OegiKh5MJxJWzcLUB7w9Oai8SPgf2oW5DOTvd5wH91EPtAvH2ko1czGfRz3QLtyys9sbXxvaVr5YUAv6I0yrIvWKLqNdmKeNPE3k5DV9awy60T7T+kzZOXH9pteXeb78kz4AyW+EIt+8oxsbBo5DJVojPEOLdFb5Rwecpbl5c9YuTH2kPm3j6/gNL/Iq0SY8PT6G3MhVLOUdL8qszZHmg/lCsnwnr4CqREuTzg6dwSJFTtJf14Vhqo7+LBbWDxyDLpQLczOepWFItLdhXfLwX7K6US7cQomIExRnZl6srzfqhWmisf3hBIJCVC2ZTiRsOr4kLZ41cQWkCl7R9Ov1recdayRCRNSHTScSNp1I2HQE1dxZhAWJDEc4jdAgM8K1rHsICbytR+zJzT7mrS4wHMvFKO9h8pKWaI6n6CYDU+G+W/AcS1m6J+s4seZ4n4mR5sgu71nyrqufwNt6eKdLefPHqtwP4Y08LtKWLKZ3z8w/qXzsF8j5Dmb3Wu5KAAAAAElFTkSuQmCC",self.location),href:"/shop/contract"}),h.clear()})).catch((()=>{h.clear()}))})),(s,e)=>{const t=c("fx-header"),i=r;return u(),o("div",W,[A(t,{fixed:!0},{title:v((()=>[S(m(w(a)("店铺设置")),1)])),_:1}),R,d("div",H,[(u(!0),o(f,null,g(k.value,((s,e)=>(u(),o("div",{key:e,class:"item",onClick:a=>{var e;"/shop/contract"===(e=s).href?L.value?x(e.href):x("/shop/contractSign"):x(e.href)}},[d("div",y,[d("img",{src:s.icon.href,alt:""},null,8,O)]),d("div",E,m(w(a)(s.title)),1),A(i,{name:"arrow"})],8,U)))),128))])])}}}),[["__scopeId","data-v-4b627e45"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/shop/settings/index.vue"]]);export{B as default};
