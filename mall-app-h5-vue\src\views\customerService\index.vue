<template>
  <div
    class="service-box flex flex-col has-nav-bar"
    :class="{ 'ios-device': isIOS }"
    :style="{ height: height + 'px' }"
  >
    <div class="service-header">
      <van-nav-bar
        :fixed="true"
        ref="navEl"
        :title="$t('onLineService')"
        left-arrow
        @click-left="onClickLeft"
      />
    </div>
    <div
      ref="msgContent"
      class="content flex-1 overflow-auto"
      style="background: #f5f5f5;"
    >
      <div
        class="flex flex-col px-16 box-border h-full chat-container"
        style="background: #f5f5f5;"
      >
        <div
          class="w-full py-2 text-grey text-center history-btn"
          @click="onMore"
          :style="{ display: finished ? 'none' : 'block' }"
        >
          {{ $t('historyMessage') }}
        </div>
        <!-- 临时测试按钮 -->
        <div class="w-full py-2 text-center">
          <button @click="testFetchList" style="background: #007aff; color: white; padding: 8px 16px; border: none; border-radius: 4px;">
            测试获取消息 (当前: {{ list.length }})
          </button>
        </div>
        <ul ref="msgTxtContent" class="flex flex-col pt-2 message-list">
          <li
            v-for="(item, index) in list"
            :key="item.id"
            class="flex flex-col my-2 message-item"
          >
            <p
              class="font-12 text-center py-1 text-grey time-divider"
              v-if="showTime(index)"
            >
              {{ formatZoneDate(item.createtime, 'YYYY-MM-DD') }}
            </p>

            <div
              class="flex responser-content items-center"
              :class="item.send_receive === 'send' ? 'justify-end' : ''"
            >
              <template v-if="item.send_receive === 'receive'">
                <img
                  :src="serviceLogo"
                  class="w-9 h-9"
                  :class="isArLang ? 'ml-3' : 'mr-3'"
                  style="border-radius: 50%"
                />
                <div>
                  <div class="text-xs text-grey mb-1">
                    {{ formatZoneDate(item.createtime, 'YYYY-MM-DD HH:mm') }}
                  </div>
                  <!-- <div v-if="item.type === 'text'" class="chat-res-content text-sm">{{ item.content }}</div> -->
                  <div
                    v-if="item.type === 'text'"
                    class="chat-res-content text-sm"
                  >
                    <p v-html="item.content"></p>
                  </div>
                  <div v-else class="chat-res-content img text-sm">
                    <img :src="item.imgUrl" @click="onPreview(item.imgUrl)" />
                  </div>
                </div>
              </template>
              <template v-else>
                <div>
                  <div class="text-xs text-grey mb-1" style="text-align: right">
                    {{ formatZoneDate(item.createtime, 'YYYY-MM-DD HH:mm') }}
                  </div>
                  <!-- <div v-if="item.type === 'text'" class="chat-res-content text-sm" :class="item.send_receive === 'send' ? 'send-bg' : ''">{{ item.content }}</div> -->
                  <div
                    v-if="item.type === 'text'"
                    class="chat-res-content text-sm"
                    :class="item.send_receive === 'send' ? 'send-bg' : ''"
                  >
                    <p v-html="item.content"></p>
                  </div>
                  <div v-else class="chat-res-content img text-sm">
                    <img
                      :src="`${item.imgUrl}`"
                      @click="onPreview(item.imgUrl)"
                    />
                  </div>
                </div>
                <img
                  :src="fullAvatar"
                  class="w-9 h-9"
                  :class="isArLang ? 'mr-3' : 'ml-3'"
                  style="border-radius: 50%"
                />
              </template>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div
      class="relative bottom bottomBox flex justify-between items-center w-full fixed bottom-0 borderTop px-3 py-2 box-border bgBottom bg-white ios-bottom-input"
    >
      <!-- <van-uploader :max-size="10000 * 1024" @oversize="onOversize" :after-read="afterRead" :capture="androidAttrs ? 'camera' : null"> -->
      <van-uploader
        :max-size="10000 * 1024"
        @oversize="onOversize"
        :after-read="afterRead"
        class="upload-btn"
      >
        <img :src="iconImg.photo" class="w-8 h-8" />
      </van-uploader>
      <div
        class="flex-1 mx-3 h-full border-none bgBottom textColor send-msg-content"
      >
        <textarea
          v-model="message"
          :placeholder="$t('entryYouMessage')"
          class="flex-1 mx-3 h-full border-none bgBottom textColor"
          style="resize: none; background-color: #fff;"
        ></textarea>
      </div>

      <i class="iconfont icon-fasong" @click="send('text', message)"></i>
    </div>
  </div>
</template>

<script setup>
import { Uploader, ImagePreview } from 'vant'
import { _getMsg, _getUnreadMsg, _sendMsg } from '@/service/im.api'
import { _uploadImage, uploadimgExecute } from '@/service/upload.api'
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { Toast } from 'vant'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore, useShopInfoStore } from '@/store/user.js'
import { formatZoneDate } from '@/utils'
import { useWindowSize } from '@vueuse/core'
import { arLangCheck } from '@/utils/arLangCheck'

const isArLang = arLangCheck()

const { height } = useWindowSize()

// iOS设备检测
const isIOS = ref(false)
onMounted(() => {
  const userAgent = navigator.userAgent.toLowerCase()
  isIOS.value = /iphone|ipad|ipod/.test(userAgent) || window.navigator.standalone === true

  // 如果是iOS设备，添加类名到body
  if (isIOS.value) {
    document.body.classList.add('ios-device')
  }
})

const fullAvatar = ref(
  new URL(`/src/assets/image/avatar/avatar_d.png`, import.meta.url).href
)

const { t, locale } = useI18n()

const route = useRoute()

const router = useRouter()
const list = ref([])
const message = ref('')
const lastMsgId = ref('')
const interval = ref(null)
const unread = ref(0)
const finished = ref(false)
const androidAttrs = ref(null)
const navEl = ref(null)
const navHeight = ref(0)
const startInterval = ref(false)

const iconImg = {
  responser: new URL('../../assets/image/service/responser.png', import.meta.url),
  argos: new URL('../../assets/image/logo/argos.png', import.meta.url),
  argos2: new URL('../../assets/image/logo/argos.png', import.meta.url),
  familyShop: new URL('../../assets/image/logo/familyShop.png', import.meta.url),
  hive: new URL('../../assets/image/logo/hive.png', import.meta.url),
  inchoi: new URL('../../assets/image/logo/inchoi.png', import.meta.url),
  mbuy: new URL('../../assets/image/logo/mbuy.png', import.meta.url),
  greenMall: new URL('../../assets/image/logo/greenMall.png', import.meta.url),
  tiktokMall: new URL('../../assets/image/logo/tiktokMall.png', import.meta.url),
  shop2u: new URL('../../assets/image/logo/shop2u.png', import.meta.url),
  sm: new URL('../../assets/image/logo/sm.png', import.meta.url),
  iceland: new URL('../../assets/image/logo/iceland.png', import.meta.url),
  int: new URL('../../assets/image/logo/int.png', import.meta.url),
  'tiktok-wholesale': new URL('../../assets/image/logo/tiktok-wholesale.png', import.meta.url),
  antMall: new URL('../../assets/image/logo/antMall.png', import.meta.url),
  simon: new URL('../../assets/image/logo/simon.png', import.meta.url),
  texm: new URL('../../assets/image/logo/texm.png', import.meta.url),
  'alibaba-wholesale': new URL('../../assets/image/logo/alibaba-wholesale.png', import.meta.url),
  photo: new URL('../../assets/image/service/photo.png', import.meta.url)
}

const msgContent = ref(null)
const msgTxtContent = ref(null)
const sendOrFirstLoad = ref(false)

const serviceLogo = computed(() => {
  const mode = import.meta.env.MODE
  let logo = iconImg.responser.href
  if (mode && iconImg[mode]) {
    logo = iconImg[mode].href
  }

  return logo
})

// 店铺信息
const useShopStore = useShopInfoStore()
const shopLogo = computed(() => {
  return useShopStore.shopInfo?.avatar || ''
})

onMounted(async () => {
  navHeight.value = navEl.value.$el.getBoundingClientRect().height
  startInterval.value = false
  const model = navigator.userAgent
  // 判断是否是安卓手机，是则是true
  androidAttrs.value =
    model.indexOf('Android') > -1 || model.indexOf('Linux') > -1

  const { avatar, token, lang } = route.query
  if (shopLogo.value) {
    fullAvatar.value = shopLogo.value
  }

  if (avatar) {
    fullAvatar.value = avatar
  }

  if (lang) {
    locale.value = lang
    localStorage.setItem('lang', lang)
  }

  if (token) {
    const userStore = useUserStore()
    await userStore.getUserInfo(true, token)
  }

  sendOrFirstLoad.value = true
  fetchList()
})

const onOversize = (file) => {
  Toast(t('fileMaxLimit'))
}
const onPreview = (url) => {
  // 预览
  ImagePreview([url])
}
const showTime = (index) => {
  // 时间显示
  let res = false
  if (index === 0) {
    res = true
  }
  if (
    index > 0 &&
    list.value[index].createtime.split(' ')[0] !==
      list.value[index - 1].createtime.split(' ')[0]
  ) {
    res = true
  }
  return res
}
const afterRead = (file) => {
  // 文件上传
  Toast.loading({ duration: 0 })
  uploadimgExecute({
    file: file.file,
    moduleName: 'customerService'
  })
    .then((data) => {
      Toast.clear()
      send('img', data)
    })
    .catch(() => {
      Toast.clear()
    })

  // _uploadImage(file, (percent) => {
  //   console.log(percent)
  // }).then(data => {
  //   Toast.clear()
  //   send('img', data)
  // }).catch(() => {
  //   Toast.clear()
  // })
}
const fetchList = (message_id = '') => {
  // 获取消息列表
  _getMsg({ message_id }).then((data) => {
    console.log('获取到消息数据:', data, '消息数量:', data?.length, '数据类型:', typeof data)

    // 确保data是数组
    if (!Array.isArray(data)) {
      console.log('数据不是数组，尝试转换:', data)
      if (data && typeof data === 'object' && data.list) {
        data = data.list
      } else if (data && typeof data === 'object' && data.data) {
        data = data.data
      } else {
        console.log('无法处理的数据格式')
        data = []
      }
    }

    if (!lastMsgId.value && data.length > 0) {
      lastMsgId.value = data[data.length - 1]['id']
    }

    if (data && data.length > 0) {
      let dataArr = []
      data.forEach((item) => {
        // 确保每个消息项都有必要的字段
        if (!item.id) {
          console.warn('消息缺少id字段:', item)
          return
        }

        if (item.type === 'img') {
          const imgUrl = item.content
          const str = 'imagePath='
          item.imgUrl = imgUrl
          const index = imgUrl.indexOf(str)
          if (index > 0) {
            const url = imgUrl.slice(index + str.length)
            item.imgUrl = url
          }
        }
      })

      if (message_id) {
        // 加载更多
        if (data.length > 0) {
          lastMsgId.value = data[data.length - 1]['id']
        }
        dataArr = filterDeleteMsg([...data.reverse(), ...list.value])
      } else {
        const beforeFilter = [...list.value, ...data.reverse()]
        console.log('去重前数据:', beforeFilter.length)
        dataArr = filterDeleteMsg(beforeFilter)
        console.log('过滤删除消息后:', dataArr.length)

        // 简化去重逻辑
        const seen = new Set()
        dataArr = dataArr.filter(item => {
          if (seen.has(item.id)) {
            console.log('发现重复消息:', item.id)
            return false
          }
          seen.add(item.id)
          return true
        })
        console.log('去重后数据:', dataArr.length)
      }

      list.value = dataArr
      console.log('最终消息列表:', list.value, '数量:', list.value.length)

      if (data.length < 10) {
        finished.value = true
      }
    } else {
      console.log('没有获取到消息数据或数据为空')
    }

    if (sendOrFirstLoad.value) {
      nextTick(() => {
        if (msgContent.value && msgTxtContent.value) {
          msgContent.value.scrollTop = msgTxtContent.value.offsetHeight
        }
        sendOrFirstLoad.value = false
      })
    }

    if (!startInterval.value) {
      startInterval.value = true
      interval.value = setInterval(() => {
        fetchList()
      }, 1000)
    }
  }).catch((error) => {
    console.error('获取消息列表失败:', error)
    // 即使API调用失败，也要启动定时器以便重试
    if (!startInterval.value) {
      startInterval.value = true
      interval.value = setInterval(() => {
        fetchList()
      }, 1000)
    }
  })
}

const filterDeleteMsg = (data) => {
  if (!data || !Array.isArray(data)) {
    console.log('filterDeleteMsg: 数据无效', data)
    return []
  }
  let ids = []
  ids = data.filter(item => Number(item.delete_status) === -1).map(item => item.id)
  const filteredData = data.filter(item => !ids.includes(item.id))
  console.log('filterDeleteMsg: 过滤前', data.length, '过滤后', filteredData.length)
  return filteredData
}

const onMore = () => {
  // 加载更多
  fetchList(lastMsgId.value)
}
const clearIntervalTimer = () => {
  if (interval.value) {
    clearInterval(interval.value)
    interval.value = null
  }
}
const fetchUnread = () => {
  // 获取未读
  _getUnreadMsg().then((data) => {
    unread.value = data
    // console.log(data)
  }).catch((error) => {
    console.error('获取未读消息失败:', error)
  })
}

// 测试函数
const testFetchList = () => {
  console.log('手动触发获取消息列表')
  fetchList()
}
const onClickLeft = () => {
  // 返回 - 增强iOS兼容性
  try {
    // 尝试使用history.back()
    if (window.history.length > 1) {
      window.history.back()
    } else {
      // 如果没有历史记录，跳转到首页
      router.push('/')
    }
  } catch (error) {
    console.error('返回操作失败:', error)
    // 备用方案
    router.push('/')
  }
}

const sendLoading = ref(false)
const send = (type = 'text', content = '') => {
  // 发送消息, img 也当消息text
  if (sendLoading.value) {
    return
  }
  if (!content) {
    Toast(t('entryMessageContent'))
    return
  }

  Toast.loading({
    duration: 0,
    forbidClick: true
  })
  sendLoading.value = true

  _sendMsg(type, content)
    .then((data) => {
      console.log(data)
      message.value = ''
      sendLoading.value = false
      Toast.clear()
      // document.getElementById('bottom').click()
      sendOrFirstLoad.value = true
      fetchList()
    })
    .catch(() => {
      sendLoading.value = false
      Toast.clear()
    })
}
onUnmounted(() => {
  clearIntervalTimer()
})
</script>
<style lang="scss" scoped>
.service-box {
  width: 100%;
  box-sizing: border-box;
  position: relative;

  :deep(.van-hairline--bottom::after) {
    border-color: #f3f3f3;
  }

  /* iOS设备特殊适配 */
  &.ios-device {
    padding-bottom: env(safe-area-inset-bottom);
    padding-bottom: var(--ios-safe-area-bottom, 34px);
  }
}

.service-header {
  position: relative;
  z-index: 100;
}

.chat-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;

  .history-btn {
    flex-shrink: 0;
    margin-bottom: 8px;
    font-size: 12px;
    padding: 8px 0;
  }

  .message-list {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding-bottom: 10px;

    .message-item {
      margin: 8px 0;

      &:first-child {
        margin-top: 0;
      }

      &:last-child {
        margin-bottom: 0;
      }

      .time-divider {
        margin: 4px 0;
        font-size: 11px;
        opacity: 0.7;
      }
    }
  }
}

.content {
  padding-top: 46px; /* 默认导航栏高度 */
  padding-bottom: 0; /* 底部间距设置为0，最大化空间利用 */
  min-height: calc(100vh - 46px);
  max-height: calc(100vh - 46px);

  /* iOS设备内容区域适配 */
  .ios-device & {
    padding-top: calc(46px + env(safe-area-inset-top, 0px));
    padding-bottom: 0; /* iOS设备底部间距也设置为0 */
    min-height: calc(100vh - 46px - env(safe-area-inset-top, 0px));
    max-height: calc(100vh - 46px - env(safe-area-inset-top, 0px));
  }
}

.send-bg {
  background-color: rgb(255, 234, 209) !important;
}

.break-word {
  word-wrap: break-word;
}

.max-w-230 {
  max-width: 115px;
}

.responser {
  position: relative;

  &::after {
    content: '';
    width: 0;
    height: 0;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-right: 10px solid #f3f3f3;
    position: absolute;
    left: -10px;
    top: 10px;
  }
}

.borderTop {
  border-top: 1px solid #f3f3f3;
}

.bottomBox {
  min-height: 46px; /* 减少最小高度 */
  max-height: 76px; /* 相应减少最大高度 */
  z-index: 1000;
  padding: 6px 12px; /* 减少上下padding */

  /* 优化输入框布局 */
  .send-msg-content {
    display: flex;
    align-items: center;
    min-height: 32px; /* 减少最小高度 */

    textarea {
      min-height: 32px; /* 减少最小高度 */
      max-height: 56px; /* 相应减少最大高度 */
      line-height: 1.4;
      padding: 6px 12px; /* 减少上下padding */
      border-radius: 16px; /* 相应调整圆角 */
      border: 1px solid #e5e5e5;
      font-size: 14px;

      &:focus {
        outline: none;
        border-color: var(--site-main-color, #007aff);
      }
    }
  }

  /* iOS设备底部输入框适配 */
  &.ios-bottom-input {
    bottom: env(safe-area-inset-bottom, 0px);
    padding-bottom: 20px !important; /* 固定底部padding为20px */
    min-height: calc(46px + env(safe-area-inset-bottom, 0px)); /* 减少最小高度 */
  }
}

.black {
  color: #1f2025;
}

.chatBg {
  border-radius: 10.0022px 10.0022px 0px 10.0022px;
  max-width: 50vw;
}

.responser-content {
  > img {
    &.res {
      //width: 36px;
      //height: 36px;
      //margin: 0;
      //margin-right: 15px;
    }
  }
  > .res-content {
    border-radius: 10.0022px 10.0022px 10.0022px 0;
  }
}

.chat-res-content {
  padding: 10px;
  border-radius: 10.0022px 10.0022px 10.0022px 0;
  background-color: #fff;
  max-width: 70vw;
  word-break: break-all;
  &.img {
    max-width: 50vw;
    > img {
      width: 100%;
      height: auto;
      margin: 0;
    }
  }
  > p {
    margin: 0;
    line-height: 1.4;
  }
}

.send-msg-content {
  padding: 10px 0;
  > textarea {
    width: 100%;
    padding: 10px 0;
    margin: 0;
  }
}

.upload-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 50%;
  transition: all 0.2s ease;

  &:active {
    background-color: rgba(0, 0, 0, 0.05);
    transform: scale(0.95);
  }

  img {
    border-radius: 4px;
  }
}

.icon-fasong {
  color: var(--site-main-color, #007aff);
  font-size: 24px;
  padding: 6px;
  border-radius: 50%;
  transition: all 0.2s ease;

  &:active {
    background-color: rgba(0, 122, 255, 0.1);
    transform: scale(0.95);
  }
}
</style>
