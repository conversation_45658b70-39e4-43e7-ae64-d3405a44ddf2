import{P as l,S as e,ai as i,a5 as a,Q as t,d as s,e as r,ad as n,I as o,X as c}from"./index-3d21abf8.js";import{r as u,u as d}from"./use-route-cd41a893.js";const[b,v]=l("cell"),f={icon:String,size:String,title:e,value:e,label:e,center:Boolean,isLink:Boolean,border:i,required:Boolean,iconPrefix:String,valueClass:a,labelClass:a,titleClass:a,titleStyle:null,arrowDirection:String,clickable:{type:Boolean,default:null}};const p=c(s({name:b,props:t({},f,u),setup(l,{slots:e}){const i=d(),a=()=>{if(e.label||n(l.label))return r("div",{class:[v("label"),l.labelClass]},[e.label?e.label():l.label])},t=()=>{if(e.title||n(l.title))return r("div",{class:[v("title"),l.titleClass],style:l.titleStyle},[e.title?e.title():r("span",null,[l.title]),a()])},s=()=>{const i=e.value||e.default;if(i||n(l.value)){const a=e.title||n(l.title);return r("div",{class:[v("value",{alone:!a}),l.valueClass]},[i?i():r("span",null,[l.value])])}},c=()=>{if(e["right-icon"])return e["right-icon"]();if(l.isLink){const e=l.arrowDirection?`arrow-${l.arrowDirection}`:"arrow";return r(o,{name:e,class:v("right-icon")},null)}};return()=>{var a,n;const{size:u,center:d,border:b,isLink:f,required:p}=l,C=null!=(a=l.clickable)?a:f,g={center:d,required:p,clickable:C,borderless:!b};return u&&(g[u]=!!u),r("div",{class:v(g),role:C?"button":void 0,tabindex:C?0:void 0,onClick:i},[e.icon?e.icon():l.icon?r(o,{name:l.icon,class:v("left-icon"),classPrefix:l.iconPrefix},null):void 0,t(),s(),c(),null==(n=e.extra)?void 0:n.call(e)])}}}));export{p as C,f as c};
