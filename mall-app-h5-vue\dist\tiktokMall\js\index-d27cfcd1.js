import{Q as e,P as t,d as a,a4 as n,r,m as u,q as l,s as o,g as s,a9 as m,e as i,ag as c,aY as p,bD as v,aQ as d,R as g,bE as f,X as y}from"./index-3d21abf8.js";import{p as h,P as D}from"./index-573e22f7.js";const x=e({},h,{filter:Function,columnsOrder:Array,formatter:{type:Function,default:(e,t)=>t}}),M=Object.keys(h);function V(e,t){if(e<0)return[];const a=Array(e);let n=-1;for(;++n<e;)a[n]=t(n);return a}const H=(e,t)=>32-new Date(e,t-1,32).getDate(),k=(e,t)=>{const a=["setValues","setIndexes","setColumnIndex","setColumnValue"];return new Proxy(e,{get:(e,n)=>a.includes(n)?(...a)=>{e[n](...a),t()}:e[n]})},[$]=t("time-picker");var b=a({name:$,props:e({},x,{minHour:n(0),maxHour:n(23),minMinute:n(0),maxMinute:n(59),modelValue:String}),emits:["confirm","cancel","change","update:modelValue"],setup(e,{emit:t,slots:a}){const n=t=>{const{minHour:a,maxHour:n,maxMinute:r,minMinute:u}=e;t||(t=`${v(a)}:${v(u)}`);let[l,o]=t.split(":");return l=v(d(+l,+a,+n)),o=v(d(+o,+u,+r)),`${l}:${o}`},g=r(),f=r(n(e.modelValue)),y=u((()=>[{type:"hour",range:[+e.minHour,+e.maxHour]},{type:"minute",range:[+e.minMinute,+e.maxMinute]}])),h=u((()=>y.value.map((({type:t,range:a})=>{let n=V(a[1]-a[0]+1,(e=>v(a[0]+e)));return e.filter&&(n=e.filter(t,n)),{type:t,values:n}})))),x=u((()=>h.value.map((t=>({values:t.values.map((a=>e.formatter(t.type,a)))}))))),H=()=>{const t=f.value.split(":"),a=[e.formatter("hour",t[0]),e.formatter("minute",t[1])];o((()=>{var e;null==(e=g.value)||e.setValues(a)}))},$=()=>{const[e,t]=g.value.getIndexes(),[a,r]=h.value,u=a.values[e]||a.values[0],l=r.values[t]||r.values[0];f.value=n(`${u}:${l}`),H()},b=()=>t("confirm",f.value),O=()=>t("cancel"),Y=()=>{$(),o((()=>{o((()=>t("change",f.value)))}))};return l((()=>{H(),o($)})),s(x,H),s((()=>[e.filter,e.maxHour,e.minMinute,e.maxMinute]),$),s((()=>e.minHour),(()=>{o($)})),s(f,(e=>t("update:modelValue",e))),s((()=>e.modelValue),(e=>{(e=n(e))!==f.value&&(f.value=e,H())})),m({getPicker:()=>g.value&&k(g.value,$)}),()=>i(D,c({ref:g,columns:x.value,onChange:Y,onCancel:O,onConfirm:b},p(e,M)),a)}});const O=(new Date).getFullYear(),[Y]=t("date-picker");var w=a({name:Y,props:e({},x,{type:g("datetime"),modelValue:Date,minDate:{type:Date,default:()=>new Date(O-10,0,1),validator:f},maxDate:{type:Date,default:()=>new Date(O+10,11,31),validator:f}}),emits:["confirm","cancel","change","update:modelValue"],setup(e,{emit:t,slots:a}){const n=t=>{if(f(t)){const a=d(t.getTime(),e.minDate.getTime(),e.maxDate.getTime());return new Date(a)}},g=r(),y=r(n(e.modelValue)),h=(t,a)=>{const n=e[`${t}Date`],r=n.getFullYear();let u=1,l=1,o=0,s=0;return"max"===t&&(u=12,l=H(a.getFullYear(),a.getMonth()+1),o=23,s=59),a.getFullYear()===r&&(u=n.getMonth()+1,a.getMonth()+1===u&&(l=n.getDate(),a.getDate()===l&&(o=n.getHours(),a.getHours()===o&&(s=n.getMinutes())))),{[`${t}Year`]:r,[`${t}Month`]:u,[`${t}Date`]:l,[`${t}Hour`]:o,[`${t}Minute`]:s}},x=u((()=>{const{maxYear:t,maxDate:a,maxMonth:n,maxHour:r,maxMinute:u}=h("max",y.value||e.minDate),{minYear:l,minDate:o,minMonth:s,minHour:m,minMinute:i}=h("min",y.value||e.minDate);let c=[{type:"year",range:[l,t]},{type:"month",range:[s,n]},{type:"day",range:[o,a]},{type:"hour",range:[m,r]},{type:"minute",range:[i,u]}];switch(e.type){case"date":c=c.slice(0,3);break;case"year-month":c=c.slice(0,2);break;case"month-day":c=c.slice(1,3);break;case"datehour":c=c.slice(0,4)}if(e.columnsOrder){const t=e.columnsOrder.concat(c.map((e=>e.type)));c.sort(((e,a)=>t.indexOf(e.type)-t.indexOf(a.type)))}return c})),$=u((()=>x.value.map((({type:t,range:a})=>{let n=V(a[1]-a[0]+1,(e=>v(a[0]+e)));return e.filter&&(n=e.filter(t,n)),{type:t,values:n}})))),b=u((()=>$.value.map((t=>({values:t.values.map((a=>e.formatter(t.type,a)))}))))),O=()=>{const t=y.value||e.minDate,{formatter:a}=e,n=$.value.map((e=>{switch(e.type){case"year":return a("year",`${t.getFullYear()}`);case"month":return a("month",v(t.getMonth()+1));case"day":return a("day",v(t.getDate()));case"hour":return a("hour",v(t.getHours()));case"minute":return a("minute",v(t.getMinutes()));default:return""}}));o((()=>{var e;null==(e=g.value)||e.setValues(n)}))},Y=()=>{const{type:t}=e,a=g.value.getIndexes(),r=e=>{let t=0;$.value.forEach(((a,n)=>{e===a.type&&(t=n)}));const{values:n}=$.value[t];return function(e){if(!e)return 0;for(;Number.isNaN(parseInt(e,10));){if(!(e.length>1))return 0;e=e.slice(1)}return parseInt(e,10)}(n[a[t]])};let u,l,o;"month-day"===t?(u=(y.value||e.minDate).getFullYear(),l=r("month"),o=r("day")):(u=r("year"),l=r("month"),o="year-month"===t?1:r("day"));const s=H(u,l);o=o>s?s:o;let m=0,i=0;"datehour"===t&&(m=r("hour")),"datetime"===t&&(m=r("hour"),i=r("minute"));const c=new Date(u,l-1,o,m,i);y.value=n(c)},w=()=>{t("update:modelValue",y.value),t("confirm",y.value)},C=()=>t("cancel"),F=()=>{Y(),o((()=>{Y(),o((()=>t("change",y.value)))}))};return l((()=>{O(),o(Y)})),s(b,O),s(y,((e,a)=>t("update:modelValue",a?e:null))),s((()=>[e.filter,e.minDate,e.maxDate]),(()=>{o(Y)})),s((()=>e.modelValue),(e=>{var t;(e=n(e))&&e.valueOf()!==(null==(t=y.value)?void 0:t.valueOf())&&(y.value=e)})),m({getPicker:()=>g.value&&k(g.value,Y)}),()=>i(D,c({ref:g,columns:b.value,onChange:F,onCancel:C,onConfirm:w},p(e,M)),a)}});const[C,F]=t("datetime-picker"),P=Object.keys(b.props),I=Object.keys(w.props);const j=y(a({name:C,props:e({},b.props,w.props,{modelValue:[String,Date]}),setup(e,{attrs:t,slots:a}){const n=r();return m({getPicker:()=>{var e;return null==(e=n.value)?void 0:e.getPicker()}}),()=>{const r="time"===e.type,u=r?b:w,l=p(e,r?P:I);return i(u,c({ref:n,class:F()},l,t),a)}}}));export{j as D};
