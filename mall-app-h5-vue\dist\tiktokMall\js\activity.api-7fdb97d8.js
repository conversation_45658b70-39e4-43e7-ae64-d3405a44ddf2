import{N as t,O as a}from"./index-3d21abf8.js";const i=i=>t({url:"/wap/api/activity/lottery!detail.action",method:a.GET,params:i}),r=i=>t({url:"/wap/api/activity/lottery!getPoints.action",method:a.GET,params:i}),o=i=>t({url:"/wap/api/activity/lottery!draw.action",method:a.POST,params:i}),e=i=>t({url:"/wap/api/activity/lottery!countPrize.action",method:a.GET,params:i}),p=i=>t({url:"/wap/api/activity/lottery!receivePrize.action",method:a.POST,params:i}),s=i=>t({url:"/wap/api/activity/lottery!getCountPoints.action",method:a.POST,params:i}),c=i=>t({url:"/wap/api/activity/lottery!pageListMyPrize.action",method:a.GET,params:i}),l=()=>t({url:"/wap/api/activity/lottery!getCurrentActivity.action",method:a.GET});export{e as a,s as b,i as c,r as d,l as g,o as l,c as p,p as r};
