System.register(["./index-legacy-46a00900.js","./login.api-legacy-d31fdc92.js"],(function(e,t){"use strict";var n,o,i,l,r,a,p,s,d,m,c,u,g,x=document.createElement("style");return x.textContent=".CommonProblem{width:100%;box-sizing:border-box}pre{white-space:pre-wrap}.CommonProblem-padding{padding-left:18px;padding-right:18px;font-weight:400;font-size:15px;line-height:18px;color:#333}.CommonProblem-title{font-style:normal;font-weight:700;font-size:25px;line-height:30px;color:#333;padding-left:18px;padding-right:18px;margin-top:29px;margin-bottom:11px}\n",document.head.appendChild(x),{setters:[e=>{n=e._,o=e.u,i=e.r,l=e.q,r=e.av,a=e.c,p=e.e,s=e.w,d=e.a,m=e.o,c=e.f,u=e.t},e=>{g=e.b}],execute:function(){const t={class:"CommonProblem"},x={class:"CommonProblem-padding"},f=["innerHTML"];e("default",n({__name:"index",setup(e){const{locale:n}=o(),h=i("");l((()=>{v()}));const v=()=>{g({content_code:"020",language:"en-US"===n.value?"en":n.value}).then((e=>{h.value=e?e.content:""}))};return(e,n)=>{const o=r("fx-header");return m(),a("div",t,[p(o,null,{title:s((()=>[c(u(e.$t("termsOfService")),1)])),_:1}),d("div",x,[d("p",{innerHTML:h.value},null,8,f)])])}}},[["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/termsOfService/index.vue"]]))}}}));
