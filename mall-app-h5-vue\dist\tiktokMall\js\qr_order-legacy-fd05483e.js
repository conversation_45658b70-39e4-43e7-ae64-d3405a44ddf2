System.register(["./index-legacy-46a00900.js","./index-legacy-72e00c5f.js","./index-legacy-322f21cd.js","./index-legacy-c989a436.js","./use-route-legacy-be86ac1c.js"],(function(e,t){"use strict";var a,l,i,s,n,o,r,d,c,u,p,v,f,m,x,g,b,h,y,w,k,$,P,_,j,z,D,I,N,C,q,E,V,F,S,T=document.createElement("style");return T.textContent=".list[data-v-7f0433a8]{padding:10px 15px 0;box-sizing:border-box}.list.is-ar .listitem .name[data-v-7f0433a8]{padding-left:0;padding-right:10px}.list .listitem[data-v-7f0433a8]{background:#fff;margin-bottom:15px;border-radius:5px;display:-webkit-box;display:-webkit-flex;display:flex;padding:10px;box-sizing:border-box;position:relative}.list .listitem .pic[data-v-7f0433a8]{width:90px;height:90px;text-align:center}.list .listitem .pic img[data-v-7f0433a8]{height:90px;width:auto}.list .listitem .name[data-v-7f0433a8]{padding-left:10px;word-break:break-all;-webkit-box-flex:1;-webkit-flex:1 1;flex:1 1}.list .listitem .name .title[data-v-7f0433a8]{font-size:15px;font-weight:700}.list .listitem .name .col[data-v-7f0433a8]{font-size:13px;color:#aaa}.list .listitem .name .money[data-v-7f0433a8]{color:var(--site-main-color)}.list .listitem .name span[data-v-7f0433a8]{position:absolute;color:#aaa;right:10px;bottom:35px;color:var(--site-main-color)}.line-content[data-v-7f0433a8]{padding:0 15px}.line-content.is-ar .price-content>span[data-v-7f0433a8]{padding-left:0;padding-right:5px}.line[data-v-7f0433a8]{border-radius:5px;padding:15px;box-sizing:border-box;background:#fff}.btn[data-v-7f0433a8]{width:100%;padding:0 15px;box-sizing:border-box;margin-top:40px;margin-bottom:30px}.btn[data-v-7f0433a8] .van-button--primary{background-color:var(--site-main-color);border-color:var(--site-main-color);border-radius:4px}.line-item[data-v-7f0433a8]{width:100%;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between;font-size:15px;color:#999}.line-item p[data-v-7f0433a8]{height:40px;line-height:40px;text-align:right}.line-item .count[data-v-7f0433a8]{color:#333}.price-content[data-v-7f0433a8]{font-size:14px;line-height:20px;color:#333;text-align:right}.price-content>span[data-v-7f0433a8]{color:#1552f0;font-weight:700;padding-left:5px}\n",document.head.appendChild(T),{setters:[e=>{a=e._,l=e.i,i=e.c0,s=e.j,n=e.u,o=e.Y,r=e.r,d=e.m,c=e.q,u=e.T,p=e.c4,v=e.c5,f=e.g,m=e.av,x=e.c,g=e.e,b=e.w,h=e.a,y=e.F,w=e.y,k=e.b,$=e.n,P=e.t,_=e.f,j=e.x,z=e.aV,D=e.o,I=e.aF,N=e.D,C=e.E,q=e.c6},e=>{E=e.B},e=>{V=e.A},e=>{F=e.P,S=e.N},()=>{}],execute:function(){const t=(e=>(N("data-v-7f0433a8"),e=e(),C(),e))((()=>h("div",{style:{height:"46px"}},null,-1))),T={class:"listitem"},U={class:"pic"},B=["src"],L={class:"name"},O={class:"title"},A={class:"col"},R={class:"title money"},Y={class:"line"},G={class:"line-item"},H={class:"count"},J={class:"line-item"},K={class:"count"},M={key:0,class:"price-content"},Q={class:"line-item"},W={class:"count"},X={class:"line-item"},Z={class:"count"},ee={class:"line-item"},te={class:"count"},ae={class:"line-item"},le={class:"count"},ie={class:"line-item"},se={class:"count"},ne={style:{height:"22rem"}},oe={key:1,class:"btn"};e("default",a({__name:"qr_order",setup(e){const a=l(),N=i(),C=s(),{t:re}=n(),de=o();let ce=r(!0),ue=r(0),pe=r(0),ve=r(0),fe=r(0),me=r([]),xe=r(0),ge=r(0);const be=r("");let he=r(!1);const ye=r(!0),we=d((()=>C.userInfo?.safeword)),ke=()=>{we.value?(he.value=!0,ce.value=!0):(u(re("请设置资金密码")),setTimeout((()=>{router.push({path:"/fundsPasswordSettings"})}),1500))};c((()=>{$e()}));const $e=()=>{_e()},Pe=r({}),_e=async()=>{ye.value=!0;const e=de.query.id;u.loading({duration:0,message:re("loading"),forbidClick:!0}),await p({orderId:e}).then((e=>{me.value=e.pageList,me.value.forEach((e=>{ue.value+=e.systemPrice,pe.value+=e.goodsNum,xe.value+=e.fees,ge.value+=e.tax,ve.value+=e.profit})),fe.value=ue.value+xe.value+ge.value})),await v({orderId:e}).then((e=>{Pe.value=e.orderInfo,ye.value=!1,u.clear()})).catch((()=>{u.clear()}))};f((()=>be.value),(e=>{6===e.length&&(u.loading(""),(()=>{const e={orderId:de.query.id,safeword:be.value};q(e).then((t=>{$e(),N.decrement(),document.dispatchEvent(new CustomEvent("reloadOrderList")),setTimeout((()=>{router.push({path:"/passsuess",query:{id:e.orderId}})}),1e3)}))})(),he.value=!1,be.value="")}));const je=d((()=>{I((Pe.sellerDiscountPrice?Pe.sellerDiscountPrice:Pe.systemPrice)/1+Pe.fees/1+Pe.tax/1);const{sellerDiscountPrice:e,systemPrice:t,fees:a,tax:l}=Pe.value;return I(Number(e||t)+Number(a)+Number(l))}));return(e,l)=>{const i=m("fx-header"),s=F,n=S,o=V,r=E;return D(),x("div",null,[g(i,{fixed:""},{title:b((()=>[_(P(e.$t("采购确认")),1)])),_:1}),t,h("div",{class:$(["list",{"is-ar":k(a)}])},[(D(!0),x(y,null,w(k(me),(t=>(D(),x("div",T,[h("div",U,[h("img",{style:{"object-fit":"contain"},src:t.goodsIcon},null,8,B)]),h("div",L,[h("p",O,P(t.goodsName),1),h("p",A,P(e.$t("采购数量")),1),h("p",R,"$"+P(k(I)(t.systemPrice)),1),h("span",null,"x"+P(t.goodsNum),1)])])))),256))],2),Object.keys(Pe.value).length>0?(D(),x("div",{key:0,class:$(["line-content",{"is-ar":k(a)}])},[h("div",Y,[h("div",G,[h("p",null,P(e.$t("买家付款")),1),h("p",H,"$"+P(k(I)(Pe.value.prizeReal)),1)]),h("div",J,[h("p",null,P(e.$t("采购金额")),1),h("p",K,"$"+P(k(I)(Pe.value.systemPrice)),1)]),Pe.value.sellerDiscountPrice?(D(),x("div",M,[_(P(k(re)("优惠价")),1),h("span",null,"$"+P(k(I)(Pe.value.sellerDiscountPrice)),1),_(", "+P(k(re)("采购优惠")),1),h("span",null,P(100*Number(Pe.value.sellerDiscount))+"%",1)])):j("v-if",!0),h("div",Q,[h("p",null,P(e.$t("采购数量")),1),h("p",W,P(Pe.value.goodsCount),1)]),h("div",X,[h("p",null,P(e.$t("利润")),1),h("p",Z,"$"+P(k(I)(Pe.value.profit)),1)]),h("div",ee,[h("p",null,P(e.$t("运费")),1),h("p",te,"$"+P(k(I)(Pe.value.fees)),1)]),h("div",ae,[h("p",null,P(e.$t("税")),1),h("p",le,"$"+P(k(I)(Pe.value.tax)),1)]),h("div",ie,[h("p",null,P(e.$t("合计")),1),h("p",se,"$"+P(k(je)),1)])])],2)):j("v-if",!0),g(o,{show:k(he),"onUpdate:show":l[3]||(l[3]=e=>z(he)?he.value=e:he=e),title:k(re)("请输入交易密码")},{default:b((()=>[h("div",ne,[g(s,{length:6,value:be.value,focused:k(ce),onFocus:l[0]||(l[0]=e=>z(ce)?ce.value=!0:ce=!0)},null,8,["value","focused"]),g(n,{modelValue:be.value,"onUpdate:modelValue":l[1]||(l[1]=e=>be.value=e),show:k(ce),onBlur:l[2]||(l[2]=e=>z(ce)?ce.value=!1:ce=!1)},null,8,["modelValue","show"])])])),_:1},8,["show","title"]),ye.value||Pe.value.purchStatus/1==1||Pe.value.status/1==-1||Pe.value.status/1==0||Pe.value.status/1==6?j("v-if",!0):(D(),x("div",oe,[g(r,{type:"primary",size:"large",onClick:ke},{default:b((()=>[_(P(`${e.$t("立即支付")} $${k(je)}`),1)])),_:1})]))])}}},[["__scopeId","data-v-7f0433a8"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/order/qr_order.vue"]]))}}}));
