System.register(["./stringify-legacy-93b715ae.js"],(function(e,t){"use strict";var r,i,a;return{setters:[e=>{r=e.u,i=e.s,a=e.f}],execute:function(){var t=r,o=Object.prototype.hasOwnProperty,n=Array.isArray,l={allowDots:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decoder:t.decode,delimiter:"&",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},s=function(e){return e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(parseInt(t,10))}))},c=function(e,t){return e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1?e.split(","):e},p=function(e,t,r,i){if(e){var a=r.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,n=/(\[[^[\]]*])/g,l=r.depth>0&&/(\[[^[\]]*])/.exec(a),s=l?a.slice(0,l.index):a,p=[];if(s){if(!r.plainObjects&&o.call(Object.prototype,s)&&!r.allowPrototypes)return;p.push(s)}for(var u=0;r.depth>0&&null!==(l=n.exec(a))&&u<r.depth;){if(u+=1,!r.plainObjects&&o.call(Object.prototype,l[1].slice(1,-1))&&!r.allowPrototypes)return;p.push(l[1])}return l&&p.push("["+a.slice(l.index)+"]"),function(e,t,r,i){for(var a=i?t:c(t,r),o=e.length-1;o>=0;--o){var n,l=e[o];if("[]"===l&&r.parseArrays)n=[].concat(a);else{n=r.plainObjects?Object.create(null):{};var s="["===l.charAt(0)&&"]"===l.charAt(l.length-1)?l.slice(1,-1):l,p=parseInt(s,10);r.parseArrays||""!==s?!isNaN(p)&&l!==s&&String(p)===s&&p>=0&&r.parseArrays&&p<=r.arrayLimit?(n=[])[p]=a:"__proto__"!==s&&(n[s]=a):n={0:a}}a=n}return a}(p,t,r,i)}};e("l",{formats:a,parse:function(e,r){var i=function(e){if(!e)return l;if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw new TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=void 0===e.charset?l.charset:e.charset;return{allowDots:void 0===e.allowDots?l.allowDots:!!e.allowDots,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:l.allowPrototypes,allowSparse:"boolean"==typeof e.allowSparse?e.allowSparse:l.allowSparse,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:l.arrayLimit,charset:r,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:l.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:l.comma,decoder:"function"==typeof e.decoder?e.decoder:l.decoder,delimiter:"string"==typeof e.delimiter||t.isRegExp(e.delimiter)?e.delimiter:l.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:l.depth,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:l.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:l.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:l.plainObjects,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:l.strictNullHandling}}(r);if(""===e||null==e)return i.plainObjects?Object.create(null):{};for(var a="string"==typeof e?function(e,r){var i,a={},p=r.ignoreQueryPrefix?e.replace(/^\?/,""):e,u=r.parameterLimit===1/0?void 0:r.parameterLimit,d=p.split(r.delimiter,u),f=-1,m=r.charset;if(r.charsetSentinel)for(i=0;i<d.length;++i)0===d[i].indexOf("utf8=")&&("utf8=%E2%9C%93"===d[i]?m="utf-8":"utf8=%26%2310003%3B"===d[i]&&(m="iso-8859-1"),f=i,i=d.length);for(i=0;i<d.length;++i)if(i!==f){var y,h,b=d[i],g=b.indexOf("]="),w=-1===g?b.indexOf("="):g+1;-1===w?(y=r.decoder(b,l.decoder,m,"key"),h=r.strictNullHandling?null:""):(y=r.decoder(b.slice(0,w),l.decoder,m,"key"),h=t.maybeMap(c(b.slice(w+1),r),(function(e){return r.decoder(e,l.decoder,m,"value")}))),h&&r.interpretNumericEntities&&"iso-8859-1"===m&&(h=s(h)),b.indexOf("[]=")>-1&&(h=n(h)?[h]:h),o.call(a,y)?a[y]=t.combine(a[y],h):a[y]=h}return a}(e,i):e,u=i.plainObjects?Object.create(null):{},d=Object.keys(a),f=0;f<d.length;++f){var m=d[f],y=p(m,a[m],i,"string"==typeof e);u=t.merge(u,y,i)}return!0===i.allowSparse?u:t.compact(u)},stringify:i})}}}));
