import{P as e,S as t,ai as s,d as l,r as a,a$ as r,e as i,b0 as o,b1 as n,I as c,X as f}from"./index-3d21abf8.js";import{u as d}from"./use-placeholder-c97cb410.js";const[x,p]=e("nav-bar");const g=f(l({name:x,props:{title:String,fixed:Boolean,zIndex:t,border:s,leftText:String,rightText:String,leftArrow:<PERSON><PERSON><PERSON>,placeholder:Boolean,safeAreaInsetTop:Boolean},emits:["click-left","click-right"],setup(e,{emit:t,slots:s}){const l=a(),f=d(l,p),x=e=>t("click-left",e),g=e=>t("click-right",e),h=()=>{const{title:t,fixed:a,border:f,zIndex:d}=e,h=r(d),T=e.leftArrow||e.leftText||s.left,m=e.rightText||s.right;return i("div",{ref:l,style:h,class:[p({fixed:a}),{[n]:f,"van-safe-area-top":e.safeAreaInsetTop}]},[i("div",{class:p("content")},[T&&i("div",{class:[p("left"),o],onClick:x},[s.left?s.left():[e.leftArrow&&i(c,{class:p("arrow"),name:"arrow-left"},null),e.leftText&&i("span",{class:p("text")},[e.leftText])]]),i("div",{class:[p("title"),"van-ellipsis"]},[s.title?s.title():t]),m&&i("div",{class:[p("right"),o],onClick:g},[s.right?s.right():i("span",{class:p("text")},[e.rightText])])])])};return()=>e.fixed&&e.placeholder?f(h):h()}}));export{g as N};
