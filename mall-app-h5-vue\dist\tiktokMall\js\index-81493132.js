import{_ as e,u as a,l as t,r as l,av as o,c as i,e as s,a as n,t as r,n as u,b as c,w as d,T as v,cv as p,o as m,f}from"./index-3d21abf8.js";import{B as x}from"./index-2406f514.js";import{E as h}from"./index-9c8e9dca.js";import{n as y}from"./nationalityList-c6365b2b.js";import"./use-route-cd41a893.js";/* empty css              *//* empty css               */import"./index-8c1841f6.js";import"./index-7dfcb82a.js";import"./use-id-a0619e01.js";import"./index-3d6106f5.js";import"./index-179203f3.js";import"./countryList-016fc82e.js";const _={class:"forget"},j={class:"forgetCont"},g={class:"title textColor"},C={class:"flex re-tab text-grey"},$=e({__name:"index",setup(e){const{t:$}=a(),b=t(),k=l(""),A=l(0),V=l(!1),w=l(0);let B=l("");const L=e=>{A.value=e,V.value=1==e},N=()=>{""!=k.value?T():v($("entryAccount"))},T=()=>{let e;0==A.value?e=2:1==A.value?e=1:2==A.value&&(e=3),p({username:1==e?`${w.value}${k.value}`:k.value,verifcode_type:e}).then((a=>{if(1==e&&!a.phone_authority)return v($("noBindPhoneNum")),!1;if(2==e&&!a.email_authority)return v($("noBindEmail")),!1;if(3==e&&!a.google_auth_bind)return v($("noBindGoogleAuth")),!1;let t;1==e?t=a.phone:2==e&&(t=a.email),b.push({name:"safeVerify",query:{type:e,account:t,username:k.value}})}))},E=l(null),G=()=>{E.value.open()},P=(e,a,t)=>{B.value=a,w.value=t};return(e,a)=>{const t=o("fx-header"),l=x;return m(),i("div",_,[s(t),n("div",j,[n("div",g,r(e.$t("resetLoginPassword")),1),n("div",C,[n("div",{class:u(0==A.value?"active":""),onClick:a[0]||(a[0]=e=>L(0))},r(e.$t("email")),3),n("div",{class:u(1==A.value?"active":""),onClick:a[1]||(a[1]=e=>L(1))},r(e.$t("phoneNum")),3),n("div",{class:u(2==A.value?"active":""),onClick:a[2]||(a[2]=e=>L(2))},r(e.$t("googleVerify")),3)]),s(h,{label:e.$t("account"),placeholderText:e.$t("entryAccount"),modelValue:k.value,"onUpdate:modelValue":a[3]||(a[3]=e=>k.value=e),dialCode:w.value,onSelectArea:G,area:V.value,icon:c(B)},null,8,["label","placeholderText","modelValue","dialCode","area","icon"]),s(l,{class:"w-full",style:{"margin-top":"10px"},type:"primary",onClick:N},{default:d((()=>[f(r(e.$t("nextStep")),1)])),_:1}),s(y,{ref_key:"controlChildRef",ref:E,title:e.$t("selectArea"),onGetName:P},null,8,["title"])])])}}},[["__scopeId","data-v-665122df"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/forget/index.vue"]]);export{$ as default};
