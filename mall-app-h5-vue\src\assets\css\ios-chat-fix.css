/* iOS聊天页面专用修复样式 */

/* 全局iOS设备检测和基础适配 */
.ios-device {
  --ios-safe-area-top: 44px;
  --ios-safe-area-bottom: 34px;
  --ios-safe-area-left: 0px;
  --ios-safe-area-right: 0px;
}

/* 现代浏览器支持 */
@supports (padding-top: env(safe-area-inset-top)) {
  .ios-device {
    --ios-safe-area-top: env(safe-area-inset-top);
    --ios-safe-area-bottom: env(safe-area-inset-bottom);
    --ios-safe-area-left: env(safe-area-inset-left);
    --ios-safe-area-right: env(safe-area-inset-right);
  }
}

/* 客服页面iOS适配 */
.ios-device .service-box {
  position: relative;
  height: 100vh;
  overflow: hidden;
  padding-top: 0;
}

.ios-device .service-box .van-nav-bar {
  position: fixed !important;
  top: var(--ios-safe-area-top) !important;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  width: 100%;
}

.ios-device .service-box .content {
  padding-top: calc(var(--ios-safe-area-top) + 46px) !important;
  padding-bottom: 40px !important; /* 底部间距设置为0，最大化空间利用 */
  min-height: calc(100vh - var(--ios-safe-area-top) - 46px);
  max-height: calc(100vh - var(--ios-safe-area-top) - 46px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  position: relative;
}

/* iOS设备底部输入框适配 */
.ios-device .service-box .bottomBox,
.ios-device .service-box .ios-bottom-input {
  position: fixed !important;
  bottom: 0 !important;
  left: 0;
  right: 0;
  z-index: 101;
  background: #fff;
  border-top: 1px solid #f3f3f3;
  padding-bottom: 20px !important; /* 固定底部padding为20px */
  min-height: calc(65px + var(--ios-safe-area-bottom));
  width: 100%;
  box-sizing: border-box;
}

/* 消息中心页面iOS适配 - 仅针对客服页面，不影响商品库页面 */
.ios-device .service-box.has-fixed-header,
.ios-device .page-main-content.has-fixed-header.message-center-page {
  position: relative;
  height: 100vh;
  overflow: hidden;
}

.ios-device .page-main-content.has-fixed-header .van-nav-bar {
  position: fixed !important;
  top: var(--ios-safe-area-top) !important;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  width: 100%;
}

.ios-device .page-main-content.has-fixed-header .fixed-header-spacer {
  height: calc(46px + var(--ios-safe-area-top)) !important;
  background: transparent;
}

.ios-device .page-main-content.has-fixed-header .iframe-content {
  height: calc(100vh - var(--ios-safe-area-top) - var(--ios-safe-area-bottom)) !important;
  padding-top: calc(46px + var(--ios-safe-area-top)) !important;
  padding-bottom: var(--ios-safe-area-bottom) !important;
  position: relative;
}

.ios-device .page-main-content.has-fixed-header .iframe-content iframe {
  height: calc(100% - var(--ios-safe-area-bottom)) !important;
  width: 100%;
  border: none;
}

/* iOS设备返回按钮增强 */
.ios-device .van-nav-bar__left {
  min-width: 44px !important;
  min-height: 44px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  pointer-events: auto !important;
  z-index: 1000 !important;
  position: relative;
}

/* iOS设备输入框优化 */
.ios-device textarea,
.ios-device input[type="text"] {
  -webkit-appearance: none;
  border-radius: 0;
  font-size: 16px; /* 防止iOS缩放 */
}

/* iOS设备触摸滚动优化 */
.ios-device .content,
.ios-device .iframe-content,
.ios-device .service-box .content {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

/* iOS设备防止页面缩放 */
.ios-device {
  -webkit-text-size-adjust: 100%;
  -webkit-user-select: none;
  user-select: none;
}

/* 修复iOS设备上的点击延迟 */
.ios-device * {
  -webkit-tap-highlight-color: transparent;
}

.ios-device .van-nav-bar__left,
.ios-device .bottomBox,
.ios-device button,
.ios-device .van-button {
  -webkit-tap-highlight-color: rgba(0,0,0,0.1);
}

/* 特定iPhone机型强制适配 */
@media only screen 
  and (device-width: 375px) 
  and (device-height: 812px) 
  and (-webkit-device-pixel-ratio: 3) {
  .ios-device {
    --ios-safe-area-top: 44px !important;
    --ios-safe-area-bottom: 34px !important;
  }
}

@media only screen 
  and (device-width: 414px) 
  and (device-height: 896px) {
  .ios-device {
    --ios-safe-area-top: 44px !important;
    --ios-safe-area-bottom: 34px !important;
  }
}

@media only screen 
  and (device-width: 390px) 
  and (device-height: 844px) 
  and (-webkit-device-pixel-ratio: 3) {
  .ios-device {
    --ios-safe-area-top: 44px !important;
    --ios-safe-area-bottom: 34px !important;
  }
}

/* iPhone 14 Pro Max */
@media only screen 
  and (device-width: 430px) 
  and (device-height: 932px) 
  and (-webkit-device-pixel-ratio: 3) {
  .ios-device {
    --ios-safe-area-top: 59px !important;
    --ios-safe-area-bottom: 34px !important;
  }
}

/* iPhone 14 Pro */
@media only screen 
  and (device-width: 393px) 
  and (device-height: 852px) 
  and (-webkit-device-pixel-ratio: 3) {
  .ios-device {
    --ios-safe-area-top: 59px !important;
    --ios-safe-area-bottom: 34px !important;
  }
}

/* 强制确保所有iOS设备都应用适配 */
body.ios-device .service-box,
html.ios-device .service-box {
  height: 100vh !important;
  overflow: hidden !important;
}

body.ios-device .service-box .bottomBox,
html.ios-device .service-box .bottomBox,
body.ios-device .service-box .ios-bottom-input,
html.ios-device .service-box .ios-bottom-input {
  position: fixed !important;
  bottom: 0 !important;
  padding-bottom: 20px !important; /* 固定底部padding为20px */
  z-index: 1001 !important;
}

body.ios-device .page-main-content.has-fixed-header .iframe-content,
html.ios-device .page-main-content.has-fixed-header .iframe-content {
  height: calc(100vh - var(--ios-safe-area-top) - var(--ios-safe-area-bottom)) !important;
  padding-top: calc(46px + var(--ios-safe-area-top)) !important;
}

/* 商品库页面滑动修复 - 确保商品库页面可以正常滑动 */
.ios-device .product.has-fixed-header {
  overflow: auto !important;
  height: auto !important;
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

.ios-device .product.has-fixed-header .list {
  overflow: visible !important;
  height: auto !important;
}
