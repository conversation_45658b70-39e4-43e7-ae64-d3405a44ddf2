System.register(["./index-legacy-46a00900.js"],(function(e,t){"use strict";var i,a,o,n,s,d,l,r,c,g,m,b,p,f,x=document.createElement("style");return x.textContent=".goods-item[data-v-a0b32eee]{padding:10px;background-color:#fff;border-radius:4px;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;margin-bottom:15px}.goods-item[data-v-a0b32eee]:last-child{margin-bottom:0}.goods-item>.poster[data-v-a0b32eee]{width:78px;height:78px;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center}.goods-item>.poster>img[data-v-a0b32eee]{width:100%;height:auto}.goods-item>.info-content[data-v-a0b32eee]{-webkit-box-flex:1;-webkit-flex:1 1;flex:1 1;padding-left:10px;word-break:break-all}.goods-item>.info-content.is-ar[data-v-a0b32eee]{padding-left:0;padding-right:10px}.goods-item>.info-content>.name[data-v-a0b32eee]{font-size:14px;line-height:18px;color:#333;word-break:break-all;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}.goods-item>.info-content>div[data-v-a0b32eee]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.goods-item>.info-content>div>div[data-v-a0b32eee]{font-size:12px;color:#999;margin-right:15px}.goods-item>.info-content>div>div[data-v-a0b32eee]:last-child{margin-right:0}.goods-item>.info-content>.price[data-v-a0b32eee]{font-size:16px;color:var(--site-main-color)}\n",document.head.appendChild(x),{setters:[e=>{i=e.d,a=e.u,o=e.aj,n=e.i,s=e.m,d=e.aF,l=e._,r=e.ak,c=e.o,g=e.c,m=e.a,b=e.t,p=e.x,f=e.n}],execute:function(){const t=i({name:"GoodsItem",props:{goodsData:{type:Object,default:()=>{}}},setup(e){const{t:t}=a(),{goodsData:i}=o(e),l=n();return{t:t,getdDtails:()=>{r({path:"/productPage/details",query:{item:JSON.stringify(i.value)}})},showBrowse:s((()=>!["tiktok-wholesale"].includes("tiktokMall"))),numberStrFormat:d,isArLang:l}}}),x={class:"poster"},v=["src"],u={class:"name"},w={key:0},k={class:"price"};e("G",l(t,[["render",function(e,t,i,a,o,n){return c(),g("div",{class:"goods-item",onClick:t[0]||(t[0]=(...t)=>e.getdDtails&&e.getdDtails(...t))},[m("div",x,[m("img",{src:e.goodsData.imgUrl1||e.goodsData.imgUrl2||e.goodsData.imgUrl3||e.goodsData.imgUrl4,alt:""},null,8,v)]),m("div",{class:f(["info-content",{"is-ar":e.isArLang}])},[m("p",u,b(e.goodsData.name),1),m("div",null,[e.showBrowse?(c(),g("div",w,b(e.t("browse"))+": "+b(e.numberStrFormat(e.goodsData.viewsNum,0)),1)):p("v-if",!0),m("div",null,b(e.t("sales"))+": "+b(e.numberStrFormat(e.goodsData.soldNum,0)),1)]),m("p",k,"$"+b(e.numberStrFormat(e.goodsData.sellingPrice)),1)],2)])}],["__scopeId","data-v-a0b32eee"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/shop/components/GoodsItem.vue"]]))}}}));
