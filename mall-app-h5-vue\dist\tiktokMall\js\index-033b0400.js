import{_ as a,d as e,u as s,r as l,s as n,av as i,c as t,e as c,a as v,f as d,t as r,b as o,F as u,y as p,L as _,T as f,cc as h,B as m,b6 as g,o as b,aF as y,D as w,E as x}from"./index-3d21abf8.js";import{u as k}from"./index-54dce367.js";const $=a=>(w("data-v-70c5e749"),a=a(),x(),a),C=$((()=>v("div",{class:"header-spacer",style:{height:"46px"}},null,-1))),L={class:"block-content info"},j={class:"title"},N=$((()=>v("div",null,[v("span"),v("span"),v("span")],-1))),R=$((()=>v("div",null,[v("span"),v("span"),v("span")],-1))),T={class:"info-txt"},A={class:"intro-table"},D={class:"item title"},F={key:0},H={key:1},I={class:"info-txt"},M=["innerHTML"],O={class:"block-content"},S={class:"title"},U=$((()=>v("div",null,[v("span"),v("span"),v("span")],-1))),B=$((()=>v("div",null,[v("span"),v("span"),v("span")],-1))),E={class:"record-content"},J={class:"item"},q={class:"item"},z={class:"link-content"},G={class:"txt"},K=e({name:"InvitationActivity"}),P=a(Object.assign(K,{setup(a){const{t:e,locale:w}=s(),{toClipboard:x}=k(),$=l(new URL("/www/png/name-868c9bdc.png",self.location)),K=l([]),P=l(0),Q=l(0),V=l(0),W=l(""),X=async()=>{try{await x(W.value),f(e("copySuccess"))}catch(a){}};return n((()=>{f.loading({forbidClick:!0,duration:0}),h("mall_first_invite_recharge_rewards").then((a=>{const e=a.mall_first_invite_recharge_rewards?JSON.parse(a.mall_first_invite_recharge_rewards):[];K.value=e})),h("valid_recharge_amount_for_first_recharge_bonus").then((a=>{V.value=a.valid_recharge_amount_for_first_recharge_bonus||0})),m().then((a=>{P.value=a.inviteNum,Q.value=a.inviteReceivedReward})),g().then((a=>{W.value=a.download&&a.code?`${a.download}/#?usercode=${a.code}&lang=${w.value}`:""}))})),(a,s)=>{const l=i("fx-header");return b(),t("div",{style:_({"background-image":"url("+$.value.href+")"}),class:"invitation-content page-main-content invitation-activity-page"},[c(l,{fixed:""}),C,v("div",L,[v("div",j,[v("div",null,[N,d(" "+r(o(e)("活动规则"))+" ",1),R])]),v("p",T,r(o(e)("活动期间，你每成功邀请一个新用户注册并激活店铺都将得到奖金,达到邀请人数之后奖金提升如下：")),1),v("div",A,[v("div",D,[v("div",null,r(o(e)("邀请人数")),1),v("div",null,r(o(e)("每人奖励")),1)]),(b(!0),t(u,null,p(K.value,((a,e)=>(b(),t("div",{key:e,class:"item"},[e<K.value.length-1?(b(),t("div",F,r(a[1])+"-"+r(Number(K.value[e+1][1])-1),1)):(b(),t("div",H,"≥ "+r(a[1]),1)),v("div",null,"$"+r(o(y)(a[0])),1)])))),128))]),v("p",I,r(o(e)("邀请越多，奖励越多，先到先得，数量有限！")),1),v("p",{class:"info-txt one",innerHTML:o(e)("好友开店首次充值金额满足",{money:V.value})},null,8,M)]),v("div",O,[v("div",S,[v("div",null,[U,d(" "+r(o(e)("我的邀请记录"))+" ",1),B])]),v("div",E,[v("div",J,[v("h2",null,r(P.value),1),v("p",null,r(o(e)("成功邀请(人)")),1)]),v("div",q,[v("h2",null,r(o(y)(Q.value)),1),v("p",null,r(o(e)("累计返现($)")),1)])]),v("div",z,[v("div",G,r(W.value),1),v("div",{class:"btn",onClick:X},r(o(e)("复制链接")),1)])])],4)}}}),[["__scopeId","data-v-70c5e749"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/invitationActivity/index.vue"]]);export{P as default};
