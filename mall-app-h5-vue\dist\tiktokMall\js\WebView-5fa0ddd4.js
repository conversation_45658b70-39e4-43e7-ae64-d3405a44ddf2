import{_ as e,u as s,l,c as r,e as a,a as t,o as c}from"./index-3d21abf8.js";import{N as o}from"./index-cfdda867.js";import"./use-placeholder-c97cb410.js";const i={class:"flex flex-col w-screen h-screen"},n=["src"],f=e({__name:"WebView",props:["query"],setup(e){const f=e;s();const p=l(),u=()=>{p.go(-1)};return(e,s)=>{const l=o;return c(),r("div",i,[a(l,{ref:"navEl",title:e.$t(f.query.title),"left-arrow":"",onClickLeft:u,fixed:""},null,8,["title"]),t("iframe",{src:f.query.src,class:"flex-1 mt-12"},null,8,n)])}}},[["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/components/webView/WebView.vue"]]);export{f as default};
