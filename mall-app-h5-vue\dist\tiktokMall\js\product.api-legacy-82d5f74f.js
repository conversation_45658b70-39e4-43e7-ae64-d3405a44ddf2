System.register(["./index-legacy-46a00900.js","./index-legacy-a4cde014.js"],(function(e,a){"use strict";var t,o,s;return{setters:[e=>{t=e.N,o=e.O},e=>{s=e.l}],execute:function(){e("g",(e=>t({url:"/wap/seller/systemGoods!list.action",method:o.POST,data:e}))),e("d",(e=>t({url:"/wap/seller/goods!delete.action",method:o.POST,data:e}))),e("s",(()=>t({url:"/wap/api/sysParaProduct!info.action",method:o.POST}))),e("m",(e=>t({url:"/wap/seller/goods!list.action",method:o.POST,data:e}))),e("c",(()=>t({url:"/wap/api/category!tree.action",method:o.GET}))),e("e",(e=>t({url:"/wap/seller/evaluation!list.action",method:o.POST,data:e}))),e("b",(e=>t({url:"/wap/seller/goods!update.action",method:o.POST,data:e}))),e("a",(e=>t({loadingPass:!0,url:"/wap/seller/goods!addOrUpdate.action",method:o.POST,data:e}))),e("f",(e=>t({url:"/wap/seller/goods!search-goods.action?"+s.stringify(e),method:o.POST})))}}}));
