import{P as e,d as a,aH as l,g as s,bF as t,e as o,S as d,a5 as i,X as r,R as u,Q as n,bc as c,ai as p,r as m,m as v,I as f,a8 as h,a6 as b,aY as x,ag as g,_,Y as V,q as k,o as y,c as w,w as j,F as C,y as S,A as U,a as P,n as F,f as T,t as $,D as R,E as B,u as D,l as I,cr as z,av as L,x as q,cs as E}from"./index-3d21abf8.js";import{B as G}from"./index-2406f514.js";import"./index-7d1632e5.js";/* empty css              *//* empty css               */import{U as A}from"./index-1f1846c6.js";import{E as N}from"./index-9c8e9dca.js";import{_ as O}from"./upload.api-28f256be.js";import"./use-route-cd41a893.js";import"./index-0d6a7179.js";import"./index-a439655d.js";/* empty css              *//* empty css               */import"./index-8c1841f6.js";import"./index-7dfcb82a.js";import"./use-id-a0619e01.js";const[Y,H]=e("radio-group"),M={disabled:Boolean,iconSize:d,direction:String,modelValue:i,checkedColor:String},Q=Symbol(Y);const X=r(a({name:Y,props:M,emits:["change","update:modelValue"],setup(e,{emit:a,slots:d}){const{linkChildren:i}=l(Q);return s((()=>e.modelValue),(e=>a("change",e))),i({props:e,updateValue:e=>a("update:modelValue",e)}),t((()=>e.modelValue)),()=>{var a;return o("div",{class:H([e.direction]),role:"radiogroup"},[null==(a=d.default)?void 0:a.call(d)])}}})),J={name:i,shape:u("round"),disabled:Boolean,iconSize:d,modelValue:i,checkedColor:String,labelPosition:String,labelDisabled:Boolean};var K=a({props:n({},J,{bem:c(Function),role:String,parent:Object,checked:Boolean,bindGroup:p}),emits:["click","toggle"],setup(e,{emit:a,slots:l}){const s=m(),t=a=>{if(e.parent&&e.bindGroup)return e.parent.props[a]},d=v((()=>t("disabled")||e.disabled)),i=v((()=>t("direction"))),r=v((()=>{const a=e.checkedColor||t("checkedColor");if(a&&e.checked&&!d.value)return{borderColor:a,backgroundColor:a}})),u=l=>{const{target:t}=l,o=s.value,i=o===t||(null==o?void 0:o.contains(t));d.value||!i&&e.labelDisabled||a("toggle"),a("click",l)},n=()=>{const{bem:a,shape:i,checked:u}=e,n=e.iconSize||t("iconSize");return o("div",{ref:s,class:a("icon",[i,{disabled:d.value,checked:u}]),style:{fontSize:h(n)}},[l.icon?l.icon({checked:u,disabled:d.value}):o(f,{name:"success",style:r.value},null)])},c=()=>{if(l.default)return o("span",{class:e.bem("label",[e.labelPosition,{disabled:d.value}])},[l.default()])};return()=>{const a="left"===e.labelPosition?[c(),n()]:[n(),c()];return o("div",{role:e.role,class:e.bem([{disabled:d.value,"label-disabled":e.labelDisabled},i.value]),tabindex:d.value?void 0:0,"aria-checked":e.checked,onClick:u},[a])}}});const[W,Z]=e("radio");const ee=r(a({name:W,props:J,emits:["update:modelValue"],setup(e,{emit:a,slots:l}){const{parent:s}=b(Q),t=()=>{s?s.updateValue(e.name):a("update:modelValue",e.name)};return()=>o(K,g({bem:Z,role:"radio",parent:s,checked:(s?s.props.modelValue:e.modelValue)===e.name,onToggle:t},e),x(l,["default","icon"]))}})),ae={class:"checkBox"},le=[(e=>(R("data-v-2a3835d5"),e=e(),B(),e))((()=>P("div",{class:"checked"},null,-1)))],se=_({__name:"index",props:{list:{type:Array,default:[]},initRadio:{type:Number,default:0}},emits:["checkedSelect"],setup(e,{emit:a}){const l=V(),s=m(0);k((()=>{let e=l.query.type||0;s.value=Number(e)}));const t=()=>{a("checkedSelect",s.value+1)};return(a,l)=>{const d=ee,i=X;return y(),w("div",ae,[o(i,{modelValue:s.value,"onUpdate:modelValue":l[0]||(l[0]=e=>s.value=e),onChange:t},{default:j((()=>[(y(!0),w(C,null,S(e.list,((e,a)=>(y(),U(d,{key:a,name:a},{icon:j((e=>[P("div",{class:F(["select",e.checked?"selected":""])},le,2)])),default:j((()=>[T($(e.name)+" ",1)])),_:2},1032,["name"])))),128))])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-2a3835d5"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/components/ex-checked/index.vue"]]),te={class:"resetVerify"},oe={class:"content"},de={class:"textColor"},ie={class:"flex mt-4 mb-8 justify-between"},re={class:"flex-1 flex flex-col text-center justify-center items-center"},ue={class:"upload-wrap"},ne={class:"mt-4 font-14 h-5 text-grey"},ce={class:"flex-1 flex flex-col text-center justify-center items-center"},pe={class:"upload-wrap"},me={class:"mt-4 font-14 h-5 text-grey"},ve={class:"flex-1 flex flex-col text-center justify-center items-center"},fe={class:"upload-wrap"},he={class:"mt-4 font-14 h-5 text-grey"},be={key:0},xe=_({__name:"index",setup(e){const{t:a}=D(),l=I(),s=V(),t=m(""),d=m(""),i=m(""),r=m(""),u=m(0),n=m([{name:a("resetFundsPassword"),type:0},{name:a("resetPhone"),type:1},{name:a("resetEmail"),type:2},{name:a("resetGoogleVerify"),type:3}]),c=m([]),p=m([]),v=m([]),f=m(""),h=m(""),b=m(""),x=m("frontFile"),g=m("");m(!1),m(!1),m(!1),k((()=>{u.value=s.query.type,_(u.value),F()}));const _=e=>{t.value=a(1==e?"artificialResetPhone":2==e?"artificialResetEmail":3==e?"artificialResetGoogleVerify":"artificialResetFundsPassword")},C=e=>{u.value=e-1,_(u.value)},S=e=>{e.status="uploading",e.message=a("uploading"),O(e).then((l=>{e.status="success",e.message=a("uploadSuccess"),e.resURL=l,"frontFile"==x.value?c.value=[e]:"reverseFile"==x.value?p.value=[e]:v.value=[e]})).catch((l=>{e.status="failed",e.message=a("uploadFailed")}))},U=e=>{x.value=e},F=()=>{z({}).then((e=>{0!=e.length&&(g.value=e[0].status,f.value=e[0].idcard_path_front_path,h.value=e[0].idcard_path_back_path,b.value=e[0].idcard_path_hold_path)}))},R=()=>{(()=>{let e;0==u.value?e=0:1==u.value?e=2:2==u.value?e=3:3==u.value&&(e=1),E({idcard_path_front:c.value.length&&c.value[0].resURL||f.value||"",idcard_path_back:p.value.length&&p.value[0].resURL||h.value||"",idcard_path_hold:v.value.length&&v.value[0].resURL||h.value||"",operate:e,safeword:i.value,safeword_confirm:r.value,remark:d.value}).then((e=>{l.push({name:"resetSuccess",query:{type:u.value}})}))})()};return(e,a)=>{const l=L("fx-header"),s=A,m=G;return y(),w("div",te,[o(l,null,{title:j((()=>[T($(t.value),1)])),_:1}),P("div",oe,[P("div",null,[P("div",de,$(e.$t("uploadCredentPassport")),1),P("div",ie,[P("div",re,[P("div",ue,[q(' <img :src="idcard_path_front_path" alt="" class="w-full imgShow" v-if="showImg1" /> '),o(s,{modelValue:c.value,"onUpdate:modelValue":a[0]||(a[0]=e=>c.value=e),multiple:"","max-count":1,"after-read":S,onClickUpload:a[1]||(a[1]=e=>U("frontFile"))},null,8,["modelValue"])]),P("div",ne,$(e.$t("credentFront")),1)]),P("div",ce,[P("div",pe,[q(' <img :src="idcard_path_back_path" alt="" class="w-full imgShow" v-if="showImg2" /> '),o(s,{modelValue:p.value,"onUpdate:modelValue":a[2]||(a[2]=e=>p.value=e),multiple:"","max-count":1,"after-read":S,onClickUpload:a[3]||(a[3]=e=>U("reverseFile"))},null,8,["modelValue"])]),P("div",me,$(e.$t("credentObverse")),1)]),P("div",ve,[P("div",fe,[q(' <img :src="idcard_path_hold_path" alt="" class="w-full imgShow" v-if="showImg3" /> '),o(s,{modelValue:v.value,"onUpdate:modelValue":a[4]||(a[4]=e=>v.value=e),multiple:"","max-count":1,"after-read":S,onClickUpload:a[5]||(a[5]=e=>U("fileList"))},null,8,["modelValue"])]),P("div",he,$(e.$t("handCredent")),1)])])]),o(se,{class:"mb-5",list:n.value,onCheckedSelect:C},null,8,["list"]),0==u.value?(y(),w("div",be,[o(N,{label:e.$t("fundsPassword"),placeholderText:e.$t("fundsPasswordContTips"),modelValue:i.value,"onUpdate:modelValue":a[6]||(a[6]=e=>i.value=e),tips:e.$t("funsPasswordTips"),typeText:"password"},null,8,["label","placeholderText","modelValue","tips"]),o(N,{label:e.$t("confirmFundsPassword"),placeholderText:e.$t("fundsPasswordContTips"),modelValue:r.value,"onUpdate:modelValue":a[7]||(a[7]=e=>r.value=e),tips:e.$t("funsPasswordTips"),typeText:"password"},null,8,["label","placeholderText","modelValue","tips"])])):q("v-if",!0),o(N,{label:e.$t("message"),placeholderText:e.$t("entryMessage"),modelValue:d.value,"onUpdate:modelValue":a[8]||(a[8]=e=>d.value=e)},null,8,["label","placeholderText","modelValue"]),o(m,{class:"w-full",style:{"margin-top":"10px"},onClick:R,type:"primary"},{default:j((()=>[T($(e.$t("submit")),1)])),_:1})])])}}},[["__scopeId","data-v-c5914ef7"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/resetVerify/index.vue"]]);export{xe as default};
