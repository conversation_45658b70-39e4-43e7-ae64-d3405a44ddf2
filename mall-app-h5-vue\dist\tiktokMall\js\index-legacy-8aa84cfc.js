System.register(["./index-legacy-46a00900.js","./index-legacy-72e00c5f.js","./index-legacy-e952cf7f.js","./nationalityList-legacy-4b6c8c35.js","./use-route-legacy-be86ac1c.js","./index-legacy-f9c0699e.js","./index-legacy-f9c0699e2.js","./index-legacy-0ade4760.js","./index-legacy-df3e01aa.js","./use-id-legacy-df76950f.js","./index-legacy-322f21cd.js","./index-legacy-ff56f089.js","./countryList-legacy-94cb363f.js"],(function(e,t){"use strict";var a,l,i,n,o,d,c,s,r,u,f,p,v,x,g,y,h,m,b,j=document.createElement("style");return j.textContent=".forget[data-v-665122df]{width:100%;box-sizing:border-box;font-size:13px}.forgetCont[data-v-665122df]{padding:15px}.header[data-v-665122df]{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between;padding:0 13px;font-size:14px;height:50px;line-height:50px}.title[data-v-665122df]{font-weight:700;font-size:26px;margin-top:27px;margin-bottom:33px}.re-tab[data-v-665122df]{margin-bottom:22px}.re-tab div[data-v-665122df]{padding:0 18px;height:34px;line-height:34px;text-align:center;border-radius:4px;margin-right:10px}.re-tab .active[data-v-665122df]{background:#f6f6f6}\n",document.head.appendChild(j),{setters:[e=>{a=e._,l=e.u,i=e.l,n=e.r,o=e.av,d=e.c,c=e.e,s=e.a,r=e.t,u=e.n,f=e.b,p=e.w,v=e.T,x=e.cv,g=e.o,y=e.f},e=>{h=e.B},e=>{m=e.E},e=>{b=e.n},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{}],execute:function(){const t={class:"forget"},j={class:"forgetCont"},_={class:"title textColor"},C={class:"flex re-tab text-grey"},k={__name:"index",setup(e){const{t:a}=l(),k=i(),w=n(""),$=n(0),A=n(!1),V=n(0);let z=n("");const B=e=>{$.value=e,A.value=1==e},E=()=>{""!=w.value?L():v(a("entryAccount"))},L=()=>{let e;0==$.value?e=2:1==$.value?e=1:2==$.value&&(e=3),x({username:1==e?`${V.value}${w.value}`:w.value,verifcode_type:e}).then((t=>{if(1==e&&!t.phone_authority)return v(a("noBindPhoneNum")),!1;if(2==e&&!t.email_authority)return v(a("noBindEmail")),!1;if(3==e&&!t.google_auth_bind)return v(a("noBindGoogleAuth")),!1;let l;1==e?l=t.phone:2==e&&(l=t.email),k.push({name:"safeVerify",query:{type:e,account:l,username:w.value}})}))},N=n(null),S=()=>{N.value.open()},T=(e,t,a)=>{z.value=t,V.value=a};return(e,a)=>{const l=o("fx-header"),i=h;return g(),d("div",t,[c(l),s("div",j,[s("div",_,r(e.$t("resetLoginPassword")),1),s("div",C,[s("div",{class:u(0==$.value?"active":""),onClick:a[0]||(a[0]=e=>B(0))},r(e.$t("email")),3),s("div",{class:u(1==$.value?"active":""),onClick:a[1]||(a[1]=e=>B(1))},r(e.$t("phoneNum")),3),s("div",{class:u(2==$.value?"active":""),onClick:a[2]||(a[2]=e=>B(2))},r(e.$t("googleVerify")),3)]),c(m,{label:e.$t("account"),placeholderText:e.$t("entryAccount"),modelValue:w.value,"onUpdate:modelValue":a[3]||(a[3]=e=>w.value=e),dialCode:V.value,onSelectArea:S,area:A.value,icon:f(z)},null,8,["label","placeholderText","modelValue","dialCode","area","icon"]),c(i,{class:"w-full",style:{"margin-top":"10px"},type:"primary",onClick:E},{default:p((()=>[y(r(e.$t("nextStep")),1)])),_:1}),c(b,{ref_key:"controlChildRef",ref:N,title:e.$t("selectArea"),onGetName:T},null,8,["title"])])])}}};e("default",a(k,[["__scopeId","data-v-665122df"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/forget/index.vue"]]))}}}));
