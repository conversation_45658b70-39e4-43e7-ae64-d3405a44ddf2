System.register(["./index-legacy-46a00900.js","./use-route-legacy-be86ac1c.js"],(function(n,a){"use strict";var o,t,r,i,e,v,b,u,d,l,c,s,g,p=document.createElement("style");return p.textContent=':root{--van-button-mini-height: 24px;--van-button-mini-padding: 0 var(--van-padding-base);--van-button-mini-font-size: var(--van-font-size-xs);--van-button-small-height: 32px;--van-button-small-padding: 0 var(--van-padding-xs);--van-button-small-font-size: var(--van-font-size-sm);--van-button-normal-padding: 0 15px;--van-button-normal-font-size: var(--van-font-size-md);--van-button-large-height: 50px;--van-button-default-height: 44px;--van-button-default-line-height: 1.2;--van-button-default-font-size: var(--van-font-size-lg);--van-button-default-color: var(--van-text-color);--van-button-default-background-color: var(--van-background-color-light);--van-button-default-border-color: var(--van-border-color);--van-button-primary-color: var(--van-white);--van-button-primary-background-color: var(--van-primary-color);--van-button-primary-border-color: var(--van-primary-color);--van-button-success-color: var(--van-white);--van-button-success-background-color: var(--van-success-color);--van-button-success-border-color: var(--van-success-color);--van-button-danger-color: var(--van-white);--van-button-danger-background-color: var(--van-danger-color);--van-button-danger-border-color: var(--van-danger-color);--van-button-warning-color: var(--van-white);--van-button-warning-background-color: var(--van-warning-color);--van-button-warning-border-color: var(--van-warning-color);--van-button-border-width: var(--van-border-width-base);--van-button-border-radius: var(--van-border-radius-sm);--van-button-round-border-radius: var(--van-border-radius-max);--van-button-plain-background-color: var(--van-white);--van-button-disabled-opacity: var(--van-disabled-opacity);--van-button-icon-size: 1.2em;--van-button-loading-icon-size: 20px }.van-button{position:relative;display:inline-block;box-sizing:border-box;height:var(--van-button-default-height);margin:0;padding:0;font-size:var(--van-button-default-font-size);line-height:var(--van-button-default-line-height);text-align:center;border-radius:var(--van-button-border-radius);cursor:pointer;-webkit-transition:opacity var(--van-animation-duration-fast);transition:opacity var(--van-animation-duration-fast);-webkit-appearance:none}.van-button:before{position:absolute;top:50%;left:50%;width:100%;height:100%;background:var(--van-black);border:inherit;border-color:var(--van-black);border-radius:inherit;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);opacity:0;content:" "}.van-button:active:before{opacity:.1}.van-button--loading:before,.van-button--disabled:before{display:none}.van-button--default{color:var(--van-button-default-color);background:var(--van-button-default-background-color);border:var(--van-button-border-width) solid var(--van-button-default-border-color)}.van-button--primary{color:var(--van-button-primary-color);background:var(--van-button-primary-background-color);border:var(--van-button-border-width) solid var(--van-button-primary-border-color)}.van-button--success{color:var(--van-button-success-color);background:var(--van-button-success-background-color);border:var(--van-button-border-width) solid var(--van-button-success-border-color)}.van-button--danger{color:var(--van-button-danger-color);background:var(--van-button-danger-background-color);border:var(--van-button-border-width) solid var(--van-button-danger-border-color)}.van-button--warning{color:var(--van-button-warning-color);background:var(--van-button-warning-background-color);border:var(--van-button-border-width) solid var(--van-button-warning-border-color)}.van-button--plain{background:var(--van-button-plain-background-color)}.van-button--plain.van-button--primary{color:var(--van-button-primary-background-color)}.van-button--plain.van-button--success{color:var(--van-button-success-background-color)}.van-button--plain.van-button--danger{color:var(--van-button-danger-background-color)}.van-button--plain.van-button--warning{color:var(--van-button-warning-background-color)}.van-button--large{width:100%;height:var(--van-button-large-height)}.van-button--normal{padding:var(--van-button-normal-padding);font-size:var(--van-button-normal-font-size)}.van-button--small{height:var(--van-button-small-height);padding:var(--van-button-small-padding);font-size:var(--van-button-small-font-size)}.van-button__loading{color:inherit;font-size:inherit}.van-button__loading .van-loading__spinner{color:currentColor;width:var(--van-button-loading-icon-size);height:var(--van-button-loading-icon-size)}.van-button--mini{height:var(--van-button-mini-height);padding:var(--van-button-mini-padding);font-size:var(--van-button-mini-font-size)}.van-button--mini+.van-button--mini{margin-left:var(--van-padding-base)}.van-button--block{display:block;width:100%}.van-button--disabled{cursor:not-allowed;opacity:var(--van-button-disabled-opacity)}.van-button--loading{cursor:default}.van-button--round{border-radius:var(--van-button-round-border-radius)}.van-button--square{border-radius:0}.van-button__content{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;height:100%}.van-button__content:before{content:" "}.van-button__icon{font-size:var(--van-button-icon-size);line-height:inherit}.van-button__icon+.van-button__text,.van-button__loading+.van-button__text,.van-button__text+.van-button__icon,.van-button__text+.van-button__loading{margin-left:var(--van-padding-base)}.van-button--hairline{border-width:0}.van-button--hairline:after{border-color:inherit;border-radius:calc(var(--van-button-border-radius) * 2)}.van-button--hairline.van-button--round:after{border-radius:var(--van-button-round-border-radius)}.van-button--hairline.van-button--square:after{border-radius:0}\n',document.head.appendChild(p),{setters:[n=>{o=n.P,t=n.Q,r=n.R,i=n.S,e=n.d,v=n.U,b=n.e,u=n.I,d=n.V,l=n.W,c=n.X},n=>{s=n.r,g=n.u}],execute:function(){const[a,p]=o("button"),h=t({},s,{tag:r("button"),text:String,icon:String,type:r("default"),size:r("normal"),color:String,block:Boolean,plain:Boolean,round:Boolean,square:Boolean,loading:Boolean,hairline:Boolean,disabled:Boolean,iconPrefix:String,nativeType:r("button"),loadingSize:i,loadingText:String,loadingType:String,iconPosition:r("left")});var f=e({name:a,props:h,emits:["click"],setup(n,{emit:a,slots:o}){const t=g(),r=()=>n.loading?o.loading?o.loading():b(l,{size:n.loadingSize,type:n.loadingType,class:p("loading")},null):o.icon?b("div",{class:p("icon")},[o.icon()]):n.icon?b(u,{name:n.icon,class:p("icon"),classPrefix:n.iconPrefix},null):void 0,i=()=>{let a;if(a=n.loading?n.loadingText:o.default?o.default():n.text,a)return b("span",{class:p("text")},[a])},e=()=>{const{color:a,plain:o}=n;if(a){const n={color:o?a:"white"};return o||(n.background=a),a.includes("gradient")?n.border=0:n.borderColor=a,n}},c=o=>{n.loading?d(o):n.disabled||(a("click",o),t())};return()=>{const{tag:a,type:o,size:t,block:u,round:d,plain:l,square:s,loading:g,disabled:h,hairline:f,nativeType:m,iconPosition:k}=n,y=[p([o,t,{plain:l,block:u,round:d,square:s,loading:g,disabled:h,hairline:f}]),{[v]:f}];return b(a,{type:m,class:y,style:e(),disabled:h,onClick:c},{default:()=>[b("div",{class:p("content")},["left"===k&&r(),i(),"right"===k&&r()])]})}}});n("B",c(f))}}}));
