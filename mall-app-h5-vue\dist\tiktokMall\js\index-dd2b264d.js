import{_ as e,i as t,ci as n,r as a,q as l,u as s,Y as i,l as o,m as r,ao as c,j as d,s as A,J as m,c as g,a as f,e as u,L as v,t as p,F as w,y as x,x as h,w as U,h as k,v as b,n as S,b as y,T as C,o as B,b4 as j}from"./index-3d21abf8.js";import{I as R}from"./index-7d1632e5.js";/* empty css              *//* empty css               */import{U as J}from"./index-1f1846c6.js";import{N as E}from"./index-cfdda867.js";import{a as M,b as L}from"./im.api-39225697.js";import{u as K}from"./upload.api-28f256be.js";import"./index-0d6a7179.js";import"./index-a439655d.js";import"./use-placeholder-c97cb410.js";const Q={class:"service-header"},I={class:"flex flex-col px-16 box-border h-full chat-container",style:{background:"#f5f5f5"}},T={key:0,class:"font-12 text-center py-1 text-grey time-divider"},Y=["src"],Z={class:"text-xs text-grey mb-1"},P={key:0,class:"chat-res-content text-sm"},G=["innerHTML"],H={key:1,class:"chat-res-content img text-sm"},z=["src","onClick"],W={class:"text-xs text-grey mb-1",style:{"text-align":"right"}},V=["innerHTML"],N={key:1,class:"chat-res-content img text-sm"},q=["src","onClick"],D=["src"],X={class:"relative bottom bottomBox flex justify-between items-center w-full fixed bottom-0 borderTop px-3 py-2 box-border bgBottom bg-white ios-bottom-input"},F=["src"],O={class:"flex-1 mx-3 h-full border-none bgBottom textColor send-msg-content"},_=["placeholder"],$=e({__name:"index",setup(e){const $=t(),{height:ee}=n(),te=a(!1);l((()=>{const e=navigator.userAgent.toLowerCase();te.value=/iphone|ipad|ipod/.test(e)||!0===window.navigator.standalone,te.value&&document.body.classList.add("ios-device")}));const ne=a(new URL("data:image/png;base64,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",self.location).href),{t:ae,locale:le}=s(),se=i(),ie=o(),oe=a([]),re=a(""),ce=a(""),de=a(null);a(0);const Ae=a(!1),me=a(null),ge=a(null),fe=a(0),ue=a(!1),ve={responser:new URL("data:image/png;base64,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",self.location),argos:new URL("/www/png/name-d7f7a48e.png",self.location),argos2:new URL("/www/png/name-d7f7a48e.png",self.location),familyShop:new URL("/www/png/name-f6e12968.png",self.location),hive:new URL("/www/png/name-3a1dd991.png",self.location),inchoi:new URL("/www/png/name-924c0133.png",self.location),mbuy:new URL("/www/png/name-5d5887cc.png",self.location),greenMall:new URL("/www/png/name-b8e3926e.png",self.location),tiktokMall:new URL("/www/png/name-54adb70a.png",self.location),shop2u:new URL("/www/png/name-4c3635af.png",self.location),sm:new URL("/www/png/name-1eca6583.png",self.location),iceland:new URL("/www/png/name-4d16d45f.png",self.location),int:new URL("/www/png/name-84219bb9.png",self.location),"tiktok-wholesale":new URL("/www/png/name-eca5f455.png",self.location),antMall:new URL("/www/png/name-fb841661.png",self.location),simon:new URL("/www/png/name-e93c092e.png",self.location),texm:new URL("/www/png/name-5baf10a3.png",self.location),"alibaba-wholesale":new URL("/www/png/name-02067a4b.png",self.location),photo:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAYAAABV7bNHAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAJvSURBVHgB7ZtNbhoxGIZfAwvUVY+QGzRdsiqzaPc5QbkBvQHDDeAG6TVSVUMWFeqq6Q1yhCwqStNkHH8RlrLANg6e0czwPtLIlj5rJD/y8PkPgBBCCCGEEEJI3ahQg6L49fYB26mpjndPB1A3Gnr5KRtdBlv6gkWxPns0hQbO0EFM52/7QJZlo1tXm57vBV2WI0jfpI/ylbjaOAVdFT8mXZZjkT7eY/PFFXcKUuhPcSL00PvgjjnR5zgdxq7AAJF8zEbBzNdkvhVrHdO+B+KFggJQUAAKCkBBAaKzmA+7bjNp7nmSKVN5U14OMFxm2fs7tJBkI0jk/Nfblanmdga+K/MH/PNO55tMMkEl7qdK4d3+qD73TeebTDJBGuXEF++j9xktJKEg/8K2rQvfZILkB9kX14F4U0k5gr7646U3fijfi58zs56aoSaSCRpgu5CtzH2xUuP3Bm8WOBKRU6LMTTWvS1IyQWbf8m6Av5mpzu3ntPvs5hs1HF8cOQ96IcdSi6SkE0WRZIp89yRjjxyLSJItmDkqovFLDY8cS6UjqdGCDpBjqUxSbYJis0+EHEslkmoRFJt9XiHHklxS5YJis88Rcg56fyyVCgpkn1lE+1hyJKIyQbHZJ6GcpCSdB1kis48c3KGJcoRKBMVmH9MeTYV70gEoKAAFBaCgABQUoJIs1vYbIC/hCApAQQEoKAAFBaCgANFZLPaOX9vxjCC1wsmgb1wR3zXga5wIGmrpijkFyUlpW8/TY5A++v7U4hQkh4CPpuiyJOmb9NHX5qAlwVWxniioaYdu36/Mc/0Hw8VFS68GEkIIIYQQQkiXeQJ31xQ8cq8AoAAAAABJRU5ErkJggg==",self.location)},pe=a(null),we=a(null),xe=a(!1),he=r((()=>{const e="tiktokMall";let t=ve.responser.href;return ve[e]&&(t=ve[e].href),t})),Ue=c(),ke=r((()=>{var e;return(null==(e=Ue.shopInfo)?void 0:e.avatar)||""}));l((async()=>{fe.value=ge.value.$el.getBoundingClientRect().height,ue.value=!1;const e=navigator.userAgent;me.value=e.indexOf("Android")>-1||e.indexOf("Linux")>-1;const{avatar:t,token:n,lang:a}=se.query;if(ke.value&&(ne.value=ke.value),t&&(ne.value=t),a&&(le.value=a,localStorage.setItem("lang",a)),n){const e=d();await e.getUserInfo(!0,n)}xe.value=!0,Be()}));const be=e=>{C(ae("fileMaxLimit"))},Se=e=>{R([e])},ye=e=>{let t=!1;return 0===e&&(t=!0),e>0&&oe.value[e].createtime.split(" ")[0]!==oe.value[e-1].createtime.split(" ")[0]&&(t=!0),t},Ce=e=>{C.loading({duration:0}),K({file:e.file,moduleName:"customerService"}).then((e=>{C.clear(),Me("img",e)})).catch((()=>{C.clear()}))},Be=(e="")=>{M({message_id:e}).then((t=>{if(ce.value||(ce.value=t.length&&t[t.length-1].id),t.length){let n=[];if(t.forEach((e=>{if("img"===e.type){const t=e.content,n="imagePath=";e.imgUrl=t;const a=t.indexOf(n);if(a>0){const l=t.slice(a+n.length);e.imgUrl=l}}})),e)ce.value=t[t.length-1].id,n=je([...t.reverse(),...oe.value]);else{n=je([...oe.value,...t.reverse()]);let e={};n=n.reduce((function(t,n){return e[n.id]||(e[n.id]=t.push(n)),t}),[])}oe.value=n,t.length<10&&(Ae.value=!0)}xe.value&&A((()=>{pe.value.scrollTop=we.value.offsetHeight,xe.value=!1})),ue.value||(ue.value=!0,de.value=setInterval((()=>{Be()}),1e3))}))},je=e=>{let t=[];return t=e.filter((e=>-1===Number(e.delete_status))).map((e=>e.id)),e.filter((e=>!t.includes(e.id)))},Re=()=>{Be(ce.value)},Je=()=>{try{window.history.length>1?window.history.back():ie.push("/")}catch(e){ie.push("/")}},Ee=a(!1),Me=(e="text",t="")=>{Ee.value||(t?(C.loading({duration:0,forbidClick:!0}),Ee.value=!0,L(e,t).then((e=>{re.value="",Ee.value=!1,C.clear(),xe.value=!0,Be()})).catch((()=>{Ee.value=!1,C.clear()}))):C(ae("entryMessageContent")))};return m((()=>{de.value&&(clearInterval(de.value),de.value=null)})),(e,t)=>{const n=E,a=J;return B(),g("div",{class:S(["service-box flex flex-col has-nav-bar",{"ios-device":te.value}]),style:v({height:y(ee)+"px"})},[f("div",Q,[u(n,{fixed:!0,ref_key:"navEl",ref:ge,title:e.$t("onLineService"),"left-arrow":"",onClickLeft:Je},null,8,["title"])]),f("div",{ref_key:"msgContent",ref:pe,class:"content flex-1 overflow-auto",style:{background:"#f5f5f5"}},[f("div",I,[f("div",{class:"w-full py-2 text-grey text-center history-btn",onClick:Re,style:v({display:Ae.value?"none":"block"})},p(e.$t("historyMessage")),5),f("ul",{ref_key:"msgTxtContent",ref:we,class:"flex flex-col pt-2 message-list"},[(B(!0),g(w,null,x(oe.value,((e,t)=>(B(),g("li",{key:e.id,class:"flex flex-col my-2 message-item"},[ye(t)?(B(),g("p",T,p(y(j)(e.createtime,"YYYY-MM-DD")),1)):h("v-if",!0),f("div",{class:S(["flex responser-content items-center","send"===e.send_receive?"justify-end":""])},["receive"===e.send_receive?(B(),g(w,{key:0},[f("img",{src:y(he),class:S(["w-9 h-9",y($)?"ml-3":"mr-3"]),style:{"border-radius":"50%"}},null,10,Y),f("div",null,[f("div",Z,p(y(j)(e.createtime,"YYYY-MM-DD HH:mm")),1),h(' <div v-if="item.type === \'text\'" class="chat-res-content text-sm">{{ item.content }}</div> '),"text"===e.type?(B(),g("div",P,[f("p",{innerHTML:e.content},null,8,G)])):(B(),g("div",H,[f("img",{src:e.imgUrl,onClick:t=>Se(e.imgUrl)},null,8,z)]))])],64)):(B(),g(w,{key:1},[f("div",null,[f("div",W,p(y(j)(e.createtime,"YYYY-MM-DD HH:mm")),1),h(" <div v-if=\"item.type === 'text'\" class=\"chat-res-content text-sm\" :class=\"item.send_receive === 'send' ? 'send-bg' : ''\">{{ item.content }}</div> "),"text"===e.type?(B(),g("div",{key:0,class:S(["chat-res-content text-sm","send"===e.send_receive?"send-bg":""])},[f("p",{innerHTML:e.content},null,8,V)],2)):(B(),g("div",N,[f("img",{src:`${e.imgUrl}`,onClick:t=>Se(e.imgUrl)},null,8,q)]))]),f("img",{src:ne.value,class:S(["w-9 h-9",y($)?"mr-3":"ml-3"]),style:{"border-radius":"50%"}},null,10,D)],64))],2)])))),128))],512)])],512),f("div",X,[h(' <van-uploader :max-size="10000 * 1024" @oversize="onOversize" :after-read="afterRead" :capture="androidAttrs ? \'camera\' : null"> '),u(a,{"max-size":1024e4,onOversize:be,"after-read":Ce,class:"upload-btn"},{default:U((()=>[f("img",{src:ve.photo,class:"w-8 h-8"},null,8,F)])),_:1}),f("div",O,[k(f("textarea",{"onUpdate:modelValue":t[0]||(t[0]=e=>re.value=e),placeholder:e.$t("entryYouMessage"),class:"flex-1 mx-3 h-full border-none bgBottom textColor",style:{resize:"none","background-color":"#fff"}},null,8,_),[[b,re.value]])]),f("i",{class:"iconfont icon-fasong",onClick:t[1]||(t[1]=e=>Me("text",re.value))})])],6)}}},[["__scopeId","data-v-864b89f2"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/customerService/index.vue"]]);export{$ as default};
