import{_ as e,Y as a,l,u as t,r as u,j as s,q as o,cn as i,g as v,av as n,W as r,c,e as d,w as p,a as m,x as f,t as y,h as A,v as g,f as h,F as x,b as C,T as V,co as b,cp as j,cq as k,o as w,D as B,E}from"./index-3d21abf8.js";import{B as S}from"./index-2406f514.js";import{P as $,N as T}from"./index-2c2a5a87.js";import{E as U}from"./index-9c8e9dca.js";import{a as I}from"./login.api-cb7fcde3.js";import{n as q}from"./nationalityList-c6365b2b.js";import{u as z}from"./index-54dce367.js";import{I as F}from"./index-a439655d.js";import"./use-route-cd41a893.js";/* empty css              *//* empty css               */import"./index-8c1841f6.js";import"./index-7dfcb82a.js";import"./use-id-a0619e01.js";import"./index-3d6106f5.js";import"./index-179203f3.js";import"./countryList-016fc82e.js";const H={class:"bindVerify h-full bg-white"},N={class:"content"},R={key:0},D={style:{"margin-top":"22px"}},O={class:"label mt-2 textColor"},P={class:"iptbox inputBackground"},Y=["placeholder"],Z={style:{"margin-top":"22px"}},_={key:1},J={class:"pl-30 pr-30 text-center flex flex-col items-center justify-center mt40"},X={class:"imgbox"},G={class:"code flex items-center justify-center textColor"},W=["src"],K={class:"tips"},M={class:"flex justify-between mt-6 mb-3"},Q={class:"flex items-center"},L=(e=>(B("data-v-59a3607d"),e=e(),E(),e))((()=>m("div",{class:"mt-2"},null,-1))),ee={class:"mt-5 bottom tabBackground textColor"},ae=e({__name:"index",setup(e){const B=a(),E=l(),{t:ae}=t(),{toClipboard:le}=z(),te=u(""),ue=u(""),se=u(""),oe=u(!1),ie=u(0),ve=u(""),ne=u(""),re=u(""),ce=u(!1),de=u("");u(!0);const pe=u(""),me=u(0),fe=u(null),ye=u(0),Ae=u(""),ge=s(),he=u(!1);o((()=>{xe(),clearInterval(fe.value),fe.value=null}));const xe=()=>{ie.value=B.query.type,ve.value=B.query.verifyCode||"",1==ie.value?(te.value=ae("bindPhone"),oe.value=!0):2==ie.value?te.value=ae("bindEmail"):3==ie.value&&(te.value=ae("googleAuthenticatorEn"),we())},Ce=async()=>{if(2!=ie.value||""!=ue.value)if(2!=ie.value||/^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(ue.value))if(1!=ie.value||""!=ue.value)if(1!=ie.value||/^[0-9]+$/.test(ue.value)){if(ye.value>0)return!1;V.loading({mask:!0}),await b({target:1==ie.value?`${me.value} ${ue.value}`:ue.value}),I({target:1==ie.value?`${me.value} ${ue.value}`:ue.value}).then((e=>{V(ae("sendSuccess")),ye.value=30,fe.value=setInterval((()=>{ye.value>0?ye.value=ye.value-1:(ye.value=0,clearInterval(fe.value),fe.value=null)}),1e3)})).catch((()=>{V.clear()}))}else V(ae("请输入正确的手机号码"));else V(ae("entryPhone"));else V(ae("请输入正确的邮箱地址"));else V(ae("entryEmail"))},Ve=()=>{2!=ie.value||""!=ue.value?1!=ie.value||""!=ue.value?""!=se.value?(he.value=!0,1==ie.value?ke():2==ie.value?je():3==ie.value&&Be()):V(ae("请输入登录密码")):V(ae("entryPhone")):V(ae("entryEmail"))},be=async()=>{await ge.getUserInfo(!0),V(ae("bindSuccess")),he.value=!1,setTimeout((()=>{B.query.reset?E.go(-2):E.back()}),1e3)},je=()=>{j({target:ue.value,email:ue.value,verifcode:ne.value,password:se.value,verifyCode:ve.value}).then((e=>{be()})).catch((e=>{he.value=!1}))},ke=()=>{j({target:`${me.value} ${ue.value}`,phone:`${me.value} ${ue.value}`,verifcode:ne.value,password:se.value,verifyCode:ve.value}).then((e=>{be()})).catch((e=>{he.value=!1}))},we=()=>{i({}).then((e=>{re.value=e.google_auth_secret,pe.value=e.google_auth_url}))},Be=()=>{k({secret:re.value,code:de.value}).then((e=>{e.google_auth_bind?be():V(err||ae("bindFailed"))}))},Ee=async()=>{try{await le(re.value),V(ae("copySuccess"))}catch(e){}},Se=async()=>{de.value=await navigator.clipboard.readText()},$e=(e,a,l)=>{Ae.value=a,me.value=l},Te=u(null),Ue=()=>{Te.value.open()};return v(de,((e,a)=>{6===de.value.length&&(ce.value=!1)})),(e,a)=>{const l=n("fx-header"),t=r,u=$,s=T,o=S;return w(),c("div",H,[d(l,null,{title:p((()=>[h(y(te.value),1)])),_:1}),m("div",N,[f(" 手机邮箱验证 "),1==ie.value||2==ie.value?(w(),c("div",R,[m("div",D,[d(U,{label:2==ie.value?e.$t("email"):e.$t("phoneNum"),placeholderText:2==ie.value?e.$t("entryEmail"):e.$t("entryPhone"),modelValue:ue.value,"onUpdate:modelValue":a[0]||(a[0]=e=>ue.value=e),area:oe.value,onSelectArea:Ue,dialCode:me.value,icon:Ae.value},null,8,["label","placeholderText","modelValue","area","dialCode","icon"])]),m("p",O,y(e.$t("verificationCode")),1),m("div",P,[A(m("input",{class:"inputBackground textColor",type:"text",placeholder:e.$t("entryVerifyCode"),"onUpdate:modelValue":a[1]||(a[1]=e=>ne.value=e)},null,8,Y),[[g,ne.value]]),3!=ie.value?(w(),c("span",{key:0,onClick:Ce},[h(y(e.$t("sendVerifyCode")),1),ye.value?(w(),c(x,{key:0},[h(" ("+y(ye.value)+")s",1)],64)):f("v-if",!0)])):f("v-if",!0)]),m("div",Z,[d(U,{label:e.$t("登录密码"),placeholderText:e.$t("请输入登录密码"),modelValue:se.value,"onUpdate:modelValue":a[2]||(a[2]=e=>se.value=e),typeText:"password",clearBtn:!1},null,8,["label","placeholderText","modelValue"])])])):f("v-if",!0),f(" 谷歌验证 "),3==ie.value?(w(),c("div",_,[m("div",J,[m("div",X,[d(C(F),{src:pe.value},{loading:p((()=>[d(t,{type:"spinner",size:"20"})])),_:1},8,["src"])]),m("div",G,[h(y(re.value)+" ",1),m("img",{src:C("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAKzSURBVHgBvVZdbtpAEP5214G+VOUI9ATlBqEnKDlB4CVV1JeitJHSKIqjCJCaEsRDFYVUIj1BuEHICUJPUN+gPDY23unuEsjaQLDz00+yZHt29tud2Z35GFKi9vV7AYIXGHiOJHLg0pMS3t7nzX4Sf5Zk0OHRSdHhYp2AkvrM3TO0F8qwvYi80ep07yVsNE7ylHW6ICoiDQgDPwjX3J1NzyZTESnzRT714x/rlBHXC8iG6vFun1kwFDIZ8bvePP1ok41Nc9Bone2TJDdOov61QaPe7vaHgW0weWVOiXOmw563bcTYgBEV7tYSw+G305Jg7CLiRGgHYsV1q5UhlqB2dOoyzvYX2SOEOmfIiEt7lRSisru9cY4UqDVPygyiO88WzWFG7EdCIqmalsxMysXqItt0h+ZEqkRbhvOdrY0KUsI+IHMXM3khh5dsww0PD/DEZBFCVT3eTV717tzq3R16KrIooXXfJHCFFEhKpuGMHVT+pPU3DAZIgZu/JvyJUmAIRyPkhXVeiWf/IAXsErYMhsZxkNjhsTCEN3gRqSBEwWs8Ewzhbcka3v3kRTwTHOu9j3G/AxNsFU+EerNzobuHmZfQnx6VkOjndBShqJsuHglTm/UmSJVL9ejrNiXc+/S+Byusgouu2+rm8BioRmB9eQFf6UWKNyGsWp/5jPS7eCDqrbNWrBG09VmZ6Yf1486lDqm1jJ7vy2rSu+a63Vz2VdCKVR7vy9aGOfkzEsNnK2ssIh1YScmFy1qzU15GpvOefRlcx8l8Hr6dzjbPUZc6yGgjnjgrhz6BfoVSmvLHOfIMzhsViTJmFZ0hsxvBQtVm6mvIXTC2joeAWN8Xo0q86yzVpVoucMSUwP3w1NiD3a35SiGRENbQ+eFMlJRAeoOxCpuEb6h2o0joSlLYS6rA/xv+AZfLLFBKDLSAAAAAAElFTkSuQmCC"),alt:"",onClick:we},null,8,W)]),m("p",K,y(e.$t("saveKeyTips")),1),m("div",{class:"copy textColor",onClick:Ee},y(e.$t("copy")),1)]),m("div",M,[m("div",null,y(e.$t("googleVerificationCode")),1),m("div",Q,[m("div",{class:"colorMain",onClick:a[3]||(a[3]=e=>de.value="")},y(e.$t("clear")),1),m("div",{class:"colorMain ml-30",onClick:Se},y(e.$t("paste")),1)])]),d(u,{value:de.value,gutter:16,focused:ce.value,onFocus:a[4]||(a[4]=e=>ce.value=!0),mask:!1},null,8,["value","focused"]),d(s,{modelValue:de.value,"onUpdate:modelValue":a[5]||(a[5]=e=>de.value=e),show:ce.value,onBlur:a[6]||(a[6]=e=>ce.value=!1)},null,8,["modelValue","show"]),L,m("div",ee,[m("p",null,y(e.$t("precautions")),1),m("p",null,y(e.$t("precautionsTips1")),1),m("p",null,y(e.$t("precautionsTips2")),1)])])):f("v-if",!0),d(o,{class:"w-full",style:{"margin-top":"30px"},type:"primary",loading:he.value,onClick:Ve},{default:p((()=>[h(y(e.$t("confirm")),1)])),_:1},8,["loading"])]),d(q,{ref_key:"controlChildRef",ref:Te,title:e.$t("selectArea"),onGetName:$e},null,8,["title"])])}}},[["__scopeId","data-v-59a3607d"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/bindVerify/index.vue"]]);export{ae as default};
