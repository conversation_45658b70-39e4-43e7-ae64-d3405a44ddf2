const o=[{id:0,txt:"未退款",color:"#FF3E3E"},{id:1,txt:"申请中",color:"#F99746"},{id:2,txt:"成功",color:"#0ECB81"},{id:3,txt:"失败",color:"#FF3E3E"}],t=[{id:-1,txt:"订单已取消",color:"#F99746"},{id:0,txt:"等待买家付款",color:"#0ECB81"},{id:1,txt:"买家已付款",color:"#FF3E3E"},{id:2,txt:"供应商已接单",color:"#F99746"},{id:3,txt:"物流运输中",color:"#0ECB81"},{id:4,txt:"买家已签收",color:"#FF3E3E"},{id:5,txt:"订单已完成",color:"#F99746"},{id:6,txt:"已退款",color:"#0ECB81"}],E=[{id:1,txt:"未收到货",color:"#FF3E3E"},{id:2,txt:"不喜欢，不想要",color:"#F99746"},{id:3,txt:"卖家发错货",color:"#0ECB81"},{id:4,txt:"假冒品牌",color:"#FF3E3E"},{id:5,txt:"少发、漏发",color:"#FF3E3E"},{id:6,txt:"收到商品破损",color:"#F99746"},{id:7,txt:"存在质量问题",color:"#0ECB81"},{id:8,txt:"与商家协商一致退款",color:"#FF3E3E"},{id:9,txt:"其他原因",color:"#FF3E3E"}];export{t as l,E as r,o as s};
