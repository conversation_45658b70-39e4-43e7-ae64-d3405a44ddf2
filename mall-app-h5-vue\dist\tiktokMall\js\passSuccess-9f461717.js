import{_ as s,l as a,j as e,av as c,c as t,e as o,a as i,b as r,t as l,w as n,o as u,f as d}from"./index-3d21abf8.js";import{B as p}from"./index-2406f514.js";import{s as f}from"./success-4eb68329.js";import"./use-route-cd41a893.js";const m={class:"resetSuccess"},v={class:"content"},g={class:"success-container"},_={class:"imgBox"},b=["src"],x={class:"title"},h={class:"subtitle"},j=s({__name:"passSuccess",setup(s){const j=a(),w=e(),k=()=>{w.userInfo={},j.push("/login")};return(s,a)=>{const e=c("fx-header"),j=p;return u(),t("div",m,[o(e,{back:!1,onBack:k}),i("div",v,[i("div",g,[i("div",_,[i("img",{src:r(f),alt:""},null,8,b)]),i("div",x,l(s.$t("passwordChangeSuccess")),1),i("div",h,l(s.$t("useNewPasswordLogin"))+"!",1)]),o(j,{class:"login-btn",type:"primary",onClick:k},{default:n((()=>[d(l(s.$t("login")),1)])),_:1})])])}}},[["__scopeId","data-v-fddfa7f1"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/forget/passSuccess.vue"]]);export{j as default};
