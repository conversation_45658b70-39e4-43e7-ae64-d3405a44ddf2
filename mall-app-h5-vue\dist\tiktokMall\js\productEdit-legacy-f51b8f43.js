System.register(["./index-legacy-46a00900.js","./index-legacy-72e00c5f.js","./index-legacy-1fd93e33.js","./index-legacy-71866ecf.js","./index-legacy-f9c0699e.js","./index-legacy-f9c0699e2.js","./product.api-legacy-82d5f74f.js","./index-legacy-0ade4760.js","./index-legacy-b65b115e.js","./index-legacy-9e9f7160.js","./function-call-legacy-3e53b389.js","./use-route-legacy-be86ac1c.js","./index-legacy-a4cde014.js","./stringify-legacy-93b715ae.js","./___vite-browser-external_commonjs-proxy-legacy-70ac8ee8.js","./index-legacy-df3e01aa.js","./use-id-legacy-df76950f.js","./use-placeholder-legacy-f22ccc27.js"],(function(e,t){"use strict";var a,i,l,o,n,r,s,c,u,d,v,p,m,f,b,h,g,x,w,y,k,T,C,V,P,_,S,$,N,z,j,D,F,I,M,<PERSON>,B,Y,O,E,R,q,H,A,G,J,K,W,X=document.createElement("style");return X.textContent=":root{--van-switch-size: 30px;--van-switch-width: 2em;--van-switch-height: 1em;--van-switch-node-size: 1em;--van-switch-node-background-color: var(--van-white);--van-switch-node-box-shadow: 0 3px 1px 0 rgba(0, 0, 0, .05), 0 2px 2px 0 rgba(0, 0, 0, .1), 0 3px 3px 0 rgba(0, 0, 0, .05);--van-switch-background-color: var(--van-background-color-light);--van-switch-on-background-color: var(--van-primary-color);--van-switch-transition-duration: var(--van-animation-duration-base);--van-switch-disabled-opacity: var(--van-disabled-opacity);--van-switch-border: var(--van-border-width-base) solid rgba(0, 0, 0, .1) }.van-switch{position:relative;display:inline-block;box-sizing:content-box;width:var(--van-switch-width);height:var(--van-switch-height);font-size:var(--van-switch-size);background:var(--van-switch-background-color);border:var(--van-switch-border);border-radius:var(--van-switch-node-size);cursor:pointer;-webkit-transition:background-color var(--van-switch-transition-duration);transition:background-color var(--van-switch-transition-duration)}.van-switch__node{position:absolute;top:0;left:0;width:var(--van-switch-node-size);height:var(--van-switch-node-size);font-size:inherit;background:var(--van-switch-node-background-color);border-radius:100%;box-shadow:var(--van-switch-node-box-shadow);-webkit-transition:-webkit-transform var(--van-switch-transition-duration) cubic-bezier(.3,1.05,.4,1.05);transition:-webkit-transform var(--van-switch-transition-duration) cubic-bezier(.3,1.05,.4,1.05);transition:transform var(--van-switch-transition-duration) cubic-bezier(.3,1.05,.4,1.05);transition:transform var(--van-switch-transition-duration) cubic-bezier(.3,1.05,.4,1.05),-webkit-transform var(--van-switch-transition-duration) cubic-bezier(.3,1.05,.4,1.05)}.van-switch__loading{top:25%;left:25%;width:50%;height:50%;line-height:1}.van-switch--on{background:var(--van-switch-on-background-color)}.van-switch--on .van-switch__node{-webkit-transform:translate(calc(var(--van-switch-width) - var(--van-switch-node-size)));transform:translate(calc(var(--van-switch-width) - var(--van-switch-node-size)))}.van-switch--on .van-switch__loading{color:var(--van-switch-on-background-color)}.van-switch--disabled{cursor:not-allowed;opacity:var(--van-switch-disabled-opacity)}.van-switch--loading{cursor:default}.editProduct[data-v-47e02415]{background:#fff;color:#333}.editProduct .title[data-v-47e02415]{text-align:center;line-height:55px;font-size:16px;font-weight:700}.editProduct .edit-product-pop[data-v-47e02415]{padding-top:46px}.editProduct .edit-product-pop.is-ar[data-v-47e02415] .van-field__control{text-align:right}.editProduct .tip[data-v-47e02415]{font-size:14px}.editProduct .tips[data-v-47e02415]{font-size:12px;line-height:18px;color:#000}.editProduct .tips span[data-v-47e02415]{color:#1552f0}.editProduct .input-field[data-v-47e02415]{border:1px solid #ddd}.editProduct .input-field .profit[data-v-47e02415]{color:#0ecb81}.editProduct .input-item[data-v-47e02415]{-webkit-box-pack:justify;-webkit-justify-content:space-between;justify-content:space-between;-webkit-box-align:center;-webkit-align-items:center;align-items:center}.time-clear[data-v-47e02415]{width:44px;height:44px;background-color:#fff;position:absolute;z-index:9;right:0;top:0;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-align:center;-webkit-align-items:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center}.btn-content[data-v-47e02415]{background-color:var(--site-main-color);border-color:var(--site-main-color);border-radius:4px}\n",document.head.appendChild(X),{setters:[e=>{a=e.P,i=e.S,l=e.a5,o=e.d,n=e.bF,r=e.a8,s=e.e,c=e.W,u=e.X,d=e._,v=e.j,p=e.m,m=e.i,f=e.u,b=e.Y,h=e.l,g=e.r,x=e.bA,w=e.q,y=e.g,k=e.p,T=e.av,C=e.I,V=e.bn,P=e.c,_=e.w,S=e.a,$=e.x,N=e.n,z=e.b,j=e.T,D=e.o,F=e.t,I=e.f,M=e.A,U=e.K,B=e.ak,Y=e.D,O=e.E},e=>{E=e.B},()=>{},e=>{R=e.C},()=>{},()=>{},e=>{q=e.s,H=e.b,A=e.d},e=>{G=e.F},e=>{J=e.D},e=>{K=e.F},e=>{W=e.D},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{},()=>{}],execute:function(){const[t,X]=a("switch"),L={size:i,loading:Boolean,disabled:Boolean,modelValue:l,activeColor:String,inactiveColor:String,activeValue:{type:l,default:!0},inactiveValue:{type:l,default:!1}};var Q=o({name:t,props:L,emits:["change","update:modelValue"],setup(e,{emit:t,slots:a}){const i=()=>e.modelValue===e.activeValue,l=()=>{if(!e.disabled&&!e.loading){const a=i()?e.inactiveValue:e.activeValue;t("update:modelValue",a),t("change",a)}},o=()=>{if(e.loading){const t=i()?e.activeColor:e.inactiveColor;return s(c,{class:X("loading"),color:t},null)}if(a.node)return a.node()};return n((()=>e.modelValue)),()=>{var t;const{size:n,loading:c,disabled:u,activeColor:d,inactiveColor:v}=e,p=i(),m={fontSize:r(n),backgroundColor:p?d:v};return s("div",{role:"switch",class:X({on:p,loading:c,disabled:u}),style:m,tabindex:u?void 0:0,"aria-checked":p,onClick:l},[s("div",{class:X("node")},[o()]),null==(t=a.background)?void 0:t.call(a)])}}});const Z=u(Q),ee=e=>(Y("data-v-47e02415"),e=e(),O(),e),te={class:"editProduct"},ae={class:"tip pt-2 pb-2 pl-4 pr-4"},ie={class:"profit"},le={key:0,class:"flex pl-4 pr-4 input-item pt-3 pb-3"},oe={class:"flex"},ne={class:"flex pl-4 pr-4 input-item pt-3 pb-3"},re={class:"flex"},se={class:"flex pl-4 pr-4 input-item pt-3 pb-3"},ce={class:"flex"},ue={class:"tip pt-2 pb-2 pl-4 pr-4"},de=ee((()=>S("span",null,"%",-1))),ve={class:"tips pt-2 pb-2 pl-4 pr-4"},pe={class:"tip pt-2 pb-2 pl-4 pr-4"},me={class:"tip pt-2 pb-2 pl-4 pr-4"},fe={class:"tip pt-2 pb-2 pl-4 pr-4"},be=ee((()=>S("span",null,"%",-1))),he={style:{margin:"16px"},class:"pb-8"},ge={__name:"productEdit",setup(e){const t=v(),a=p((()=>{})),i=p((()=>{})),l=m(),{t:o}=f(),n=b(),r=h(),c=g(JSON.parse(n.query.item)),u=g(new Date),d=g({sellingPrice:"",isShelf:0,recTime:0,isCombo:!1,startTime:x(),endTime:x(),discount:"",percent:"",profit:"",id:""}),Y=()=>{W.confirm({title:o("product.21"),message:o("product.22"),confirmButtonText:o("sure"),cancelButtonText:o("cancel")}).then((()=>{A({sellerGoodsId:d.value.id}).then((()=>{sessionStorage.setItem("currentProductId",c.value.id),sessionStorage.setItem("productDelete",!0),j(o("product.10")),r.go(-1)}))})).catch((()=>{}))},O=g(!1),X=g(!1),L=g(!1),Q=g(""),ee=g(""),ge=g(""),xe=p((()=>{let e={};return t.userInfo.token?e={...t.userInfo}:r.push("/login"),e}));w((()=>{Ve()})),y((()=>d.value.percent),((e,t)=>{})),y((()=>d.value.isCombo),((e,t)=>{e&&ye()})),y((()=>d.value.isShelf),((e,t)=>{e/1==1&&Te()}));const we=g(!1),ye=()=>{we.value=!0,_e()},ke=g(!1),Te=()=>{ke.value=!0,_e()},Ce=k({min:"",max:""}),Ve=()=>{d.value.id=c.value.id,d.value.discountPrice=c.value.discountPrice?(c.value.discountPrice/1).toFixed(2):(c.value.sellingPrice/1).toFixed(2),d.value.sellingPrice=(c.value.sellingPrice/1).toFixed(2),d.value.isShelf=c.value.isShelf/1,d.value.isCombo=Boolean(c.value.isCombo/1),d.value.recTime=c.value.recTime/1,d.value.startTime=c.value.discountStartTime?c.value.discountStartTime.split(" ")[0]:c.value.discountStartTime,d.value.endTime=c.value.discountEndTime?c.value.discountEndTime.split(" ")[0]:c.value.discountEndTime,d.value.profit=(100*c.value.profitRatio).toFixed(2),d.value.discount=(100*c.value.discountRatio).toFixed(2),d.value.systemPrice=(c.value.systemPrice/1).toFixed(2),d.value.percent=(c.value.profitRatio/1*100).toFixed(2),Ce.min=Number(c.value.sysParaMin),Ce.max=Number(c.value.sysParaMax),q().then((e=>{Ce.min=Number(e.sysParaMin),Ce.max=Number(e.sysParaMax)}))},Pe=()=>{if([].includes("tiktokMall"))if(xe.value.phoneverif&&xe.value.emailverif)_e();else{const e=xe.value.phoneverif?"bindEmailTips":"请绑定手机号";W.confirm({title:o("dialogTips"),message:o(e),cancelButtonText:o("cancel"),confirmButtonText:o("gotoSet"),confirmButtonColor:"#1552F0",cancelButtonColor:"#999"}).then((()=>{B("/personalInfo")})).catch((()=>{}))}else _e()},_e=()=>{const{startTime:e,endTime:t,discount:a}=d.value;if(e||t){const i=new Date(e.replace(/-/g,"/")+" 00:00:00").getTime(),l=new Date(t.replace(/-/g,"/")+" 00:00:00").getTime();if(!a)return we.value=!1,ke.value=!1,d.value.isCombo=!1,d.value.isShelf=!1,void j(o("请设置折扣比例"));if(i>l)return we.value=!1,ke.value=!1,d.value.isCombo=!1,d.value.isShelf=!1,void j(o("开始时间应小于结束时间"))}if(Number(a)&&(!e||!t))return we.value=!1,ke.value=!1,d.value.isCombo=!1,d.value.isShelf=!1,void j(o("请正确填写活动开启时间和结束时间"));if(!Fe()){j.loading({forbidClick:!0,loadingType:"spinner",duration:0});let e={isShelf:d.value.isShelf?1:0,recTime:d.value.recTime?1:0,isCombo:d.value.isCombo?1:0,sellerGoodsId:c.value.id,startTime:d.value.startTime?d.value.startTime+" 00:00:00":"",endTime:d.value.endTime?d.value.endTime+" 00:00:00":"",discount:(d.value.discount/100).toFixed(2),percent:(d.value.percent/100).toFixed(2),profit:(d.value.percent/100).toFixed(2)};return H(e).then((e=>{sessionStorage.setItem("currentProductId",c.value.id),we.value?we.value=!1:ke.value?ke.value=!1:(j(o("product.10")),r.push("/product"))})).catch((e=>{const t=e.msg;if(we.value=!1,ke.value=!1,t.indexOf("未购买")>-1)j(o("您暂未购买直通车套餐，请购买再试"));else if(t.indexOf("已到期")>-1)j(o("您的直通车已到期"));else if(t.indexOf("最多推广")>-1){const e=t.split("最多推广商品数量为");j(o("最多推广商品数量为")+": "+e[1])}else if(t.indexOf("未激活")>-1)j(o("您的直通未激活"));else if(t.indexOf("未激活")>-1)j(o("您的直通未激活"));else if(t.indexOf("最小下架")>-1)j(o("少于店铺设置最小下架商品数"));else if(t.indexOf("首次上架")>-1){const a="string"==typeof e.data?JSON.parse(e.data):e.data;j(o(t,{_$1:a._$1}))}else j(o(t));d.value.isCombo=!1}))}},Se=e=>{switch(e){case 1:O.value=!0;break;case 2:X.value=!0;break;case 3:L.value=!0}},$e=e=>{switch(e){case 1:O.value=!1,d.value.startTime=x(Q.value).format("YYYY-MM-DD");break;case 2:X.value=!1,d.value.endTime=x(ee.value).format("YYYY-MM-DD");break;case 3:L.value=!1,d.value.recTime=ze(ge.value)}},Ne=e=>{switch(e){case 1:O.value=!1;break;case 2:X.value=!1;break;case 3:L.value=!1}},ze=e=>e.getFullYear()+"-"+(e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-"+(e.getDate()<10?"0"+e.getDate():e.getDate())+" "+(e.getHours()<10?"0"+e.getHours():e.getHours())+":"+(e.getMinutes()<10?"0"+e.getMinutes():e.getMinutes())+":"+(e.getSeconds()<10?"0"+e.getSeconds():e.getSeconds()),je=p((()=>{let e=0;if(!isNaN(d.value.sellingPrice)&&!isNaN(d.value.discount)){const t=Number(d.value.systemPrice),a=Number((Number(d.value.discount)/100).toFixed(2)),i=Number(d.value.sellingPrice)*(1-a);e=Number(i-t)}return e.toFixed(2)})),De=()=>{const e=Number(d.value.systemPrice),t=Number((Number(d.value.percent)/100).toFixed(2));d.value.sellingPrice=Number((e+e*t).toFixed(2))},Fe=()=>{const e=Number(d.value.percent),t=Number(d.value.discount);let a=!1;if(!isNaN(e)){const i=/^\+?[1-9][0-9]*$/;if(!i.test(String(e)))return j(o("百分比必须为正整数")),!0;if(t&&!i.test(String(t)))return j(o("折扣比例必须为正整数")),!0;e>Ce.max&&(d.value.percent=Ce.max,a=!0,De()),e<Ce.min&&(d.value.percent=Ce.min,a=!0,De())}return a&&Ce.min&&Ce.max&&j(o("百分比设置范围为")+`：${Ce.min}% ~ ${Ce.max}%`),a},Ie=()=>{const e=Number(d.value.sellingPrice),t=Number(d.value.systemPrice),a=Number(((e-t)/t*100).toFixed(2));d.value.percent=a};return(e,t)=>{const n=T("fx-header"),r=G,c=R,v=Z,p=C,m=J,f=V,b=E,h=K;return D(),P("div",te,[s(n,{fixed:""},{title:_((()=>[S("div",null,F(e.$t("编辑商品")),1)])),_:1}),S("div",{class:N(["edit-product-pop",{"is-ar":z(l)}])},[$("        <div class=\"title\">{{$t('编辑商品')}}</div>"),s(h,null,{default:_((()=>[S("div",ae,F(e.$t("当前售价")),1),s(c,{class:"input-field",inset:""},{default:_((()=>[$(' <van-field v-model="fromData.discountPrice" readonly :placeholder="$t(\'当前售价\')"> '),s(r,{modelValue:d.value.sellingPrice,"onUpdate:modelValue":t[0]||(t[0]=e=>d.value.sellingPrice=e),type:"number",placeholder:e.$t("当前售价"),onInput:Ie},{button:_((()=>[$(" <span class=\"profit\">{{$t('利润')}} {{ (fromData.discountPrice - fromData.systemPrice).toFixed(2) }}</span> "),S("span",ie,F(e.$t("利润"))+" "+F(z(je)),1)])),_:1},8,["modelValue","placeholder"])])),_:1}),z(a)?$("v-if",!0):(D(),P("div",le,[S("div",null,F(e.$t("是否上架")),1),S("div",oe,[s(v,{modelValue:d.value.isShelf,"onUpdate:modelValue":t[1]||(t[1]=e=>d.value.isShelf=e),size:"25","inactive-color":"#fff","inactive-value":0,"active-value":1},null,8,["modelValue"])])])),S("div",ne,[S("div",null,F(e.$t("是否推荐")),1),S("div",re,[s(v,{modelValue:d.value.recTime,"onUpdate:modelValue":t[2]||(t[2]=e=>d.value.recTime=e),"inactive-value":0,"active-value":1,size:"25","inactive-color":"#fff"},null,8,["modelValue"])])]),$('          <div class="tip pt-2 pb-2 pl-4 pr-4" v-if="fromData.isRecommend">{{$t(\'推荐时间\')}}</div>'),$('          <van-cell-group v-if="fromData.isRecommend" class="input-field" inset>'),$('            <van-field @click-input="onClick(3)"  v-model="fromData.recTime" :placeholder="t(\'推荐时间\')">'),$("            </van-field>"),$("          </van-cell-group>"),$('          <van-popup v-model:show="isRecommendShow"'),$("                     round"),$('                     position="bottom"'),$("          >"),$("            <van-datetime-picker"),$("                :confirm-button-text=\"$t('确定')\""),$("                :cancel-button-text=\"$t('取消')\""),$('                v-model="recommendTime"'),$('                type="datetime"'),$("                :title=\"t('选择完整时间')\""),$('                @confirm="onConfirm(3)"'),$('                @cancel="onCancel(3)"'),$("            />"),$("          </van-popup>"),S("div",se,[S("div",null,F(e.$t("直通车")),1),S("div",ce,[s(v,{loading:we.value,modelValue:d.value.isCombo,"onUpdate:modelValue":t[3]||(t[3]=e=>d.value.isCombo=e),size:"25","inactive-color":"#fff"},null,8,["loading","modelValue"])])]),S("div",ue,F(e.$t("百分比")),1),s(c,{class:"input-field",inset:""},{default:_((()=>[s(r,{modelValue:d.value.percent,"onUpdate:modelValue":t[4]||(t[4]=e=>d.value.percent=e),type:"number",placeholder:z(o)("百分比"),rules:[{required:!0,message:z(o)("请填写百分比"),max:Ce.max,min:Ce.min}],onInput:De,onBlur:Fe},{button:_((()=>[de])),_:1},8,["modelValue","placeholder","rules"])])),_:1}),S("div",ve,[I(F(e.$t("将选中的商品发布到你的店铺，并填写商品利润比例，推荐比例"))+": ",1),S("span",null,F(Ce.min)+"%-"+F(Ce.max)+"%",1)]),S("div",pe,F(e.$t("折扣开始日期")),1),s(c,{class:"input-field",inset:"",style:{position:"relative"}},{default:_((()=>[d.value.startTime?(D(),P("div",{key:0,class:"time-clear",onClick:t[5]||(t[5]=e=>d.value.startTime="")},[s(p,{name:"cross"})])):$("v-if",!0),s(r,{onClickInput:t[6]||(t[6]=e=>Se(1)),modelValue:d.value.startTime,"onUpdate:modelValue":t[7]||(t[7]=e=>d.value.startTime=e),placeholder:z(o)("折扣开始日期")},null,8,["modelValue","placeholder"]),s(f,{show:O.value,"onUpdate:show":t[11]||(t[11]=e=>O.value=e),round:"",position:"bottom"},{default:_((()=>[s(m,{"min-date":u.value,"confirm-button-text":e.$t("确定"),"cancel-button-text":e.$t("取消"),modelValue:Q.value,"onUpdate:modelValue":t[8]||(t[8]=e=>Q.value=e),type:"date",title:z(o)("选择完整时间"),onConfirm:t[9]||(t[9]=e=>$e(1)),onCancel:t[10]||(t[10]=e=>Ne(1))},null,8,["min-date","confirm-button-text","cancel-button-text","modelValue","title"])])),_:1},8,["show"])])),_:1}),S("div",me,F(e.$t("折扣结束日期")),1),s(c,{class:"input-field",inset:"",style:{position:"relative"}},{default:_((()=>[d.value.endTime?(D(),P("div",{key:0,class:"time-clear",onClick:t[12]||(t[12]=e=>d.value.endTime="")},[s(p,{name:"cross"})])):$("v-if",!0),s(r,{onClickInput:t[13]||(t[13]=e=>Se(2)),modelValue:d.value.endTime,"onUpdate:modelValue":t[14]||(t[14]=e=>d.value.endTime=e),placeholder:z(o)("折扣结束日期")},null,8,["modelValue","placeholder"])])),_:1}),S("div",fe,F(e.$t("折扣比例")),1),s(c,{class:"input-field",inset:""},{default:_((()=>[s(r,{modelValue:d.value.discount,"onUpdate:modelValue":t[15]||(t[15]=e=>d.value.discount=e),type:"number",placeholder:z(o)("折扣比例")},{button:_((()=>[be])),_:1},8,["modelValue","placeholder"])])),_:1}),s(f,{show:X.value,"onUpdate:show":t[19]||(t[19]=e=>X.value=e),round:"",position:"bottom"},{default:_((()=>[s(m,{"min-date":u.value,"cancel-button-text":e.$t("取消"),"confirm-button-text":e.$t("确定"),modelValue:ee.value,"onUpdate:modelValue":t[16]||(t[16]=e=>ee.value=e),type:"date",title:z(o)("选择完整时间"),onConfirm:t[17]||(t[17]=e=>$e(2)),onCancel:t[18]||(t[18]=e=>Ne(2))},null,8,["min-date","cancel-button-text","confirm-button-text","modelValue","title"])])),_:1},8,["show"]),S("div",he,[s(b,{class:"btn-content",block:"",type:"primary",onClick:Pe,"native-type":"submit"},{default:_((()=>[I(F(e.$t("保存")),1)])),_:1}),z(i)?$("v-if",!0):(D(),M(b,{key:0,style:{"margin-top":"16px","background-color":"red","border-radius":"4px","border-color":"red"},block:"",type:"primary",onClick:U(Y,["stop"]),"native-type":"submit"},{default:_((()=>[I(F(e.$t("删除")),1)])),_:1},8,["onClick"]))])])),_:1})],2)])}}};e("default",d(ge,[["__scopeId","data-v-47e02415"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/product/components/productEdit.vue"]]))}}}));
