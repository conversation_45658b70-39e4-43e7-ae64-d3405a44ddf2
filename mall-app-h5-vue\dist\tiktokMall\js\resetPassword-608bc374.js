import{_ as e,l as a,Y as s,u as l,r as t,m as d,q as r,av as o,c as u,e as p,w as i,a as n,b as c,T as v,cw as m,o as f,f as w,t as x,D as h,E as y}from"./index-3d21abf8.js";import{B as P}from"./index-2406f514.js";import{E as b}from"./index-9c8e9dca.js";import"./use-route-cd41a893.js";/* empty css              *//* empty css               */import"./index-8c1841f6.js";import"./index-7dfcb82a.js";import"./use-id-a0619e01.js";const T={class:"changePassword"},_=(e=>(h("data-v-9dddd8bf"),e=e(),y(),e))((()=>n("div",{class:"line"},null,-1))),g={class:"content"},j=e({__name:"resetPassword",setup(e){const h=a(),y=s(),{t:j}=l(),$=t(""),V=t(""),q=t(""),U=t(""),k=t(""),C=t(""),D=d((()=>$.value.length>=6&&V.value.length>=6));r((()=>{q.value=y.query.type,U.value=y.query.username,C.value=y.query.account,k.value=y.query.verifycode}));const E=()=>$.value.length<6||$.value.length>20?(v(j("setPasswordTips")),!1):$.value!==V.value?(v(j("noSamePassword")),!1):void m({username:1==q.value?C.value:U.value,password:$.value,verifcode_type:q.value,verifcode:k.value}).then((e=>{h.push("/passSuccess")}));return(e,a)=>{const s=o("fx-header"),l=P;return f(),u("div",T,[p(s,null,{title:i((()=>[w(x(e.$t("changeLoginPassword")),1)])),_:1}),_,n("div",g,[p(b,{label:e.$t("newPassword"),placeholderText:e.$t("entryPassword"),tips:e.$t("setPasswordTips"),modelValue:$.value,"onUpdate:modelValue":a[0]||(a[0]=e=>$.value=e),typeText:"password"},null,8,["label","placeholderText","tips","modelValue"]),p(b,{label:e.$t("sureNewPassword"),placeholderText:e.$t("entryPassword"),tips:e.$t("setPasswordTips"),modelValue:V.value,"onUpdate:modelValue":a[1]||(a[1]=e=>V.value=e),typeText:"password"},null,8,["label","placeholderText","tips","modelValue"]),p(l,{class:"w-full",disabled:!c(D),style:{"margin-top":"22px"},type:"primary",onClick:E},{default:i((()=>[w(x(e.$t("sure")),1)])),_:1},8,["disabled"])])])}}},[["__scopeId","data-v-9dddd8bf"],["__file","C:/Users/<USER>/Desktop/前端/mall-app-h5-vue-fix(卖家端开源可以编译的)/mall-app-h5-vue/src/views/forget/resetPassword.vue"]]);export{j as default};
